# Merge Checklist

- [ ] Security impact of change has been considered
- [ ] Code follows [company security practices](https://kalepa.atlassian.net/wiki/spaces/IN/pages/1617592340/Secure+Development+Policy) and [guidelines](https://owasp.org/www-pdf-archive/OWASP_SCP_Quick_Reference_Guide_v2.pdf)
- [ ] I have verified that my migration is compatible with the blue/green deployment strategy.
- [ ] I have reviewed any changes to the V3 Report and Submission APIs to verify compatibility with external clients.
- [ ] I have verified that my migration is safe. See the [Postgres 11 documentation](https://www.postgresql.org/docs/11/sql-altertable.html) and this [incomplete cheat sheet](https://leopard.in.ua/2016/09/20/safe-and-unsafe-operations-postgresql#.YElgUXVKgUE).
- [ ] Tests section below is filled out, describing what kind of tests were performed for this PR

### Tests Performed
