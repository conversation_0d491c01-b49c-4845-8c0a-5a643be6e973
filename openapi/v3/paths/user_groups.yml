get:
  x-openapi-router-controller: copilot.v3.controllers.user_groups
  operationId: get_user_groups
  parameters:
    - name: organization_id
      in: query
      required: false
      schema:
        type: number
        format: integer
        nullable: true
      example: 3
  responses:
    "200":
      description: list of user groups
      content:
        application/json:
          schema:
            type: array
            items:
              $ref: "../../v3.yml#/components/schemas/UserGroup"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
post:
  x-openapi-router-controller: copilot.v3.controllers.user_groups
  operationId: create_user_group
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/UserGroup"
  responses:
    "201":
      description: user group created
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/UserGroup"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "409":
      description: Conflict when creating user group.
    "500":
      description: an unexpected error occurred.
