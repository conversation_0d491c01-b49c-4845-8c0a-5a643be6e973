get:
  parameters:
    - in: query
      name: organization_id
      required: false
      schema:
        type: integer
        nullable: true
      description: the ID of the Organization that owns the Classifier.
    - in: query
      name: active_only
      required: false
      schema:
        type: boolean
    - in: query
      name: is_business_classifier
      required: false
      schema:
        type: boolean
    - in: query
      name: is_submission_classifier
      required: false
      schema:
        type: boolean
    - in: query
      name: fact_subtype_id
      required: false
      schema:
        type: string
        example: HAS_SAUNA
        nullable: true
    - in: query
      name: with_latest_test_run
      required: false
      schema:
        type: boolean
      description: true if you want to have latest_test_run filled, otherwise that filled is always null
  x-openapi-router-controller: copilot.v3.controllers.customization
  operationId: get_customizable_classifiers
  responses:
    "200":
      description: The array of CustomizableClassiers was returned successfully.
      content:
        application/json:
          schema:
            type: object
            properties:
              customizable_classifiers:
                type: array
                items:
                  $ref: "../../v3.yml#/components/schemas/CustomizableClassifier"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
post:
  x-openapi-router-controller: copilot.v3.controllers.customization
  operationId: create_customizable_classifier
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/CustomizableClassifier"
  responses:
    "201":
      description: The CustomizableClassifier was created successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/CustomizableClassifier"
    "400":
      description: the request is invalid.
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
