parameters:
  - in: path
    name: task_definition_id
    required: true
    schema:
      type: string
      format: uuid
    description: The code of the task definition to retrieve.
  - in: query
    name: organization_id
    required: false
    schema:
      type: integer
    description: The organization ID to filter the task definition models by.
  - in: query
    name: task_dataset_input_id
    required: false
    schema:
      type: string
      format: uuid
    description: The task dataset input ID to get outcome models if exist.
  - in: query
    name: use_cost_fallback
    required: false
    schema:
      type: boolean
      default: false
    description: Whether to include task definition models that are designated as cost fallbacks.
get:
  x-openapi-router-controller: copilot.v3.controllers.task_definitions
  operationId: get_task_definition_by_id
  responses:
    "200":
      description: The task definition was retrieved successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/TaskDefinition"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.

patch:
  x-openapi-router-controller: copilot.v3.controllers.task_definitions
  operationId: update_task_definition
  parameters:
    - name: task_definition_id
      in: path
      required: true
      schema:
        type: string
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/TaskDefinition"
  responses:
    "200":
      description: The task definition was updated successfully.
    "400":
      description: The request is invalid.
    "404":
      description: The resource was not found.


delete:
  x-openapi-router-controller: copilot.v3.controllers.task_definitions
  operationId: delete_task_definition
  parameters:
    - name: task_definition_id
      in: path
      required: true
      schema:
        type: string
  responses:
    "204":
      description: Task definition was deleted successfully.
    "400":
      description: The request is invalid
    "404":
      description: The resource was not found
