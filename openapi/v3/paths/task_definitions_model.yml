get:
  x-openapi-router-controller: copilot.v3.controllers.task_definition_models
  operationId: get_task_definition_model
  parameters:
    - name: task_definition_model_id
      in: path
      required: true
      schema:
        type: string
  responses:
    "200":
      description: The task definition model was retrieved successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/TaskDefinitionModel"
    "404":
      description: the resource was not found.
patch:
  x-openapi-router-controller: copilot.v3.controllers.task_definition_models
  operationId: update_task_definition_model
  parameters:
    - name: task_definition_model_id
      in: path
      required: true
      schema:
        type: string
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/TaskDefinitionModel"
  responses:
    "200":
      description: The task definition model was updated successfully.
    "400":
      description: The request is invalid.
    "404":
      description: The resource was not found.

delete:
  x-openapi-router-controller: copilot.v3.controllers.task_definition_models
  operationId: delete_task_definition_model
  parameters:
    - name: task_definition_model_id
      in: path
      required: true
      schema:
        type: string
  responses:
    "204":
      description: Task definition model was deleted successfully.
    "400":
      description: The request is invalid
    "404":
      description: The resource was not found
