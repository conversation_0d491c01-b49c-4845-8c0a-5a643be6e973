parameters:
  - in: path
    name: id
    required: true
    schema:
      type: string
      format: uuid
patch:
  parameters:
    - in: query
      name: is_waiting_for
      required: false
      schema:
        type: boolean
      description: true if inviting user is waiting for grantee
  x-openapi-router-controller: copilot.v3.controllers.permissions
  operationId: update_permission
  requestBody:
    description: Updates permission
    content:
      application/json:
        schema:
          $ref: '../../v3.yml#/components/schemas/Permission'
  responses:
    "200":
      description: permission was updated successfully
    "400":
      description: the specified ID is not a valid UUID
    "404":
      description: permission was not found
delete:
  x-openapi-router-controller: copilot.v3.controllers.permissions
  operationId: delete_permission
  responses:
    "204":
      description: the permission was deleted successfully
    "400":
      description: the specified ID is not a valid UUID
    "404":
      description: permission was not found
    "500":
      description: an unexpected error occurred
