patch:
  x-openapi-router-controller: copilot.v3.controllers.task_models
  operationId: update_task_model
  parameters:
    - name: task_model_id
      in: path
      required: true
      schema:
        type: string
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/TaskModel"
  responses:
    "200":
      description: The task model was updated successfully.
    "400":
      description: The request is invalid.
    "404":
      description: The resource was not found.
delete:
  x-openapi-router-controller: copilot.v3.controllers.task_models
  operationId: delete_task_model
  parameters:
    - name: task_model_id
      in: path
      required: true
      schema:
        type: string
  responses:
    "204":
      description: Task model was deleted successfully.
    "400":
      description: The request is invalid
    "404":
      description: The resource was not found

get:
  x-openapi-router-controller: copilot.v3.controllers.task_models
  operationId: get_task_model
  parameters:
    - name: task_model_id
      in: path
      required: true
      schema:
        type: string
  responses:
    "200":
      description: The task model was retrieved successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/TaskModel"
    "404":
      description: the resource was not found.
