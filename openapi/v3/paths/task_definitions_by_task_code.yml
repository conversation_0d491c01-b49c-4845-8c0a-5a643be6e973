parameters:
  - in: path
    name: task_code
    required: true
    schema:
      type: string
    description: The code of the task definition to retrieve.
  - in: query
    name: organization_id
    required: false
    schema:
      type: integer
    description: The organization ID to filter the task definition models by.
  - in: query
    name: task_dataset_input_id
    required: false
    schema:
      type: string
      format: uuid
    description: The task dataset input ID to get outcome models if exist.
  - in: query
    name: use_cost_fallback
    required: false
    schema:
      type: boolean
      default: false
    description: Whether to include task definition models that are designated as cost fallbacks.
get:
  x-openapi-router-controller: copilot.v3.controllers.task_definitions
  operationId: get_task_definition
  responses:
    "200":
      description: The task definition was retrieved successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/TaskDefinition"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
