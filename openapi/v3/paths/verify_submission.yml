parameters:
  - $ref: '../../v3.yml#/components/parameters/submission_idParam'
  - in: query
    name: force
    required: false
    schema:
      type: boolean
  - in: query
    name: manual
    required: false
    schema:
      type: boolean
  - in: query
    name: send_alerts
    required: false
    schema:
      type: boolean
  - in: query
    name: force_verify
    required: false
    schema:
      type: boolean
  - in: query
    name: verify_for_user_id
    required: false
    schema:
      type: integer
post:
  x-openapi-router-controller: copilot.v3.controllers.submission_verification
  operationId: verify
  requestBody:
    content:
      application/json:
        schema:
          type: object
          nullable: true
  responses:
    "200":
      description: The request to verify submission was processed successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/VerificationResult"
    "400":
      description: the request is invalid.
    "409":
      description: The submission was already verified.
    "500":
      description: an unexpected error occurred.
