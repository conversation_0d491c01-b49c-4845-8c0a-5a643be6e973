parameters:
  - in: path
    name: user_id
    required: true
    schema:
      type: integer
    description: the ID of reports owner
get:
  x-openapi-router-controller: copilot.v3.controllers.users
  operationId: get_reports_by_user
  parameters:
    - $ref: "../../v3.yml#/components/parameters/expandParam"
    - $ref: "../../v3.yml#/components/parameters/pageParam"
    - $ref: "../../v3.yml#/components/parameters/perPageParam"
    - name: broker_ids
      in: query
      description: Filter reports by specified broker ids
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: broker_group_ids
      in: query
      description: Filter reports by specified broker group ids
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: brokerage_ids
      in: query
      description: Filter reports by specified brokerage ids
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: stage
      in: query
      description: The Stages of the Report's Submission
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
          enum:
            - ALL
            - INDICATED
            - ON_MY_PLATE
            - WAITING_FOR_OTHERS
            - QUOTED
            - COMPLETED
            - DECLINED
            - QUOTED_LOST
            - QUOTED_BOUND
            - CLEARING_ISSUE
            - BLOCKED
            - EXPIRED
            - CANCELED
        default: ["ALL"]
      example: "ON_MY_PLATE"
    - name: coverages
      in: query
      description: Filter reports by specified coverages Ids
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: coverage_operator
      in: query
      required: false
      schema:
        type: string
        enum:
          - AND
          - OR
    - name: only_unassigned
      in: query
      description: whether to include only Reports that are unassigned.
      required: false
      schema:
        type: boolean
    - name: only_referred
      in: query
      required: false
      schema:
        type: boolean
    - name: only_not_referred
      in: query
      required: false
      schema:
        type: boolean
    - name: assignees
      in: query
      description: Filter reports by assigned underwriters
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: number
          format: integer
    - name: referred_to
      in: query
      required: false
      schema:
        type: array
        items:
          type: number
          format: integer
    - name: assignee_groups
      in: query
      description: Filter reports by specified assignee user group ids
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: recommended_v2_action
      in: query
      description: The Recommended Action of the Report's Submission.
      required: false
      schema:
        type: string
        enum:
          - "DECLINE"
          - "REFER"
          - "NO_ACTION"
          - "FLAG_FOR_REVIEW"
          - "NOTICE"
          - "RED_FLAG"
          - "PREFERRED"
    - in: query
      name: before
      description: Date 'to' of Report's Submission. See date_type.
      required: false
      schema:
        type: string
        format: date-time
      example: "04/26/2021"
    - in: query
      name: after
      description: Date 'from' of Report's Submission. See date_type.
      required: false
      schema:
        type: string
        format: date-time
      example: "04/26/2021"
    - in: query
      name: date_type
      description: Type of date for which after/before filters are applicable.
      required: false
      schema:
        type: string
        enum:
          - "PROPOSED_EFFECTIVE_DATE"
          - "CREATED_AT"
          - "DUE_DATE"
      example: "CREATED_AT"
    - in: query
      name: before_second
      description: 2nd Date 'to' of Report's Submission. See date_type.
      required: false
      schema:
        type: string
        format: date-time
      example: "04/26/2021"
    - in: query
      name: after_second
      description: 2nd Date 'from' of Report's Submission. See date_type.
      required: false
      schema:
        type: string
        format: date-time
      example: "04/26/2021"
    - in: query
      name: date_type_second
      description: 2nd Type of date for which after/before filters are applicable.
      required: false
      schema:
        type: string
        enum:
          - "PROPOSED_EFFECTIVE_DATE"
          - "CREATED_AT"
          - "DUE_DATE"
      example: "CREATED_AT"
    - name: name
      in: query
      description: The name of the Report.
      required: false
      schema:
        type: string
      example: "The Essential"
    - name: declination_reason
      in: query
      description: the reason for declining the Submission.
      required: false
      schema:
        type: string
      example: "High Exposure/Risk"
    - name: only_owned_by_me
      in: query
      description: whether to include only Reports that are owned by the user.
      required: false
      schema:
        type: boolean
    - name: only_shared_with_me
      in: query
      description: whether to include only Reports that were shared with the user.
      required: false
      schema:
        type: boolean
    - name: clearing_stage
      in: query
      description: what clearing stage to include in the response.
      required: false
      schema:
        type: string
        enum:
          - "ALL"
          - "PENDING_CLEARING"
          - "PRE_CLEARING"
          - "IN_CLEARING_CONFLICT"
          - "PENDING_LIGHT_CLEARING"
          - "BLOCKED_ON_CLEARING"
          - "POST_CLEARING"
          - "MISSING_DETAILS"
        default: "ALL"
    - name: only_bookmarked
      in: query
      description: whether to include only Reports that were shared with the user.
      required: false
      schema:
        type: boolean
    - name: only_not_bookmarked
      in: query
      required: false
      schema:
        type: boolean
    - name: only_with_client_id
      in: query
      required: false
      schema:
        type: boolean
    - name: include_email
      in: query
      required: false
      schema:
        type: boolean
    - name: include_unverified
      in: query
      required: false
      schema:
        type: boolean
    - name: read
      in: query
      required: false
      schema:
        type: boolean
    - name: owned_by_user
      in: query
      description: include only Reports that are owned by the specified user.
      required: false
      schema:
        type: string
        format: email
        example: <EMAIL>
    - name: only_recommended
      in: query
      description: whether to include only Reports that are recommended.
      required: false
      schema:
        type: boolean
    - name: only_renewals
      in: query
      description: whether to include only Reports that are renewals.
      required: false
      schema:
        type: boolean
    - name: only_rush
      in: query
      required: false
      schema:
        type: boolean
    - name: only_non_rush
      in: query
      required: false
      schema:
        type: boolean
    - name: fni_state
      in: query
      required: false
      schema:
        type: string
    - name: show_renewals
      in: query
      description: whether to include only Reports that are renewals.
      required: false
      schema:
        type: string
        enum:
          - "Y"
          - "N"
    - name: sorting
      in: query
      description: the keys to use to sort the Reports.
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
          enum:
            - "CREATED_AT"
            - "EFFECTIVE_AT"
            - "DECLINED_AT"
            - "REPORT_NAME"
            - "BROKER_NAME"
            - "BROKERAGE_NAME"
            - "DUE_DATE"
            - "STAGE"
            - "RECOMMENDATION_V2"
            - "RECOMMENDATION_SCORE"
            - "BOOKMARKED"
            - "READ"
            - "NAICS"
            - "EXPIRED_PREMIUM"
            - "TARGET_PREMIUM"
            - "BOUND_PREMIUM"
            - "QUOTED_PREMIUM"
            - "SALES"
            - "AMOUNT_QUOTED"
            - "AMOUNT_BOUND"
            - "TOTAL_PREMIUM_OR_BOUND_PREMIUM"
            - "ADJUSTED_TIV"
            - "ESTIMATED_PREMIUM"
            - "PAYROLL"
            - "X_MOD_SCORES"
            - "FNI_STATE"
            - "TIV"
            - "ASSIGNEE"
    - name: descending
      in: query
      description: whether to apply each key passed in 'sorting' in the descending order
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: boolean
    - name: naics_2
      in: query
      description: Filter reports by specified 2-digit NAICS code.
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
    - name: naics_6
      in: query
      description: Filter reports by specified 6-digit NAICS code.
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
    - name: report_ids
      in: query
      description: Filter reports by specified ids
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - in: query
      name: account_id
      description: Account ID
      required: false
      schema:
        type: string
    - in: query
      name: target_premium_min
      required: false
      schema:
        type: number
    - in: query
      name: target_premium_max
      required: false
      schema:
        type: number
    - in: query
      name: expired_premium_min
      required: false
      schema:
        type: number
    - in: query
      name: expired_premium_max
      required: false
      schema:
        type: number
    - in: query
      name: sales_min
      required: false
      schema:
        type: number
    - in: query
      name: sales_max
      required: false
      schema:
        type: number
    - name: verified
      in: query
      required: false
      schema:
        type: boolean
    - name: verified_shell
      in: query
      required: false
      schema:
        type: boolean
    - name: only_bundled
      in: query
      required: false
      schema:
        type: boolean
    - name: exclude_bundled
      in: query
      required: false
      schema:
        type: boolean
    - name: with_wc_info
      in: query
      required: false
      schema:
        type: boolean
    - name: client_stage_ids
      in: query
      required: false
      schema:
        type: array
        items:
          type: number
    - name: client_clearing_statuses
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
    - name: org_groups
      in: query
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
    - name: only_assigned
      in: query
      required: false
      schema:
        type: boolean
    - name: not_assignees
      in: query
      required: false
      schema:
        type: array
        items:
          type: number
          format: integer
    - name: not_assignee_groups
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: not_referred_to
      in: query
      required: false
      schema:
        type: array
        items:
          type: number
          format: integer
    - name: not_broker_ids
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: not_broker_group_ids
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: not_brokerage_ids
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: not_coverages
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: not_coverage_operator
      in: query
      required: false
      schema:
        type: string
        enum:
          - AND
          - OR
    - name: not_before
      in: query
      required: false
      schema:
        type: string
        format: date-time
    - name: not_after
      in: query
      required: false
      schema:
        type: string
        format: date-time
    - name: not_before_second
      in: query
      required: false
      schema:
        type: string
        format: date-time
    - name: not_after_second
      in: query
      required: false
      schema:
        type: string
        format: date-tim
    - name: not_naics_2
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
    - name: not_naics_6
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
    - name: not_expired_premium_min
      in: query
      required: false
      schema:
        type: number
    - name: not_expired_premium_max
      in: query
      required: false
      schema:
        type: number
    - name: not_target_premium_min
      in: query
      required: false
      schema:
        type: number
    - name: not_target_premium_max
      in: query
      required: false
      schema:
        type: number
    - name: not_org_groups
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: not_recommended_v2_action
      in: query
      required: false
      schema:
        type: string
        enum:
          - "DECLINE"
          - "REFER"
          - "NO_ACTION"
          - "FLAG_FOR_REVIEW"
          - "NOTICE"
          - "RED_FLAG"
          - "PREFERRED"
    - name: not_sales_min
      in: query
      required: false
      schema:
        type: number
    - name: not_sales_max
      in: query
      required: false
      schema:
        type: number
    - name: not_stage
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
          enum:
            - ALL
            - INDICATED
            - ON_MY_PLATE
            - WAITING_FOR_OTHERS
            - QUOTED
            - COMPLETED
            - DECLINED
            - QUOTED_LOST
            - QUOTED_BOUND
            - CLEARING_ISSUE
            - BLOCKED
            - EXPIRED
            - CANCELED
    - name: not_client_stage_ids
      in: query
      required: false
      schema:
        type: array
        items:
          type: number
          format: integer
    - name: not_client_clearing_statuses
      in: query
      required: false
      schema:
        type: array
        items:
          type: string
          format: uuid
    - name: brokerage_offices
      in: query
      required: false
      schema:
        type: string
    - name: not_brokerage_offices
      in: query
      required: false
      schema:
        type: string
    - in: query
      name: insurance_accounting_date_from
      description: Date 'from' of Report's Submission.
      required: false
      schema:
        type: string
        format: date-time
      example: "04/26/2021"
    - in: query
      name: insurance_accounting_date_to
      description: Date 'to' of Report's Submission.
      required: false
      schema:
        type: string
        format: date-time
      example: "04/26/2021"
    - name: sic_2
      in: query
      description: Filter reports by specified 2-digit SIC code
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
    - name: sic_code
      in: query
      description: Filter reports by specific SIC codes
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
    - name: not_sic_2
      in: query
      description: Exclude reports with specified 2-digit SIC code
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
    - name: not_sic_code
      in: query
      description: Exclude reports with specific SIC codes
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          type: string
    - name: score_ml_min
      in: query
      description: Filter reports by minimum ML score
      required: false
      schema:
          type: number
    - name: score_ml_max
      in: query
      description: Filter reports by maximum ML score
      required: false
      schema:
          type: number
    - name: pm_rules_modifier_min
      in: query
      description: Filter reports by minimum PM rules modifier
      required: false
      schema:
          type: number
    - name: pm_rules_modifier_max
      in: query
      description: Filter reports by maximum PM rules modifier
      required: false
      schema:
          type: number
    - name: only_bind_likely
      in: query
      required: false
      schema:
        type: boolean
    - name: include_clearing_user_unassigned
      in: query
      required: false
      schema:
        type: boolean
    - name: exclude_clearing_user_unassigned
      in: query
      required: false
      schema:
        type: boolean
    - name: clearing_assignees
      in: query
      required: false
      schema:
        type: array
        items:
          type: number
          format: integer
    - name: not_clearing_assignees
      in: query
      required: false
      schema:
        type: array
        items:
          type: number
          format: integer
  responses:
    "200":
      description: The Reports were retrieved successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/ReportsEnvelope"
    "400":
      description: the request is invalid.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: the resource was not found.
    "500":
      description: an unexpected error occurred.
