get:
  x-openapi-router-controller: copilot.v3.controllers.async_ops
  operationId: get_async_op_by_id
  parameters:
    - name: async_op_id
      in: path
      required: true
      description: The UUID of the async op to get.
      schema:
        type: string
        format: uuid
  responses:
    "200":
      description: Async op with the specified ID found.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/AsyncOpFullDetails"
    "400":
      description: Invalid or missing query parameters (report_id, operation).
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: No async op found matching the specified ID
    "500":
      description: An unexpected error occurred.
patch:
  x-openapi-router-controller: copilot.v3.controllers.async_ops
  operationId: update_async_op_by_id
  parameters:
    - name: async_op_id
      in: path
      required: true
      description: The UUID of the async op to update.
      schema:
        type: string
        format: uuid
  requestBody:
    description: Fields to update in the async op.
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/AsyncOpUpdateRequest"
  responses:
    "200":
      description: Async op updated successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/AsyncOpCreateResponse"
    "400":
      description: Invalid request body or invalid async_op_id format.
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: Async op with the specified ID not found.
    "500":
      description: An unexpected error occurred.
