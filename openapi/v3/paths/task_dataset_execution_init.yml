post:
  x-openapi-router-controller: copilot.v3.controllers.task_dataset.task_dataset
  operationId: init_task_dataset_execution
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3/schemas/init_task_dataset_execution_request.yml"
  responses:
    "201":
      description: Task dataset execution initialized successfully
      content:
        application/json:
          schema:
            $ref: "../../v3/schemas/init_task_dataset_execution_response.yml"
    "400":
      description: The request is invalid
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: Task dataset inputs not found
    "409":
      description: Task dataset execution already exists
    "500":
      description: An unexpected error occurred 
