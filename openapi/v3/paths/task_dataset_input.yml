post:
  x-openapi-router-controller: copilot.v3.controllers.task_dataset.task_dataset_input
  operationId: upsert_dataset_input
  summary: Create or update a task dataset input
  description: Creates a new task dataset input if it doesn't exist, or updates an existing one if it does
  requestBody:
    required: true
    content:
      application/json:
        schema:
          type: object
          properties:
            id:
              type: string
              format: uuid
              description: ID of the task dataset input
              nullable: true
            dataset_inputs_id:
              type: string
              format: uuid
              description: ID of the dataset inputs group this input belongs to
            submission_id:
              type: string
              format: uuid
              description: Optional ID of related submission
              nullable: true
            file_id:
              type: string
              format: uuid
              description: Optional ID of related file
              nullable: true
            business_id:
              type: string
              format: uuid
              description: Optional ID of related business
              nullable: true
            context:
              type: object
              description: Optional context data for the input
              nullable: true
            input:
              type: object
              description: Input data
              nullable: true
          required:
            - dataset_inputs_id
  responses:
    '200':
      description: Dataset input updated successfully
    '201':
      description: Dataset input created successfully
    '400':
      description: Bad request - missing required fields
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
    '401':
      description: Unauthorized - authentication required
    '404':
      description: Related task dataset not found
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
    '500':
      description: Internal server error 
