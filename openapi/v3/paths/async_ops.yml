post:
  x-openapi-router-controller: copilot.v3.controllers.async_ops
  operationId: create_new_async_op
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/AsyncOpCreateRequest"
  responses:
    "201":
      description: new async op created
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/AsyncOpCreateResponse"
    "400":
      description: the request is invalid
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred
get:
  x-openapi-router-controller: copilot.v3.controllers.async_ops
  operationId: get_latest_async_op_by_report_and_operation
  parameters:
    - name: report_id
      in: query
      required: true
      description: The UUID of the report associated with the op.
      schema:
        type: string
        format: uuid
    - name: operation
      in: query
      required: true
      description: The async operation name.
      schema:
        type: string
  responses:
    "200":
      description: Latest async op found for the given criteria.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/AsyncOpLowDetails"
    "400":
      description: Invalid or missing query parameters (report_id, operation).
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: No async op found matching the specified report ID and operation.
    "500":
      description: An unexpected error occurred.
