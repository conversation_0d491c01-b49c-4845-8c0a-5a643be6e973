get:
  x-openapi-router-controller: copilot.v3.controllers.fact_subtype_selection_benchmark
  operationId: get_latest_fact_subtype_selection_benchmarks
  parameters:
    - name: amount
      in: query
      required: false
      schema:
        type: integer
  responses:
    "200":
      description: Successfully retrieved latest benchmarks
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              properties:
                benchmark_id:
                  type: string
                  description: Identifier for the benchmark.
                file_type:
                  type: string
                  description: Type of the file.
    "400":
      description: Invalid request parameters.
    "401":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "500":
      description: an unexpected error occurred.
