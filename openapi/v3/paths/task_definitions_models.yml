post:
  x-openapi-router-controller: copilot.v3.controllers.task_definition_models
  operationId: create_task_definition_model
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/TaskDefinitionModel"
  responses:
    "201":
      description: The task definition model was created successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/TaskDefinitionModel"
    "400":
      description: The request is invalid.
