post:
  x-openapi-router-controller: copilot.v3.controllers.task_models
  operationId: create_task_model
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/TaskModel"
  responses:
    "201":
      description: The task model was created successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/TaskModel"
    "400":
      description: The request is invalid.
