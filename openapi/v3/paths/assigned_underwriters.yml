parameters:
  - $ref: '../../v3.yml#/components/parameters/submission_idParam'
post:
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: add_submission_user
  parameters:
    - name: source
      in: query
      required: false
      schema:
        type: string
        enum:
          - "EMAIL"
          - "AUTO"
          - "MANUAL"
          - "RECOMMENDATIONS"
          - "SYNC"
  requestBody:
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/SubmissionUser"
  responses:
    "201":
      description: The SubmissionUser was created successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/SubmissionUser"
    "400":
      description: the request is invalid.
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: Submission with the specified ID was not found.
    "422":
      description: the request was well-formed, but the server refused to process it.
    "500":
      description: an unexpected error occurred.
delete:
  x-openapi-router-controller: copilot.v3.controllers.submissions
  operationId: delete_submission_user
  parameters:
    - in: query
      name: user_id
      required: true
      schema:
        type: integer
      description: the ID of the user to delete
    - in: query
      name: ignore_frozen
      required: false
      schema:
        type: boolean
        default: false
      description: when set to true ignores the check for frozen submissions
  responses:
    "204":
      description: the Submission's User was deleted successfully.
    "400":
      description: the specified ID is not a valid UUID.
    "403":
      $ref: "../../v3.yml#/components/responses/UnauthorizedError"
    "404":
      description: either Submission or User with the specified ID was not found.
    "422":
      description: the request was well-formed, but the server refused to process it.
    "500":
      description: an unexpected error occurred.
