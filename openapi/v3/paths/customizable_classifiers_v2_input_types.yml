get:
  x-openapi-router-controller: copilot.v3.controllers.custom_file_type
  operationId: get_customizable_classifiers_v2_input_types
  parameters:
    - $ref: '../../v3.yml#/components/parameters/organizationIdPathParam'
    - name: enabled_only
      in: query
      description: include only enabled types.
      required: false
      schema:
        type: boolean
  responses:
    "200":
      description: Customizable classifiers v2 file types were retrieved successfully
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/CustomizableClassifiersV2InputTypes"
    "500":
      description: an unexpected error occurred. 
