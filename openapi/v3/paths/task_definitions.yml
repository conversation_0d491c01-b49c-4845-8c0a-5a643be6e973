post:
  x-openapi-router-controller: copilot.v3.controllers.task_definitions
  operationId: create_task_definition
  requestBody:
    required: true
    content:
      application/json:
        schema:
          $ref: "../../v3.yml#/components/schemas/TaskDefinition"
  responses:
    "201":
      description: The task definition was created successfully.
      content:
        application/json:
          schema:
            $ref: "../../v3.yml#/components/schemas/TaskDefinition"
    "400":
      description: The request is invalid.
