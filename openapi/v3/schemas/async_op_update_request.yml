type: object
properties:
  status:
    type: string
    enum:
      - Success
      - Failure
      - Pending
      - Cancelled
      - InProgress
      - Timeout
    nullable: false
  error_details:
    type: object
    additionalProperties: true
    nullable: true
  additional_data:
    type: object
    additionalProperties: true
    nullable: true
  logical_identifier:
    type: string
    nullable: true
  attempts:
    type: integer
    nullable: true
  executing_user_email:
    type: string
    nullable: true
