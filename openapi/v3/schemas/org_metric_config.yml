allOf:
  - $ref: '../../v3.yml#/components/schemas/ID'
  - $ref: '../../v3.yml#/components/schemas/CreatedAt'
  - $ref: '../../v3.yml#/components/schemas/UpdatedAt'
  - type: object
    properties:
      metric_name:
        type: string
        nullable: false
      organization_id:
        type: integer
        nullable: false
      weight:
        type: number
        format: double
        nullable: false
        description: OrgMetricConfig weight in terms of ordering.
