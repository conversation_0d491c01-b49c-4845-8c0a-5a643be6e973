type: object
properties:
  email:
    type: string
    nullable: false
  timestamp:
    type: number
    format: integer
    nullable: false
  event:
    type: string
    nullable: false
  sg_event_id:
    type: string
    nullable: false
  sg_message_id:
    type: string
    nullable: false
  reason:
    type: string
    nullable: true
  bounce_classification:
    type: string
    nullable: true
  kalepa_env:
    type: string
    nullable: true
  tracking_id:
    type: string
    format: uuid
    nullable: true
