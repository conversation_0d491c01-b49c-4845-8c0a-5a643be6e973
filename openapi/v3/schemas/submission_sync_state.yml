type: object
properties:
  id:
    type: string
    format: uuid
    pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
    nullable: false
  account_id:
    type: string
    nullable: true
  bound_date:
    type: string
    format: date-time
    nullable: true
  declined_date:
    type: string
    format: date-time
    nullable: true
  is_renewal:
    type: boolean
    nullable: true
  proposed_effective_date:
    type: string
    format: date-time
    nullable: true
  quoted_date:
    type: string
    format: date-time
    nullable: true
  received_date:
    type: string
    format: date-time
    nullable: true
  stage:
    type: string
    nullable: false
  coverages:
    type: array
    default: [ ]
    items:
      $ref: '../../v3.yml#/components/schemas/SubmissionSyncCoverageState'
  underwriters:
    type: array
    default: [ ]
    items:
      $ref: '../../v3.yml#/components/schemas/SubmissionSyncUnderwriterState'
  client_ids:
    type: array
    default: [ ]
    items:
      $ref: '../../v3.yml#/components/schemas/SubmissionSyncClientIdState'
  submission_relations:
    type: array
    default: [ ]
    items:
      $ref: '../../v3.yml#/components/schemas/SubmissionSyncRelationState'
  submission_identifiers:
    type: array
    default: [ ]
    items:
      $ref: '../../v3.yml#/components/schemas/SubmissionSyncIdentifierState'
  report:
    nullable: true
    allOf:
      - $ref: '../../v3.yml#/components/schemas/SubmissionSyncReportState'
  brokerage:
    nullable: true
    allOf:
      - $ref: '../../v3.yml#/components/schemas/SubmissionSyncBrokerageState'
  broker:
    nullable: true
    allOf:
      - $ref: '../../v3.yml#/components/schemas/SubmissionSyncBrokerState'
  premises_data:
    type: array
    default: [ ]
    items:
      $ref: '../../v3.yml#/components/schemas/SubmissionSyncPremisesState'
