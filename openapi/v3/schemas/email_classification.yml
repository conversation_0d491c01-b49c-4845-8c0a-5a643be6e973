type: object
properties:
  id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    nullable: true
  created_at:
    type: string
    format: date-time
    description: the datetime at which the item was created.
    nullable: true
  updated_at:
    type: string
    format: date-time
    nullable: true
    description: the datetime at which the resource was updated.
  email_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    nullable: true
  classifier_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    nullable: true
  label_id:
    type: string
    format: uuid
    pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
    nullable: true
  label:
    nullable: true
    allOf:
      - $ref: "../../v3.yml#/components/schemas/EmailClassificationLabel"
  explanation:
    type: string
    nullable: true
  snippet:
    type: string
    nullable: true
  additional_data:
    type: object
    nullable: true
