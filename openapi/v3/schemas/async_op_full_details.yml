type: object
properties:
  id:
    type: string
    format: uuid
    nullable: false
  organization_id:
    type: integer
    nullable: false
  report_id:
    type: string
    format: uuid
    nullable: true
  submission_id:
    type: string
    format: uuid
    nullable: true
  created_at:
    type: string
    format: date-time
    nullable: true
  updated_at:
    type: string
    format: date-time
    nullable: true
  status:
    type: string
    enum:
      - Success
      - Failure
      - Pending
      - Cancelled
      - InProgress
      - Timeout
    nullable: false
  operation:
    type: string
    nullable: false
  error_details:
    type: object
    additionalProperties: true
    nullable: true
  additional_data:
    type: object
    additionalProperties: true
    nullable: true
  logical_identifier:
    type: string
    nullable: true
  attempts:
    type: integer
    nullable: true
  executing_user_email:
    type: string
    nullable: true
