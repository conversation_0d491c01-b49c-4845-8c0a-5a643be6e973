type: object
properties:
  execution_id:
    type: string
    format: uuid
  parent_id:
    type: string
    format: uuid
    nullable: true
    pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
  parent_type:
    type: string
    nullable: true
    example: 'BUSINESS'
    enum:
      - BUSINESS
      - PREMISES
      - ORGANIZATION
      - SUBMISSION
      - REPORT
      - VEHICLE
      - EQUIPMENT
      - STRUCTURE
      - DRIVER
      - PRODUCT
      - PERSON
      - ERISA_PLAN
  children_type:
    type: string
    nullable: true
    default: 'BUSINESS'
  summary_config_id:
    type: string
    nullable: true
  metric_type:
    type: string
  name:
    type: string
  metric_group_name:
    type: string
    nullable: true
  submission_business_id:
    type: string
    format: uuid
    pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
    nullable: true
  value_business_ids:
    type: array
    nullable: true
    items:
      type: string
      format: uuid
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
      nullable: true
  value_parent_ids:
    type: array
    nullable: true
    items:
      type: string
      format: uuid
      pattern: '^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$'
      nullable: true
  value_parent_types:
    type: array
    nullable: true
    items:
      type: string
  string_values:
    type: array
    nullable: true
    items:
      type: string
      nullable: true
  datetime_values:
    type: array
    nullable: true
    items:
      type: string
      format: date-time
      nullable: true
  float_values:
    type: array
    nullable: true
    items:
      type: number
      format: float
      nullable: true
  sources:
    type: array
    items:
      $ref: "../../v3.yml#/components/schemas/MetricSource"
    nullable: true
  minimums:
    type: array
    nullable: true
    items:
      type: number
      format: double
      nullable: true
  maximums:
    type: array
    nullable: true
    items:
      type: number
      format: double
      nullable: true
  units:
    type: string
    nullable: true
  list_item_type:
    type: string
    nullable: true
  distance_threshold:
    type: number
    format: float
