openapi: "3.0.1"
info:
  title: Copilot API
  version: 3.0.0
servers:
  - url: http://0.0.0.0:5000/api/v3.0/
paths:
  /task-datasets:
    $ref: "./v3/paths/task_dataset.yml"
  /task-datasets/task-dataset-input:
    $ref: "./v3/paths/task_dataset_input.yml"
  /task-dataset/task-dataset-ground-truth:
    $ref: "./v3/paths/task_dataset_ground_truth.yml"
  /task-dataset-executions:
    $ref: "./v3/paths/task_dataset_execution_init.yml"
  /task-dataset-executions/{task_dataset_execution_id}/cancel:
    $ref: "./v3/paths/task_dataset_execution_cancel.yml"
  /task-dataset-executions/{task_dataset_execution_id}/complete:
    $ref: "./v3/paths/task_dataset_execution_complete.yml"
  /classifier-version-task-dataset:
    $ref: "./v3/paths/classifier_version_task_dataset.yml"
  /classifier-version-task-dataset/{id}:
    $ref: "./v3/paths/classifier_version_task_dataset_delete.yml"
  /classifier-version-task-dataset/{classifier_version_id}:
    $ref: "./v3/paths/task_datasets_by_classifier_version.yml"
  /classifier-config/{id}:
    $ref: "./v3/paths/classifier_config.yml"
  /subtypes-benchmark-data:
    $ref: "./v3/paths/subtypes_benchmark_data.yml"
  /subtypes-benchmark-data-bulk:
    $ref: "./v3/paths/subtypes_benchmark_data_bulk.yml"
  /data-validation-results:
    $ref: "./v3/paths/data_validation_results.yml"
  /fact-subtype-selection-benchmark:
    $ref: "./v3/paths/fact_subtype_selection_benchmark.yml"
  /fact-subtype-selection-benchmarks:
    $ref: "./v3/paths/fact_subtype_selection_benchmarks.yml"
  /fact-subtypes:
    $ref: "./v3/paths/fact_subtypes.yml"
  /adhoc-tasks:
    $ref: "./v3/paths/adhoc_tasks.yml"
  /admin/organizations:
    $ref: "./v3/paths/admin_organizations.yml"
  /admin/users:
    $ref: "./v3/paths/admin_users.yml"
  /user-groups:
    $ref: "./v3/paths/user_groups.yml"
  /user-groups/{id}:
    $ref: "./v3/paths/user_group.yml"
  /user-groups/{id}/users/{user_id}:
    $ref: "./v3/paths/user_groups_users.yml"
  /version:
    $ref: "./v3/paths/version.yml"
  /health-check:
    $ref: "./v3/paths/health_check.yml"
  /time-out:
    $ref: "./v3/paths/time_out.yml"
  /trigger-error:
    $ref: "./v3/paths/trigger_error.yml"
  /resolutions:
    $ref: "./v3/paths/resolutions.yml"
  /resolutions/identifiers:
    $ref: "./v3/paths/resolution_identifiers.yml"
  /reports:
    $ref: "./v3/paths/reports.yml"
  /reports/{id}/rush-evidence:
    $ref: "./v3/paths/reports_rush_evidence.yml"
  /reports/bulk-get:
    $ref: "./v3/paths/reports_post.yml"
  /reports/external:
    $ref: "./v3/paths/reports_external.yml"
  /link-reports:
    $ref: "./v3/paths/link_reports.yml"
  /bundle-reports:
    $ref: "./v3/paths/bundle_reports.yml"
  /unbundle-reports:
    $ref: "./v3/paths/unbundle_reports.yml"
  /report-alerts/naics:
    $ref: "./v3/paths/alerts_naics.yml"
  /report-alerts/not-processed-submissions:
    $ref: "./v3/paths/alerts_not_processed_submissions.yml"
  /report-alerts/still-not-processed-alerts:
    $ref: "./v3/paths/alerts_still_not_processed.yml"
  /report-alerts/missing-processing:
    $ref: "./v3/paths/alerts_missing_processing.yml"
  /report-alerts/weekend-processing:
    $ref: "./v3/paths/alerts_weekend_processing.yml"
  /reports_lite/{id}:
    $ref: "./v3/paths/report_lite.yml"
  /reports/{id}:
    $ref: "./v3/paths/report.yml"
  /reports/{id}/external:
    $ref: "./v3/paths/report_external.yml"
  /reports/{report_id}/external/files:
    $ref: "./v3/paths/report_files_for_external_consumers.yml"
  /reports/{report_id}/files/download_file:
    $ref: "./v3/paths/download_file.yml"
  /reports/{report_id}/files/download-url:
    $ref: "./v3/paths/files_url.yml"
  /reports/{report_id}/files/{external_id}/external:
    $ref: "./v3/paths/report_files_external.yml"
  /reports/{id}/summary_preferences:
    $ref: "./v3/paths/report_summary_preferences.yml"
  /reports/{report_id}/share_within_organization:
    $ref: "./v3/paths/report_share_within_organization.yml"
  /reports/{id}/get_metrics_v2:
    $ref: "./v3/paths/get_metrics_v2.yml"
  /reports/{id}/create_metric_v2:
    $ref: "./v3/paths/create_metric_v2.yml"
  /reports/{id}/bulk_create_metric_v2:
    $ref: "./v3/paths/create_metric_v2_bulk.yml"
  /reports/{id}/subscriptions:
    $ref: "./v3/paths/report_subscriptions.yml"
  /reports/{id}/metric_preferences:
    $ref: "./v3/paths/report_metric_preferences.yml"
  /reports/{id}/execution-events:
    $ref: "./v3/paths/report_execution_events.yml"
  /reports/{id}/permissions:
    $ref: "./v3/paths/report_permissions.yml"
  /reports/{id}/exports:
    $ref: "./v3/paths/report_exports.yml"
  /reports/{id}/process:
    $ref: "./v3/paths/report_process.yml"
  /reports/{id}/cancel_process:
    $ref: "./v3/paths/report_cancel_process.yml"
  /reports/{id}/reprocess:
    $ref: "./v3/paths/report_reprocess.yml"
  /reports/{id}/retry_process:
    $ref: "./v3/paths/report_retry_process.yml"
  /reports/{id}/revert_to_bc:
    $ref: "./v3/paths/report_revert_to_bc.yml"
  /reports/{id}/revert_to_do:
    $ref: "./v3/paths/report_revert_to_do.yml"
  /reports/{id}/revert/{state}:
    $ref: "./v3/paths/report_revert_to_state.yml"
  /reports/{id}/auto_set_name:
    $ref: "./v3/paths/report_auto_set_name.yml"
  /reports/{id}/triage:
    $ref: "./v3/paths/report_triage.yml"
  /reports/extract:
    $ref: "./v3/paths/report_extracts.yml"
  /reports/merge:
    $ref: "./v3/paths/merge_reports.yml"
  /reports/{id}/extract:
    $ref: "./v3/paths/report_extract.yml"
  /reports/{report_id}/generate_extract:
    $ref: "./v3/paths/generate_report_extract.yml"
  /reports/{report_id}/correspondence:
    $ref: "./v3/paths/report_email_correspondence.yml"
  /reports/{report_id}/emails:
    $ref: "./v3/paths/report_emails.yml"
  /reports/{report_id}/shadow:
    $ref: "./v3/paths/report_shadow.yml"
  /reports/{report_id}/pds-debugger-data/files:
    $ref: "./v3/paths/report_pds_debugger_data.yml"
  /reports/snapshots:
    $ref: "./v3/paths/report_snapshots.yml"
  /report_processing_dependencies:
    $ref: "./v3/paths/report_processing_dependency.yml"
  /routing_rules:
    $ref: "./v3/paths/routing_rules.yml"
  /routing_rules/{id}:
    $ref: "./v3/paths/routing_rule.yml"
  /metric_preferences/{id}:
    $ref: "./v3/paths/metric_preference.yml"
  /submission-permissions/{id}/close-referral:
    $ref: "./v3/paths/submission_permissions.yml"
  /organizations/{id}/org-metric-configs:
    $ref: "./v3/paths/org_metric_configs.yml"
  /metric_groups:
    $ref: "./v3/paths/metric_groups.yml"
  /timezones:
    $ref: "./v3/paths/timezones.yml"
  /feature-flags/{feature}:
    $ref: "./v3/paths/feature_flags.yml"
  /subscriptions/{id}:
    $ref: "./v3/paths/subscription.yml"
  /experiments:
    $ref: "./v3/paths/experiments.yml"
  /experiments/{exp_id}:
    $ref: "./v3/paths/experiment.yml"
  /experiments/{exp_id}/runs:
    $ref: "./v3/paths/experiment_runs.yml"
  /experiments/fix_em_experiments:
    $ref: "./v3/paths/experiment_fix_em.yml"
  /ers_search_results:
    $ref: "./v3/paths/ers_search_results.yml"
  /submissions:
    $ref: "./v3/paths/submissions.yml"
  /submissions/{id}/read:
    $ref: "./v3/paths/read_submission.yml"
  /submissions/expire:
    $ref: "./v3/paths/expire_submissions.yml"
  /submissions/sync/load:
    $ref: "./v3/paths/submissions_sync_load.yml"
  /submissions/{id}/report-id:
    $ref: "./v3/paths/submission_report_id.yml"
  /submissions/sync/pending/{organization_id}:
    $ref: "./v3/paths/submissions_sync_pending.yml"
  /submissions/sync/run:
    $ref: "./v3/paths/submissions_sync_run.yml"
  /submissions/sync/schedule_to_resync:
    $ref: "./v3/paths/schedule_to_resync.yml"
  /submissions/sync/shift_to_last_position:
    $ref: "./v3/paths/shift_to_last_position.yml"
  /submissions/sync/delete_sync_requests:
    $ref: "./v3/paths/delete_sync_requests.yml"
  /submissions/sync/run-matching:
    $ref: "./v3/paths/submissions_sync_run_matching.yml"
  /submissions/sync/get-requests:
    $ref: "./v3/paths/submission_sync_requests.yml"
  /submissions/sync/sync_matching_runs:
    $ref: "./v3/paths/sync_matching_runs.yml"
  /submissions/sync/sync_matching_runs/{run_id}:
    $ref: "./v3/paths/sync_matching_run.yml"
  /submissions/sync/sync_matching_runs/{run_id}/test:
    $ref: "./v3/paths/sync_matching_run_test.yml"
  /submissions/sync/sync_matching_runs/{run_id}/status:
    $ref: "./v3/paths/sync_matching_run_status.yml"
  /submissions/sync/requests/{id}:
    $ref: "./v3/paths/submission_sync_request.yml"
  /submissions/bulk_decline:
    $ref: "./v3/paths/submissions_bulk_decline.yml"
  /submissions/send_declined_email:
    $ref: "./v3/paths/submissions_send_declined_email.yml"
  /submissions/send_email:
    $ref: "./v3/paths/submissions_send_email.yml"
  /submissions/email_dynamic_data:
    $ref: "./v3/paths/submissions_email_dynamic_data.yml"
  /submissions/send_scheduled_emails:
    $ref: "./v3/paths/submissions_send_scheduled_emails.yml"
  /submissions/{id}:
    $ref: "./v3/paths/submission.yml"
  /submissions/{id}/public-info:
    $ref: "./v3/paths/submission_public_info.yml"
  /submissions/{id}/premises_data:
    $ref: "./v3/paths/submission_premises_data.yml"
  /submissions_lite/{id}:
    $ref: "./v3/paths/submission_lite.yml"
  /submissions/{submission_id}/businesses:
    $ref: "./v3/paths/businesses_by_submission.yml"
  /submissions/{submission_id}/businesses/full:
    $ref: "./v3/paths/businesses_by_submission_id.yml"
  /submissions/{submission_id}/businesses/{id}:
    $ref: "./v3/paths/business_by_submission.yml"
  /submissions/{submission_id}/businesses/{id}/at_location:
    $ref: "./v3/paths/businesses_at_location.yml"
  /submissions/{submission_id}/businesses/{id}/prometrix_risks:
    $ref: "./v3/paths/prometrix_risks.yml"
  /submissions/{submission_id}/policies:
    $ref: "./v3/paths/submission_policies.yml"
  /submissions/{submission_id}/users:
    $ref: "./v3/paths/assigned_underwriters.yml"
  /submissions/{submission_id}/auto-assign-users:
    $ref: "./v3/paths/auto_assign_underwriters.yml"
  /submissions/{submission_id}/losses:
    $ref: "./v3/paths/losses.yml"
  /submissions/{submission_id}/loss_lob_inference_requirements:
    $ref: "./v3/paths/loss_lob_inference_requirements.yml"
  /submissions/{submission_id}/audit_questions:
    $ref: "./v3/paths/audit_questions.yml"
  /submissions/{submission_id}/correspondence:
    $ref: "./v3/paths/email_correspondence_for_submission.yml"
  /losses/submission/{submission_id}/file_statuses:
    $ref: "./v3/paths/get_loss_file_statuses.yml"
  /losses/{id}:
    $ref: "./v3/paths/loss.yml"
  /submission_level_extracted_data:
    $ref: "./v3/paths/submission_level_extracted_data.yml"
  /submissions/{submission_id}/update_lob_bulk:
    $ref: "./v3/paths/update_loss_lob_bulk.yml"
  /submissions/{submission_id}/losses/summary:
    $ref: "./v3/paths/losses_summary.yml"
  /submissions/{submission_id}/losses/policy:
    $ref: "./v3/paths/loss_policy.yml"
  /submissions/{submission_id}/losses/policies:
    $ref: "./v3/paths/loss_policies.yml"
  /submissions/{submission_id}/fields/{id}/history:
    $ref: "./v3/paths/submission_fields_history.yml"
  /submissions/{submission_id}/files:
    $ref: "./v3/paths/submission_files.yml"
  /submissions/{submission_id}/files/{id}:
    $ref: "./v3/paths/submission_file.yml"
  /submissions/{submission_id}/file_by_parent/{id}:
    $ref: "./v3/paths/submission_file_by_parent.yml"
  /submissions/{submission_id}/download-files:
    $ref: "./v3/paths/download_files.yml"
  /submissions/{submission_id}/files/{file_id}/file_processing_state:
    $ref: "./v3/paths/file_processing_state.yml"
  /submissions/{submission_id}/files/{file_id}/submission_level_data:
    $ref: "./v3/paths/store_submission_level_data.yml"
  /submissions/{submission_id}/files/{file_id}/download:
    $ref: "./v3/paths/download_submission_file.yml"
  /submissions/{submission_id}/files/{file_id}/url:
    $ref: "./v3/paths/get_url_by_file_id.yml"
  /submissions/{submission_id}/download_loaded_data:
    $ref: "./v3/paths/download_loaded_data.yml"
  /submissions/{submission_id}/files/{file_id}/onboarded_data:
    $ref: "./v3/paths/file_onboarded_data.yml"
  /submissions/{submission_id}/entity_mapping:
    $ref: "./v3/paths/entity_mapping.yml"
  /submissions/{submission_id}/business_confirmation:
    $ref: "./v3/paths/business_confirmation.yml"
  /submissions/{submission_id}/data_onboarding:
    $ref: "./v3/paths/data_onboarding.yml"
  /submissions/{submission_id}/files_clearing:
    $ref: "./v3/paths/files_clearing.yml"
  /submissions/{submission_id}/complete:
    $ref: "./v3/paths/complete_submission.yml"
  /submissions/{submission_id}/business_resolution_data:
    $ref: "./v3/paths/business_resolution_data.yml"
  /submissions/{submission_id}/processed_data:
    $ref: "./v3/paths/processed_data.yml"
  /submissions/{submission_id}/suggestions/groups:
    $ref: "./v3/paths/first_party_field_group_suggestions.yml"
  /submissions/{submission_id}/start-clearing:
    $ref: "./v3/paths/start_clearing.yml"
  /submissions/{submission_id}/finish-external-clearing:
    $ref: "./v3/paths/finish_external_clearing.yml"
  /submissions/{submission_id}/pds-check:
    $ref: "./v3/paths/pds_check.yml"
  /submissions/merge-correspondence:
    $ref: "./v3/paths/merge_correspondence.yml"
  /submissions/handle-event:
    $ref: "./v3/paths/handle_submission_event.yml"
  /submissions/{submission_id}/action:
    $ref: "./v3/paths/submission_action.yml"
  /submissions/{submission_id}/clear:
    $ref: "./v3/paths/clear.yml"
  /submissions/{submission_id}/verify:
    $ref: "./v3/paths/verify_submission.yml"
  /submissions/{submission_id}/verify_shell:
    $ref: "./v3/paths/verify_shell_submission.yml"
  /submissions/{submission_id}/assign_clearing_user:
    $ref: "./v3/paths/submission_clearing_assignee.yml"
  /submissions/{submission_id}/notebook/threads:
    $ref: "./v3/paths/notebook_threads.yml"
  /submissions/{submission_id}/consolidate_acord_data:
    $ref: "./v3/paths/consolidate_acord_data.yml"
  /submissions/{submission_id}/experiments/{exp_id}:
    $ref: "./v3/paths/experiment_submission.yml"
  /submissions/{submission_id}/experiments/{exp_id}/force:
    $ref: "./v3/paths/experiment_submission_force.yml"
  /submissions/{submission_id}/experiments/{exp_id}/samples/{sample_id}:
    $ref: "./v3/paths/experiment_submission_sample.yml"
  /notebook-threads/{id}:
    $ref: "./v3/paths/notebook_thread.yml"
  /submissions/{submission_id}/notebook/threads/{thread_id}/messages:
    $ref: "./v3/paths/notebook_messages_by_thread.yml"
  /submissions/{submission_id}/notebook/messages/{id}:
    $ref: "./v3/paths/notebook_message.yml"
  /submissions/{id}/reports:
    $ref: "./v3/paths/reports_by_submission.yml"
  /submissions/{submission_id}/requested-coverages:
    $ref: "./v3/paths/requested_coverages_by_submission.yml"
  /submissions/{submission_id}/requested-coverages/{coverage_id}:
    $ref: "./v3/paths/requested_coverage_by_submission.yml"
  /submissions/{submission_id}/history:
    $ref: "./v3/paths/submission-history.yml"
  /submissions/{submission_id}/coverage-history:
    $ref: "./v3/paths/submission_coverage_history.yml"
  /submissions/{submission_id}/shareholders:
    $ref: "./v3/paths/shareholders.yml"
  /submissions/{submission_id}/description-of-operations:
    $ref: "./v3/paths/submission_descriptions_of_operations.yml"
  /submissions/{submission_id}/files-data:
    $ref: "./v3/paths/submission_files_data.yml"
  /submissions/{submission_id}/shadow-info:
    $ref: "./v3/paths/submission_shadow_info.yml"
  /first-party-field-identification-jobs:
    $ref: "./v3/paths/identify_first_party_fields.yml"
  /first-party-suggestions:
    $ref: "./v3/paths/first_party_suggestions.yml"
  /first-party-suggestions/v2:
    $ref: "./v3/paths/first_party_suggestions_lean.yml"
  /submission-relations:
    $ref: "./v3/paths/submission_relations.yml"
  /submission-relations/replace:
    $ref: "./v3/paths/replace_submission_relations.yml"
  /submission-relations/{id}:
    $ref: "./v3/paths/submission_relation.yml"
  /submission-relations/lookalike/bind-rate:
    $ref: "./v3/paths/lookalike_bind_rate.yml"
  /submissions/{submission_id}/reassign-first-party-fact-subtype:
    $ref: "./v3/paths/reassign_first_party_fact_subtype.yml"
  /submissions/{submission_id}/fact-subtype/{fact_subtype_id}:
    $ref: "./v3/paths/submission_fact_subtype.yml"
  /submissions/{submission_id}/execution-events:
    $ref: "./v3/paths/submission_execution_events.yml"
  /submissions/{submission_id}/upload-osha-violation:
    $ref: "./v3/paths/submission_upload_osha_violation.yml"
  /submissions/{submission_id}/client-submission-ids:
    $ref: "./v3/paths/submission_client_id.yml"
  /submissions/{submission_id}/client-submission-ids/bulk:
    $ref: "./v3/paths/submission_client_ids_bulk.yml"
  /submissions/{submission_id}/identifier-suggestions:
    $ref: "./v3/paths/submission_identifier_suggestions.yml"
  /submissions/client-submission-ids/{client_submission_id}:
    $ref: "./v3/paths/submission_client_ids.yml"
  /submissions/align-client-stages:
    $ref: "./v3/paths/align_client_stages.yml"
  /submissions/client-submission-ids/retrieve:
    $ref: "./v3/paths/submission_client_ids_retrieve.yml"
  /submissions/check_processing:
    $ref: "./v3/paths/submissions_check_processing.yml"
  /submissions/check_not_started_api_subs:
    $ref: "./v3/paths/check_not_started_api_subs.yml"
  /submissions/cancel_processing:
    $ref: "./v3/paths/submissions_cancel_processing.yml"
  /submissions/cleanup_duplicates:
    $ref: "./v3/paths/submissions_cleanup_duplicates.yml"
  /submissions/{id}/notes:
    $ref: "./v3/paths/submission_notes.yml"
  /submissions/notes/{id}:
    $ref: "./v3/paths/submission_note.yml"
  /submissions/notes/recommendations:
    $ref: "./v3/paths/recommendation_submission_notes.yml"
  /submissions/notes/pdf_export:
    $ref: "./v3/paths/export_pdf_notes.yml"
  /submissions/{id}/users/notifications:
    $ref: "./v3/paths/submission_users_notifications.yml"
  /submissions/{submission_id}/aggregations:
    $ref: "./v3/paths/submission_aggregations.yml"
  /submissions/{organization_id}/active:
    $ref: "./v3/paths/submission_organization_active.yml"
  /settings:
    $ref: "./v3/paths/settings.yml"
  /settings/{id}:
    $ref: "./v3/paths/setting.yml"
  /users/{external_id}:
    $ref: "./v3/paths/user.yml"
  /users:
    $ref: "./v3/paths/users.yml"
  /users/support:
    $ref: "./v3/paths/support_users.yml"
  /users/support/check-activity:
    $ref: "./v3/paths/support_user_check_activity.yml"
  /users/support/current:
    $ref: "./v3/paths/support_user_current.yml"
  /users/support/{user_id}:
    $ref: "./v3/paths/support_user.yml"
  /users/{user_id}/profile:
    $ref: "./v3/paths/user_profile.yml"
  /users/{user_id}/reports:
    $ref: "./v3/paths/report_by_user.yml"
  /users/{id}/subscriptions:
    $ref: "./v3/paths/user_subscriptions.yml"
  /users/{id}/bookmarks:
    $ref: "./v3/paths/bookmark.yml"
  /users/{id}/bookmarks/{submission_id}:
    $ref: "./v3/paths/delete_bookmark.yml"
  /users/organization/{organization_id}:
    $ref: "./v3/paths/users_by_organization.yml"
  /users/all:
    $ref: "./v3/paths/all_users.yml"
  /users/{user_id}/tenants-feedback:
    $ref: "./v3/paths/tenant_feedback_by_user.yml"
  /tenants-feedback/{id}:
    $ref: "./v3/paths/tenant_feedback.yml"
  /stuck-submission-feedback:
    $ref: "./v3/paths/stuck_submission_feedback.yml"
  /users/{user_id}/feedback/insights:
    $ref: "./v3/paths/insight_feedback_by_user.yml"
  /users/{user_id}/client-applications:
    $ref: "./v3/paths/user_client_applications.yml"
  /users/{user_id}/metric_templates:
    $ref: "./v3/paths/user_metric_templates.yml"
  /users/{user_id}/hub_templates:
    $ref: "./v3/paths/user_hub_templates.yml"
  /users/{user_id}/open-reports:
    $ref: "./v3/paths/open_reports.yml"
  /users/{user_id}/delete-open-reports:
    $ref: "./v3/paths/delete_open_reports.yml"
  /users/{user_id}/open-reports/{report_id}:
    $ref: "./v3/paths/open_report.yml"
  /users/{user_id}/email-templates:
    $ref: "./v3/paths/user_email_templates.yml"
  /users/{user_id}/latest_email:
    $ref: "./v3/paths/latest_email.yml"
  /users/signup:
    $ref: "./v3/paths/user_signup.yml"
  /dossiers/{id}/reports:
    $ref: "./v3/paths/reports_by_dossier.yml"
  /dossiers/{dossier_id}/feedback/insights:
    $ref: "./v3/paths/insight_feedback_by_dossier.yml"
  /permissions:
    $ref: "./v3/paths/permissions.yml"
  /emails/send:
    $ref: "./v3/paths/send-email.yml"
  /emails-unauthorized/send:
    $ref: "./v3/paths/send-email-without-authorization.yml"
  /emails/send-queued:
    $ref: "./v3/paths/queued_email.yml"
  /emails/status:
    $ref: "./v3/paths/email_status.yml"
  /emails/classifications:
    $ref: "./v3/paths/email_classifications.yml"
  /emails/classifications/{id}:
    $ref: "./v3/paths/email_classification.yml"
  /emails/classification_labels:
    $ref: "./v3/paths/email_classification_labels.yml"
  /email_classifiers:
    $ref: "./v3/paths/email_classifiers.yml"
  /email_classifiers/{id}:
    $ref: "./v3/paths/email_classifier.yml"
  /files:
    $ref: "./v3/paths/files.yml"
  /enhanced-files:
    $ref: "./v3/paths/enhanced_files.yml"
  /files/{file_id}:
    $ref: "./v3/paths/file.yml"
  /files/{file_id}/split_pdf:
    $ref: "./v3/paths/split_pdf_file.yml"
  /files/{file_id}/process_from_cache:
    $ref: "./v3/paths/process_file_from_cache.yml"
  /files/{file_id}/file-metrics:
    $ref: "./v3/paths/file_metrics.yml"
  /files/{file_id}/extracted-metadata/{metadata_type}:
    $ref: "./v3/paths/files_extracted_metadata.yml"
  /files/extracted-metadata/check-pending-extractions:
    $ref: "./v3/paths/files_extracted_metadata_check.yml"
  /files/external:
    $ref: "./v3/paths/files_external.yml"
  /files/upload_url:
    $ref: "./v3/paths/files_upload_url.yml"
  /files/to_pdf:
    $ref: "./v3/paths/files_to_pdf.yml"
  /files/send-to-label-studio:
    $ref: "./v3/paths/files_send_to_label_studio.yml"
  /files/send-to-label-studio/{file_id}/pages:
    $ref: "./v3/paths/files_send_to_label_studio_pages.yml"
  /files/send-to-label-studio/{file_id}/pages/{page_num}:
    $ref: "./v3/paths/files_send_to_label_studio_page.yml"
  /files/update-page-processing-status:
    $ref: "./v3/paths/files_update_page_processing_status.yml"
  /files/check_processing:
    $ref: "./v3/paths/files_check_processing.yml"
  /files/reprocess/{file_id}:
    $ref: "./v3/paths/reprocess_file.yml"
  /files/ingest-acord:
    $ref: "./v3/paths/ingest_acord.yml"
  /additional-file:
    $ref: "./v3/paths/additional_file.yml"
  /urls:
    $ref: "./v3/paths/presigned_url.yml"
  /file-types:
    $ref: "./v3/paths/file_types.yml"
  /file-types/aggregated:
    $ref: "./v3/paths/aggregated_file_types.yml"
  /data-onboarding/units:
    $ref: "./v3/paths/units.yml"
  /permissions/{id}:
    $ref: "./v3/paths/permission.yml"
  /coverages:
    $ref: "./v3/paths/coverages.yml"
  /coverages/groups:
    $ref: "./v3/paths/coverage_groups.yml"
  /requested-coverages/{id}:
    $ref: "./v3/paths/requested_coverage.yml"
  /recommendation-invocations:
    $ref: "./v3/paths/recommendation_invocation.yml"
  /validate-requested-recommendations:
    $ref: "./v3/paths/validate_requested_recommendations.yml"
  /recommendation-aggregation:
    $ref: "./v3/paths/recommendation_aggregation.yml"
  /metric_templates/{id}:
    $ref: "./v3/paths/metric_templates.yml"
  /hub_templates/{id}:
    $ref: "./v3/paths/hub_template.yml"
  /submission-businesses:
    $ref: "./v3/paths/submission_businesses.yml"
  /classifiers-metadata:
    $ref: "./v3/paths/classifiers_metadata.yml"
  /customizable-classifiers:
    $ref: "./v3/paths/customizable_classifiers.yml"
  /customizable-classifiers/{id}:
    $ref: "./v3/paths/customizable_classifier.yml"
  /customizable-classifiers/{classifier_id}/test-runs:
    $ref: "./v3/paths/test_runs_by_customizable_classifier.yml"
  /customizable-classifiers-v2:
    $ref: "./v3/paths/customizable_classifiers_v2.yml"
  /customizable-classifiers-v2/{id}:
    $ref: "./v3/paths/customizable_classifier_v2.yml"
  /customizable-classifiers-v2/dataset:
    $ref: "./v3/paths/customizable_classifiers_v2_dataset.yml"
  /customizable-classifiers-v2/{classifier_id}/activate-version/{version_id}:
    $ref: "./v3/paths/customizable_classifier_v2_activate_version.yml"
  /customizable-classifiers-v2/{classifier_id}/versions/{version_id}:
    $ref: "./v3/paths/customizable_classifier_v2_version.yml"
  /customizable-classifiers-v2/{id}/create-task-definitions:
    $ref: "./v3/paths/customizable_classifier_v2_create_task_definitions.yml"
  /test-runs/{id}:
    $ref: "./v3/paths/test_run.yml"
  /classification-tasks/{id}:
    $ref: "./v3/paths/classification_task.yml"
  /organizations:
    $ref: "./v3/paths/organizations.yml"
  /organizations/{organization_id}:
    $ref: "./v3/paths/organization.yml"
  /organizations/{organization_id}/policies:
    $ref: "./v3/paths/organization_policies.yml"
  /organizations/settings:
    $ref: "./v3/paths/organizations_settings.yml"
  /organizations/{organization_id}/settings:
    $ref: "./v3/paths/organization_settings.yml"
  /organizations/{organization_id}/sic_codes:
    $ref: "./v3/paths/sic_codes.yml"
  /organizations/{organization_id}/policies/{policy_id}:
    $ref: "./v3/paths/organization_policy.yml"
  /organizations/support-email:
    $ref: "./v3/paths/organization_support_email.yml"
  /organizations/{organization_id}/client-applications/{client_id}:
    $ref: "./v3/paths/client_applications_by_organization.yml"
  /organizations/{organization_id}/losses:
    $ref: "./v3/paths/organization_losses.yml"
  /organizations/{organization_id}/losses/{policy_id}/{claim_number}:
    $ref: "./v3/paths/organization_loss.yml"
  /lobs:
    $ref: "./v3/paths/lobs.yml"
  /lobs/{id}:
    $ref: "./v3/paths/lob.yml"
  /lob-types:
    $ref: "./v3/paths/lob_types.yml"
  /brokerages:
    $ref: "./v3/paths/brokerages.yml"
  /brokerages/find:
    $ref: "./v3/paths/brokerages_find.yml"
  /brokerage_employees/find:
    $ref: "./v3/paths/brokerage_employees_find.yml"
  /brokerages/{id}:
    $ref: "./v3/paths/brokerage.yml"
  /brokerages/merge:
    $ref: "./v3/paths/merge_brokerages.yml"
  /brokerages/offices:
    $ref: "./v3/paths/brokerage_offices.yml"
  /brokerage_employees:
    $ref: "./v3/paths/brokers.yml"
  /brokerage_employees/{id}:
    $ref: "./v3/paths/broker.yml"
  /brokerage_employees/merge:
    $ref: "./v3/paths/merge_brokerage_employees.yml"
  /broker_groups:
    $ref: "./v3/paths/broker_groups.yml"
  /broker_groups/{id}:
    $ref: "./v3/paths/broker_group.yml"
  /temp_create_broker_aliases:
    $ref: "./v3/paths/temp_create_broker_aliases.yml"
  /temp_fix_invalid_broker_names:
    $ref: "./v3/paths/temp_fix_invalid_broker_names.yml"
  /temp_fix_user_brokerages:
    $ref: "./v3/paths/temp_fix_user_brokerages.yml"
  /dashboards/{name}:
    $ref: "./v3/paths/dashboard.yml"
  /search:
    $ref: "./v3/paths/search.yml"
  /processed_file:
    $ref: "./v3/paths/processed_file.yml"
  /processed_file/{id}:
    $ref: "./v3/paths/processed_file_update.yml"
  /processed_file/{id}/resolution_data_row:
    $ref: "./v3/paths/processed_file_resolution_data_row.yml"
  /webhooks/sensible/lossruns:
    $ref: "./v3/paths/sensible_loss_run.yml"
  /webhooks/sensible/acords:
    $ref: "./v3/paths/sensible_acord.yml"
  /webhooks/sendgrid/event:
    $ref: "./v3/paths/sendgrid_event.yml"
  /processed_file/{file_id}:
    $ref: "./v3/paths/processed_file_by_file.yml"
  /submissions/{submission_id}/invoke_loss_run_processing:
    $ref: "./v3/paths/invoke_loss_run_processing.yml"
  /submissions/{submission_id}/process_loss_runs:
    $ref: "./v3/paths/process_loss_runs.yml"
  /recommendations/sync_submission_data:
    $ref: "./v3/paths/recommendations_submission_data_sync.yml"
  /sensible/refresh_metadata:
    $ref: "./v3/paths/sensible_refresh_metadata.yml"
  /sensible/delete_cache:
    $ref: "./v3/paths/sensible_delete_cache.yml"
  /submissions/{submission_id}/identifiers:
    $ref: "./v3/paths/submissions_identifiers.yml"
  /submissions/{submission_id}/identifier/delete:
    $ref: "./v3/paths/delete_submission_identifier.yml"
  /submissions/client_stage_config:
    $ref: "./v3/paths/client_submission_stage_config.yml"
  /sensible/save_extraction/{sensible_extraction_id}:
    $ref: "./v3/paths/sensible_save_extraction.yml"
  /sensible/validate_extraction/{id}:
    $ref: "./v3/paths/sensible_validate_extraction.yml"
  /sensible/extractions:
    $ref: "./v3/paths/sensible_extractions.yml"
  /sensible/validate-configuration:
    $ref: "./v3/paths/validate_sensible_configuration.yml"
  /sensible/get-files-for-configuration:
    $ref: "./v3/paths/get_files_for_configuration.yml"
  /businesses/update_business_ids:
    $ref: "./v3/paths/update_business_ids.yml"
  /businesses/revert_updates:
    $ref: "./v3/paths/revert_updates.yml"
  /email-templates:
    $ref: "./v3/paths/email_template_create.yml"
  /email-templates/{id}:
    $ref: "./v3/paths/email_template.yml"
  /emails:
    $ref: "./v3/paths/emails.yml"
  /emails-v2:
    $ref: "./v3/paths/emails_v2.yml"
  /emails/{id}:
    $ref: "./v3/paths/emails_update.yml"
  /emails/{id}/classification_labels:
    $ref: "./v3/paths/email_classification_labels_update.yml"
  /report-email-correspondence:
    $ref: "./v3/paths/report_correspondence_by_thread_id.yml"
  /report-email-correspondence/{id}:
    $ref: "./v3/paths/update_report_correspondence.yml"
  /ask-questions:
    $ref: "./v3/paths/ask_questions.yml"
  /verify/candidates:
    $ref: "./v3/paths/verify_candidates.yml"
  /lob-carrier:
    $ref: "./v3/paths/lob_carrier.yml"
  /submissions-queue:
    $ref: "./v3/paths/submissions_queue.yml"
  /submissions-queue/count:
    $ref: "./v3/paths/submissions_queue_count.yml"
  /submissions-naics-queue:
    $ref: "./v3/paths/submissions_naics_queue.yml"
  /submissions/{submission_id}/submission_priority:
    $ref: "./v3/paths/submission_escalations.yml"
  /statistics/recommendations_breakdown:
    $ref: "./v3/paths/statistics_recommendations_breakdown.yml"
  /workers-comp-experience:
    $ref: "./v3/paths/workers_comp_experience.yml"
  /workers-comp-experience/{submission_id}:
    $ref: "./v3/paths/workers_comp_experiences.yml"
  /workers-comp-state-rating-information/{submission_id}:
    $ref: "./v3/paths/workers_comp_state_rating_information.yml"
  /workers-comp-state-rating-information/class-codes:
    $ref: "./v3/paths/workers_comp_state_rating_class_codes.yml"
  /ifta-data/{file_id}:
    $ref: "./v3/paths/ifta_data.yml"
  /ifta-entity/{id}:
    $ref: "./v3/paths/ifta_entity.yml"
  /ifta-entity:
    $ref: "./v3/paths/create_ifta_entity.yml"
  /aggregated-ifta-data/{submission_id}:
    $ref: "./v3/paths/aggregated_ifta_data.yml"
  /aggregated-ifta-data/{submission_id}/quarters:
    $ref: "./v3/paths/aggregated_ifta_data_quarters.yml"
  /report_id:
    $ref: "./v3/paths/report_id_by_external_id.yml"
  /reports/{report_id}/notes/external:
    $ref: "./v3/paths/report_notes_external.yml"
  /reports/{report_id}/notes/{note_id}/external:
    $ref: "./v3/paths/report_notes.yml"
  /reports/{report_id}/status/external:
    $ref: "./v3/paths/report_status_external.yml"
  /paragon/underwriters:
    $ref: "./v3/paths/paragon_underwriters.yml"
  /paragon/company_lines:
    $ref: "./v3/paths/paragon_company_lines.yml"
  /paragon/submissions/{submission_id}/split:
    $ref: "./v3/paths/paragon_split_submission.yml"
  /paragon/reports/{report_id}/notifications:
    $ref: "./v3/paths/paragon_notifications.yml"
  /submissions/{submission_id}/stuck-details:
    $ref: "./v3/paths/stuck_details.yml"
  /file-stats:
    $ref: "./v3/paths/file_stats.yaml"
  /file-metrics/score-acord-extraction:
    $ref: "./v3/paths/score_acord_extraction.yml"
  /metadata/{submission_id}:
    $ref: "./v3/paths/metadata.yml"
  /rater/execute:
    $ref: "./v3/paths/rater_execute.yml"
  /task-definitions:
    $ref: "./v3/paths/task_definitions.yml"
  /task-definitions/{task_code}:
    $ref: "./v3/paths/task_definitions_by_task_code.yml"
  /task-definitions/by-id/{task_definition_id}:
    $ref: "./v3/paths/task_definitions_by_id.yml"
  /task-definitions/{task_definition_id}/metrics:
    $ref: "./v3/paths/task_definitions_metrics.yml"
  /task-definitions/{task_definition_id}/calibration:
    $ref: "./v3/paths/task_definitions_calibration.yml"
  /task-models:
    $ref: "./v3/paths/task_models.yml"
  /task-models/{task_model_id}:
    $ref: "./v3/paths/task_model.yml"
  /task-definition-models:
    $ref: "./v3/paths/task_definitions_models.yml"
  /task-definition-models/{task_definition_model_id}:
    $ref: "./v3/paths/task_definitions_model.yml"
  /tasks:
    $ref: "./v3/paths/tasks.yml"
  /tasks/handle-pending:
    $ref: "./v3/paths/tasks_handle_pending.yml"
  /tasks/{task_id}:
    $ref: "./v3/paths/task.yml"
  /tasks/{task_id}/executions:
    $ref: "./v3/paths/task_executions.yml"
  /taxonomy-mappings:
    $ref: "./v3/paths/taxonomy_mappings.yml"
  /taxonomy-mappings/{mapping_id}:
    $ref: "./v3/paths/taxonomy_mapping.yml"
  /taxonomy-descriptions:
    $ref: "./v3/paths/taxonomy_descriptions.yml"
  /taxonomy-descriptions/{code}:
    $ref: "./v3/paths/taxonomy_description.yml"
  /preferences:
    $ref: "./v3/paths/preferences.yml"
  /custom-file-types/{organization_id}:
    $ref: "./v3/paths/custom_file_types.yml"
  /custom-file-type:
    $ref: "./v3/paths/custom_file_type.yml"
  /custom-file-types/aggregated:
    $ref: "./v3/paths/aggregated_custom_file_types.yml"
  /insights-document-types/{organization_id}:
    $ref: "./v3/paths/insights_document_types.yml"
  /customizable-classifiers-v2-input-types/{organization_id}:
    $ref: "./v3/paths/customizable_classifiers_v2_input_types.yml"
  /task-scoring:
    $ref: "./v3/paths/task_scoring.yml"
  /task-scoring/check-pending:
    $ref: "./v3/paths/task_scoring_check_pending.yml"
  /document-ingestion-queue:
    $ref: "./v3/paths/document_ingestion_queue.yml"
  /document-ingestion-queue/assignments:
    $ref: "./v3/paths/document_ingestion_queue_assignments.yml"
  /document-ingestion-queue/assignments/{file_id}:
    $ref: "./v3/paths/document_ingestion_queue_assignment_by_file.yml"
  /task-cost-limits:
    $ref: "./v3/paths/task_cost_limits.yml"
  /task-cost-limits/{limit_id}:
    $ref: "./v3/paths/task_cost_limit.yml"
  /tasks/cost-last-day:
    $ref: "./v3/paths/tasks_cost_last_day.yml"
  /tasks/avg-daily-cost:
    $ref: "./v3/paths/tasks_avg_daily_cost.yml"
  /integrations/logs:
    $ref: "./v3/paths/integration_logs.yml"
  /integrations/logs/{integration_log_id}:
    $ref: "./v3/paths/integration_logs_by_id.yml"
  /async-ops:
    $ref: "./v3/paths/async_ops.yml"
  /async-ops/{async_op_id}:
    $ref: "./v3/paths/async_ops_by_id.yml"
components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: Authorization
      x-apikeyInfoFunc:
  responses:
    UnauthorizedError:
      description: the API key is missing or invalid
  parameters:
    idParam:
      in: path
      name: id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the ID of the object.
      example: "f116fa5c-e5d9-43c2-8dc3-ac3fa60ff3b3"
    submission_idParam:
      in: path
      name: submission_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the ID of the Submission.
    report_idParam:
      in: path
      name: report_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the ID of the Report.
    external_idParam:
      in: path
      name: external_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the external identifier of the File.
    file_idParam:
      in: path
      name: file_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the ID of the file.
    note_idParam:
      in: path
      name: note_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the ID of the note.
    email_idParam:
      in: path
      name: email_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the ID of the email.
    experiment_idParam:
      in: path
      name: exp_id
      required: true
      schema:
        type: string
        description: the ID or name of the experiment.
    sample_idParam:
      in: path
      name: sample_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
        description: the ID or name of the experiment sample.
    page_numParam:
      in: path
      name: page_num
      required: true
      schema:
        type: integer
        minimum: 1
      description: The number of the File Page.
    reportIdsParam:
      in: query
      name: report_ids
      description: the IDs of the reports.
      required: false
      schema:
        type: array
        items:
          type: string
          format: uuid
          pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      style: form
      explode: false
    submission_coverage_idParam:
      in: path
      name: coverage_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the ID of the Submission Coverage.
    expandParam:
      in: query
      name: expand
      required: false
      schema:
        type: string
      style: form
      explode: false
      description: the name of an attribute that should be expanded.
      example: "notebook"
    expandListParam:
      in: query
      name: expand
      required: false
      schema:
        type: array
        items:
          type: string
      description: names of attributes that should be expanded.
      example: ["name", "submissions.name"]
    userIdParam:
      in: path
      name: user_id
      required: true
      schema:
        type: integer
      description: the ID of the User.
    organizationIdPathParam:
      in: path
      name: organization_id
      required: true
      schema:
        type: integer
      description: the ID of the Organization.
    policyIdPathParam:
      in: path
      name: policy_id
      required: true
      schema:
        type: string
      description: "the external ID of the Policy."
      example: "CBA1379437"
    pageParam:
      in: query
      name: page
      required: false
      schema:
        type: integer
      example: 1
      description: the index of the page of the collection.
    perPageParam:
      in: query
      name: per_page
      required: false
      schema:
        type: integer
      example: 10
      description: the number of elements to include in the page.
    callerContext:
      in: header
      name: X-Kalepa-Caller-Context
      required: false
      schema:
        type: string
      example: taxonomy-sync
      description: the context of the caller
    supportUserIdParam:
      in: path
      name: user_id
      required: true
      schema:
        type: string
        format: uuid
        pattern: "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
      description: the ID of the support user.
      example: "f116fa5c-e5d9-43c2-8dc3-ac3fa60ff3b3"
    fileProcessingStateParam:
      in: query
      name: file_processing_state
      required: false
      schema:
        type: string
        nullable: true
        enum:
          - "NOT_CLASSIFIED"
          - "CLASSIFICATION_FAILED"
          - "NOT_APPLICABLE_FOR_PROCESSING"
          - "CLASSIFIED"
          - "PROCESSING"
          - "PROCESSING_FAILED"
          - "PROCESSED"
          - "WAITING_FOR_DATA_ONBOARDING"
          - "DATA_ONBOARDED"
          - "WAITING_FOR_HUMAN_INPUT"
          - "WAITING_FOR_ENTITY_MAPPING"
          - "WAITING_FOR_BUSINESS_CONFIRMATION"
          - "AUTOCONFIRMING"
          - "WAITING_FOR_DATA_CONSOLIDATION"
          - "REPLACED"
          - "COMPLETED"
          - "WAITING_FOR_COMPLETION"
          - "IGNORED"
          - "WAITING_FOR_PROCESSING"
          - "CLASSIFYING"
          - "WAITING_FOR_FACTS_AND_SUGGESTIONS_RESOLUTION"
          - "FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED"
          - "WAITING_FOR_FILE_INSIGHTS"
          - "FILE_INSIGHTS_FINISHED"
  schemas:
    AdminOrganization:
      $ref: "./v3/schemas/admin_organization.yml"
    AdminUser:
      $ref: "./v3/schemas/admin_user.yml"
    AggregatedFileTypes:
      $ref: "./v3/schemas/aggregated_file_types.yml"
    AggregatedCustomFileTypes:
      $ref: "./v3/schemas/aggregated_custom_file_types.yml"
    UserGroup:
      $ref: "./v3/schemas/user_group.yml"
    SubmissionNote:
      $ref: "./v3/schemas/submission_note.yml"
    SaveSubmissionNoteOptions:
      $ref: "./v3/schemas/save_submission_note_options.yml"
    SaveSubmissionNoteRequest:
      $ref: "./v3/schemas/save_submission_note_request.yml"
    RecommendationSubmissionNoteRequest:
      $ref: "./v3/schemas/recommendation_submission_note_request.yml"
    AdminUserUpdateRequest:
      $ref: "./v3/schemas/admin_user_update_request.yml"
    ID:
      $ref: "./v2/schemas/id.yml"
    NotNullableUUID:
      $ref: "./v3/schemas/not_nullable_uuid.yml"
    CreatedAt:
      $ref: "./v2/schemas/created_at.yml"
    UpdatedAt:
      $ref: "./v2/schemas/updated_at.yml"
    Version:
      $ref: "./v2/schemas/version.yml"
    Report:
      $ref: "./v3/schemas/report.yml"
    ReportExternal:
      $ref: "./v3/schemas/report_external.yml"
    ReportsEnvelope:
      $ref: "./v3/schemas/reports.yml"
    ReportExtract:
      $ref: "./v3/schemas/report_extract.yml"
    GeneratedReportExtract:
      $ref: "./v3/schemas/generated_report_extract.yml"
    ReportPatch:
      $ref: "./v3/schemas/report_patch.yml"
    SubmissionPremises:
      $ref: "./v3/schemas/submission_premises.yml"
    SubmissionPremisesEnvelope:
      $ref: "./v3/schemas/submission_premises_envelope.yml"
    Submission:
      $ref: "./v3/schemas/submission.yml"
    Submissions:
      $ref: "./v3/schemas/submissions.yml"
    ERSEntity:
      $ref: "./v3/schemas/ers_v3_entity.yml"
    ERSEntityName:
      $ref: "./v3/schemas/ers_v3_entity_name.yml"
    ERSEntityPremises:
      $ref: "./v3/schemas/ers_v3_entity_premises.yml"
    ERSInnerPremises:
      $ref: "./v3/schemas/ers_v3_inner_premises.yml"
    ERSExternalIdentifier:
      $ref: "./v3/schemas/ers_v3_external_identifier.yml"
    ERSEntityIndustry:
      $ref: "./v3/schemas/ers_v3_entity_industry.yml"
    ERSEntitiesRelation:
      $ref: "./v3/schemas/ers_v3_entities_relation.yml"
    FilesClearing:
      $ref: "./v3/schemas/files_clearing.yml"
    FirstPartySuggestions:
      $ref: "./v3/schemas/first_party_suggestions.yml"
    FirstPartySuggestion:
      $ref: "./v3/schemas/first_party_suggestion.yml"
    FirstPartySuggestionValue:
      $ref: "./v3/schemas/first_party_suggestion_value.yml"
    FirstPartySuggestionsLean:
      $ref: "./v3/schemas/first_party_suggestions_lean.yml"
    FirstPartySuggestionLean:
      $ref: "./v3/schemas/first_party_suggestion_lean.yml"
    FirstPartySuggestionValueLean:
      $ref: "./v3/schemas/first_party_suggestion_value_lean.yml"
    SubmissionBusiness:
      $ref: "./v3/schemas/submission_business.yml"
    FirstPartyField:
      $ref: "./v3/schemas/first_party_field.yml"
    FirstPartyFieldValue:
      $ref: "./v3/schemas/first_party_field_value.yml"
    FactSubtypeSuggestion:
      $ref: "./v3/schemas/fact_subtype_suggestion.yml"
    ValueSuggestionResult:
      $ref: "./v3/schemas/value_suggestion_result.yml"
    FirstPartyFieldEntity:
      $ref: "./v3/schemas/first_party_field_entity.yml"
    FirstPartyFieldsGroup:
      $ref: "./v3/schemas/first_party_fields_group.yml"
    FirstPartyGroupSuggestion:
      $ref: "./v3/schemas/first_party_group_suggestion.yml"
    FirstPartyFieldGroupRequest:
      $ref: "./v3/schemas/first_party_field_group_request.yml"
    FirstPartyFieldGroupSuggestion:
      $ref: "./v3/schemas/first_party_field_group_suggestion.yml"
    FirstPartyFieldGroupSuggestions:
      $ref: "./v3/schemas/first_party_field_group_suggestions.yml"
    OnboardedFile:
      $ref: "./v3/schemas/onboarded_file.yml"
    OnboardedDataUpdate:
      $ref: "./v3/schemas/onboarded_data_update.yml"
    ProcessedFileBRD:
      $ref: "./v3/schemas/processed_file_brd.yml"
    ProcessedFilePD:
      $ref: "./v3/schemas/processed_file_pd.yml"
    PFResolutionDataRowUpdate:
      $ref: "./v3/schemas/pf_resolution_data_row_update.yml"
    SubmissionEntity:
      $ref: "./v3/schemas/submission_entity.yml"
    ResolvedDataField:
      $ref: "./v3/schemas/resolved_data_field.yml"
    MatchingMetadata:
      $ref: "./v3/schemas/matching_metadata.yml"
    ResolvedDataValue:
      $ref: "./v3/schemas/resolved_data_value.yml"
    SubmissionHistoryElement:
      $ref: "./v3/schemas/submission_history_element.yml"
    SubmissionRecommendationResult:
        $ref: "./v3/schemas/submission_recommendation_result.yml"
    Subscription:
      $ref: "./v3/schemas/subscription.yml"
    SubscriptionUpdate:
      $ref: "./v3/schemas/subscription_update.yml"
    DateRangeDistributionCategory:
      $ref: "./v3/schemas/date_range_distribution_category.yml"
    MetricV2Request:
      $ref: "./v3/schemas/metric_v2_request.yml"
    BulkCreateMetricV2Request:
      $ref: "./v3/schemas/bulk_metric_v2_request.yml"
    GetMetricsV2Request:
      $ref: "./v3/schemas/get_metrics_v2_request.yml"
    MetricV2:
      $ref: "./v3/schemas/metric_v2.yml"
    MetricSource:
      $ref: "./v3/schemas/metric_source.yml"
    MetricsV2Envelope:
      $ref: "./v3/schemas/metrics_v2.yml"
    SummaryPreference:
      $ref: "./v3/schemas/summary_preference.yml"
    SummaryPreferences:
      $ref: "./v3/schemas/summary_preferences.yml"
    ReportSummaryPreferences:
      $ref: "./v3/schemas/report_summary_preferences.yml"
    ReportSummaryPreferenceFields:
      $ref: "./v3/schemas/report_summary_preference_fields.yml"
    ReportSummaryPreferencesFields:
      $ref: "./v3/schemas/report_summary_preferences_fields.yml"
    ResolutionResult:
      $ref: "./v3/schemas/resolution_result.yml"
    RequestedCoverages:
      $ref: "./v3/schemas/requested_coverages.yml"
    RequestedCoverage:
      $ref: "./v3/schemas/requested_coverage.yml"
    UnderlyingPolicy:
      $ref: "./v3/schemas/underlying_policy.yml"
    Deductible:
      $ref: "./v3/schemas/deductible.yml"
    ClearingIssue:
      $ref: "./v3/schemas/clearing_issue.yml"
    User:
      $ref: "./v3/schemas/user.yml"
    SupportUser:
      $ref: "./v3/schemas/support_user.yml"
    UserUpdate:
      $ref: "./v3/schemas/user_update.yml"
    UserMetadata:
      $ref: "./v3/schemas/user_metadata.yml"
    AssignedUnderwriter:
      $ref: "./v3/schemas/assigned_underwriter.yml"
    Settings:
      $ref: "./v3/schemas/settings.yml"
    SICCode:
      $ref: "./v3/schemas/sic_code.yml"
    SICCodes:
      $ref: "./v3/schemas/sic_codes.yml"
    Organization:
      $ref: "./v3/schemas/organization.yml"
    Organizations:
      $ref: "./v3/schemas/organizations.yml"
    Permission:
      $ref: "./v3/schemas/permission.yml"
    PermissionsEnvelope:
      $ref: "./v3/schemas/permissions.yml"
    RequestedProperties:
      $ref: "./v3/schemas/requested_properties.yml"
    ERSSearchResults:
      $ref: "./v3/schemas/ers_search_results.yml"
    File:
      $ref: "./v3/schemas/file.yml"
    FileStats:
      $ref: "./v3/schemas/file_stats.yml"
    ExternalFile:
      $ref: "./v3/schemas/external_file.yml"
    FileForExternalConsumer:
      $ref: "./v3/schemas/file_for_external_consumer.yml"
    FileExtractedMetadata:
      $ref: "./v3/schemas/file_extracted_metadata.yml"
    FilePatch:
      $ref: "./v3/schemas/file_patch.yml"
    FileAndSensibleProcessingPatch:
      $ref: "./v3/schemas/file_and_sensible_processing_patch.yml"
    FilesRequest:
      $ref: "./v3/schemas/files_request.yml"
    FilePageLabelStudio:
      $ref: "./v3/schemas/file_page_label_studio.yml"
    FileSendToLabelStudioRequest:
      $ref: "./v3/schemas/file_send_to_label_studio_request.yml"
    FilePageProcessedRequest:
      $ref: "./v3/schemas/file_page_processed_request.yml"
    FilePageProcessedResponse:
      $ref: "./v3/schemas/file_page_processed_response.yml"
    FileUploadUrl:
      $ref: "./v3/schemas/file_upload_url.yml"
    ProcessedFile:
      $ref: "./v3/schemas/processed_file.yml"
    ProcessedFileUpdate:
      $ref: "./v3/schemas/processed_file_update.yml"
    PdsDebuggerFileData:
      $ref: "./v3/schemas/pds_debugger_file_data.yml"
    SubmissionClearingSubStatus:
      $ref: "./v3/schemas/submission_clearing_sub_status.yml"
    OrgMetricConfig:
      $ref: "./v3/schemas/org_metric_config.yml"
    OrgMetricConfigs:
      $ref: "./v3/schemas/org_metric_configs.yml"
    MetricGroup:
      $ref: "./v3/schemas/metric_group.yml"
    MetricGroups:
      $ref: "./v3/schemas/metric_groups.yml"
    MetricPreference:
      $ref: "./v3/schemas/metric_preference.yml"
    MetricPreferences:
      $ref: "./v3/schemas/metric_preferences.yml"
    NotebookThread:
      $ref: "./v3/schemas/notebook_thread.yml"
    NotebookMessage:
      $ref: "./v3/schemas/notebook_message.yml"
    Feedback:
      $ref: "./v2/schemas/feedback.yml"
    V3Feedback:
      $ref: "./v3/schemas/feedback.yml"
    CreateReport:
      $ref: "./v3/schemas/create_report.yml"
    Coverage:
      $ref: "./v3/schemas/coverage.yml"
    ExecutionEvent:
      $ref: "./v3/schemas/execution_event.yml"
    ExecutionEventEnvelope:
      $ref: "./v3/schemas/execution_event_envelope.yml"
    CopilotWorkerExecutionEvent:
      $ref: "./v3/schemas/copilot_worker_execution_event.yml"
    CopilotWorkerExecutionEventEnvelope:
      $ref: "./v3/schemas/copilot_worker_execution_event_envelope.yml"
    ClientApplication:
      $ref: "./v3/schemas/client_application.yml"
    ClientApplications:
      $ref: "./v3/schemas/client_applications.yml"
    InsightFeedback:
      $ref: "./v3/schemas/insight_feedback.yml"
    MetricTemplate:
      $ref: "./v3/schemas/metric_template.yml"
    MetricTemplates:
      $ref: "./v3/schemas/metric_templates.yml"
    HubTemplate:
      $ref: "./v3/schemas/hub_template.yml"
    InterpretationConfig:
      $ref: "./v3/schemas/interpretation_config.yml"
    InterpretationConfigEnvelope:
      $ref: "./v3/schemas/interpretation_config_envelope.yml"
    BusinessAlias:
      $ref: "./v3/schemas/business_alias.yml"
    BusinessAliasesEnvelope:
      $ref: "./v3/schemas/business_aliases_envelope.yml"
    TenantFeedback:
      $ref: "./v3/schemas/tenant_feedback.yml"
    StuckSubmissionFeedback:
      $ref: "./v3/schemas/stuck_submission_feedback.yml"
    Loss:
      $ref: "./v3/schemas/loss.yml"
    LossRunFileStatus:
      $ref: "./v3/schemas/loss_run_file_status.yml"
    LossesEnvelope:
      $ref: "./v3/schemas/losses_envelope.yml"
    LossSummary:
      $ref: "./v3/schemas/loss_summary.yml"
    LossPolicy:
      $ref: "./v3/schemas/loss_policy.yml"
    PutLossPolicyRequest:
      $ref: "./v3/schemas/put_loss_policy_request.yml"
    UpdateLossLobBulkRequest:
      $ref: "./v3/schemas/update_loss_lob_bulk_request.yml"
    LossLobInferenceRequirement:
      $ref: "./v3/schemas/loss_lob_inference_requirement.yml"
    ClassifierMetadataEnvelope:
      $ref: "./v3/schemas/classifier_metadata_envelope.yml"
    ClassifierMetadata:
      $ref: "./v3/schemas/classifier_metadata.yml"
    CustomizableClassifier:
      $ref: "./v3/schemas/customizable_classifier.yml"
    CustomizableClassifierTestRun:
      $ref: "./v3/schemas/customizable_classifier_test_run.yml"
    ClassifierTestRunEnvelope:
      $ref: "./v3/schemas/classifier_test_run_envelope.yml"
    CustomizablePhraseMatchingClassifierPhrase:
      $ref: "./v3/schemas/customizable_phrase_matching_classifier_phrase.yml"
    TestRunClassificationTask:
      $ref: "./v3/schemas/test_run_classification_task.yml"
    SendEmailRequest:
      $ref: "./v3/schemas/send_email_request.yml"
    Policy:
      $ref: "./v3/schemas/policy.yml"
    Policies:
      $ref: "./v3/schemas/policies.yml"
    SubmissionHistory:
      $ref: "./v3/schemas/submission_history.yml"
    PresignedUrlRequest:
      $ref: "./v3/schemas/presigned_url_request.yml"
    PresignedUrl:
      $ref: "./v3/schemas/presigned_url.yml"
    UserCreateIfNotPresent:
      $ref: "./v3/schemas/user_create_if_not_present.yml"
    FirstPartyValueChangeRequest:
      $ref: "./v3/schemas/first_party_value_change_request.yml"
    FactSubtypeReassignmentRequest:
      $ref: "./v3/schemas/fact_subtype_reassignment_request.yml"
    ReassignmentResponse:
      $ref: "./v3/schemas/reassignment_response.yml"
    OshaViolationRequest:
      $ref: "./v3/schemas/osha_violation_request.yml"
    AdditionalFileRequest:
      $ref: "./v3/schemas/additional_file_request.yml"
    Lob:
      $ref: "./v3/schemas/lob.yml"
    Lobs:
      $ref: "./v3/schemas/lobs.yml"
    Brokerage:
      $ref: "./v3/schemas/brokerage.yml"
    Brokerages:
      $ref: "./v3/schemas/brokerages.yml"
    BrokerGroups:
      $ref: "./v3/schemas/broker_groups.yml"
    BrokerGroup:
      $ref: "./v3/schemas/broker_group.yml"
    BrokerGroupPatch:
      $ref: "./v3/schemas/broker_group_patch.yml"
    UpdateBrokerGroupMappings:
      $ref: "./v3/schemas/update_broker_group_mappings.yml"
    BrokerGroupMapping:
      $ref: "./v3/schemas/broker_group_mapping.yml"
    MergeBrokeragesRequest:
      $ref: "./v3/schemas/merge_brokerages_request.yml"
    BrokerageEmployee:
      $ref: "./v3/schemas/brokerage_employee.yml"
    BrokerageEmployees:
      $ref: "./v3/schemas/brokerage_employees.yml"
    MergeBrokerageEmployeesRequest:
      $ref: "./v3/schemas/merge_brokerage_employees_request.yml"
    DashboardUrl:
      $ref: "./v3/schemas/dashboard_url.yml"
    OpenReport:
      $ref: "./v3/schemas/open_report.yml"
    UserSignupData:
      $ref: "./v3/schemas/user_signup_data.yml"
    Bookmark:
      $ref: "./v3/schemas/bookmark.yml"
    SubmissionUser:
      $ref: "./v3/schemas/submission_user.yml"
    SubmissionUserUpdate:
      $ref: "./v3/schemas/submission_user_update.yml"
    SubmissionClientId:
      $ref: "./v3/schemas/submission_client_id.yml"
    SearchResult:
      $ref: "./v3/schemas/search_result.yml"
    SearchReportHit:
      $ref: "./v3/schemas/search_report_hit.yml"
    SensibleLossRun:
      $ref: "./v3/schemas/sensible_loss_run.yml"
    SensibleAcord:
      $ref: "./v3/schemas/sensible_acord.yml"
    ExtractionError:
      $ref: "./v3/schemas/extraction_error.yml"
    UpdateBusinessIdsRequest:
      $ref: "./v3/schemas/update_business_ids_request.yml"
    RevertBusinessUpdatesRequest:
      $ref: "./v3/schemas/revert_business_updates_request.yml"
    UpdateBusinessIdsResponse:
      $ref: "./v3/schemas/update_business_ids_response.yml"
    BusinessesEnvelope:
      $ref: "./v3/schemas/ers_businesses_envelope.yml"
    EmailTemplate:
      $ref: "./v3/schemas/email_template.yml"
    SubmissionsBulkDeclineRequest:
      $ref: "./v3/schemas/submissions_bulk_decline_request.yml"
    SubmissionSendEmailRequest:
      $ref: "./v3/schemas/submissions_send_email_request.yml"
    SummaryOfLosses:
      $ref: "./v3/schemas/summary_of_losses.yml"
    SummaryOfLossesGrouping:
      $ref: "./v3/schemas/summary_of_losses_grouping.yml"
    SentEmail:
      $ref: "./v3/schemas/sent_email.yml"
    SentScheduledEmails:
      $ref: "./v3/schemas/sent_scheduled_emails.yml"
    SubmissionSync:
      $ref: "./v3/schemas/submission_sync.yml"
    SubmissionSyncCoverage:
      $ref: "./v3/schemas/submission_sync_coverage.yml"
    SubmissionHandlerOutput:
      $ref: "./v3/schemas/submission_handler_output.yml"
    SubmissionSyncUnderwriterState:
      $ref: "./v3/schemas/submission_sync_underwriter_state.yml"
    SubmissionSyncClientIdState:
      $ref: "./v3/schemas/submission_sync_client_id_state.yml"
    SubmissionSyncCoverageState:
      $ref: "./v3/schemas/submission_sync_coverage_state.yml"
    SubmissionSyncState:
      $ref: "./v3/schemas/submission_sync_state.yml"
    SubmissionSyncRelationState:
      $ref: "./v3/schemas/submission_sync_relation_state.yml"
    SubmissionSyncReportState:
      $ref: "./v3/schemas/submission_sync_report_state.yml"
    SubmissionSyncBrokerageState:
      $ref: "./v3/schemas/submission_sync_brokerage_state.yml"
    SubmissionSyncBrokerState:
      $ref: "./v3/schemas/submission_sync_broker_state.yml"
    SubmissionSyncReport:
      $ref: "./v3/schemas/submission_sync_report.yml"
    SubmissionSyncIdentifierState:
      $ref: "./v3/schemas/submission_sync_identifier_state.yml"
    SubmissionSyncPremisesState:
      $ref: "./v3/schemas/submission_sync_premises_state.yml"
    SubmissionChanges:
      $ref: "./v3/schemas/submission_changes.yml"
    SubmissionSyncMatcherData:
      $ref: "./v3/schemas/submission_sync_matcher_data.yml"
    SubmissionSyncIdentifiers:
      $ref: "./v3/schemas/submission_sync_identifiers.yml"
    SubmissionSyncResponse:
      $ref: "./v3/schemas/submission_sync_response.yml"
    RunSyncRequest:
      $ref: "./v3/schemas/run_sync_request.yml"
    DeleteSyncRequests:
      $ref: "./v3/schemas/delete_sync_requests.yml"
    MatchResult:
      $ref: "./v3/schemas/submission_match_result.yml"
    VerificationResult:
      $ref: "./v3/schemas/verification_result.yml"
    VerificationCheckResult:
      $ref: "./v3/schemas/verification_check_result.yml"
    VerifyShellResult:
      $ref: "./v3/schemas/verify_shell_result.yml"
    Email:
      $ref: "./v3/schemas/email.yml"
    EmailClassificationLabelsBulkUpdateRequest:
      $ref: "./v3/schemas/email_classification_labels_bulk_update_request.yml"
    EmailRequest:
      $ref: "./v3/schemas/email_request.yml"
    EmailResponse:
      $ref: "./v3/schemas/email_response.yml"
    ReportEmailCorrespondence:
      $ref: "./v3/schemas/report_email_correspondence.yml"
    ReportEmailCorrespondenceExtended:
      $ref: "./v3/schemas/report_email_correspondence_extended.yml"
    AskQuestionsResponse:
      $ref: "./v3/schemas/ask_questions_response.yml"
    AskQuestionsRequest:
      $ref: "./v3/schemas/ask_questions_request.yml"
    LOBCarrier:
      $ref: "./v3/schemas/lob_carrier.yml"
    VerifyCandidates:
      $ref: "./v3/schemas/verify_candidates.yml"
    QualityAuditQuestion:
      $ref: "./v3/schemas/quality_audit_question.yml"
    QualityAuditQuestions:
      $ref: "./v3/schemas/quality_audit_questions.yml"
    SensibleExtraction:
      $ref: "./v3/schemas/sensible_extraction.yml"
    SensibleExtractionDocument:
      $ref: "./v3/schemas/sensible_extraction_document.yml"
    StatisticsResponse:
      $ref: "./v3/schemas/statistics_response.yml"
    ProcessFileFromCacheResult:
      $ref: "./v3/schemas/process_file_from_cache_result.yml"
    SubmissionPriority:
      $ref: "./v3/schemas/submission_priority.yml"
    FileMetric:
      $ref: "./v3/schemas/file_metric.yml"
    WorkersCompExperience:
      $ref: "./v3/schemas/workers_comp_experience.yml"
    WorkersCompStateRatingInfo:
      $ref: "./v3/schemas/workers_comp_state_rating_info.yml"
    PDSEventRequest:
      $ref: "./v3/schemas/pds_event_request.yml"
    AcordLocationInformation:
      $ref: "./v3/schemas/acord_location_information.yml"
    IFTAData:
      $ref: "./v3/schemas/ifta_data.yml"
    AggregatedIFTADataRequest:
      $ref: "./v3/schemas/aggregated_ifta_data_request.yml"
    IFTASingleJurisdictionResponse:
      $ref: "./v3/schemas/ifta_single_jurisdiction_response.yml"
    AggregatedIFTADataResponse:
      $ref: "./v3/schemas/aggregated_ifta_data_response.yml"
    AggregatedIFTAQuartersResponse:
      $ref: "./v3/schemas/aggregated_ifta_quarters_response.yml"
    MatcherDefinition:
      $ref: "./v3/schemas/matcher_definition.yml"
    MatcherType:
      $ref: "./v3/schemas/matcher_type.yml"
    SubmissionMatcherConfig:
      $ref: "./v3/schemas/submission_matcher_config.yml"
    ScorerDefinition:
      $ref: "./v3/schemas/scorer_definition.yml"
    ScorerType:
      $ref: "./v3/schemas/scorer_type.yml"
    ScorerConfig:
      $ref: "./v3/schemas/scorer_config.yml"
    CloseMatch:
      $ref: "./v3/schemas/close_match.yml"
    MatchingInformation:
      $ref: "./v3/schemas/matching_information.yml"
    LookupConfig:
      $ref: "./v3/schemas/lookup_config.yml"
    MatcherDryRunRequest:
      $ref: "./v3/schemas/matcher_dry_run_request.yml"
    DryRunSubmissionMatch:
      $ref: "./v3/schemas/dry_run_submission_match.yml"
    MatcherDryRunResult:
      $ref: "./v3/schemas/matcher_dry_run_result.yml"
    ValidateSensibleConfigurationRequest:
      $ref: "./v3/schemas/validate_sensible_configuration_request.yml"
    ParagonUnderwriter:
      $ref: "./v3/schemas/paragon_underwriter.yml"
    ParagonCompanyLine:
      $ref: "./v3/schemas/paragon_company_line.yml"
    ParagonNotification:
      $ref: "./v3/schemas/paragon_notification.yml"
    ParagonNotificationResponse:
      $ref: "./v3/schemas/paragon_notification_response.yml"
    SendgridEvent:
      $ref: "./v3/schemas/sendgrid_event.yml"
    EmailStatus:
      $ref: "./v3/schemas/email_status.yml"
    EmailClassification:
      $ref: "./v3/schemas/email_classification.yml"
    EmailClassificationLabel:
      $ref: "./v3/schemas/email_classification_label.yml"
    EmailClassificationLabelStats:
      $ref: "./v3/schemas/email_classification_label_stats.yml"
    EmailClassifier:
      $ref: "./v3/schemas/email_classifier.yml"
    CustomizableEmailClassifier:
      $ref: "./v3/schemas/customizable_email_classifier.yml"
    AttachmentsBasedEmailClassifier:
      $ref: "./v3/schemas/attachments_based_email_classifier.yml"
    StuckDetails:
      $ref: "./v3/schemas/stuck_details.yml"
    InvokeRecommendationsRequest:
      $ref: "./v3/schemas/invoke_recommendations_request.yml"
    SyncSubmissionRecommendationsDataRequest:
      $ref: "./v3/schemas/sync_submission_recommendation_data_request.yml"
    ExperimentDefinition:
      $ref: "./v3/schemas/experiment_definition.yml"
    ExperimentDefinitionUpdateRequest:
      $ref: "./v3/schemas/experiment_definition_update_request.yml"
    ExperimentSample:
      $ref: "./v3/schemas/experiment_sample.yml"
    ExperimentRun:
      $ref: "./v3/schemas/experiment_run.yml"
    ExperimentRunPage:
      $ref: "./v3/schemas/experiment_run_page.yml"
    ExperimentSampleUpdateRequest:
      $ref: "./v3/schemas/experiment_sample_update_request.yml"
    UWAssignmentConfig:
      $ref: "./v3/schemas/uw_assignment_configuration.yml"
    SubmissionRelation:
      $ref: "./v3/schemas/submission_relation.yml"
    CalculateLookalikeBindRateForSubmissions:
      $ref: "./v3/schemas/calculate_lookalike_bind_rate_for_submissions_request.yml"
    RoutingRule:
      $ref: "./v3/schemas/routing_rule.yml"
    SyncRequestsForClientIdsRequest:
      $ref: "./v3/schemas/sync_requests_for_client_ids_request.yml"
    AdditionalIdentifier:
      $ref: "./v3/schemas/additional_identifier.yml"
    ScoreAcordExtractionRequest:
      $ref: "./v3/schemas/score_acord_extraction_request.yml"
    ScoreAcordExtractionResponse:
      $ref: "./v3/schemas/score_acord_extraction_response.yml"
    SubmissionAggregationsRequest:
      $ref: "./v3/schemas/submission_aggregations_request.yml"
    AddAccountReviewDocReadyNoteRequest:
      $ref: "./v3/schemas/add_account_review_doc_ready_note_request.yml"
    MatchingResult:
      $ref: "./v3/schemas/fact_subtype_match_response.yml"
    ClientSubmissionStageConfig:
      $ref: "./v3/schemas/client_submission_stage_config.yml"
    Shareholders:
      $ref: "./v3/schemas/shareholders.yml"
    SubmissionLevelExtractedData:
      $ref: "./v3/schemas/submission_level_extracted_data.yml"
    SubmissionFilesData:
      $ref: "./v3/schemas/submission_files_data.yml"
    SubmissionIdentifier:
      $ref: "./v3/schemas/submission_identifier.yml"
    CreateNotesExternalRequest:
      $ref: "./v3/schemas/create_notes_external_request.yml"
    ExternalNote:
      $ref: "./v3/schemas/external_note.yml"
    ExternalNotes:
      $ref: "./v3/schemas/external_notes.yml"
    CreateOrReplaceNoteResponse:
      $ref: "./v3/schemas/create_or_replace_note_response.yml"
    ExternalCustomStatusPart:
      $ref: "./v3/schemas/external_custom_status_part.yml"
    ExternalCustomStatus:
      $ref: "./v3/schemas/external_custom_status.yml"
    SubmissionMetadata:
      $ref: "./v3/schemas/submission_metadata.yml"
    ServiceAvailability:
      $ref: "./v3/schemas/service_availability.yml"
    ValueValidationResult:
      $ref: "./v3/schemas/value_validation_result.yml"
    SubmissionShadowInfo:
      $ref: "./v3/schemas/submission_shadow_info.yml"
    SubtypesBenchmarkData:
      $ref: "./v3/schemas/subtypes_benchmark_data.yml"
    DataValidationResults:
      $ref: "./v3/schemas/data_validation_results.yml"
    FactSubtypeSelectionBenchmark:
      $ref: "./v3/schemas/fact_subtype_selection_benchmark.yml"
    ActiveOrganizationSubmissionsInfo:
      $ref: "./v3/schemas/active_organizational_submissions_info.yml"
    TaskDefinition:
      $ref: "./v3/schemas/task_definition.yml"
    TaskModel:
      $ref: "./v3/schemas/task_model.yml"
    TaskDefinitionModel:
      $ref: "./v3/schemas/task_definition_model.yml"
    Task:
      $ref: "./v3/schemas/task.yml"
    TaskExecution:
      $ref: "./v3/schemas/task_execution.yml"
    TaxonomyMapping:
      $ref: "./v3/schemas/taxonomy_mapping.yml"
    TaxonomyDescription:
      $ref: "./v3/schemas/taxonomy_description.yml"
    ReportSnapshot:
      $ref: "./v3/schemas/report_snapshot.yml"
    PrometrixRisksEnvelope:
      $ref: "./v3/schemas/prometrix_risks.yml"
    PrometrixRisk:
      $ref: "./v3/schemas/prometrix_risk.yml"
    PrometrixReport:
      $ref: "./v3/schemas/prometrix_report.yml"
    PrometrixRiskOccupant:
      $ref: "./v3/schemas/prometrix_risk_occupant.yml"
    PdsCheckRequest:
      $ref: "./v3/schemas/pds_check_request.yml"
    Preferences:
      $ref: "./v3/schemas/preferences.yml"
    SetSubmissionIdentifiersRequest:
      $ref: "./v3/schemas/set_submission_identifiers_request.yml"
    CustomFileType:
      $ref: "./v3/schemas/custom_file_type.yml"
    CustomizableClassifiersV2InputTypes:
      $ref: "./v3/schemas/customizable_classifiers_v2_input_types.yml"
    SplitFileRequest:
      $ref: "./v3/schemas/split_file_request.yml"
    TaskScoring:
      $ref: "./v3/schemas/task_scoring.yml"
    FilesQueueEnvelope:
      $ref: "./v3/schemas/files_queue.yml"
    DIAssignmentRequest:
      $ref: "./v3/schemas/di_assignment_request.yml"
    SupportUserFile:
      $ref: "./v3/schemas/support_user_file.yml"
    FinishExternalClearingRequest:
      $ref: "./v3/schemas/finish_external_clearing_request.yml"
    SyncMatchingRun:
      $ref: "./v3/schemas/sync_matching_run.yml"
    SyncMatchingRunRequest:
      $ref: "./v3/schemas/sync_matching_run_request.yml"
    SyncMatchingRunResponse:
      $ref: "./v3/schemas/sync_matching_run_response.yml"
    SubmissionIdentifiersSuggestion:
      $ref: "./v3/schemas/submission_identifiers_suggestion.yml"
    SubmissionIdentifiersSuggestions:
      $ref: "./v3/schemas/submission_identifiers_suggestions.yml"
    EnhancedFileRequest:
      $ref: "./v3/schemas/enhanced_file_request.yml"
    EnhancedFile:
      $ref: "./v3/schemas/enhanced_file.yml"
    AddPdfExportReadyNoteRequest:
      $ref: "./v3/schemas/add_pdf_export_ready_note_request.yml"
    CustomizableClassifierV2:
      $ref: "./v3/schemas/customizable_classifier_v2.yml"
    ClassifierVersion:
      $ref: "./v3/schemas/classifier_version.yml"
    FilterRule:
      $ref: "./v3/schemas/filter_rule.yml"
    ClassifierConfig:
      $ref: "./v3/schemas/classifier_config.yml"
    ClassifierConfigVersion:
      $ref: "./v3/schemas/classifier_config_version.yml"
    ClassifierPhrase:
      $ref: "./v3/schemas/classifier_phrase.yml"
    PhrasesWithLLMConfigVersion:
      $ref: "./v3/schemas/phrases_with_llm_config_version.yml"
    PhrasesConfigVersion:
      $ref: "./v3/schemas/phrases_config_version.yml"
    LLMConfigVersion:
      $ref: "./v3/schemas/llm_config_version.yml"
    ClassifierToConfigVersion:
      $ref: "./v3/schemas/classifier_to_config_version.yml"
    ClassifierTaskDefinition:
      $ref: "./v3/schemas/classifier_task_definition.yml"
    ClassifierVersionTaskDataset:
      $ref: "./v3/schemas/classifier_version_task_dataset.yml"
    InitTaskDatasetExecutionRequest:
      $ref: "./v3/schemas/init_task_dataset_execution_request.yml"
    InitTaskDatasetExecutionResponse:
      $ref: "./v3/schemas/init_task_dataset_execution_response.yml"
    CreateTaskDatasetRequest:
      $ref: "./v3/schemas/create_task_dataset_request.yml"
    TaskDatasetInputWithGroundTruth:
      $ref: "./v3/schemas/task_dataset_input_with_ground_truth.yml"
    TaskCostLimit:
      $ref: './v3/schemas/task_cost_limit.yml'
    IntegrationLogCreateRequest:
      $ref: "./v3/schemas/integration_log_create_request.yml"
    IntegrationLogCreateResponse:
      $ref: "./v3/schemas/integration_log_create_response.yml"
    IntegrationLogUpdateRequest:
      $ref: "./v3/schemas/integration_log_update_request.yml"
    IntegrationLogFullDetails:
      $ref: "./v3/schemas/integration_log_full_details.yml"
    IntegrationLogLowDetails:
      $ref: "./v3/schemas/integration_log_low_details.yml"
    AsyncOpCreateRequest:
      $ref: "./v3/schemas/async_op_create_request.yml"
    AsyncOpCreateResponse:
      $ref: "./v3/schemas/async_op_create_response.yml"
    AsyncOpUpdateRequest:
      $ref: "./v3/schemas/async_op_update_request.yml"
    AsyncOpFullDetails:
      $ref: "./v3/schemas/async_op_full_details.yml"
    AsyncOpLowDetails:
      $ref: "./v3/schemas/async_op_low_details.yml"
    GetReportsExternalReportsEnvelope:
      $ref: "./v3/schemas/get_reports_external_reports_envelope.yml"
