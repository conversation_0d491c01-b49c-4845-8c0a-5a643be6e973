[project]
name = "copilot-api"
version = "1.0.0"
description = ""
authors = [{ name = "Kalepa Tech" }]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "ddtrace",
    "uwsgi~=2.0.26",
    "nh3~=0.2.15",
    "sqlalchemy-utils~=0.41.1",
    "aiohttp~=3.9",
    "en-core-web-sm",
    "convert-outlook-msg-file",
    "flask-log-request-id",
    # forked Spacy 3.4.1 with Pydantic v2 compatibility
    "spacy>=3.4.5",

]

[dependency-groups]
ecs = [
    "auth0-python~=3.24.1",
    "measurement==4.0a8",
    "datadog~=0.51",
    "alabaster~=0.7.12",
    "alembic~=1.9",
    "attrs~=23.1",
    "authlib~=0.14.3",
    "babel~=2.9",
    "bcrypt~=3.1",
    "behave~=1.2.6",
    "blinker~=1.4",
    "boto3==1.26.29",
    "botocore==1.29.29",
    "boto3-type-annotations~=0.3.1",
    "beautifulsoup4~=4.11",
    "business-rules~=1.0",
    "certifi~=2024.12.14",
    "cffi~=1.14",
    "charset-normalizer~=2.0",
    "click~=8.0",
    "clickclick~=20.10.2",
    "pyopenssl>=23.0.0,<24.3.0",
    "connexion>=2.14.2,<3",                 # v3+ is **heavily** incompatible with our current codebase
    "coverage~=5.5",
    "dataclasses-json~=0.5.1",
    "dateparser~=1.0",
    "decorator~=5.1.0",
    "dnspython~=1.16.0",
    "docutils~=0.17.1",
    "ecdsa~=0.17.0",
    "flask~=2.2.0",                         # 2.3.0+ is incompatible with Connexion 2, but we can't use Connexion 3 without a major code overhaul
    "flask-executor~=1.0.0",
    "flask-httpauth~=4.8.0",
    "flask-login~=0.6.3",
    "flask-migrate~=4.0.7",
    "flask-script~=2.0.6",
    "flask-sqlalchemy<3.1.0",               # 3.1.0+ is incompatible with SQLAlchemy 1.4
    "fpdf2~=2.7",
    "future~=0.18.2",
    "geographiclib~=1.52",
    "geopy~=1.20",
    "gitdb~=4.0.7",
    "gitpython~=0.3.6",
    "idna~=3.2",
    "imagesize~=1.2",
    "inflection~=0.5.1",
    "intervaltree",
    "jason~=0.1.7",
    "jinja2>=3.1.4,<4",
    "jmespath~=0.10.0",
    "jsonpath-ng~=1.5",
    "jsonschema<4.0",
    "knockapi~=0.1.0",
    "mako~=1.1",
    "markupsafe~=2.0",
    "marshmallow~=3.22.0",
    "marshmallow-enum~=1.5",
    "marshmallow-oneofschema~=3.0.1",
    "marshmallow-sqlalchemy~=0.28.2",
    "matplotlib~=3.6",
    "more-itertools~=10.5.0",
    "mpmath~=1.2",
    "nose~=1.3.7",
    "numpy~=1.22",
    "openapi-spec-validator~=0.2.8",
    "packaging~=22.0",
    "parse~=1.19",
    "parse-type~=0.5.2",
    "pluggy~=0.13.1",
    "ply~=3.11",
    "prance~=0.22",
    "protobuf~=3.18",
    "psutil~=5.9.5",
    "psycopg2-binary~=2.9.3",
    "py>=1.11",
    "pycparser~=2.20",
    "pygments~=2.10",
    "pyicu>=2.8,<=2.14",
    "pyjwt~=2.4",
    "pynumeral~=0.1.2",
    "pyparsing~=3.0",
    "pypi-publisher~=0.0.4",
    "pyrsistent~=0.18.0",
    "pytest~=7.2",
    "python-dateutil~=2.8",
    "python-editor~=1.0.4",
    "python-http-client~=3.3.2",
    "python-magic~=0.4.15",
    "python-slugify~=5.0.2",
    "pytz~=2022.6",
    "pyxlsb~=1.0.10",
    "pyyaml~=6.0.1",
    "regex==2022.10.31",
    "requests~=2.32",
    "requests-futures~=1.0.0",
    "retrying~=1.3.3",
    "s3transfer~=0.6.0",
    "semver~=2.13.0",
    "sendgrid~=6.8",
    "sentry-sdk[flask]<2",
    "six~=1.16.0",
    "smmap~=4.0.0",
    "snowballstemmer~=2.1",
    "sortedcontainers~=2.4.0",
    "sphinx~=4.5.0",
    "sphinx-rtd-theme~=1.3.0",
    "sphinxcontrib-applehelp~=1.0.2",
    "sphinxcontrib-devhelp~=1.0.2",
    "sphinxcontrib-htmlhelp~=2.1.0",
    "sphinxcontrib-jquery~=4.1",
    "sphinxcontrib-jsmath~=1.0.1",
    "sphinxcontrib-qthelp~=1.0.3",
    "sphinxcontrib-serializinghtml~=1.1.5",
    "sqlalchemy~=1.4.42",
    "starkbank-ecdsa~=2.2.0",
    "stringcase~=1.2.0",
    "swagger-ui-bundle==0.0.6",
    "sympy~=1.8",
    "tenacity~=8.5.0",
    "text-unidecode~=1.3",
    "typing-extensions~=4.4",
    "typing-inspect~=0.8.0",
    "tzlocal~=4.0",
    "urllib3>=1.26.16,<2",
    "vine~=1.3",
    "wcwidth~=0.2.5",
    "webargs~=5.5.3",
    "werkzeug<4",
    "openpyxl~=3.1.2",
    "xlsxwriter~=3.2.0",
    "pyasn1~=0.4.8",
    "pycryptodome~=3.11",
    "python-jose~=3.3",
    "rsa~=4.7",
    "email-validator~=1.3.1",
    "pymitter~=0.3.1",
    "pymupdf>=1.22.5,!=1.23.7,<1.25.0",
    "python-redis-lock~=3.7.0",
    "redis~=5.1.0",
    "amplitude-analytics~=1.1.0",
    "xhtml2pdf~=0.2.9",
    "html-sanitizer~=2.5.0",
    "launchdarkly-server-sdk~=8.1",
    "pdpyras~=5.2",
    "pyarrow~=17.0",
]
kalepa = [
    "adhoc-tasks-sdk",
    "datascience-common",
    "common[boss-api-client]",
    "copilot-client",
    "copilot-client-v3",
    "entity-resolution-service-client",
    "entity-resolution-service-client-v3",
    "facts-client",
    "facts-client-v2",
    "events-common",
    "infrastructure-services-common",
    "xlrd",
    "file-processing",
    "infrastructure-common[flask,db-tools]",
    "paragon-ims-api-client",
    "wrapt-timeout-decorator",
    "llm-common",
    "static-common",
]
dev = [
    "fakeredis~=2.18.0",
    "freezegun~=1.4.0",
    "lupa~=2.0",
    "moto[server]==4.0.11",
    "pytest-mock~=3.6.1",
    "pre-commit~=2.18",
    "pytest-postgresql~=1.4.1",
    "pytest-socket~=0.3.3",
    "pytest-timeout~=1.3.3",
    "requests-mock~=1.5.2",
    "pytest-cov~=4.0.0",
    "pytest-dotenv~=0.5.2",
    "types-flask-migrate==4.0.0.0",
    "types-flask-sqlalchemy~=2.5.9.4",
    "types-beautifulsoup4~=4.12.0.5",
    "types-dateparser~=1.1.4.9",
    "types-fpdf2~=2.7.4.1",
    "types-psutil~=5.9.5.15",
    "types-psycopg2~=2.9.21.10",
    "types-python-dateutil~=2.8.19.13",
    "types-pytz~=2023.3.0.0",
    "types-redis~=4.5.5.2",
    "types-requests~=2.31.0.1",
    "mypy~=1.4.0",
    "sqlalchemy-stubs~=0.4",
    "pytest-xdist[psutil]~=3.3.1",
    "testcontainers[postgres]>=4.8.2",
]

[tool.uv.sources]
adhoc-tasks-sdk = { index = "kalepi" }
common = { index = "kalepi" }
copilot-client = { index = "kalepi" }
copilot-client-v3 = { index = "kalepi" }
datascience-common = { index = "kalepi" }
entity-resolution-service-client = { index = "kalepi" }
entity-resolution-service-client-v3 = { index = "kalepi" }
events-common = { index = "kalepi" }
facts-client = { index = "kalepi" }
facts-client-v2 = { index = "kalepi" }
file-processing = { index = "kalepi" }
infrastructure-common = { index = "kalepi" }
infrastructure-services-common = { index = "kalepi" }
llm-common = { index = "kalepi" }
paragon-ims-api-client = { index = "kalepi" }
static-common = { index = "kalepi" }
wrapt-timeout-decorator = { index = "kalepi" }
xlrd = { index = "kalepi" }
spacy = { index = "kalepi" }
en-core-web-sm = { index = "kalepi" }
convert-outlook-msg-file = { index = "kalepi" }
flask-log-request-id = { index = "kalepi" }
intervaltree = { index = "kalepi" }

[[tool.uv.index]]
name = "kalepi"
url = "https://kalepi.kalepa.com/pypi/kalepa/packages/simple/"
# explicit = true
authenticate = "always"

[[tool.uv.index]]
name = "pypi"
url = "https://pypi.org/simple/"


[tool.uv]
default-groups = ["dev", "kalepa", "ecs"]
package = false

[tool.black]
line-length = 120
preview = true
target-version = ['py311']

[tool.isort]
profile = "black"
skip = ["__init__.py"]
from_first = true

[tool.ruff]
select = ["E", "F", "W", "PLC", "PLE", "PLW", "FLY", "RUF"]
extend-ignore = ["E722", "RUF009", "PLW0603", "E711", "E712"]
line-length = 120
target-version = "py311"
extend-exclude = [
    "tests/",
    "scripts/",
    "migrations/",
    "features/",
    "test_utils/",
    "bin/",
    "**/__init__.py",
]

[tool.mypy]
plugins = "sqlmypy"
python_version = "3.11"
strict = true
show_error_codes = true
disallow_untyped_calls = false
disallow_any_generics = false
warn_return_any = false
ignore_missing_imports = true
disable_error_code = "abstract"
exclude = [
    '^test_utils/',
    '^tests/',
    '^scripts/',
    '^migrations/',
    '^features/',
    '^bin/',
    '.*__init__.py',
]
