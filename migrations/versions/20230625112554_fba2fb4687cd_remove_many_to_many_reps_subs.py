"""Remove many-to-many relationship between reports and submissions

Revision ID: fba2fb4687cd
Revises: 1d1a690bfca5
Create Date: 2023-06-25 11:25:54.679664+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fba2fb4687cd"
down_revision = "1d1a690bfca5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("report_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.create_foreign_key(None, "submissions", "reports_v2", ["report_id"], ["id"])
    op.create_index(op.f("ix_submissions_report_id"), "submissions", ["report_id"], unique=False)

    # Backfill submissions.report_id with (cleaned up to unique by /bin script) submissions_reports.report_id
    conn = op.get_bind()
    op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
    conn.execute("""
        UPDATE submissions
        SET report_id = sr.report_id
        FROM submissions_reports sr
        WHERE submissions.id = sr.submission_id;
    """)

    # Update existing triggers
    conn.execute("""
CREATE OR REPLACE FUNCTION submission_history_insert_report_permission() RETURNS TRIGGER AS $$
    DECLARE
        s_id UUID;
        is_owner_perm bool;
        history_rows SMALLINT;
BEGIN
    SELECT id into s_id FROM submissions WHERE report_id = NEW.report_id;

    SELECT COUNT(*)
    INTO history_rows
    FROM submission_history sh
    WHERE sh.submission_action_type = 'SHARED' AND sh.submission_id = s_id;

    SELECT CASE WHEN (owner_id = NEW.grantee_user_id)
        THEN TRUE
        ELSE FALSE END
        INTO is_owner_perm
        FROM reports_v2
        WHERE id = NEW.report_id;


    IF history_rows = 0 AND NOT is_owner_perm THEN
            INSERT INTO submission_history (id, submission_id, submission_action_type, occurred_at, parent_type, parent_id)
            VALUES (uuid_generate_v4(), s_id, 'SHARED', now(), 'REPORT', NEW.report_id);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';
    
    
CREATE OR REPLACE FUNCTION submission_history_insert_report_org_permission() RETURNS TRIGGER AS $$
    DECLARE
        s_id UUID;
        history_rows SMALLINT;
BEGIN
    SELECT id into s_id FROM submissions WHERE report_id = NEW.id;

    SELECT COUNT(*)
    INTO history_rows
    FROM submission_history sh
    WHERE sh.submission_action_type = 'SHARED' AND sh.submission_id = s_id;

    IF history_rows = 0 THEN
            INSERT INTO submission_history (id, submission_id, submission_action_type, occurred_at, parent_type, parent_id)
            VALUES (uuid_generate_v4(), s_id, 'SHARED', now(), 'REPORT', NEW.id);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';


CREATE OR REPLACE FUNCTION submission_history_insert_report_subm_org_permission() RETURNS TRIGGER AS $$
    DECLARE
        org_perm_level permissiontype;
        history_rows SMALLINT;
BEGIN
    SELECT organization_permission_level
    INTO org_perm_level
    FROM reports_v2
    WHERE id = NEW.report_id;

    IF org_perm_level IS NULL THEN
        RETURN NEW;
    END IF;

    SELECT COUNT(*)
    INTO history_rows
    FROM submission_history sh
    WHERE sh.submission_action_type = 'SHARED' AND sh.submission_id = new.id;

    IF history_rows = 0 THEN
            INSERT INTO submission_history (id, submission_id, submission_action_type, occurred_at, parent_type, parent_id)
            VALUES (uuid_generate_v4(), NEW.id, 'SHARED', now(), 'REPORT', NEW.report_id);
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

DROP TRIGGER IF EXISTS submission_history_insert_report_subm_org_permission_trigger ON submissions_reports;
CREATE TRIGGER submission_history_insert_report_subm_org_permission_trigger
AFTER INSERT ON submissions
FOR EACH ROW
EXECUTE PROCEDURE submission_history_insert_report_subm_org_permission();
        """)

    # Create triggers to dual-write to "submissions_reports" table
    conn.execute("""
CREATE OR REPLACE FUNCTION submissions_insert_trigger() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.report_id IS NOT NULL THEN
        INSERT INTO submissions_reports (id, submission_id, report_id)
        VALUES (uuid_generate_v4(), NEW.id, NEW.report_id);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE TRIGGER submissions_after_insert
AFTER INSERT ON submissions
FOR EACH ROW EXECUTE FUNCTION submissions_insert_trigger();

CREATE OR REPLACE FUNCTION submissions_update_trigger() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.report_id IS NULL THEN
        DELETE FROM submissions_reports WHERE submission_id = NEW.id;
    ELSIF NEW.report_id != OLD.report_id THEN
        UPDATE submissions_reports
        SET report_id = NEW.report_id
        WHERE submission_id = NEW.id;

        IF NOT FOUND THEN
            INSERT INTO submissions_reports (id, submission_id, report_id)
            VALUES (uuid_generate_v4(), NEW.id, NEW.report_id);
        END IF;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE TRIGGER submissions_after_update
AFTER UPDATE ON submissions
FOR EACH ROW EXECUTE FUNCTION submissions_update_trigger();
        """)


def downgrade():
    pass
