"""add models for classifiers v2

Revision ID: a53cd7e73f0f
Revises: 003022bfce70
Create Date: 2025-04-07 10:14:31.123456

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'a53cd7e73f0f'
down_revision = '003022bfce70'
branch_labels = None
depends_on = None


def upgrade():
    # Create enums if they don't exist
        
    # Check if extractiontype exists
    try:
        op.execute("CREATE TYPE extractiontype AS ENUM ('PHRASES_WITH_LLM', 'PHRASES', 'LLM')")
    except:
        # If it fails, the enum likely already exists
        pass
    
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('customizable_classifiers_v2',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('fact_subtype_id', sa.String(), nullable=True),
        sa.Column('extracted_value_name', sa.String(), nullable=True),
        sa.Column('output_type', postgresql.ENUM('BOOLEAN', 'NUMBER', 'CURRENCY', 'TEXT', 'DATE', name='classifieroutputtype', create_type=False), nullable=True),
        sa.Column('output_unit', postgresql.ENUM('USD', 'PERCENTAGE', 'USD_PER_UNIT', 'UNIT', name='classifierunits', create_type=False), nullable=True),
        sa.Column('input_types', postgresql.ARRAY(sa.String()), nullable=False),
        sa.Column('organization_ids', postgresql.ARRAY(sa.Integer()), server_default='{}', nullable=False),
        sa.Column('run_in_pds', sa.Boolean(), nullable=False),
        sa.Column('is_internal', sa.Boolean(), nullable=False),
        sa.CheckConstraint('(fact_subtype_id IS NULL) != (extracted_value_name IS NULL)', name='ck_classifier_v2_xor'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('fact_subtype_id', 'extracted_value_name', 'output_unit', 'output_type', name='uq_classifier_v2_identifier')
    )
    
    op.create_table('classifier_versions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('comment', sa.String(), nullable=True),
        sa.Column('classifier_description', sa.String(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('classifier_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['classifier_id'], ['customizable_classifiers_v2.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    with op.batch_alter_table('classifier_versions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_classifier_versions_classifier_id'), ['classifier_id'], unique=False)
        batch_op.create_index('ix_unique_active_version_per_classifier', ['classifier_id'], unique=True, postgresql_where=sa.text('is_active IS TRUE'))
    
    op.create_table('classifier_config',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('input_type', sa.String(), nullable=False),
        sa.Column('classifier_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['classifier_id'], ['customizable_classifiers_v2.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    with op.batch_alter_table('classifier_config', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_classifier_config_classifier_id'), ['classifier_id'], unique=False)
    
    op.create_table('classifier_config_versions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('classifier_config_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('input_processing_type', sa.String(), nullable=False),
        sa.Column('extraction_type', postgresql.ENUM('PHRASES_WITH_LLM', 'PHRASES', 'LLM', name='extractiontype', create_type=False), nullable=False),
        sa.Column('is_autogenerated', sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(['classifier_config_id'], ['classifier_config.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    with op.batch_alter_table('classifier_config_versions', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_classifier_config_versions_classifier_config_id'), ['classifier_config_id'], unique=False)
    
    op.create_table('classifier_phrases',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('classifier_config_version_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('phrase', sa.String(), nullable=False),
        sa.Column('weight', sa.Float(), nullable=False),
        sa.Column('excludes', postgresql.ARRAY(sa.String()), server_default='{}', nullable=False),
        sa.CheckConstraint('weight >= 0 AND weight <= 1', name='ck_weight_range'),
        sa.ForeignKeyConstraint(['classifier_config_version_id'], ['classifier_config_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    with op.batch_alter_table('classifier_phrases', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_classifier_phrases_classifier_config_version_id'), ['classifier_config_version_id'], unique=False)
    
    op.create_table('classifier_to_config_version',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('classifier_version_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('classifier_config_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('classifier_config_version_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['classifier_config_id'], ['classifier_config.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['classifier_config_version_id'], ['classifier_config_versions.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['classifier_version_id'], ['classifier_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('classifier_version_id', 'classifier_config_id', 'classifier_config_version_id', name='uq_classifier_to_config_version')
    )
    
    with op.batch_alter_table('classifier_to_config_version', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_classifier_to_config_version_classifier_config_id'), ['classifier_config_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_classifier_to_config_version_classifier_config_version_id'), ['classifier_config_version_id'], unique=False)
        batch_op.create_index(batch_op.f('ix_classifier_to_config_version_classifier_version_id'), ['classifier_version_id'], unique=False)
    
    op.create_table('llm_config_versions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('llm_model', sa.String(), nullable=False),
        sa.Column('prompt', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(['id'], ['classifier_config_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table('phrases_config_versions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(['id'], ['classifier_config_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table('phrases_with_llm_config_versions',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('llm_model', sa.String(), nullable=False),
        sa.Column('prompt', sa.String(), nullable=False),
        sa.ForeignKeyConstraint(['id'], ['classifier_config_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('phrases_with_llm_config_versions')
    op.drop_table('phrases_config_versions')
    op.drop_table('llm_config_versions')
    
    with op.batch_alter_table('classifier_to_config_version', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_classifier_to_config_version_classifier_version_id'))
        batch_op.drop_index(batch_op.f('ix_classifier_to_config_version_classifier_config_version_id'))
        batch_op.drop_index(batch_op.f('ix_classifier_to_config_version_classifier_config_id'))

    op.drop_table('classifier_to_config_version')
    
    with op.batch_alter_table('classifier_phrases', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_classifier_phrases_classifier_config_version_id'))

    op.drop_table('classifier_phrases')
    
    with op.batch_alter_table('classifier_config_versions', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_classifier_config_versions_classifier_config_id'))

    op.drop_table('classifier_config_versions')
    
    with op.batch_alter_table('classifier_config', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_classifier_config_classifier_id'))

    op.drop_table('classifier_config')
    
    with op.batch_alter_table('classifier_versions', schema=None) as batch_op:
        batch_op.drop_index('ix_unique_active_version_per_classifier', postgresql_where=sa.text('is_active IS TRUE'))
        batch_op.drop_index(batch_op.f('ix_classifier_versions_classifier_id'))

    op.drop_table('classifier_versions')
    op.drop_table('customizable_classifiers_v2')
    # ### end Alembic commands ###
