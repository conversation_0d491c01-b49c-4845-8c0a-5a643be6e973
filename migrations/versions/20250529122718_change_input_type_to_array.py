"""change input_type to array in classifier_config

Revision ID: 80b599e26b7e
Revises: c475b6cad463
Create Date: 2025-05-29 12:27:18.000000

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "80b599e26b7e"
down_revision = "c475b6cad463"
branch_labels = None
depends_on = None


def upgrade():
    # clear all existing classifiers as they might be in incorrect state
    op.execute(
        """
        TRUNCATE TABLE classifier_version_task_definition CASCADE;
        TRUNCATE TABLE classifier_version_task_dataset CASCADE;
        TRUNCATE TABLE classifier_phrases CASCADE;
        TRUNCATE TABLE classifier_to_config_version CASCADE;
        TRUNCATE TABLE classifier_config_versions CASCADE;
        TRUNCATE TABLE classifier_versions CASCADE;
        TRUNCATE TABLE customizable_classifiers_v2 CASCADE;
    """
    )
    # First, add the new column as nullable
    op.add_column("classifier_config", sa.Column("input_types", postgresql.ARRAY(sa.String()), nullable=True))

    # Migrate existing data: convert single input_type to array
    op.execute(
        """
        UPDATE classifier_config 
        SET input_types = ARRAY[input_type] 
        WHERE input_type IS NOT NULL
    """
    )

    # Make the new column non-nullable and set default
    op.alter_column("classifier_config", "input_types", nullable=False, server_default="{}")

    op.alter_column("classifier_config", "input_type", nullable=True)


def downgrade():
    # Add back the old column
    op.add_column("classifier_config", sa.Column("input_type", sa.String(), nullable=True))

    # Migrate data back: take first element from array
    op.execute(
        """
        UPDATE classifier_config 
        SET input_type = input_types[1] 
        WHERE input_types IS NOT NULL AND array_length(input_types, 1) > 0
    """
    )

    # Make the old column non-nullable
    op.alter_column("classifier_config", "input_type", nullable=False)
