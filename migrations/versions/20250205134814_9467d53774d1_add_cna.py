"""Add CNA

Revision ID: 9467d53774d1
Revises: 781763dd657b
Create Date: 2025-02-05 13:48:14.217339+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '9467d53774d1'
down_revision = '781763dd657b'
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
                    insert into organization values
                    (63, 'CNA', null, null, INTERVAL '90 day', 'cna.com', null, null, true)
                """)
    op.execute("""
                INSERT INTO settings (
                    id, created_at, updated_at, organization_id, is_map_enabled_by_default, support_email, email_domains, default_tier, max_tier_for_auto_processing, show_coverage_filter, allow_multiple_assigned_underwriters_per_submission
                )
                values (
                    uuid_generate_v4(), now(), null, 63, false, '<EMAIL>', '{cna.com, kalepa.co, kalepa.com}', 1, 0, true, false
                )
                """)
    op.execute("""
                    insert into users values
                    (default, '<EMAIL>', null, 'auth0|67a37ae45f38843d3dae44dc', 63, 'manager', '<EMAIL>', null, null, now(), null, false, null, false, null, true, null, null, null, null, true)
                """)
    conn = op.get_bind()
    conn.execute("insert into sensible_quota values (uuid_generate_v4(), 63, 10000)")
    conn.execute("UPDATE settings SET loss_runs_enabled = true WHERE organization_id = 63;")
    conn.execute("""
                                INSERT INTO coverages (id, name, display_name, organization_id, coverage_types, is_disabled)
                                VALUES
                                    (uuid_generate_v4(), 'liability', 'Liability', 63, '{PRIMARY, EXCESS}', false),
                                    (uuid_generate_v4(), 'property', 'Commercial Property', 63, '{PRIMARY, EXCESS}', false),
                                    (uuid_generate_v4(), 'professionalLiability', 'Professional Liability', 63, '{PRIMARY, EXCESS}', false),
                                    (uuid_generate_v4(), 'fiduciaryLiability', 'Directors & Officers (D&O)', 63, '{PRIMARY, EXCESS}', false),
                                    (uuid_generate_v4(), 'employmentPracticesLiability', 'Employment Practices Liability', 63, '{PRIMARY, EXCESS}', false),
                                    (uuid_generate_v4(), 'liquorLiability', 'Liquor Liability', 63, '{PRIMARY, EXCESS}', false),
                                    (uuid_generate_v4(), 'businessAuto', 'Business Auto', 63, '{PRIMARY, EXCESS}', false),
                                    (uuid_generate_v4(), 'businessOwners', 'Business Owner''s Policy (BOP)', 63, '{PRIMARY, EXCESS}', false)
                                """)


def downgrade():
    pass
