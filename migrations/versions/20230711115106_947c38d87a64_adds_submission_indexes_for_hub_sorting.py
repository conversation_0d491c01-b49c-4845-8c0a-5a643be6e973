from alembic import op

"""Adds submission indexes for hub sorting

Revision ID: 947c38d87a64
Revises: a524156cbec7
Create Date: 2023-07-11 11:51:06.360183+00:00

"""
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "947c38d87a64"
down_revision = "a524156cbec7"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.create_index(
            op.f("ix_submissions_expired_premium"),
            "submissions",
            ["expired_premium"],
            unique=False,
            postgresql_concurrently=True,
        )
        op.create_index(
            op.f("ix_submissions_target_premium"),
            "submissions",
            ["target_premium"],
            unique=False,
            postgresql_concurrently=True,
        )
        op.create_index(
            op.f("ix_submissions_sales"), "submissions", ["sales"], unique=False, postgresql_concurrently=True
        )


def downgrade():
    op.drop_index(op.f("ix_submissions_target_premium"), table_name="submissions")
    op.drop_index(op.f("ix_submissions_sales"), table_name="submissions")
    op.drop_index(op.f("ix_submissions_expired_premium"), table_name="submissions")
