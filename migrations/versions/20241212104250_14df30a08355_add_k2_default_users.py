"""Add K2 default users

Revision ID: 14df30a08355
Revises: 7e698bf25273
Create Date: 2024-12-12 10:42:50.646311+00:00

"""
import os

from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "14df30a08355"
down_revision = "7e698bf25273"
branch_labels = None
depends_on = None


def upgrade():
    external_ids_by_env = {
        "dev": {
            "<EMAIL>": "auth0|675aaf1f9f7e159881e5ce49",
            "<EMAIL>": "auth0|675aaf6bf29f2d72fdb2b94",
        },
        "stage": {
            "<EMAIL>": "auth0|675aaf90ad66e13e613c2e55",
            "<EMAIL>": "auth0|675aafb3e0200b39ddaf13ea",
        },
        "prod": {
            "<EMAIL>": "auth0|675aafdcc6fcf563ed6bc62b",
            "<EMAIL>": "auth0|675aaff93c7b802ffac789ae",
        },
    }
    env = os.environ.get("KALEPA_ENV", "dev")

    for k2_user_email in ["<EMAIL>", "<EMAIL>"]:
        user_name = k2_user_email.split("@")[0]
        op.execute(f"""
            insert into users values
            (default, '{k2_user_email}', null, '{external_ids_by_env[env][k2_user_email]}', 61, 'manager', '{user_name}', null, null, now(), null, false, null, false, null, true, null, null, null, null, true)
    """)

    conn = op.get_bind()
    aegis_uw_id = conn.execute("SELECT id FROM users WHERE email = '<EMAIL>'")
    aegis_uw_id = aegis_uw_id.scalar()
    vikco_uw_id = conn.execute("SELECT id FROM users WHERE email = '<EMAIL>'")
    vikco_uw_id = vikco_uw_id.scalar()

    aegis_group_id = conn.execute(sa.text("SELECT id from user_group where name ilike '%aegis%' and organization_id = 61"))
    aegis_group_id = aegis_group_id.first()
    vikco_group_id = conn.execute(sa.text("SELECT id from user_group where name ilike '%vikco%' and organization_id = 61"))
    vikco_group_id = vikco_group_id.first()

    if aegis_group_id:
        conn.execute(f"""
            INSERT INTO user_to_group VALUES
            ('{aegis_uw_id}', '{aegis_group_id[0]}', now(), null)
        """)

    if vikco_group_id:
        conn.execute(f"""
            INSERT INTO user_to_group VALUES
            ('{vikco_uw_id}', '{vikco_group_id[0]}', now(), null)
        """)

def downgrade():
    pass
