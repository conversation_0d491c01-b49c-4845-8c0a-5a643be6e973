"""Add cleared at for submission

Revision ID: dcca42adab0a
Revises: ab5fa33d12cf
Create Date: 2025-05-05 06:09:58.902400+00:00

"""
from alembic import op
import sqlalchemy as sa

from migrations.utils import safe_migration_pattern

# revision identifiers, used by Alembic.
revision = 'dcca42adab0a'
down_revision = 'ab5fa33d12cf'
branch_labels = None
depends_on = None


def upgrade():
    def add_cleared_date():
        op.add_column("submissions", sa.Column("cleared_date", sa.DateTime(), nullable=True))

    safe_migration_pattern("submissions", add_cleared_date)


def downgrade():
    def remove_cleared_date():
        op.drop_column("submissions", "cleared_date")

    safe_migration_pattern("submissions", remove_cleared_date)
