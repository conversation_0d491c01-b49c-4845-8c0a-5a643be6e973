"""Add Coverages to Paragon

Revision ID: 1df30bb104c0
Revises: a02f485411d6
Create Date: 2025-05-28 08:35:02.925533+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '1df30bb104c0'
down_revision = 'a02f485411d6'
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        INSERT INTO coverages (id, name, display_name, is_disabled, organization_id, coverage_types)
        VALUES (
            uuid_generate_v4(),
            'equipmentBreakdown',
            'Equipment Breakdown',
            false,
            37,
            '{PRIMARY}'
        )
        """
    )

    op.execute(
        """
        INSERT INTO coverages (id, name, display_name, is_disabled, organization_id, coverage_types)
        VALUES (
            uuid_generate_v4(),
            'package',
            'Package',
            false,
            37,
            '{PRIMARY, EXCESS}'
        )
        """
    )


def downgrade():
    pass
