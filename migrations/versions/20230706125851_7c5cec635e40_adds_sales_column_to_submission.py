"""Adds sales column to submission

Revision ID: 7c5cec635e40
Revises: e16bd7c2a5f6
Create Date: 2023-07-06 12:58:51.708438+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "7c5cec635e40"
down_revision = "e16bd7c2a5f6"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("submissions", sa.Column("sales", sa.Float(), nullable=True))


def downgrade():
    op.drop_column("submissions", "sales")
