"""Align arch allowed stage updates

Revision ID: 6b6fdb063814
Revises: 57a2c16eab97
Create Date: 2024-04-17 15:36:38.212116+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "6b6fdb063814"
down_revision = "57a2c16eab97"
branch_labels = None
depends_on = None


NOT_ALLOWED_STAGE_TRANSITIONS = """
[
    {
        "to_stages": [
          "ON_MY_PLATE"
        ],
        "from_stages": [
          "INDICATED",
          "WAITING_FOR_OTHERS",
          "EXPIRED"
        ]
    }
]
"""


def upgrade():
    op.execute(f"""
            update sync_configuration
            set configuration = jsonb_set(configuration, '{{handlers,1,config,not_allowed_stage_transitions}}',
                                          to_jsonb('{NOT_ALLOWED_STAGE_TRANSITIONS}'::jsonb))
            where organization_id = 10;
        """)


def downgrade():
    op.execute("""
            update sync_configuration
            set configuration = jsonb_delete_path(configuration, '{handlers,1,config,not_allowed_stage_transitions}')
            where organization_id = 10;
            """)
