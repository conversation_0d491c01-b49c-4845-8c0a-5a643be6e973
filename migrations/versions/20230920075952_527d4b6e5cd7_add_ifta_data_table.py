"""add ifta data table

Revision ID: 527d4b6e5cd7
Revises: 3826508e71c8
Create Date: 2023-09-20 07:59:52.193286+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
from sqlalchemy.dialects import postgresql
from static_common.enums.states import CanadaProvincesEnum, USStatesEnum
import sqlalchemy as sa

revision = "527d4b6e5cd7"
down_revision = "3826508e71c8"
branch_labels = None
depends_on = None


def upgrade():
    valid_jurisdictions = [p.name for p in CanadaProvincesEnum] + [s.name for s in USStatesEnum]
    jurisdiction_constraint = f"jurisdiction IN {tuple(valid_jurisdictions)}"

    op.create_table(
        "ifta_data",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("jurisdiction", sa.String(), nullable=False, index=True),
        sa.Column("date", sa.DateTime(timezone=True), nullable=False),
        sa.Column("total_miles", sa.Integer(), nullable=True),
        sa.Column("taxable_gallons", sa.Integer(), nullable=True),
        sa.Column("file_id", postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.CheckConstraint(jurisdiction_constraint),
        sa.ForeignKeyConstraint(["file_id"], ["files.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"]),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade():
    op.drop_table("ifta_data")
