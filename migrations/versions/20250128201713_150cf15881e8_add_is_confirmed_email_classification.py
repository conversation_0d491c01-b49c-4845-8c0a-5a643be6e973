"""add is_confirmed email classification

Revision ID: 150cf15881e8
Revises: 562102a74a81
Create Date: 2025-01-28 20:17:13.376954+00:00

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "150cf15881e8"
down_revision = "562102a74a81"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("email_classifications", sa.Column("is_confirmed", sa.<PERSON>(), nullable=False, server_default="true"))

def downgrade():
    op.drop_column("email_classifications", "is_confirmed")
