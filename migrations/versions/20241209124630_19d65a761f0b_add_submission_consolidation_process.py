"""Add submission_consolidation_process

Revision ID: 19d65a761f0b
Revises: bab65a761f0b
Create Date: 2024-12-09 12:46:30.293426+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "19d65a761f0b"
down_revision = "bab65a761f0b"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "submission_consolidation_process",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column(
            "status",
            sa.Enum("PENDING", "COMPLETED", "CANCELLED", "NOT_READY", name="submissionconsolidationstatus"),
            nullable=False,
        ),
        sa.Column("consolidated_files_hash", sa.BigInteger(), nullable=False),
        sa.Column("is_description_and_naics_consolidated", sa.Boolean(), nullable=True),
        sa.Column("is_em_and_acord_files_consolidated", sa.Boolean(), nullable=True),
        sa.Column("is_financial_statements_consolidated", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(["submission_id"], ["submissions.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )

    op.create_index(
        op.f("ix_submission_consolidation_process_submission_id"),
        "submission_consolidation_process",
        ["submission_id"],
        unique=False,
    )

    conn = op.get_bind()
    conn.execute(
        """
            CREATE FUNCTION check_pending_submission_consolidation_process() RETURNS trigger AS $check_pending_submission_consolidation_process$
                DECLARE
                    pending_process boolean;                            
                BEGIN
                    IF NEW.status = 'PENDING' THEN
                        pending_process := (select exists(select 1 from submission_consolidation_process 
                        where submission_id = NEW.submission_id and status = 'PENDING' and id != NEW.id));
                        IF pending_process THEN
                            RAISE EXCEPTION 'There is already a pending consolidation process for this submission';
                        END IF;
                    END IF;                            
                    RETURN NEW;
                END;
            $check_pending_submission_consolidation_process$ LANGUAGE plpgsql;
            CREATE TRIGGER submission_consolidation_process_check BEFORE INSERT OR UPDATE ON submission_consolidation_process FOR EACH ROW EXECUTE PROCEDURE check_pending_submission_consolidation_process();
        """
    )


def downgrade():
    pass
