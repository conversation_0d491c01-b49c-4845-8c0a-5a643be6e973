"""remove aliases

Revision ID: edba5c26e124
Revises: c1aa7427ea44
Create Date: 2024-04-25 19:12:55.627364+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "edba5c26e124"
down_revision = "c1aa7427ea44"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '3600 s';")
        op.execute("UPDATE brokerage_employees SET aliases = '{}';")


def downgrade():
    pass
