"""aru custom stage

Revision ID: 7a61a8efd229
Revises: a8ce0f0b3b7a
Create Date: 2024-12-18 15:50:45.884890+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '7a61a8efd229'
down_revision = 'a8ce0f0b3b7a'
branch_labels = None
depends_on = None


def upgrade():
       op.execute("""

INSERT INTO client_submission_stage_config (organization_id, client_stage, tags, tag_labels, copilot_stage, with_comment, "default")
VALUES (59, 'Indicated', '{}', '{}', 'INDICATED', false, true),
         (59, 'In Progress', '{}', '{}', 'ON_MY_PLATE', false, true),
         (59, 'Rating', '{}', '{}', 'INDICATED', false, false),
         (59, 'Awaiting Reply', '{}', '{}', 'WAITING_FOR_OTHERS', false, true),
         (59, 'Quoted', '{}', '{}', 'QUOTED', false, true),
         (59, 'Declined', '{}', '{}', 'DECLINED', false, true),
         (59, 'Lost', '{}', '{}', 'QUOTED_LOST', false, true),
         (59, 'Bound', '{}', '{}', 'QUOTED_BOUND', false, true),
         (59, 'Expired', '{}', '{}', 'EXPIRED', false, true),
         (59, 'Completed', '{}', '{}', 'COMPLETED', false, true),
         (59, 'Canceled', '{}', '{}', 'CANCELED', false, true);
                  
--- MIGRATE OLD ONES                  
WITH owner  AS (
    select id from users where email = '<EMAIL>'
),
to_fill AS (
    select
        id,
        copilot_stage

    from client_submission_stage_config
    where organization_id = 59
    and "default" = true
)
UPDATE submissions
SET client_stage_id = to_fill.id
FROM to_fill
WHERE submissions.stage = to_fill.copilot_stage
AND submissions.owner_id = (select id from owner)
AND submissions.client_stage_id IS NULL
AND is_deleted = false;                  
    """)
       
def downgrade():
       pass
