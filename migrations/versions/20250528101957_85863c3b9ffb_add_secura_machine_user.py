"""Add Secura machine user

Revision ID: 85863c3b9ffb
Revises: 1df30bb104c0
Create Date: 2025-05-28 10:19:57.113811+00:00

"""
import os

from alembic import op
from static_common.enums.organization import ExistingOrganizations
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '85863c3b9ffb'
down_revision = '1df30bb104c0'
branch_labels = None
depends_on = None


def upgrade():
    external_id_by_env = {
        "dev": "H4Yy0FB4iKUatwy0HnXuYb6FwPPqp415",
        "stage": "U6ukYmzUn1gWLKazBBHK7hIQolejfwDx",
        "prod": "A1PN29WUv4Oegx2HXTrtZJkvKKkbx1c9"
    }
    external_id = external_id_by_env[os.environ.get("KALEPA_ENV", "dev")]

    user_email = "<EMAIL>"
    organization_id = ExistingOrganizations.SECURA.value
    user_name = "SECURA"

    # Check if organization_id exists = just for this migration (IT tests erase DB)
    conn = op.get_bind()
    result = conn.execute(
        f"""
        SELECT id FROM organization WHERE id = {organization_id};
        """
    ).fetchone()

    if result is None:
        # Organization ID doesn't exist in IT tests
        return

    op.execute(f"""
        insert into users values
        (
            default, 
            '{user_email}', 
            null, 
            '{external_id}', 
            {organization_id}, 
            'manager', 
            '{user_name}', 
            null, 
            null, 
            now(), 
            null, 
            false, 
            null, 
            false, 
            null, 
            true, 
            null, 
            null, 
            null, 
            null, 
            false
        )                    
    """)


def downgrade():
    pass
