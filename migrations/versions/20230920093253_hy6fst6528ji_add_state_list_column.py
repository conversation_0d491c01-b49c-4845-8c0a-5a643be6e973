"""Brokerage domains

Revision ID: hy6fst6528ji
Revises: 527d4b6e5cd7
Create Date: 2023-09-20 09:23:14.749655+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "hy6fst6528ji"
down_revision = "527d4b6e5cd7"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("workers_comp_experience", sa.Column("state_list", postgresql.ARRAY(sa.String()), nullable=True))


def downgrade():
    op.drop_column("workers_comp_experience", "state_list")
