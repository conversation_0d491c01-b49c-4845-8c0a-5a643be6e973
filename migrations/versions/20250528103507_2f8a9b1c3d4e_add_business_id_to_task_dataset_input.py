"""Add business_id to task_dataset_input

Revision ID: 2f8a9b1c3d4e
Revises: 1df30bb104c0
Create Date: 2025-05-28 10:35:07.000000

"""
from alembic import op
from sqlalchemy.dialects.postgresql import UUID
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2f8a9b1c3d4e"
down_revision = "1df30bb104c0"
branch_labels = None
depends_on = None


def upgrade():
    # Add business_id column to task_dataset_input table
    op.add_column("task_dataset_input", sa.Column("business_id", UUID(as_uuid=True), nullable=True))

    # Create index for the new column for better query performance
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '600 s';")  # 10 min
        op.execute("CREATE INDEX CONCURRENTLY ix_task_dataset_input_business_id ON task_dataset_input (business_id)")


def downgrade():
    # Remove index first
    with op.get_context().autocommit_block():
        op.execute("DROP INDEX CONCURRENTLY IF EXISTS ix_task_dataset_input_business_id")

    # Remove business_id column from task_dataset_input table
    op.drop_column("task_dataset_input", "business_id") 
