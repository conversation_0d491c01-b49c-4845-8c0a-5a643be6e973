"""Adds PDS file reprocessing requested event

Revision ID: a9b7b7ee43ca
Revises: 3917b7ee43ca
Create Date: 2023-12-04 12:50:40.645180+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a9b7b7ee43ca"
down_revision = "3917b7ee43ca"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_FILE_REPROCESSING_REQUESTED';")
        op.execute(f"ALTER TYPE fileprocessingstate ADD VALUE IF NOT EXISTS 'CLASSIFICATION_FAILED';")


def downgrade():
    pass
