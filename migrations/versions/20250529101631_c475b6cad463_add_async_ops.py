"""Add async_operations table

Revision ID: c475b6cad463
Revises: 2f8a9b1c3d4e
Create Date: 2025-05-29 10:16:31.293426+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c475b6cad463"
down_revision = "2f8a9b1c3d4e"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "async_operations",
        sa.Column("id", postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text("gen_random_uuid()")),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.func.now()),
        sa.Column("updated_at", sa.DateTime(timezone=True), onupdate=sa.func.now()),

        sa.Column("organization_id", sa.<PERSON>(), sa.<PERSON>("organization.id", ondelete="SET NULL"), nullable=True),
        sa.Column("report_id", postgresql.UUID(as_uuid=True), sa.ForeignKey("reports_v2.id", ondelete="SET NULL"), nullable=True),
        sa.Column("submission_id", postgresql.UUID(as_uuid=True), sa.ForeignKey("submissions.id", ondelete="SET NULL"), nullable=True),

        sa.Column("status", sa.String(), nullable=False),
        sa.Column("operation", sa.String(), nullable=False),

        sa.Column("error_details", postgresql.JSONB(none_as_null=True, astext_type=sa.Text()), nullable=True),
        sa.Column("additional_data", postgresql.JSONB(none_as_null=True, astext_type=sa.Text()), nullable=True),
        sa.Column("logical_identifier", sa.String(), nullable=True),
        sa.Column("attempts", sa.Integer(), nullable=False),
        sa.Column("executing_user_email", sa.String(), nullable=False),
    )

    # Indices
    op.create_index("ix_async_operations_organization_id", "async_operations", ["organization_id"])
    op.create_index("ix_async_operations_report_id", "async_operations", ["report_id"])
    op.create_index("ix_async_operations_submission_id", "async_operations", ["submission_id"])
    op.create_index("ix_async_operations_operation", "async_operations", ["operation"])
    op.create_index("ix_async_operations_created_at", "async_operations", ["created_at"])
    op.create_index("ix_async_operations_logical_identifier", "async_operations", ["logical_identifier"])

def downgrade():
    pass
