"""add processd_files table

Revision ID: jp2c21370752
Revises: 2423d50b9a4b
Create Date: 2022-11-24 16:47:21.611485+00:00

"""
from alembic import op

# revision identifiers, used by Alembic.
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

revision = "jp2c21370752"
down_revision = "2423d50b9a4b"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "processed_files",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("file_id", postgresql.UUID(as_uuid=True), nullable=False, index=True),
        sa.Column("processed_data", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.ForeignKeyConstraint(["file_id"], ["files.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade():
    op.drop_table("processed_files")
