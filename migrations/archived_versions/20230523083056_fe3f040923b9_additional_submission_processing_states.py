"""Additional submission processing states

Revision ID: fe3f040923b9
Revises: 611d3ebea523
Create Date: 2023-05-23 08:30:56.004144+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "fe3f040923b9"
down_revision = "611d3ebea523"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionprocessingstate ADD VALUE IF NOT EXISTS 'PROCESSING_FAILED'")
        op.execute("ALTER TYPE submissionprocessingstate ADD VALUE IF NOT EXISTS 'NO_FILES_WITH_ENTITY_DATA'")
        op.execute("ALTER TYPE submissionprocessingstate ADD VALUE IF NOT EXISTS 'CANCELLED'")


def downgrade():
    pass
