"""adds admin report_permission type

Revision ID: 67664144751b
Revises: f2513c28572b
Create Date: 2020-09-29 21:41:16.801701+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "67664144751b"
down_revision = "f2513c28572b"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("""ALTER TYPE permissiontype ADD VALUE IF NOT EXISTS 'ADMIN';""")


def downgrade():
    pass
