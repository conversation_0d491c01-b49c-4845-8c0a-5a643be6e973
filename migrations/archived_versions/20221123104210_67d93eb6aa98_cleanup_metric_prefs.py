"""Cleanup metric prefs

Revision ID: 67d93eb6aa98
Revises: 1e4cef127aed
Create Date: 2022-11-23 10:42:10.930449+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "67d93eb6aa98"
down_revision = "1e4cef127aed"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("""
        delete from metric_preference where report_id is null;
    """)
    # alter table trick for faster constrain check
    # https://medium.com/doctolib/adding-a-not-null-constraint-on-pg-faster-with-minimal-locking-38b2c00c4d1c
    op.execute("""
        ALTER TABLE metric_preference ADD CONSTRAINT report_id_is_not_null CHECK (report_id IS NOT NULL) NOT VALID;
    """)
    op.execute("""ALTER TABLE metric_preference VALIDATE CONSTRAINT report_id_is_not_null;""")


def downgrade():
    pass
