"""Moves forms workflow to v3

Revision ID: 82678baa6832
Revises: 2fb0fa53cd54
Create Date: 2020-09-01 12:18:54.778834

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "82678baa6832"
down_revision = "6d7b3a49a2ad"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "audit_trails",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=True,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.add_column("submission_businesses", sa.Column("requested_legal_name", sa.String(), nullable=True))
    op.add_column("uploaded_forms", sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.alter_column(
        "uploaded_forms",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=True,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.create_foreign_key(None, "uploaded_forms", "reports", ["report_id"], ["id"], ondelete="CASCADE")
    op.create_foreign_key(None, "uploaded_forms", "submissions", ["submission_id"], ["id"], ondelete="CASCADE")
    op.add_column("user_fields_review", sa.Column("submission_id", postgresql.UUID(as_uuid=True), nullable=True))
    op.alter_column(
        "user_fields_review",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=True,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.alter_column("user_fields_review", "email", existing_type=sa.VARCHAR(length=200), nullable=True)
    op.alter_column("user_fields_review", "report_id", existing_type=sa.INTEGER(), nullable=True)
    op.create_index(op.f("ix_user_fields_review_report_id"), "user_fields_review", ["report_id"], unique=False)
    op.create_index(op.f("ix_user_fields_review_submission_id"), "user_fields_review", ["submission_id"], unique=False)
    op.create_foreign_key(None, "user_fields_review", "reports", ["report_id"], ["id"], ondelete="CASCADE")
    op.create_foreign_key(None, "user_fields_review", "submissions", ["submission_id"], ["id"], ondelete="CASCADE")


def downgrade():
    op.alter_column("user_fields_review", "report_id", existing_type=sa.INTEGER(), nullable=False)
    op.alter_column("user_fields_review", "email", existing_type=sa.VARCHAR(length=200), nullable=False)
    op.alter_column(
        "user_fields_review",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.drop_column("user_fields_review", "submission_id")
    op.alter_column("uploaded_forms", "ocr_engine", existing_type=sa.VARCHAR(length=50), nullable=True)
    op.alter_column(
        "uploaded_forms",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.drop_column("uploaded_forms", "submission_id")
    op.drop_column("submission_businesses", "requested_legal_name")
    op.alter_column(
        "audit_trails",
        "created_at",
        existing_type=postgresql.TIMESTAMP(),
        nullable=False,
        existing_server_default=sa.text("CURRENT_TIMESTAMP"),
    )
    op.drop_index("ix_user_fields_review_report_id")
