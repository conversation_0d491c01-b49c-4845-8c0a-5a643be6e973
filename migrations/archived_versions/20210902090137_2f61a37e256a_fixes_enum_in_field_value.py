"""Fixes ParentType enum in SubmissionBusinessFormFieldValue

Revision ID: 2f61a37e256a
Revises: fbe94746e907
Create Date: 2021-09-02 09:01:37.973058+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2f61a37e256a"
down_revision = "fbe94746e907"
branch_labels = None
depends_on = None


def upgrade():
    fact_parent_type = postgresql.ENUM("PREMISES", "BUSINESS", name="factparenttype")
    fact_parent_type.create(op.get_bind())
    op.execute("""ALTER TABLE submission_business_field_values ALTER COLUMN fact_parent_type TYPE factparenttype 
    USING fact_parent_type::text::factparenttype""")


def downgrade():
    pass
