"""coverage_type

Revision ID: 5acf832d3cb8
Revises: 61c15cb3fbab
Create Date: 2022-12-13 18:14:51.494172+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "5acf832d3cb8"
down_revision = "61c15cb3fbab"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "coverages",
        sa.Column(
            "coverage_types",
            sa.ARRAY(sa.Enum("PRIMARY", "EXCESS", name="coveragetype", native_enum=False)),
            nullable=False,
            server_default="{}",
        ),
    )
    op.add_column(
        "loss",
        sa.Column("coverage_type", sa.Enum("PRIMARY", "EXCESS", name="coveragetype", native_enum=False), nullable=True),
    )
    op.add_column(
        "submission_coverages",
        sa.Column("coverage_type", sa.Enum("PRIMARY", "EXCESS", name="coveragetype", native_enum=False), nullable=True),
    )
    op.add_column(
        "submission_deductibles",
        sa.Column("coverage_type", sa.Enum("PRIMARY", "EXCESS", name="coveragetype", native_enum=False), nullable=True),
    )
    op.drop_constraint("uq_submission_coverages_coverage_id_submission_id", "submission_coverages")
    op.create_unique_constraint(
        "uq_submission_coverages_coverage_id_coverage_type_submission_id",
        "submission_coverages",
        ["coverage_id", "coverage_type", "submission_id"],
    )


def downgrade():
    op.drop_column("submission_deductibles", "coverage_type")
    op.drop_column("submission_coverages", "coverage_type")
    op.drop_column("loss", "coverage_type")
    op.drop_column("coverages", "coverage_types")
    op.create_unique_constraint(
        "uq_submission_coverages_coverage_id_submission_id", "submission_coverages", ["coverage_id", "submission_id"]
    )
