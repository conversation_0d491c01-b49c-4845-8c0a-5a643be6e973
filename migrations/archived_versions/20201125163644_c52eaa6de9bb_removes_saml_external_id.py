"""Removes SAML external ID

Revision ID: c52eaa6de9bb
Revises: 71566a6c2a7e
Create Date: 2020-11-25 16:36:44.159034+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "c52eaa6de9bb"
down_revision = "71566a6c2a7e"
branch_labels = None
depends_on = None


def upgrade():
    op.drop_index("ix_users_external_id_saml", table_name="users")
    op.drop_column("users", "external_id_saml")


def downgrade():
    op.add_column("users", sa.Column("external_id_saml", sa.VARCHAR(), autoincrement=False, nullable=True))
    op.create_index("ix_users_external_id_saml", "users", ["external_id_saml"], unique=True)
