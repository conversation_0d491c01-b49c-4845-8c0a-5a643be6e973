"""update loss runs position in mode

Revision ID: 89a48818952a
Revises: 7f51f6debe33
Create Date: 2023-02-17 12:00:44.345978+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "89a48818952a"
down_revision = "7f51f6debe33"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    conn.execute("""
        update mode_rows set position=47 where id='8fca1744-cc32-412a-ab3e-94ffc3597a2e';
        update mode_rows set position=45 where id='3d4d4ea6-e860-497d-8daa-17a55710d920';
        update mode_cards set card_id='loss-details' where card_id='loss-runs-card';
        update mode_cards set card_id='loss-summaries' where card_id='loss-summaries-card';
    """)


def downgrade():
    pass
