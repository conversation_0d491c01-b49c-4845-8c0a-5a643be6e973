"""Indexes for verification

Revision ID: bad5b314a729
Revises: g8c56h39rpg1
Create Date: 2023-05-29 12:40:47.911286+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bad5b314a729"
down_revision = "g8c56h39rpg1"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("ALTER TYPE submissionactiontype ADD VALUE IF NOT EXISTS 'PDS_MANUAL_VERIFICATION_COMPLETED';")


def downgrade():
    pass
