"""Delete old loss runs

Revision ID: 15a496296e36
Revises: eeea13c2c12f
Create Date: 2022-12-13 15:27:45.615421+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "15a496296e36"
down_revision = "eeea13c2c12f"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()
    conn.execute("""delete from loss where submission_id='f000f4e7-2077-4ea3-889f-e69633cb6a72';""")


def downgrade():
    pass
