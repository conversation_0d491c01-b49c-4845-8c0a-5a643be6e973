"""Remove spreadsheet table

Revision ID: a18a2719ee5f
Revises: 0e9e4c7a1c3e
Create Date: 2022-07-06 10:08:55.657090+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a18a2719ee5f"
down_revision = "0e9e4c7a1c3e"
branch_labels = None
depends_on = None


def upgrade():
    with op.get_context().autocommit_block():
        op.execute("SET statement_timeout TO '300 s';")  # 5 min

        # Spreadsheets
        op.drop_index("ix_spreadsheets_submission_id", table_name="spreadsheets")
        op.drop_table("spreadsheets")

    # Form Type
    op.execute("DROP TYPE formtype")


def downgrade():
    # Form Type
    op.execute("CREATE TYPE formtype AS ENUM ('CAT_REQUEST_FORM'', 'QCP_PROP_SPECS_FORM');")

    # Spreadsheets
    op.create_table(
        "spreadsheets",
        sa.Column("id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            autoincrement=False,
            nullable=True,
        ),
        sa.Column("updated_at", postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
        sa.Column("submission_id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "form_type",
            postgresql.ENUM("CAT_REQUEST_FORM", "QCP_PROP_SPECS_FORM", name="formtype"),
            autoincrement=False,
            nullable=False,
        ),
        sa.Column("json_value", postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
        sa.PrimaryKeyConstraint("id", name="spreadsheets_pkey"),
    )
    op.create_index("ix_spreadsheets_submission_id", "spreadsheets", ["submission_id"], unique=False)
