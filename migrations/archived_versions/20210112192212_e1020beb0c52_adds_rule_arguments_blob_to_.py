"""Adds rule arguments blob to recommendation explanation

Revision ID: e1020beb0c52
Revises: 1a783fd3829f
Create Date: 2021-01-12 19:22:12.298795+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "e1020beb0c52"
down_revision = "1a783fd3829f"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "recommendation_explanation",
        sa.Column("rule_arguments", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    )


def downgrade():
    op.drop_column("recommendation_explanation", "rule_arguments")
