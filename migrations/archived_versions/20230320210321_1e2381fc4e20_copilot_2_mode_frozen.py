"""is_copilot_2_mode_frozen

Revision ID: 1e2381fc4e20
Revises: acce7caab645
Create Date: 2023-03-20 21:03:21.412339+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "1e2381fc4e20"
down_revision = "acce7caab645"
branch_labels = None
depends_on = None


def upgrade():
    op.execute("ALTER TABLE submissions ADD COLUMN IF NOT EXISTS is_copilot_2_mode_frozen BOOLEAN")
    op.execute("SET statement_timeout TO '3600 s';")  # 1 hour
    op.execute("""update submissions set is_copilot_2_mode_frozen = true where copilot_2_mode_id is not null;""")


def downgrade():
    op.execute("ALTER TABLE submissions DROP COLUMN IF EXISTS is_copilot_2_mode_frozen")
