"""Adding initial_classification

Revision ID: n9h43c98dgd2
Revises: 68037e4a1bfe
Create Date: 2023-05-26 01:55:25.508526+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "n9h43c98dgd2"
down_revision = "68037e4a1bfe"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "files",
        sa.Column(
            "initial_classification",
            sa.String(),
            nullable=True,
        ),
    )


def downgrade():
    op.drop_column("files", "initial_classification")
