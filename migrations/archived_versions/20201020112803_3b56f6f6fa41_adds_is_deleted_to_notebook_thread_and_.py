"""adds is_deleted to notebook thread and message

Revision ID: 3b56f6f6fa41
Revises: 070b24c0e1a5
Create Date: 2020-10-20 11:28:03.156170+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "3b56f6f6fa41"
down_revision = "070b24c0e1a5"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("notebook_message", sa.Column("is_deleted", sa.<PERSON>(), nullable=True))
    op.add_column("notebook_thread", sa.Column("is_deleted", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("notebook_thread", "is_deleted")
    op.drop_column("notebook_message", "is_deleted")
