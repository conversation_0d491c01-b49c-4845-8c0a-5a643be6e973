"""Add column email_subject to reports_v2 table

Revision ID: bbbb20230105
Revises: aaaa20230105
Create Date: 2023-01-05 22:22:00.000000+00:00

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "bbbb20230105"
down_revision = "aaaa20230105"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("reports_v2", sa.Column("email_subject", sa.Text(), nullable=True))


def downgrade():
    op.drop_column("reports_v2", "email_subject")
