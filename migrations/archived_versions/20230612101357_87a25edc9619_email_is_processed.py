"""Email is processed

Revision ID: 87a25edc9619
Revises: b366094fcbcf
Create Date: 2023-06-12 10:13:57.148736+00:00

"""
from alembic import op
from sqlalchemy.dialects import postgresql
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "87a25edc9619"
down_revision = "b366094fcbcf"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column("emails", sa.Column("is_processed", sa.<PERSON>(), nullable=True))


def downgrade():
    op.drop_column("emails", "is_processed")
