[{"name": "log_router", "image": "922426152114.dkr.ecr.us-east-1.amazonaws.com/kalepa-aws-for-fluent-bit:latest-arm64", "essential": true, "firelensConfiguration": {"type": "fluentbit", "options": {"enable-ecs-log-metadata": "true", "config-file-type": "file", "config-file-value": "/kalepa-fluent-bit/parse-json-multiline.conf"}}, "cpu": 0, "environment": [], "mountPoints": [], "portMappings": [], "volumesFrom": [], "user": "0"}, {"name": "datadog-agent", "image": "public.ecr.aws/datadog/agent:latest", "essential": true, "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "datadog", "dd_service": "${service_name}-ddagent", "dd_source": "datadog-agent", "dd_tags": "env:${environment},version:${image_tag}", "TLS": "on", "provider": "ecs"}, "secretOptions": [{"name": "apikey", "valueFrom": "${datadog_api_key_secret_arn}"}]}, "healthCheck": {"command": ["CMD", "/probe.sh"], "interval": 30, "timeout": 10, "retries": 2, "startPeriod": 30}, "environment": [{"name": "ECS_FARGATE", "value": "true"}, {"name": "DD_ANALYTICS_ENABLED", "value": "true"}, {"name": "DD_DOGSTATSD_NON_LOCAL_TRAFFIC", "value": "true"}, {"name": "DD_DOGSTATSD_TAG_CARDINALITY", "value": "true"}, {"name": "DD_APM_NON_LOCAL_TRAFFIC", "value": "true"}, {"name": "DD_APM_ENABLED", "value": "true"}, {"name": "DD_REMOTE_CONFIGURATION_ENABLED", "value": "true"}, {"name": "DD_TRACE_URLLIB3_ENABLED", "value": "true"}, {"name": "DD_ENV", "value": "${environment}"}, {"name": "DD_SERVICE", "value": "${service_name}-ddagent"}, {"name": "DD_SERVICE_MAPPING", "value": "postgres:postgres-capi"}, {"name": "DD_TRACE_SAMPLE_RATE", "value": "${dd_sample_rate}"}, {"name": "DD_APM_FEATURES", "value": "error_rare_sample_tracer_drop"}, {"name": "DD_VERSION", "value": "${image_tag}"}, {"name": "DD_DOGSTATSD_TAGS", "value": "'[\"dd_service:${service_name}\", \"service:${service_name}\", \"dd_env:${environment}\", , \"env:${environment}\" \"dd_version:${image_tag}\"]'"}], "secrets": [{"name": "DD_API_KEY", "valueFrom": "${datadog_api_key_secret_arn}"}], "portMappings": [{"containerPort": 8125, "hostPort": 8125, "protocol": "udp"}, {"containerPort": 8126, "hostPort": 8126, "protocol": "tcp"}], "cpu": 0, "mountPoints": [], "volumesFrom": []}, {"name": "${service_name}", "image": "${full_image_uri}", "dependsOn": [{"containerName": "datadog-agent", "condition": "HEALTHY"}], "cpu": 0, "mountPoints": [], "volumesFrom": [], "essential": true, "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "datadog", "dd_service": "${service_name}", "dd_source": "kalepa_container", "dd_tags": "env:${environment},version:${image_tag},image_name:${image_name}", "TLS": "on", "provider": "ecs"}, "secretOptions": [{"name": "apikey", "valueFrom": "${datadog_api_key_secret_arn}"}]}, "portMappings": [{"hostPort": 8081, "protocol": "tcp", "containerPort": 8081}], "environment": [{"name": "RUNTIME_SSM_PARAMETER", "value": "${runtime_ssm_parameter}"}, {"name": "COPILOT_EMAIL_LIST", "value": "[\"<EMAIL>\", \"<EMAIL>\"]"}, {"name": "ENTITY_RESOLUTION_SERVICE_URL_V3", "value": "http://entity-resolution.${internal_domain}:${alb_port}/api/v3.0"}, {"name": "ENTITY_RESOLUTION_SERVICE_V3_URL", "value": "http://entity-resolution.${internal_domain}:${alb_port}/api/v3.0"}, {"name": "ENTITY_RESOLUTION_SERVICE_URL_V1", "value": "http://entity-resolution.${internal_domain}:${alb_port}/api/v1.0"}, {"name": "NEWS_API_URL", "value": "http://news-api.${internal_domain}:${alb_port}/api/v2.0"}, {"name": "FACTS_API_URL", "value": "http://facts-api.${internal_domain}:${alb_port}/api/v1.0"}, {"name": "FACTS_API_URL_V2", "value": "http://facts-api.${internal_domain}:${alb_port}/api/v2.0"}, {"name": "SENTRY_DSN", "value": "${sentry_dsn}"}, {"name": "DD_LOGS_INJECTION", "value": "true"}, {"name": "DD_REMOTE_CONFIGURATION_ENABLED", "value": "true"}, {"name": "DD_TRACE_API_VERSION", "value": "v0.5"}, {"name": "DD_TRACE_SAMPLE_RATE", "value": "${dd_sample_rate}"}, {"name": "DD_APM_FEATURES", "value": "error_rare_sample_tracer_drop"}, {"name": "DD_TRACE_WRITER_BUFFER_SIZE_BYTES", "value": "40000000"}, {"name": "DD_TRACE_WRITER_MAX_PAYLOAD_SIZE_BYTES", "value": "40000000"}, {"name": "DD_RUNTIME_METRICS_ENABLED", "value": "false"}, {"name": "EXECUTOR_MAX_WORKERS", "value": "8"}, {"name": "COPILOT_S3_BUCKET_NAME", "value": "${copilot_s3_bucket_name}"}, {"name": "GLUE_S3_BUCKET_NAME", "value": "${glue_s3_bucket_name}"}, {"name": "TEXTRACT_S3_BUCKET_NAME", "value": "${textract_s3_bucket_name}"}, {"name": "FAILED_LOSS_RUNS_S3_BUCKET_NAME", "value": "${failed_loss_runs_s3_bucket_name}"}, {"name": "INGESTION_S3_BUCKET_NAME", "value": "${ingestion_s3_bucket_name}"}, {"name": "KALEPA_ENV", "value": "${environment}"}, {"name": "DATADOG_SERVICE_NAME", "value": "${environment}-${service_name}"}, {"name": "DD_ENV", "value": "${environment}"}, {"name": "DD_SERVICE", "value": "${service_name}"}, {"name": "DD_SERVICE_MAPPING", "value": "postgres:postgres-capi"}, {"name": "DD_VERSION", "value": "${image_tag}"}, {"name": "FORCE_INGEST_OSHA_INSPECTION_FROM_URL_STATE_MACHINE_NAME", "value": "ingestion-force-ingest-osha-inspection-from-url"}, {"name": "CUSTOMIZABLE_CLASSIFIER_TEST_RUN_STATE_MACHINE_NAME", "value": "insights-conduct-test-run-v2"}, {"name": "COPILOT_WORKERS_REASSIGN_FIRST_PARTY_OBSERVATION_FACT_SUBTYPE_LAMBDA_NAME", "value": "copilot-workers-reassign-first-party-observation-fact-subtype"}, {"name": "COPILOT_WORKERS_REASSIGN_FIRST_PARTY_OBSERVATION_PARENT_LAMBDA_NAME", "value": "copilot-workers-reassign-first-party-observation-parent"}, {"name": "COPILOT_WORKERS_MERGE_BUSINESSES_FACTS_LAMBDA_NAME", "value": "copilot-workers-merge-businesses-facts"}, {"name": "COPILOT_WORKERS_MERGE_BUSINESSES_FACT_LAMBDA_NAME", "value": "copilot-workers-merge-businesses-fact"}, {"name": "EVENT_SENDER_LAMBDA_NAME", "value": "infrastructure-events-event-sender"}, {"name": "NAICS_CLASSIFICATION_ASYNC_LAMBDA_NAME", "value": "insights-infer-submission-naics-codes"}, {"name": "LIGHT_CLEARING_LN", "value": "infrastructure-events-run-light-clearing-async"}, {"name": "BUSINESS_DEDUPLICATION_LN", "value": "copilot-workers-deduplicate-businesses"}, {"name": "PROCESS_FILE_STEP_FUNCTION", "value": "copilot-workers-process-file"}, {"name": "RESOLVE_SUBMISSION_FILES_ENTITIES_STEP_FUNCTION", "value": "copilot-workers-resolve-submission-files-entities"}, {"name": "SELECT_SCRAPERS_BULK_LAMBDA_NAME", "value": "ingestion-select-scrapers-bulk-lambda"}, {"name": "EVENT_BUS_NAME", "value": "copilot-api"}, {"name": "QUICKSIGHT_DASHBOARD_MAP", "value": "{ \"management\": \"9e70fab4-ddc7-4d0a-a8e5-4a2ecba27ee0\", \"underwriter\": \"751431d7-e7ad-42f0-83ad-53c7a2cc648f\", \"operations\": \"c82f667c-9a48-4ddb-bf3e-2416f2f56089\", \"bowhead\": \"b9761030-d246-4a0d-b6fb-041267b01547\", \"arch\": \"f96d2744-0325-442e-922b-d564dc3a9b8a\", \"nationwide\": \"********-f99b-450f-8059-450e663d607a\", \"paragon\": \"42822d04-4658-4cfd-b206-731e612dac93\" }"}, {"name": "QUICKSIGHT_ASSUME_ROLE", "value": "arn:aws:iam::************:role/quicksight-access-from-all-env-accounts"}, {"name": "SENSIBLE_BASE_URL", "value": "https://api.sensible.so/v0"}, {"name": "CLOUDSEARCH_REPORTS_ENDPOINT_URL", "value": "https://${cloudsearch_reports_search_endpoint}"}, {"name": "SOURCE_DATE_EPOCH", "value": "**********"}], "secrets": [{"name": "DB_CONFIG_SECRET_HOST", "valueFrom": "${database_config_secret_arn}:host::"}, {"name": "DB_CONFIG_SECRET_PORT", "valueFrom": "${database_config_secret_arn}:port::"}, {"name": "DB_CONFIG_SECRET_USERNAME", "valueFrom": "${database_config_secret_arn}:username::"}, {"name": "DB_CONFIG_SECRET_DBNAME", "valueFrom": "${database_config_secret_arn}:dbname::"}, {"name": "DD_PROFILING_API_KEY", "valueFrom": "${datadog_api_key_secret_arn}"}, {"name": "KNOCK_API_KEY", "valueFrom": "${copilot_knock_api_key_secret_arn}"}, {"name": "KNOCK_SIGNING_KEY", "valueFrom": "${copilot_knock_signing_key_secret_arn}"}, {"name": "COPILOT_SENDGRID_API_KEY", "valueFrom": "${copilot_sendgrid_api_key_secret_arn}"}, {"name": "COHERENT_API_KEY", "valueFrom": "${copilot_coherent_api_key_secret_arn}"}, {"name": "SENDGRID_WEBHOOK_KEY", "valueFrom": "${copilot_webhook_key_secret_arn}"}, {"name": "AMPLITUDE_API_KEY", "valueFrom": "${amplitude_api_key_secret_arn}"}, {"name": "SENSIBLE_API_KEY", "valueFrom": "${sensible_api_key_secret_arn}"}, {"name": "SENSIBLE_FILE_KEY", "valueFrom": "${sensible_file_key_secret_arn}"}, {"name": "AZURE_READ_ANALYZE_ENDPOINT", "valueFrom": "${azure_read_endpoint_secret_arn}"}, {"name": "AZURE_READ_SUBSCRIPTION_KEY", "valueFrom": "${azure_read_api_key_secret_arn}"}, {"name": "OPEN_AI_API_KEY", "valueFrom": "${openai_api_key_secret_arn}"}, {"name": "AUTH0_CLIENT_ID", "valueFrom": "${auth0_client_id_secret_arn}"}, {"name": "AUTH0_CLIENT_SECRET", "valueFrom": "${auth0_client_secret_secret_arn}"}, {"name": "SLACK_TOKEN", "valueFrom": "${slack_token_secret_arn}:token::"}, {"name": "LAUNCH_DARKLY_API_KEY", "valueFrom": "${launch_darkly_sdk_key_secret_arn}"}, {"name": "PARAGON_IMS_API_ENABLED", "valueFrom": "${paragon_ims_integration_key_arn}:enabled::"}, {"name": "PARAGON_IMS_API_ENV", "valueFrom": "${paragon_ims_integration_key_arn}:environment::"}, {"name": "PARAGON_IMS_API_USER", "valueFrom": "${paragon_ims_integration_key_arn}:user::"}, {"name": "PARAGON_IMS_API_PASSWORD", "valueFrom": "${paragon_ims_integration_key_arn}:password::"}, {"name": "PARAGON_REST_API_ENABLED", "valueFrom": "${paragon_rest_api_creds_secret_arn}:enabled::"}, {"name": "PARAGON_REST_API_BASE_URL", "valueFrom": "${paragon_rest_api_creds_secret_arn}:base_url::"}, {"name": "PARAGON_REST_API_USER_NAME", "valueFrom": "${paragon_rest_api_creds_secret_arn}:user_name::"}, {"name": "PARAGON_REST_API_PASSWORD", "valueFrom": "${paragon_rest_api_creds_secret_arn}:password::"}, {"name": "ARCH_API_ENABLED", "valueFrom": "${arch_api_config_secret_arn}:enabled::"}, {"name": "ARCH_API_BASE_URL", "valueFrom": "${arch_api_config_secret_arn}:base_url::"}, {"name": "ARCH_API_TOKEN_URL", "valueFrom": "${arch_api_config_secret_arn}:token_url::"}, {"name": "ARCH_API_CLIENT_ID", "valueFrom": "${arch_api_config_secret_arn}:client_id::"}, {"name": "ARCH_API_CLIENT_SECRET", "valueFrom": "${arch_api_config_secret_arn}:client_secret::"}, {"name": "ARCH_API_AUDIENCE", "valueFrom": "${arch_api_config_secret_arn}:audience::"}, {"name": "NW_BOSS_API_ENABLED", "valueFrom": "${nw_boss_api_config_secret_arn}:enabled::"}, {"name": "NW_BOSS_API_BASE_URL", "valueFrom": "${nw_boss_api_config_secret_arn}:base_url::"}, {"name": "NW_BOSS_API_TOKEN_URL", "valueFrom": "${nw_boss_api_config_secret_arn}:token_url::"}, {"name": "NW_BOSS_API_AUDIENCE", "valueFrom": "${nw_boss_api_config_secret_arn}:audience::"}, {"name": "NW_BOSS_API_CLIENT_ID", "valueFrom": "${nw_boss_api_config_secret_arn}:client_id::"}, {"name": "NW_BOSS_API_CLIENT_SECRET", "valueFrom": "${nw_boss_api_config_secret_arn}:client_secret::"}, {"name": "NW_CLIENT_CERT", "valueFrom": "${nw_client_cert_secret_arn}:nationwide.client.cert.pem_base64::"}, {"name": "NW_CLIENT_KEY", "valueFrom": "${nw_client_cert_secret_arn}:nationwide.client.key.pem_base64::"}, {"name": "PROMETRIX_CLIENT_NAME", "valueFrom": "${paragon_prometrix_secret_arn}:client_name::"}, {"name": "PROMETRIX_TOKEN_URL", "valueFrom": "${paragon_prometrix_secret_arn}:token_url::"}, {"name": "PROMETRIX_API_URL", "valueFrom": "${paragon_prometrix_secret_arn}:api_url::"}, {"name": "PROMETRIX_USERNAME", "valueFrom": "${paragon_prometrix_secret_arn}:username::"}, {"name": "PROMETRIX_PASSWORD", "valueFrom": "${paragon_prometrix_secret_arn}:password::"}, {"name": "PROMETRIX_BRANCH", "valueFrom": "${paragon_prometrix_secret_arn}:branch::"}, {"name": "GOOGLE_DOC_AI_CREDENTIALS", "valueFrom": "${google_doc_ai_creds_secret_arn}"}, {"name": "NW_SMTP_HOST", "valueFrom": "${nw_smtp_config_secret_arn}:smtp_host_1::"}, {"name": "NW_SMTP_PORT", "valueFrom": "${nw_smtp_config_secret_arn}:smtp_port::"}, {"name": "NW_SMTP_USERNAME", "valueFrom": "${nw_smtp_config_secret_arn}:user::"}, {"name": "NW_SMTP_PASSWORD", "valueFrom": "${nw_smtp_config_secret_arn}:password::"}], "dockerLabels": {"com.datadoghq.tags.env": "${environment}", "com.datadoghq.tags.service": "${service_name}", "com.datadoghq.tags.version": "${image_tag}"}}, {"name": "${alb_target_container_name}", "image": "${nginx_sidecar_image}", "dependsOn": [{"containerName": "datadog-agent", "condition": "HEALTHY"}], "logConfiguration": {"logDriver": "awsfire<PERSON>s", "options": {"Name": "datadog", "dd_service": "${service_name}-nginx", "dd_source": "nginx", "dd_tags": "env:${environment},version:${image_tag}", "TLS": "on", "provider": "ecs"}, "secretOptions": [{"name": "apikey", "valueFrom": "${datadog_api_key_secret_arn}"}]}, "dockerLabels": {"com.datadoghq.tags.env": "${environment}", "com.datadoghq.tags.service": "${service_name}-nginx", "com.datadoghq.tags.version": "${image_tag}"}, "memory": 512, "cpu": 256, "essential": true, "portMappings": [{"hostPort": 8080, "containerPort": 8080, "protocol": "tcp"}], "environment": [], "mountPoints": [], "volumesFrom": []}]