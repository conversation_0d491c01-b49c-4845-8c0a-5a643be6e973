from datetime import datetime
from typing import Any
from uuid import uuid4
import json
import random
import string

from pytest import fixture
from static_common.enums.coverage_names import CoverageName
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.file_onboarding import (
    Acord126TransientData,
    Acord140TransientData,
    OnboardedFile,
)
from static_common.taxonomies.industry_classification import NaicsCode
import pytz

from copilot.clients.response_configuration.sensible import (
    ACORD_101_CONFIG,
    ACORD_125_CONFIG,
    ACORD_126_CONFIG,
    ACORD_131_CONFIG,
    ACORD_140_CONFIG,
    ACORD_823_CONFIG,
    ACORD_829_CONFIG,
    APPLIED_98_CONFIG,
)
from copilot.clients.response_mappers import AcordMapper
from copilot.logic.sensible import AcordUtils, get_page_metadata
from copilot.models import Organization, db
from copilot.models.mappers import CoverageAssignment
from copilot.models.reports import Coverage
from copilot.models.sensible_claim import (
    AcordLinesOfBusinessType,
    PageMetadata,
    SensibleBoolValue,
    SensibleStringValue,
)
from copilot.models.types import CoverageType
from copilot.schemas.sensible_claim import (
    Acord101Schema,
    Acord125Schema,
    Acord126Schema,
    Acord131Schema,
    Acord140Schema,
    Acord823Schema,
    Acord829Schema,
    Applied98Schema,
)
from tests.integration.factories import coverage_fixture, organization_fixture


@fixture
def organization() -> Organization:
    organization = organization_fixture(id=ExistingOrganizations.BishopConifer.value, name="Bishop Conifer")
    db.session.add(organization)
    db.session.commit()
    return organization


@fixture
def coverages() -> list[Coverage]:
    crime = coverage_fixture(
        name="crime",
        display_name="Crime",
        organization_id=ExistingOrganizations.BishopConifer.value,
        coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS],
    )
    liability = coverage_fixture(
        name="liability",
        display_name="Liability",
        organization_id=ExistingOrganizations.BishopConifer.value,
        coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS],
    )
    businessAuto = coverage_fixture(
        name="businessAuto",
        display_name="Business Auto",
        organization_id=ExistingOrganizations.BishopConifer.value,
        coverage_types=[CoverageType.EXCESS],
    )
    property = coverage_fixture(
        name="property",
        display_name="Property",
        organization_id=ExistingOrganizations.BishopConifer.value,
        coverage_types=[],
    )

    coverages = [crime, liability, businessAuto, property]
    db.session.add_all(coverages)
    db.session.commit()
    return coverages


@fixture
def acord_mapper(organization, coverages) -> AcordMapper:
    mapper = AcordMapper()
    mapper.available_coverages = mapper._get_available_coverages(organization.id)
    return mapper


@fixture
def acord_101_schema() -> Acord101Schema:
    return Acord101Schema()


@fixture
def acord_125_schema() -> Acord125Schema:
    return Acord125Schema()


@fixture
def acord_126_schema() -> Acord126Schema:
    return Acord126Schema()


@fixture
def acord_823_schema() -> Acord823Schema:
    return Acord823Schema()


@fixture
def acord_829_schema() -> Acord829Schema:
    return Acord829Schema()


@fixture
def acord_131_schema() -> Acord131Schema:
    return Acord131Schema()


@fixture
def acord_140_schema() -> Acord140Schema:
    return Acord140Schema()


@fixture
def applied_98_schema() -> Applied98Schema:
    return Applied98Schema()


def assert_submission_field(
    data: OnboardedFile, field_name: EntityInformation, expected_value: str | datetime | bool, sub_entity_idx: int
) -> None:
    field = next((f for f in data.entity_information if f.name == field_name), None)
    assert field, f"{field_name} is missing in submission_fields"
    value = next((v.value for v in field.values if v.entity_idx == sub_entity_idx), None)
    assert value is not None, f"{field_name} has no value for Submission entity idx = {sub_entity_idx}"
    assert value == expected_value, f"Expected {expected_value}, got {value}"


def assert_submission_field_onboarded_data(
    submission_field: Any, field_name: EntityInformation, expected_value: str | datetime
):
    assert submission_field.name == field_name, f"{field_name} is missing in submission_fields"
    assert (
        submission_field.values[0].value == expected_value
    ), f"Expected {expected_value}, got {submission_field.values[0].value}"


def test_acord_101(app_context, acord_mapper, acord_101_schema):
    with open(f"tests/data/responses/acord_101.json") as f:
        file = f.read()
        form_data = acord_101_schema.loads(file)
    submission_id = uuid4()
    result = acord_mapper.process_response(
        ACORD_101_CONFIG, response_data=form_data, organization_id=37, submission_id=submission_id
    )
    onboarded_file = result.raw_processed_data

    assert len(onboarded_file.entities) == 2
    assert onboarded_file.entities[0].type == SubmissionEntityType.BUSINESS
    assert onboarded_file.entities[0].is_fni
    assert onboarded_file.entities[0].id == "samna construction and steel fabricatio"
    assert onboarded_file.entities[1].type == SubmissionEntityType.SUBMISSION
    assert onboarded_file.entities[1].id == str(submission_id)

    assert len(onboarded_file.fields) == 3
    assert onboarded_file.fields[2].name == "Additional Remarks"
    assert onboarded_file.fields[2].values[0].entity_idx == 0
    assert len(onboarded_file.fields[2].values[0].value) == 290


def test_acord_125(app_context, acord_mapper, acord_125_schema, mocker):
    mocked = mocker.patch("copilot.clients.response_mappers._get_naics_code")
    mocked.return_value = None
    with open(f"tests/data/responses/acord_125.json") as f:
        file = f.read()
        form_data = acord_125_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_125_CONFIG, response_data=form_data, organization_id=37, submission_id=uuid4()
    )

    assert len([e for e in result.raw_processed_data.entities if e.type == SubmissionEntityType.BUSINESS]) == 5
    assert len([e for e in result.raw_processed_data.entities if e.type == SubmissionEntityType.STRUCTURE]) == 4
    assert result.raw_processed_data.entities[0].is_fni
    address_field = next(
        (field for field in result.raw_processed_data.entity_information if field.name == EntityFieldID.ADDRESS), None
    )
    assert address_field
    assert (
        next((v.value for v in address_field.values if v.entity_idx == 0), None)
        == "421 6th Street, San Antonio TX 78215"
    )
    assert len(result.raw_processed_data.entity_information) == 4
    policy_effective_start_date = next(
        (
            field
            for field in result.raw_processed_data.entity_information
            if field.name == EntityInformation.POLICY_EFFECTIVE_START_DATE
        ),
        None,
    )
    assert policy_effective_start_date
    assert policy_effective_start_date.values[0].value == "2023-05-30 00:00:00+00:00"
    policy_end_date = next(
        (
            field
            for field in result.raw_processed_data.entity_information
            if field.name == EntityInformation.POLICY_END_DATE
        ),
        None,
    )
    assert policy_end_date
    assert policy_end_date.values[0].value == "2024-05-30 00:00:00+00:00"


def test_acord_125_sic_operations_facts(app_context, acord_mapper, acord_125_schema, mocker):
    mocked = mocker.patch("copilot.clients.response_mappers._get_naics_code")
    mocked.return_value = None
    with open(f"tests/data/responses/acord_125.json") as f:
        file = f.read()
        form_data = acord_125_schema.loads(file)
    form_data.applicant_information[0].applicant_sic = SensibleStringValue(value="236220")
    form_data.nature_of_business[0].description_of_primary_operations = SensibleStringValue(
        value="".join(random.choices(string.ascii_uppercase + string.digits, k=300))
    )

    result = acord_mapper.process_response(
        ACORD_125_CONFIG, response_data=form_data, organization_id=37, submission_id=uuid4()
    )

    operations_field = next(
        (field for field in result.raw_processed_data.fields if field.name == "Operations Summary"),
        None,
    )
    assert operations_field
    assert len(operations_field.values[0].value) == 256
    sic_field = next(
        (
            field
            for field in result.raw_processed_data.fields
            if field.name == "Commercial and Institutional Building Construction"
        ),
        None,
    )
    assert sic_field
    assert sic_field.values[0].value is True
    assert sic_field.fact_subtype_id is None
    assert sic_field.naics_code == NaicsCode.NAICS_236220


def test_acord_125_structures(app_context, acord_mapper, acord_125_schema):
    with open(f"tests/data/responses/acord_125_structures.json") as f:
        file = f.read()
        form_data = acord_125_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_125_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )

    business_entities = [e for e in result.raw_processed_data.entities if e.type == SubmissionEntityType.BUSINESS]
    structures = [e for e in result.raw_processed_data.entities if e.type == SubmissionEntityType.STRUCTURE]

    assert len(business_entities) == 2
    assert business_entities[1].acord_location_information.location_number == 1
    assert business_entities[1].acord_location_information.building_number is None
    assert len(structures) == 4
    assert all(s for s in structures if s.acord_location_information.building_number is not None)
    occupied_area = next((f for f in result.raw_processed_data.fields if f.name == "Occupied area"), None)
    assert occupied_area
    assert len(occupied_area.values) == 4
    assert result.raw_processed_data.entities[0].is_fni
    assert len(result.raw_processed_data.entity_information) == 3
    policy_effective_start_date = next(
        (
            field
            for field in result.raw_processed_data.entity_information
            if field.name == EntityInformation.POLICY_EFFECTIVE_START_DATE
        ),
        None,
    )
    assert policy_effective_start_date
    assert policy_effective_start_date.values[0].value == "2023-02-01 00:00:00+00:00"


def test_acord_125_no_loc_num(app_context, acord_mapper, acord_125_schema):
    with open(f"tests/data/responses/acord_125_no_loc_num.json") as f:
        file = f.read()
        form_data = acord_125_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_125_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )

    business_entities = [e for e in result.raw_processed_data.entities if e.type == SubmissionEntityType.BUSINESS]
    structures = [e for e in result.raw_processed_data.entities if e.type == SubmissionEntityType.STRUCTURE]

    assert len(business_entities) == 4
    assert len(structures) == 3


def test_acord_125_submission_fields(app_context, acord_mapper, acord_125_schema):
    with open(f"tests/data/responses/acord_125_submission_fields.json") as f:
        file = f.read()
        form_data = acord_125_schema.loads(file)
    submission_id = uuid4()
    result = acord_mapper.process_response(
        ACORD_125_CONFIG,
        response_data=form_data,
        organization_id=ExistingOrganizations.MarkelDemo.value,
        submission_id=submission_id,
    )
    sub_entity_idx = next(
        (idx for idx, e in enumerate(result.raw_processed_data.entities) if e.type == SubmissionEntityType.SUBMISSION),
        None,
    )
    assert sub_entity_idx is not None
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.AGENCY,
        "Alliant Insurance Services 560 Mission Street 6th FL San Francisco, CA 94105",
        sub_entity_idx,
    )
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER, "TBD", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data, EntityInformation.AGENCY_FAX, "<EMAIL>", sub_entity_idx
    )
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER_NAIC_CODE, "1234", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_PHONE, "************", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data, EntityInformation.AGENCY_CONTACT_NAME, "Martin van der Wal", sub_entity_idx
    )
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.AGENCY_EMAIL_ADDRESS,
        "<EMAIL>",
        sub_entity_idx,
    )
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_CUSTOMER_ID, "HL42690", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER_POLICY_NUMBER, "TBD", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data, EntityInformation.CARRIER_STATUS_OF_TRANSACTION, "quote", sub_entity_idx
    )
    assert_submission_field(
        result.raw_processed_data, EntityInformation.CARRIER_TRANSACTION_TYPE, "issue_policy", sub_entity_idx
    )
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.CARRIER_TRANSACTION_DATE,
        "2023-07-21 00:00:00+00:00",
        sub_entity_idx,
    )
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.CARRIER_COMPANY_POLICY_OR_PROGRAM_NAME,
        "Commerical Package",
        sub_entity_idx,
    )
    assert_submission_field(
        result.raw_processed_data, EntityInformation.CARRIER_UNDERWRITER, "Jan Kowalski", sub_entity_idx
    )
    assert_submission_field(
        result.raw_processed_data, EntityInformation.CARRIER_UNDERWRITER_OFFICE, "37/1", sub_entity_idx
    )
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER_PROGRAM_CODE, "4321", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_CODE, "123", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_SUBCODE, "654", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data, EntityInformation.AGENCY_DATE, "2023-07-21 00:00:00+00:00", sub_entity_idx
    )


# Two test cases for 126 submission_fields due to two major schemas of it, with some major differs
def test_acord_126_submission_fields_2007_05(app_context, acord_mapper, acord_126_schema):
    with open(f"tests/data/responses/acord_126_submission_fields_2007_05.json") as f:
        file = f.read()
        form_data = acord_126_schema.loads(file)
    submission_id = uuid4()
    result = acord_mapper.process_response(
        ACORD_126_CONFIG,
        response_data=form_data,
        organization_id=ExistingOrganizations.MarkelDemo.value,
        submission_id=submission_id,
    )
    sub_entity_idx = next(
        (idx for idx, e in enumerate(result.raw_processed_data.entities) if e.type == SubmissionEntityType.SUBMISSION),
        None,
    )
    assert sub_entity_idx is not None

    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.AGENCY,
        "Petra Risk Solutions www.PetraRiskSolutions.com 770 The City Drive S, Ste 1500 Orange, CA 92868 Nick Bender",
        sub_entity_idx,
    )
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_FAX, "+18004946829", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_CUSTOMER_ID, "SHORT-1", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_PHONE, "+18004668951", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER_NAIC_CODE, "4201", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.AGENCY_DATE,
        "2023-12-22 00:00:00+00:00",
        sub_entity_idx,
    )
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_CODE, "124", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_SUBCODE, "1245", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_BILL, False, sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_DIRECT_BILL, True, sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_AUDIT, "TBD", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data, EntityInformation.AGENCY_PAYMENT_PLAN, "New plan", sub_entity_idx
    )
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.POLICY_EFFECTIVE_START_DATE,
        "2024-02-17 00:00:00+00:00",
        sub_entity_idx,
    )
    assert_submission_field(
        result.raw_processed_data, EntityInformation.POLICY_END_DATE, "2025-02-17 00:00:00+00:00", sub_entity_idx
    )


def test_acord_126_submission_fields_2016_09(app_context, acord_mapper, acord_126_schema):
    with open(f"tests/data/responses/acord_126_submission_fields_2016_09.json") as f:
        file = f.read()
        form_data = acord_126_schema.loads(file)
    submission_id = uuid4()
    result = acord_mapper.process_response(
        ACORD_126_CONFIG,
        response_data=form_data,
        organization_id=ExistingOrganizations.MarkelDemo.value,
        submission_id=submission_id,
    )
    sub_entity_idx = next(
        (idx for idx, e in enumerate(result.raw_processed_data.entities) if e.type == SubmissionEntityType.SUBMISSION),
        None,
    )
    assert sub_entity_idx is not None
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY, "P&G Brokerage Inc.", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_CUSTOMER_ID, "00021677", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER, "TBD", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER_POLICY_NUMBER, "TBD", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER_NAIC_CODE, "1242", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.AGENCY_DATE,
        "2024-04-10 00:00:00+00:00",
        sub_entity_idx,
    )


def test_acord_126_coverages_details(app_context, acord_mapper, acord_126_schema):
    with open(f"tests/data/responses/acord_126_coverages.json") as f:
        file = f.read()
        form_data = acord_126_schema.loads(file)
    submission_id = uuid4()
    result = acord_mapper.process_response(
        ACORD_126_CONFIG,
        response_data=form_data,
        organization_id=ExistingOrganizations.MarkelDemo.value,
        submission_id=submission_id,
    )
    sub_entity_idx = next(
        (idx for idx, e in enumerate(result.raw_processed_data.entities) if e.type == SubmissionEntityType.SUBMISSION),
        None,
    )
    assert sub_entity_idx is not None
    coverages = None
    for d in result.raw_processed_data.entity_information:
        if d.name == EntityInformation.COVERAGES_DETAILS:
            coverages = d
            break
    assert coverages is not None
    coverages_details = json.loads(coverages.values[0].value)[0]
    gl_limits = coverages_details["gl_limits"]
    assert len(gl_limits) == 11
    for limit in [
        {"name": "Limits: Products and Completed Operations Aggregate", "value": 2000000.0},
        {"name": "Claims Made", "value": False},
        {"name": "Limits: Employee Benefits", "value": 1000000.0},
    ]:
        assert limit in gl_limits
    gl_deductibles = coverages_details["gl_deductibles"]
    assert len(gl_deductibles) == 3
    for limit in [
        {"name": "Deductibles: Property Damage", "value": False},
        {"name": "Deductibles: Bodily Injury", "value": False},
        {"name": "Deductibles: Per Occurrence", "value": True},
    ]:
        assert limit in gl_deductibles
    assert coverages_details["each_occurrence"] == 1000000.0
    assert coverages_details["limit"] == 2000000.0


def test_acord_126_products(app_context, acord_mapper, acord_126_schema):
    with open(f"tests/data/responses/acord_126_products.json") as f:
        file = f.read()
        form_data = acord_126_schema.loads(file)
    sub_id = uuid4()
    result = acord_mapper.process_response(
        ACORD_126_CONFIG, response_data=form_data, organization_id=1, submission_id=sub_id
    )

    assert len([e for e in result.raw_processed_data.entities if e.parent_type is None]) == 1
    assert result.raw_processed_data.entities[0].type == SubmissionEntityType.SUBMISSION
    assert result.raw_processed_data.entities[0].id == str(sub_id)
    # noinspection PyTypeChecker
    transient_data: Acord126TransientData = result.transient_data_object
    assert transient_data.schedule_of_hazards[0].classification == "As per schedule on file"


def test_acord_823_no_street(app_context, acord_mapper, acord_823_schema):
    with open(f"tests/data/responses/acord_823_no_street_name.json") as f:
        file = f.read()
        form_data = acord_823_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_823_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )

    assert len([e for e in result.raw_processed_data.entities if e.type == SubmissionEntityType.BUSINESS]) == 0


def test_acord_829(app_context, acord_mapper, acord_829_schema):
    with open(f"tests/data/responses/acord_829.json") as f:
        file = f.read()
        form_data = acord_829_schema.loads(file)

    page_metadata = {0: PageMetadata(width=8.5, height=11)}
    result = acord_mapper.process_response(
        ACORD_829_CONFIG,
        response_data=form_data,
        organization_id=37,
        page_metadata=page_metadata,
        submission_id=uuid4(),
    )
    onboarded_file = result.raw_processed_data

    assert len(onboarded_file.entities) == 6
    assert onboarded_file.entities[0].id == "waiter. com"
    assert onboarded_file.entities[0].type == SubmissionEntityType.BUSINESS
    assert onboarded_file.entities[2].id == "forms and endorsements 1"
    assert onboarded_file.entities[2].type == SubmissionEntityType.RAW_ROW

    assert len(onboarded_file.fields) == 7
    assert onboarded_file.fields[1].name == "Form Number"
    assert onboarded_file.fields[1].values[0].entity_idx == 3
    assert onboarded_file.fields[1].values[0].value == "AP2103US"
    assert onboarded_file.fields[1].values[0].evidences[0].confidence == 0.99
    assert onboarded_file.fields[3].values[0].entity_idx == 4
    assert onboarded_file.fields[3].values[0].value == "XC2132US"
    assert onboarded_file.fields[3].values[0].evidences[0].confidence == 0.992


def test_acord_131_submission_fields(app_context, acord_mapper, acord_131_schema):
    with open(f"tests/data/responses/acord_131_submission_fields.json") as f:
        file = f.read()
        form_data = acord_131_schema.loads(file)
    submission_id = uuid4()
    result = acord_mapper.process_response(
        ACORD_131_CONFIG,
        response_data=form_data,
        organization_id=ExistingOrganizations.MarkelDemo.value,
        submission_id=submission_id,
    )
    sub_entity_idx = next(
        (idx for idx, e in enumerate(result.raw_processed_data.entities) if e.type == SubmissionEntityType.SUBMISSION),
        None,
    )
    assert sub_entity_idx is not None
    assert_submission_field(
        result.raw_processed_data, EntityInformation.AGENCY, "RhodesAnderson Insurance", sub_entity_idx
    )
    assert_submission_field(result.raw_processed_data, EntityInformation.AGENCY_CUSTOMER_ID, "OREGO-1", sub_entity_idx)
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER, "Not determined", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data, EntityInformation.CARRIER_POLICY_NUMBER, "CPOR0560126029", sub_entity_idx
    )
    assert_submission_field(result.raw_processed_data, EntityInformation.CARRIER_NAIC_CODE, "125", sub_entity_idx)
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.AGENCY_DATE,
        "2024-07-09 00:00:00+00:00",
        sub_entity_idx,
    )
    assert_submission_field(
        result.raw_processed_data,
        EntityInformation.POLICY_EFFECTIVE_START_DATE,
        "2024-08-23 00:00:00+00:00",
        sub_entity_idx,
    )


def test_acord_131(app_context, acord_mapper, acord_131_schema):
    with open(f"tests/data/responses/acord_131_location_match.json") as f:
        file = f.read()
        form_data = acord_131_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_131_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )

    assert len([e for e in result.raw_processed_data.entities]) == 2
    assert result.raw_processed_data.entities[0].type == SubmissionEntityType.BUSINESS
    assert result.raw_processed_data.entities[1].type == SubmissionEntityType.SUBMISSION
    pools = next(val for val in result.raw_processed_data.fields if val.fact_subtype_id == "NUMBER_OF_SWIMMING_POOLS")
    assert len(pools.values) == 1
    assert pools.values[0].entity_idx == 0
    assert pools.values[0].entity_idx == 0


def test_acord_131_policy_extraction(app_context, acord_mapper, acord_131_schema):
    with open(f"tests/data/responses/acord_131_policy.json") as f:
        file = f.read()
        form_data = acord_131_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_131_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )
    transient_data = result.raw_processed_data.transient_data
    assert transient_data.policy_information is not None
    assert len(transient_data.policy_information) > 0
    assert transient_data.policy_information[0].new is False
    assert transient_data.policy_information[0].renewal is None
    assert transient_data.policy_information[0].umbrella is True
    assert transient_data.policy_information[0].excess is False
    assert transient_data.policy_information[0].occurrence is False
    assert transient_data.policy_information[0].claims_made is False
    assert transient_data.policy_information[0].voluntary is False
    assert transient_data.policy_information[0].limits_occurrence == 5000000.0
    assert transient_data.policy_information[0].limits_general_aggregate == 5000000.0
    assert transient_data.policy_information[0].proposed_retroactive_date == "2025-03-17"
    assert transient_data.policy_information[0].current_retroactive_date == "2025-03-17"
    assert transient_data.policy_information[0].retained_limit == 10000.0
    assert transient_data.policy_information[0].first_dollar_defense is True


def test_acord_131_with_polygons(app_context, acord_mapper, acord_131_schema):
    with open(f"tests/data/responses/acord_131_2009_10_polygons_full.json") as f:
        file = json.loads(f.read())
        output = file["documents"][0]["output"]
        form_data = acord_131_schema.loads(json.dumps(output["parsedDocument"]))
        page_metadata = get_page_metadata(output["text"])

    result = acord_mapper.process_response(
        ACORD_131_CONFIG, response_data=form_data, organization_id=1, page_metadata=page_metadata, submission_id=uuid4()
    )

    assert len([e for e in result.raw_processed_data.entities]) == 1
    assert result.raw_processed_data.entities[0].type == SubmissionEntityType.SUBMISSION
    assert len(result.raw_processed_data.fields) == 0


def test_acord_125_no_fni(app_context, acord_mapper, acord_125_schema, mocker):
    mocked = mocker.patch("copilot.clients.response_mappers._get_naics_code")
    mocked.return_value = None
    with open(f"tests/data/responses/acord_125_no_fni.json") as f:
        file = f.read()
        form_data = acord_125_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_125_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )

    assert len(result.raw_processed_data.entities) == 5
    assert (
        result.raw_processed_data.entities[0].entity_named_insured
        == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
    )
    assert (
        result.raw_processed_data.entities[1].entity_named_insured
        == SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED
    )
    name_field = next((f for f in result.raw_processed_data.entity_information if f.name == EntityFieldID.NAME), None)
    assert name_field
    assert next((v.value for v in name_field.values if v.entity_idx == 2), None) == "Vance & Hines Motorsports, Inc."
    assert result.raw_processed_data.entities[3].type == SubmissionEntityType.STRUCTURE


def test_acord_140_2014_12(app_context, acord_mapper, acord_140_schema, mocker):
    mocked = mocker.patch("copilot.clients.response_mappers._get_naics_code")
    mocked.return_value = None
    with open(f"tests/data/responses/acord_140_2014_12.json") as f:
        file = f.read()
        form_data = acord_140_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_140_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )

    assert len(result.raw_processed_data.entities) == 7
    assert result.raw_processed_data.entities[1].type == SubmissionEntityType.STRUCTURE
    name_field = next((f for f in result.raw_processed_data.entity_information if f.name == EntityFieldID.NAME), None)
    assert name_field
    assert next((v.value for v in name_field.values if v.entity_idx == 1), None) == "Devere Construction Inc"
    assert isinstance(result.transient_data_object, Acord140TransientData)
    # noinspection PyTypeChecker
    transient_data: Acord140TransientData = result.transient_data_object
    assert transient_data.premises_info


from static_common.enums.fact_subtype import FactSubtypeID


def test_acord_140_2016_03(app_context, acord_mapper, acord_140_schema, mocker):
    mocked = mocker.patch("copilot.clients.response_mappers._get_naics_code")
    mocked.return_value = None
    with open(f"tests/data/responses/acord_140_2016_03_2.json") as f:
        file = f.read()
        form_data = acord_140_schema.loads(file)

    result = acord_mapper.process_response(
        ACORD_140_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )

    assert form_data.building_information_list[0].description_of_property_covered is not None
    property_desc = [
        field
        for field in result.raw_processed_data.fields
        if field.fact_subtype_id == FactSubtypeID.PROPERTY_DESCRIPTION
    ]
    assert len(property_desc) == 1
    values = [v for v in property_desc[0].values if v.entity_idx == 1]
    assert len(values) == 1
    assert values[0].value == "This should be picked"

    form_data.building_information_list[0].building_description = None
    result = acord_mapper.process_response(
        ACORD_140_CONFIG, response_data=form_data, organization_id=1, submission_id=uuid4()
    )
    property_desc = [
        field
        for field in result.raw_processed_data.fields
        if field.fact_subtype_id == FactSubtypeID.PROPERTY_DESCRIPTION
    ]
    assert len(property_desc) == 1
    values = [v for v in property_desc[0].values if v.entity_idx == 1]
    assert len(values) == 1
    assert values[0].value == "Spoilage/Special (Including theft) - Detail"


def test_acord_125_coverages(app_context, acord_mapper, organization, coverages):
    lines_of_business = [
        AcordLinesOfBusinessType(
            crime=SensibleBoolValue(value=True),
            generalLiability=SensibleBoolValue(value=True),
            businessAuto=SensibleBoolValue(value=True),
            property=SensibleBoolValue(value=True),
            cyberPrivacy=SensibleBoolValue(value=False),
            umbrella=SensibleBoolValue(value=True),
        )
    ]
    coverages = acord_mapper._extract_coverages(lines_of_business=lines_of_business, additional_coverage_data=None)
    assert coverages
    assert len(coverages) == 5

    expected_coverages = [
        (CoverageName.Crime, CoverageType.PRIMARY),
        (CoverageName.Liability, CoverageType.PRIMARY),
        (CoverageName.Liability, CoverageType.EXCESS),
        (CoverageName.BusinessAuto, CoverageType.EXCESS),
        (CoverageName.Property, None),
    ]

    for coverage in coverages:
        assert (coverage.coverage_name, coverage.coverage_type) in expected_coverages
        expected_coverages.remove((coverage.coverage_name, coverage.coverage_type))

    assert not expected_coverages


def test_acord_126_get_static_coverages(app_context, acord_mapper, organization, coverages):
    coverage_assignment = [
        CoverageAssignment(
            coverage_name=CoverageName.Liability,
            coverage_type=CoverageType.PRIMARY,
        ),
        # should not be assigned as it's specific to organization 1
        CoverageAssignment(
            coverage_name=CoverageName.Liability, coverage_type=CoverageType.EXCESS, apply_for_organizations=[1]
        ),
    ]
    coverages = acord_mapper._process_static_coverages(coverage_assignment, organization.id)
    assert coverages
    assert len(coverages) == 1
    assert coverages[0].coverage_name == CoverageName.Liability
    assert coverages[0].coverage_type == CoverageType.PRIMARY


def test_applied_98(app_context, acord_mapper, applied_98_schema):
    with open(f"tests/data/responses/applied_98.json") as f:
        file = f.read()
        form_data = applied_98_schema.loads(file)

    result = acord_mapper.process_response(
        APPLIED_98_CONFIG, response_data=form_data, organization_id=37, submission_id=uuid4()
    )
    onboarded_file = result.raw_processed_data

    assert len(onboarded_file.fields) == 1
    assert onboarded_file.fields[0].name == "Additional Coverages"
    assert len(onboarded_file.fields[0].values[0].value) == 187
