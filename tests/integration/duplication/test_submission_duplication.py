from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch
from uuid import uuid4

from static_common.enums.business_resolve import BusinessesResolvingState
from static_common.enums.contractor import ContractorSubmissionType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.insurance import ProjectInsuranceType
from static_common.enums.origin import Origin
from static_common.enums.submission import SubmissionMode, SubmissionStage
from static_common.enums.submission_processing_state import SubmissionProcessingState
import pytest

from copilot.models import File, Organization, Settings, Submission, User, db
from copilot.models.reports import SubmissionFieldSource
from tests.integration.factories import (
    organization_fixture,
    report_and_submission_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj
from tests.unit.duplication import check_copy

# fields which are skipped because they are:
# - Are relations
# - Virtual columns
# - Not copied
# - etc..
SKIPPED_FIELDS = {
    "client_stage",
    "client_stage_id",
    "created_at",
    "updated_at",
    "policies",
    "feedback",
    "coverages",
    "underlying_policies",
    "deductibles",
    "businesses",
    "notebook_threads",
    "report",
    "clearing_issues",
    "lob",
    "agent",
    "agency",
    "broker",
    "brokerage",
    "client_submission_ids",
    "bookmarks",
    "submission_notes",
    "files",
    "user",
    "lob_id",
    "agent_id",
    "agency_id",
    "correspondence_contact_id",
    "brokerage_id",
    "broker_id",
    "broker_v2_id",
    "brokerage_contact_id",
    "decline_email_sent",
    "send_decline_email",
    "decline_email_tracking_id",
    "decline_email_delivered",
    "send_decline_email_error",
    "active_email_template_id",
    "decline_email_recipient_address",
    "is_auto_processed",
    "assigned_underwriters",
    "decline_email_cc",
    "decline_custom_template",
    "decline_attachments",
    "decline_custom_subject",
    "is_waiting_for_auto_verify",
    "_verified_at",
    "_auto_verified_at",
    "_manual_verified_at",
    "_stuck_reason",
    "pds_special_note",
    "is_for_audit",
    "audited_at",
    "audited_by",
    "verification_checks",
    "priority",
    "manual_naics_assignment_required",
    "missing_documents",
    "_is_stuck_engineering",
    "is_shadow_processed",
    "read_by_users",
    "missing_data_status",
    "is_processing",
    "correspondence_contact",
    "brokerage_contact",
    "sent_rule_email",
    "stuck_details",
    "has_unique_submission_client_id",
    "has_been_synced",
    "owner_id",
    "alerts",
    "frozen_as_of",
    "is_escalated",
    "assigned_brokerage",
    "assigned_broker",
    "assigned_brokerage_contact",
    "is_metrics_set_manually",
    "workers_comp_experience",
    "workers_comp_rating_info",
    "identifiers",
    "is_verified",
    "is_manual_verified",
    "is_auto_verified",
    "submission_processing_list",
    "org_group",
    "client_stage_comment",
    "client_clearing_status",
    "clearing_status",
    "brokerage_office",
    "sub_producer_name",
    "sub_producer_email",
    "client_recommendations_score",
    "is_pre_renewal",
    "is_renewal_shell",
    "facts_config",
    "sic_code",
    "clearing_sub_statuses",
    "recommendation_result",
    "field_sources",
    "stage_details",
    "premises",
}

# fields which are copied but with different values or complex types (so we don't perform deep comparison)
NOT_NONE_FIELDS = {"id", "stage", "report_id", "synced_at", "field_sources"}

DIFFERENT_FIELDS_IF_NONE_VALUES = {
    "account_name",
    "renewal_creation_date",
    "declined_user_id",
    "declined_date",
    "reason_for_declining",
    "is_deleted",
    "is_renewal",
    "parent_id",
    "coverage_type",
    "is_verification_required",
    "is_verified_shell",
    "_processing_state",
    "businesses_resolving_state",
    "origin",
    "lost_reasons",
    "is_enhanced_shell",
    "recommendation_v2_action",
    "recommendation_v2_priority",
    "recommendation_v2_score",
    "light_cleared",
}

DIFFERENT_FIELDS_IF_NOT_NONE_VALUES = DIFFERENT_FIELDS_IF_NONE_VALUES | {
    "agent_id",
    "agency_id",
    "correspondence_contact_id",
    "proposed_effective_date",
    "brokerage_id",
    "broker_id",
    "broker_v2_id",
    "brokerage_contact_idproposed_effective_date",
    "policy_expiration_date",
    "due_date",
    "received_date",
    "is_shadow_processed",
}

DIFFERENT_FIELDS_IF_DIFFERENT_OWNER = {
    "owner_id",
    "agent_id",
    "agency_id",
    "correspondence_contact_id",
    "brokerage_id",
    "broker_id",
    "broker_v2_id",
    "brokerage_contact_id",
    "notes",
    "account_id",
    "recommendation_v2_action",
    "recommendation_v2_priority",
    "recommendation_v2_score",
    "org_group",
}


@pytest.fixture
def original(mocker) -> Submission:
    original = Submission()
    mocker.patch("copilot.models.reports.Submission.copy_losses", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_loss_policies", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_workers_comp_experience", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_workers_comp_state_rating_info", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_ifta_data", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_submission_relations", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_shareholders", return_value=None)
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    original.id = uuid4()
    original.report_id = uuid4()
    original.name = "Submission"
    original.owner_id = 1
    original.account_name = "account name"
    original.stage = SubmissionStage.WAITING_FOR_OTHERS
    original.stage_details = {"stage_details": "Some details"}
    original.proposed_effective_date = datetime.now()
    original.policy_expiration_date = datetime.now()
    original.due_date = datetime.now()
    original.received_date = datetime.now()
    original.is_deleted = False
    original.description_of_operations = "Description of operations"
    original.generated_description_of_operations = "generated_description_of_operations"
    original.email_description = "email description"
    original.email_project_description = "email project description"
    original.is_renewal = True
    original.renewal_creation_date = datetime.now()
    original.declined_user_id = 5
    original.clearing_assignee_id = 5
    original.declined_date = datetime.now()
    original.reason_for_declining = "Reason for declining"
    original.parent_id = uuid4()
    original.lob_id = uuid4()
    original.agent_id = uuid4()
    original.agency_id = uuid4()
    original.mode = SubmissionMode.STANDARD
    original.coverage_type = "Coverage type"
    original.primary_naics_code = "NAICS_123456"
    original.iso_gl_code = "ISO_GL_12345"
    original.sic_code = "SIC_0116"
    original.icc_code = "ICC_421"
    original.is_naics_verified = True
    original.is_verified = True
    original.is_auto_verified = True
    original.is_manual_verified = True
    original.is_verification_required = True
    original.is_verified_shell = False
    original.processing_state = SubmissionProcessingState.PROCESSING
    original.businesses_resolving_state = BusinessesResolvingState.RESOLVING
    original.origin = Origin.API
    original.lost_reasons = ["reason 1", "reason 2"]
    original.notes = "hello"
    original.recommendation_v2_priority = 1000
    original.recommendation_v2_action = "ACCEPT"
    original.recommendation_v2_is_refer = False
    original.recommendation_v2_score = 60
    original.account_id = "123"
    original.verified_at = datetime.now()
    original.light_cleared = True
    original.target_premium = 28700
    original.expired_premium = 70000.5
    original.sales = 42510.5
    original.is_shadow_processed = True
    original.correspondence_contact_id = uuid4()
    original.sent_rule_email = "Rule 1"
    original.synced_at = datetime.now()
    original.broker_id = uuid4()
    original.brokerage_contact_id = uuid4()
    original.brokerage_id = uuid4()
    original.quoted_date = datetime.now()
    original.bound_date = datetime.now()
    original.cleared_date = datetime.now()
    original.contractor_submission_type = ContractorSubmissionType.PROJECT
    original.project_insurance_type = ProjectInsuranceType.WRAP_UP
    original.incumbent_carrier = [{"name": "Incumbent Carrier"}]
    original.fni_state = "WA"
    original.is_enhanced_shell = False
    original.fni_fein = "12-123123"
    original.adjusted_tiv = 1
    original.tiv = 10
    original.primary_state = "CA"
    original.org_group = "test"
    original.client_stage_comment = "Comment"
    original.client_clearing_status = "Cleared"
    original.clearing_status = "CLEARED"
    original.brokerage_office = "Brokerage Office"
    original.sub_producer_name = "SUB Producer Name"
    original.sub_producer_email = "<EMAIL>"
    original.prevent_clearing_updates = True
    original.policy_status = "Active"
    original.lookalike_bind_rate = 0.2137
    original.is_stub = False
    original.field_sources = [
        SubmissionFieldSource(id=uuid4(), field_name="primary_naics_code", source="MANUAL"),
        SubmissionFieldSource(id=uuid4(), field_name="sic_code", source="AUTO"),
        SubmissionFieldSource(id=uuid4(), field_name="iso_gl_code", source="TAXONOMY_SYNC"),
    ]
    return original


@pytest.fixture
def copy_for_processing_with_empty_values(mocker) -> Submission:
    copy_date = datetime.now() + timedelta(minutes=1)
    copy = Submission()
    mocker.patch("copilot.models.reports.Submission.copy_losses", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_loss_policies", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_workers_comp_experience", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_workers_comp_state_rating_info", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_ifta_data", return_value=None)
    mocker.patch("copilot.models.reports.Submission.copy_shareholders", return_value=None)
    mocker.patch("copilot.models._private.db.session.add", return_value=None)
    copy.id = uuid4()
    copy.name = "Copy with empty values"
    copy.owner_id = 2
    copy.account_name = "account name copy"
    copy.stage = SubmissionStage.ON_MY_PLATE
    copy.stage_details = {"stage_details": "Some details copy"}
    copy.is_deleted = True
    copy.is_renewal = False
    copy.renewal_creation_date = copy_date
    copy.declined_user_id = 6
    copy.clearing_assignee_id = 5
    copy.declined_date = copy_date
    copy.reason_for_declining = "Reason for declining copy"
    copy.parent_id = uuid4()
    copy.lob_id = uuid4()
    copy.coverage_type = "Coverage type copy"
    copy.primary_naics_code = "NAICS_111111"
    copy.iso_gl_code = "ISO_GL_12345"
    copy.icc_code = "ICC_421"
    copy.sic_code = "SIC_0116"
    copy.is_naics_verified = False
    copy.is_verified = False
    copy.is_auto_verified = False
    copy.is_manual_verified = False
    copy.is_verification_required = False
    copy.is_verified_shell = True
    copy.processing_state = SubmissionProcessingState.NOT_STARTED
    copy.businesses_resolving_state = BusinessesResolvingState.NOT_RESOLVED
    copy.origin = Origin.EMAIL
    copy.lost_reasons = ["reason 1"]
    copy.notes = "hello copy"
    copy.recommendation_v2_priority = 1
    copy.recommendation_v2_action = "DECLINE"
    copy.recommendation_v2_is_refer = False
    copy.recommendation_v2_score = 1
    copy.account_id = "344"
    copy.synced_at = datetime.now()
    copy.quoted_date = datetime.now()
    copy.bound_date = datetime.now()
    copy.cleared_date = datetime.now()
    copy.contractor_submission_type = ContractorSubmissionType.PROJECT
    copy.project_insurance_type = ProjectInsuranceType.WRAP_UP
    copy.incumbent_carrier = [{"name": "Incumbent Carrier"}]
    copy.is_enhanced_shell = True
    copy.primary_state = "CA"
    copy.light_cleared = False
    copy.policy_status = "Active"
    copy.lookalike_bind_rate = 0.2137
    copy.is_stub = False

    settings = Settings(loss_runs_enabled=True)
    organization = Organization(settings=settings)
    copy.user = User()
    copy.user.organization = organization
    return copy


@pytest.fixture
def copy_for_processing_without_empty_values(copy_for_processing_with_empty_values: Submission) -> Submission:
    copy = copy_for_processing_with_empty_values
    copy_date = datetime.now() + timedelta(minutes=1)
    copy.proposed_effective_date = copy_date
    copy.policy_expiration_date = copy_date
    copy.due_date = copy_date
    copy.received_date = copy_date
    return copy


def test_copy_submission(app_context, mocker, original: Submission) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    original.user = User()
    copy = original.copy({original.report_id: uuid4()})
    check_copy(Submission, original, copy, SKIPPED_FIELDS, NOT_NONE_FIELDS)


def test_copy_processing_data_to_with_empty_values_same_owner(
    app_context, mocker, original: Submission, copy_for_processing_with_empty_values: Submission
) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    old_to_new_ids = {original.report_id: uuid4()}
    copy_for_processing_with_empty_values.owner_id = original.owner_id
    original.copy_processing_data_to(copy_for_processing_with_empty_values, old_to_new_ids)
    check_copy(
        Submission,
        original,
        copy_for_processing_with_empty_values,
        SKIPPED_FIELDS,
        NOT_NONE_FIELDS,
        DIFFERENT_FIELDS_IF_NONE_VALUES,
    )


def test_copy_processing_data_to_without_empty_values_same_owner(
    app_context, mocker, original: Submission, copy_for_processing_without_empty_values: Submission
) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    old_to_new_ids = {original.report_id: uuid4()}
    copy_for_processing_without_empty_values.owner_id = original.owner_id
    original.copy_processing_data_to(copy_for_processing_without_empty_values, old_to_new_ids)
    check_copy(
        Submission,
        original,
        copy_for_processing_without_empty_values,
        SKIPPED_FIELDS,
        NOT_NONE_FIELDS,
        DIFFERENT_FIELDS_IF_NOT_NONE_VALUES,
    )


def test_copy_processing_data_to_without_empty_values_API_origin(
    app_context, mocker, original: Submission, copy_for_processing_without_empty_values: Submission
) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    old_to_new_ids = {original.report_id: uuid4()}
    copy_for_processing_without_empty_values.owner_id = original.owner_id
    copy_for_processing_without_empty_values.origin = Origin.API
    original.origin = Origin.EMAIL
    original.copy_processing_data_to(copy_for_processing_without_empty_values, old_to_new_ids)
    check_copy(
        Submission,
        original,
        copy_for_processing_without_empty_values,
        SKIPPED_FIELDS,
        NOT_NONE_FIELDS,
        DIFFERENT_FIELDS_IF_NOT_NONE_VALUES | {"name"},
    )


def test_copy_processing_data_to_with_empty_values_different_owner(
    app_context, mocker, original: Submission, copy_for_processing_with_empty_values: Submission
) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    old_to_new_ids = {original.report_id: uuid4()}
    original.copy_processing_data_to(copy_for_processing_with_empty_values, old_to_new_ids)
    check_copy(
        Submission,
        original,
        copy_for_processing_with_empty_values,
        SKIPPED_FIELDS,
        NOT_NONE_FIELDS,
        DIFFERENT_FIELDS_IF_NONE_VALUES | DIFFERENT_FIELDS_IF_DIFFERENT_OWNER,
    )


def test_copy_processing_data_to_without_empty_values_different_owner(
    app_context, mocker, original: Submission, copy_for_processing_without_empty_values: Submission
) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    old_to_new_ids = {original.report_id: uuid4()}
    original.copy_processing_data_to(copy_for_processing_without_empty_values, old_to_new_ids)
    check_copy(
        Submission,
        original,
        copy_for_processing_without_empty_values,
        SKIPPED_FIELDS,
        NOT_NONE_FIELDS,
        DIFFERENT_FIELDS_IF_NOT_NONE_VALUES | DIFFERENT_FIELDS_IF_DIFFERENT_OWNER,
    )


def test_copy_processing_data_with_email_files(
    app_context, mocker, original: Submission, copy_for_processing_with_empty_values: Submission
) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    old_to_new_ids = {original.report_id: uuid4()}
    copy_for_processing_with_empty_values.files = [
        File(name="email_body.pdf", file_type=FileType.EMAIL, processing_state=FileProcessingState.CLASSIFIED),
        File(
            name="email_body2.pdf",
            file_type=FileType.EMAIL,
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
        ),
    ]
    original.copy_processing_data_to(copy_for_processing_with_empty_values, old_to_new_ids)
    assert copy_for_processing_with_empty_values.files[0].processing_state == FileProcessingState.CLASSIFIED
    assert copy_for_processing_with_empty_values.files[1].processing_state == FileProcessingState.COMPLETED


def _get_uuid(last_digit: int) -> str:
    return f"00000000-0000-0000-0000-00000000000{last_digit}"


def _get_files(status, file_type, parent_file_id, number_of_files) -> list[File]:
    files = []
    for i in range(1, number_of_files + 1):
        files.append(
            File(
                id=_get_uuid(i),
                name=f"sov{i}.csv",
                file_type=file_type,
                processing_state=status,
                checksum=str(i),
                parent_file_id=parent_file_id,
                s3_key=f"sov{i}.csv",
            )
        )
    return files


def test_copy_processing_data_with_existing_files(
    app_context, mocker, original: Submission, copy_for_processing_with_empty_values: Submission
) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    mocked_events_handler = MagicMock()
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event",
        mocked_events_handler,
    )
    mocker.patch("flask.request", AnonObj(path="/v3/reports/1234/copy_processing_data"))

    old_to_new_ids = {original.report_id: uuid4()}
    parent_file_id = uuid4()
    original_files = [
        File(
            id=uuid4(),
            name="email_body.pdf",
            file_type=FileType.EMAIL,
            processing_state=FileProcessingState.COMPLETED,
            checksum="email_original",
            s3_key="email_body.pdf",
        ),
        File(
            id=parent_file_id,
            name="sov.csv",
            file_type=FileType.MERGED,
            processing_state=FileProcessingState.COMPLETED,
            checksum=str(parent_file_id),
            s3_key="sov.csv",
        ),
    ]
    original_files.extend(_get_files(FileProcessingState.COMPLETED, FileType.SOV, parent_file_id, 5))
    original.files = original_files
    copy_for_processing_with_empty_values.files = [
        File(
            id=uuid4(),
            name="email_body.pdf",
            file_type=FileType.EMAIL,
            processing_state=FileProcessingState.CLASSIFIED,
            checksum="email_copy",
            s3_key="email_body.pdf",
        ),
        File(
            id=uuid4(),
            name="sov.csv",
            file_type=FileType.UNKNOWN,
            processing_state=FileProcessingState.NOT_CLASSIFIED,
            checksum=str(parent_file_id),
            s3_key="sov.csv",
        ),
    ]

    file_list = [*original_files, *copy_for_processing_with_empty_values.files]

    def file_generator():
        yield from file_list

    file_gen = file_generator()

    def next_file(*args, **kwargs):
        return MagicMock(
            options=MagicMock(
                return_value=MagicMock(filter=MagicMock(return_value=MagicMock(first=lambda: next(file_gen))))
            )
        )

    with patch("copilot.models._private.db.session.query", side_effect=next_file):
        original.copy_processing_data_to(copy_for_processing_with_empty_values, old_to_new_ids)
    assert len(copy_for_processing_with_empty_values.files) == 7
    assert copy_for_processing_with_empty_values.files[0].processing_state == FileProcessingState.CLASSIFIED
    assert copy_for_processing_with_empty_values.files[1].file_type == FileType.MERGED
    for i in range(1, 6):
        assert copy_for_processing_with_empty_values.files[i].processing_state == FileProcessingState.COMPLETED
    assert mocked_events_handler.called


def test_copy_processing_data_to_report_twice(app_context, mocker) -> None:
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    org = organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    db.session.commit()

    report_copy = report.copy()
    db.session.commit()

    report.copy_processing_data_to(report_copy)
    db.session.commit()
