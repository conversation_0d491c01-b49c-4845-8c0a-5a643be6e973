from collections.abc import Callable
from unittest.mock import MagicMock, patch
import json
import pickle

from bs4 import BeautifulSoup
from common.clients.arch_api_client import ArchApiClient
from common.clients.smtp import SMTPNotificationsClient
from sendgrid import From, Mail, Subject, To
from sendgrid.helpers.mail.mail import Mail
from static_common.enums.emails import EmailType
from static_common.enums.organization import ExistingOrganizations
import pytest

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.models import BrokerageEmployee, Organization, Submission, User, db
from copilot.models.email_template import EmailTemplate
from copilot.models.emails import (
    Email,
    EmailStatus,
    ReportEmailCorrespondence,
    SendgridEmailData,
)
from copilot.models.sent_email import SentEmail
from copilot.models.types import EmailTemplateType
from copilot.notifications.email_data import EmailAddress, EmailData
from copilot.notifications.errors import SendNotificationError
from copilot.notifications.sendgrid_notification_client import (
    SendgridNotificationsClient,
    SendgridTemplateIds,
)
from copilot.notifications.submission_notification_handler_v2 import (
    NATIONWIDE_SENDER_EMAIL,
    NotificationHandlerV2,
)
from tests.integration.factories import (
    broker_fixture,
    brokerage_fixture,
    email_fixture,
    email_template_fixture,
    organization_fixture,
    report_and_submission_fixture,
    report_email_correspondence_fixture,
    submission_fixture,
    submission_user_fixture,
    user_fixture,
)


@pytest.fixture
def mock_sendgrid_notifications_client(app_context):
    mock = MagicMock(spec=SendgridNotificationsClient)
    app_context.sendgrid_notifications_client = mock
    return mock


@pytest.fixture
def mock_nationwide_smtp_notifications_client(app_context):
    mock = MagicMock(spec=SMTPNotificationsClient)
    app_context.nationwide_smtp_notifications_client = mock
    return mock


@pytest.fixture
def submission_with_correspondence(app_context):
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()
    report.correspondence = ReportEmailCorrespondence(recipient_address="<EMAIL>")
    report.correspondence_id = report.correspondence.id
    db.session.commit()

    return submission


@pytest.fixture
def submission_without_correspondence(app_context):
    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture()
    db.session.commit()

    return submission


def get_handler(
    env: str = "test",
    nationwide_smtp_notifications_client: SMTPNotificationsClient | None = None,
) -> "NotificationHandlerV2":
    # due to the usage of @fallback_to we need to import this here,
    # otherwise we get "Working outside of application context error"
    from copilot.notifications.submission_notification_handler_v2 import (
        NotificationHandlerV2,
    )

    return NotificationHandlerV2(
        env,
        mock_sendgrid_notifications_client,
        nationwide_smtp_notifications_client or mock_nationwide_smtp_notifications_client,
        None,
    )


def test_get_or_create_correspondence_id_already_existing(app_context, submission_with_correspondence):
    assert (
        get_handler().get_or_create_correspondence_id(submission_with_correspondence)
        == submission_with_correspondence.report.correspondence.id
    )


def test_get_or_create_correspondence_id_create_new(app_context, submission_without_correspondence):
    assert submission_without_correspondence.report.correspondence_id is None
    correspondence_id = get_handler().get_or_create_correspondence_id(submission_without_correspondence)
    assert submission_without_correspondence.report.correspondence_id == correspondence_id


@pytest.mark.parametrize(
    ["custom_template", "custom_subject", "email_template", "expected_html_content", "expected_subject"],
    [
        [
            None,
            None,
            EmailTemplate(subject="Test", html_content="Hello {{user_name}}, the email is {{user_email}}"),
            "Hello Test User, the <NAME_EMAIL>",
            "Test",
        ],
        [
            "Different template for {{user_name}} and {{user_email}}",
            "Other subject",
            EmailTemplate(subject="Test", html_content="Hello {{user_name}}, the email is {{user_email}}"),
            "Different template for Test <NAME_EMAIL>",
            "Other subject",
        ],
    ],
)
def test_render_template(
    app_context,
    custom_template: str | None,
    custom_subject: str | None,
    email_template: EmailTemplate,
    expected_html_content: str,
    expected_subject: str,
):
    email_data = EmailData(dynamic_template_data={"user_name": "Test User", "user_email": "<EMAIL>"})

    assert email_data.html_content is None
    assert email_data.subject is None

    get_handler().render_template(email_data, custom_template, custom_subject, email_template)

    assert email_data.html_content == expected_html_content
    assert email_data.subject == expected_subject


@pytest.mark.parametrize(
    ["template", "sending_user_name", "override_display_name", "expected_from_email"],
    [
        [None, "Test User 2", None, "Test User 2 via Copilot"],
        [EmailTemplate(type=EmailTemplateType.RECOMMENDATIONS), "Test User 2", "Test override", "Test override"],
        [EmailTemplate(type=EmailTemplateType.RECOMMENDATIONS), "Test User 2", None, "Test User 1 via Copilot"],
        [
            EmailTemplate(type=EmailTemplateType.RECOMMENDATIONS, reply_to="<EMAIL>"),
            "Test User 2",
            None,
            "Copilot",
        ],
    ],
)
def test_add_sender_data(
    app_context,
    template: EmailTemplate | None,
    sending_user_name: str | None,
    override_display_name: str | None,
    expected_from_email: str,
):
    organization_fixture()
    user = user_fixture(id=1, name="Test User 1")
    sending_user = user_fixture(id=2, name=sending_user_name)
    _, submission = report_and_submission_fixture()
    su = submission_user_fixture(user_id=user.id, submission_id=submission.id)
    submission.assigned_underwriters.append(su)
    db.session.commit()

    email_data = EmailData()

    get_handler().add_sender_data(email_data, template, submission, sending_user, override_display_name)

    assert email_data.from_name == expected_from_email
    assert email_data.from_email == "<EMAIL>"


def test_add_sender_data_for_nationwide(app_context):
    organization_fixture()
    user = user_fixture(id=1, name="Test User 1")
    sending_user = user_fixture(id=2, name="Sending User")
    _, submission = report_and_submission_fixture()
    su = submission_user_fixture(user_id=user.id, submission_id=submission.id)
    submission.assigned_underwriters.append(su)
    db.session.commit()

    email_data = EmailData()

    handler = get_handler()
    handler._is_nationwide_with_smtp = MagicMock(return_value=True)
    handler.add_sender_data(email_data, None, submission, sending_user, None)

    assert email_data.from_name == "Sending User"  # without "via Copilot"
    assert email_data.from_email == "<EMAIL>"


@pytest.mark.parametrize(
    [
        "recipient_emails",
        "cc_emails",
        "submission_fixture_name",
        "user_email",
        "use_broker",
        "use_correspondence_contact",
        "template",
        "expected_to_emails",
        "expected_ccs",
    ],
    [
        # explicit recipients
        [
            ["test@<EMAIL>"],
            ["<EMAIL>"],
            "submission_with_correspondence",
            "<EMAIL>",
            False,
            False,
            None,
            ["test@<EMAIL>"],
            [
                EmailAddress("<EMAIL>", "Test User"),
                EmailAddress("<EMAIL>"),
            ],
        ],
        [
            # no recipients, but report with correspondence
            [],
            ["<EMAIL>"],
            "submission_with_correspondence",
            "<EMAIL>",
            False,
            False,
            None,
            ["<EMAIL>"],
            [
                EmailAddress("<EMAIL>", "Test User"),
                EmailAddress("<EMAIL>"),
            ],
        ],
        [
            # no recipients or correspondence, but broker and brokerage contact
            [],
            ["<EMAIL>"],
            "submission_without_correspondence",
            "<EMAIL>",
            True,
            True,
            None,
            ["<EMAIL>"],
            [
                EmailAddress("<EMAIL>", "Test User"),
                EmailAddress("<EMAIL>"),
            ],
        ],
        [
            # no recipients, correspondence or brokerage contact, only broker
            [],
            ["<EMAIL>"],
            "submission_without_correspondence",
            "<EMAIL>",
            True,
            False,
            None,
            ["<EMAIL>"],
            [
                EmailAddress("<EMAIL>", "Test User"),
                EmailAddress("<EMAIL>"),
            ],
        ],
        [
            # same as above, with RECOMMENDATION template
            [],
            ["<EMAIL>"],
            "submission_without_correspondence",
            "<EMAIL>",
            True,
            False,
            EmailTemplate(type=EmailTemplateType.RECOMMENDATIONS),
            ["<EMAIL>"],
            [
                EmailAddress("<EMAIL>"),
            ],
        ],
        [
            # empty cc_list with correspondence_contact, shouldn't add broker to cc
            [],
            [],
            "submission_without_correspondence",
            "<EMAIL>",
            True,
            True,
            None,
            ["<EMAIL>"],
            [
                EmailAddress("<EMAIL>", "Test User"),
            ],
        ],
        [
            # None cc_list with correspondence_contact, should add broker to cc
            [],
            None,
            "submission_without_correspondence",
            "<EMAIL>",
            True,
            True,
            None,
            ["<EMAIL>"],
            [
                EmailAddress("<EMAIL>", "Test Broker"),
                EmailAddress("<EMAIL>", "Test User"),
            ],
        ],
    ],
)
def test_add_recipient_data(
    app_context,
    recipient_emails: list[str],
    cc_emails: list[str] | None,
    submission_fixture_name: str,
    user_email: str,
    use_broker: bool,
    use_correspondence_contact: bool,
    template: EmailTemplate | None,
    expected_to_emails: list[str],
    expected_ccs: list[EmailAddress],
    request,
):
    submission = request.getfixturevalue(submission_fixture_name)
    user = user_fixture(id=21, email=user_email, name="Test User")
    email_data = EmailData()

    brokerage = brokerage_fixture(
        organization_id=1,
    )
    broker = None
    if use_broker:
        broker = broker_fixture(
            organization_id=1, brokerage_id=brokerage.id, name="Test Broker", email="<EMAIL>"
        )

    correspondence_contact = None
    if use_correspondence_contact:
        correspondence_contact = broker_fixture(
            organization_id=1, brokerage_id=brokerage.id, name="Test Brokerage CC", email="<EMAIL>"
        )

    get_handler().add_recipient_data(
        email_data, recipient_emails, cc_emails, submission, user, broker, correspondence_contact, template, True
    )

    assert len(email_data.to_emails) == len(expected_to_emails)
    assert all(e in email_data.to_emails for e in expected_to_emails)
    assert len(email_data.cc_emails) == len(expected_ccs)
    assert all(e in email_data.cc_emails for e in expected_ccs)


def _prepare_db(organization_id, broker_email: str | None = None, use_submission_user: bool = True) -> tuple:
    organization = organization_fixture(id=organization_id)
    user = user_fixture(organization_id=organization.id, name="Test User", email="<EMAIL>")
    report, submission = report_and_submission_fixture(
        organization_id=organization.id,
        name="Test Submission",
        email_references="test_references",
        email_message_id="test_email_message_id",
    )
    brokerage = brokerage_fixture(
        organization_id=organization.id,
    )
    broker = broker_fixture(
        organization_id=organization.id,
        brokerage_id=brokerage.id,
        name="Test Broker",
        email=broker_email,
        roles=["AGENT"],
    )
    correspondence_contact = broker_fixture(
        organization_id=organization.id,
        brokerage_id=brokerage.id,
        name="Test Brokerage CC",
        email="<EMAIL>",
        roles=["CORRESPONDENCE_CONTACT"],
    )
    submission.set_brokerage(brokerage)
    submission.set_broker(broker)
    submission.set_brokerage_contact(correspondence_contact)
    if use_submission_user:
        su = submission_user_fixture(user_id=user.id, submission_id=submission.id)
        submission.assigned_underwriters.append(su)
    db.session.commit()

    return submission, user, broker, correspondence_contact


@pytest.mark.parametrize(
    [
        "organization_id",
        "additional_ccs",
        "reply_to_override",
        "display_name_override",
        "from_email",
        "from_name",
        "reply_to_list",
    ],
    [
        # generic submission notification
        [
            1,
            [],
            None,
            None,
            "<EMAIL>",
            "Test User via Copilot",
            [EmailAddress("<EMAIL>", "Test User")],
        ],
        # arch submission notification
        [
            ExistingOrganizations.Arch.value,
            [],
            None,
            None,
            "<EMAIL>",
            "Test User",
            [EmailAddress("<EMAIL>", "Test User")],
        ],
    ],
)
def test_create_preview_email_data_simple_email(
    app_context,
    organization_id,
    additional_ccs: list[EmailAddress],
    reply_to_override: str | None,
    display_name_override: str | None,
    from_email: str,
    from_name: str,
    reply_to_list: list[EmailAddress],
):
    submission, user, broker, correspondence_contact = _prepare_db(organization_id, "<EMAIL>")

    overrides = {}
    if display_name_override:
        overrides["display_name"] = display_name_override

    if reply_to_override:
        overrides["reply_to"] = reply_to_override

    custom_template = """
    Hello {{broker_name}},
    
    This email is concerning submission {{ submission_name }}, 
    which is being underwritten by {{ uw_name }} ({{ uw_email }}.
    
    Thanks,
    {{ user_name }}
    """
    email_data = get_handler().create_preview_email_data(
        submission, user, [], None, None, broker, correspondence_contact, True, None, None, custom_template, None, []
    )
    assert email_data
    assert email_data.to_emails == [correspondence_contact.email]
    assert email_data.subject == f"New message regarding {submission.name}"

    all_expected_ccs = [EmailAddress(broker.email, broker.name), EmailAddress(user.email, user.name)] + additional_ccs
    assert len(all_expected_ccs) == len(email_data.cc_email_addresses)
    for cc in all_expected_ccs:
        assert cc in email_data.cc_emails

    assert len(reply_to_list) == len(email_data.reply_to_emails)
    for reply_to in reply_to_list:
        assert reply_to in email_data.reply_to_emails

    assert email_data.from_email == from_email
    assert email_data.from_name == from_name
    assert email_data.references == "test_references"
    assert email_data.message_id == "test_email_message_id"

    expected_html_content = f"""
    Hello {broker.name},
    
    This email is concerning submission {submission.name}, 
    which is being underwritten by {user.name} ({user.email}.
    
    Thanks,
    {user.name}
    """
    assert email_data.html_content == expected_html_content


def test_create_recommendations_preview_email_with_recipient_address(app_context):
    submission, user, broker, _ = _prepare_db(1)
    to_address = "<EMAIL>"
    subject = "Template Subject"
    reply_to = "<EMAIL>"
    html_content = """
    Hello {{broker_name}},

    This email is concerning submission {{ submission_name }}, 
    which is being underwritten by {{ uw_name }} ({{ uw_email }}.

    Thanks,
    {{ user_name }}
    """
    template = email_template_fixture(
        type=EmailTemplateType.RECOMMENDATIONS,
        reply_to=reply_to,
        to_address=to_address,
        subject=subject,
        html_content=html_content,
    )
    email_data = get_handler().create_preview_email_data(
        submission, user, [], None, template, None, None, True, None, None, None, None, []
    )
    assert email_data
    assert email_data.to_emails == [to_address]
    assert email_data.subject == subject

    # recommendations email type adds assigned underwriter as only cc
    all_expected_ccs = [EmailAddress("<EMAIL>")]
    assert len(all_expected_ccs) == len(email_data.cc_email_addresses)
    for cc in all_expected_ccs:
        assert cc in email_data.cc_emails

    assert len(email_data.reply_to_emails) == 1
    assert reply_to == email_data.reply_to_emails[0].email

    assert email_data.references == "test_references"
    assert email_data.message_id == "test_email_message_id"


def test_create_preview_email_with_multiple_cc_addresses(app_context):
    submission, user, broker, _ = _prepare_db(1)
    to_address = "<EMAIL>"
    subject = "Template Subject"
    reply_to = "<EMAIL>"
    html_content = """
    Hello {{broker_name}},

    This email is concerning submission {{ submission_name }}, 
    which is being underwritten by {{ uw_name }} ({{ uw_email }}.

    Thanks,
    {{ user_name }}
    """
    template = email_template_fixture(
        reply_to=reply_to,
        to_address=to_address,
        subject=subject,
        html_content=html_content,
        cc_addresses=["<EMAIL>", "<EMAIL>"],
    )
    email_data = get_handler().create_preview_email_data(
        submission, user, [], None, template, None, None, True, None, None, None, None, []
    )
    assert email_data
    assert email_data.to_emails == [to_address]
    assert email_data.subject == subject

    all_expected_ccs = [EmailAddress("<EMAIL>"), EmailAddress("<EMAIL>")]
    assert len(all_expected_ccs) == len(email_data.cc_email_addresses)
    for cc in all_expected_ccs:
        assert cc in email_data.cc_emails

    assert len(email_data.reply_to_emails) == 1
    assert reply_to == email_data.reply_to_emails[0].email

    assert email_data.references == "test_references"
    assert email_data.message_id == "test_email_message_id"


def test_send_submission_notification_recommendations_preview_email_no_assigned_underwriters(app_context):
    submission, user, broker, correspondence_contact = _prepare_db(1, use_submission_user=False)
    to_address = "<EMAIL>"
    subject = "Template Subject"
    reply_to = "<EMAIL>"
    html_content = """
    Hello {{broker_name}},

    This email is concerning submission {{ submission_name }}, 
    which is being underwritten by {{ uw_name }} ({{ uw_email }}.

    Thanks,
    {{ user_name }}
    """
    template = email_template_fixture(
        reply_to=reply_to,
        to_address=to_address,
        subject=subject,
        html_content=html_content,
        type="RECOMMENDATIONS",
    )
    email_data = get_handler().send_submission_notification(
        user,
        submission,
        template_id=template.id,
        preview_only=True,
    )
    assert email_data
    assert set(email_data.emails_to) == {to_address}
    assert email_data.subject == subject

    assert email_data.email_reply_to == reply_to
    assert not email_data.scheduled


def test_send_submission_notification_nationwide_recommendations_preview_email(app_context):
    submission, _, broker, correspondence_contact = _prepare_db(
        6, broker_email="<EMAIL>", use_submission_user=True
    )

    user = user_fixture()

    to_address = "<EMAIL>"
    cc_address = "<EMAIL>"
    subject = "Template Subject"
    reply_to = "<EMAIL>"
    html_content = """
    Hello {{broker_name}},

    This email is concerning submission {{ submission_name }}, 
    which is being underwritten by {{ uw_name }} ({{ uw_email }}.

    Thanks,
    {{ user_name }}
    """
    template = email_template_fixture(
        reply_to=reply_to,  # this should override the default assigned uw email address
        to_address=to_address,  # this should override broker email
        cc_addresses=[cc_address],  # this should NOT override the default assigned uw address
        subject=subject,
        html_content=html_content,
        type="RECOMMENDATIONS",
    )
    with patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled", return_value=True):
        preview_data = get_handler().send_submission_notification(
            user,
            submission,
            template_id=template.id,
            preview_only=True,
        )
        email_data = get_handler().send_submission_notification(
            user,
            submission,
            template_id=template.id,
            preview_only=False,
        )
        assert email_data
        assert set(email_data.emails_to) == set(preview_data.emails_to)
        assert set(email_data.emails_to) == {cc_address, user.email, "<EMAIL>", to_address}
        assert email_data.email_from == preview_data.email_from
        assert email_data.email_from == NATIONWIDE_SENDER_EMAIL["_default"]
        assert email_data.subject == subject

        assert email_data.email_reply_to == reply_to
        assert not email_data.scheduled


def test_create_preview_email_data_with_no_broker_email(app_context):
    submission, user, broker, correspondence_contact = _prepare_db(1)

    custom_template = """
    Hello {{broker_name}},
    
    This email is concerning submission {{ submission_name }}, 
    which is being underwritten by {{ uw_name }} ({{ uw_email }}.
    
    Thanks,
    {{ user_name }}
    """
    email_data = get_handler().create_preview_email_data(
        submission, user, [], None, None, broker, correspondence_contact, True, None, None, custom_template, None, []
    )
    assert email_data
    assert email_data.to_emails == [correspondence_contact.email]
    assert email_data.subject == f"New message regarding {submission.name}"

    all_expected_ccs = [EmailAddress(user.email, user.name)]
    assert len(all_expected_ccs) == len(email_data.cc_email_addresses)
    for cc in all_expected_ccs:
        assert cc in email_data.cc_emails

    expected_html_content = f"""
    Hello {correspondence_contact.name},
    
    This email is concerning submission {submission.name}, 
    which is being underwritten by {user.name} ({user.email}.
    
    Thanks,
    {user.name}
    """
    assert email_data.html_content == expected_html_content


def test_send_submission_notification(app_context):
    organization = organization_fixture(id=1)
    user = user_fixture(organization_id=organization.id, name="Test User", email="<EMAIL>")
    report, submission = report_and_submission_fixture(
        organization_id=organization.id,
        name="Test Submission",
        email_references="test_references",
        email_message_id="test_email_message_id",
    )
    brokerage = brokerage_fixture(
        organization_id=organization.id,
    )
    broker = broker_fixture(
        organization_id=organization.id, brokerage_id=brokerage.id, name="Test Broker", email="<EMAIL>"
    )
    correspondence_contact = broker_fixture(
        organization_id=organization.id,
        brokerage_id=brokerage.id,
        name="Test Brokerage CC",
        email="<EMAIL>",
    )
    submission.set_brokerage(brokerage)
    submission.set_broker(broker)
    submission.set_brokerage_contact(correspondence_contact)
    su = submission_user_fixture(user_id=user.id, submission_id=submission.id)
    submission.assigned_underwriters.append(su)
    db.session.commit()

    assert Email.query.count() == 0
    assert SendgridEmailData.query.count() == 0

    custom_template = """
    Hello {{broker_name}},

    This email is concerning submission {{ submission_name }}, 
    which is being underwritten by {{ uw_name }} ({{ uw_email }}.

    Thanks,
    {{ user_name }}
    """
    sent_result = get_handler().send_submission_notification(
        user, submission, None, None, False, False, None, custom_template
    )
    assert sent_result.error is None

    email = Email.query.first()
    assert email is not None

    assert email.email_tracking_id is not None
    assert email.email_to == correspondence_contact.email
    assert email.correspondence_id == report.correspondence_id
    assert email.email_subject == "New message regarding Test Submission"
    assert broker.email in email.email_cc
    assert user.email in email.email_cc
    assert email.was_sent == False
    assert email.email_attachments_count == 0

    expected_html_content = f"""
    Hello {broker.name},

    This email is concerning submission {submission.name}, 
    which is being underwritten by {user.name} ({user.email}.

    Thanks,
    {user.name}
    """
    # we also encode email id in the body hence 'in' vs '=='
    assert expected_html_content in email.email_body

    assert email.email_reply_to == "<EMAIL>"

    assert email.sendgrid_data is not None


# Below are real life scenarios taken 1:1 from logs and DB, this way we can directly control each and every one of them
# and ensure we are saving in the DB only the data we actually want
def test_send_paragon_wc_cleared_notification(app_context):
    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(
        id=3663, organization_id=organization.id, name="Scarberry, Kelly", email="<EMAIL>"
    )
    paragon_user = user_fixture(
        id=1642, organization_id=organization.id, name="<EMAIL>", email="<EMAIL>"
    )
    report, submission = report_and_submission_fixture(
        organization_id=organization.id,
        name="WESTERN SAW MANUFACTURERS INC",
        email_references=(  # noqa
            "<<EMAIL>>"
            " <<EMAIL>>"
            " <<EMAIL>>"
            " <<EMAIL>>"
            " <<EMAIL>>"
            " <<EMAIL>>"
            " <<EMAIL>>"
            " <<EMAIL>> 18876"
        ),
        email_message_id="<<EMAIL>>",
        owner_id=paragon_user.id,
        primary_naics_code="NAICS_332216",
    )
    correspondence = report_email_correspondence_fixture(
        thread_id="1797098739459245910", email_account="<EMAIL>"
    )
    report.correspondence = correspondence
    report.correspondence_id = correspondence.id

    email_body = None
    with open("tests/data/emails/paragon_wc_report_email.html") as fp:
        email_body = fp.read()

    email = email_fixture(
        message_id="18876",
        correspondence_id=correspondence.id,
        email_account="<EMAIL>",
        email_subject="FW: Western Saw Manufacturers, Inc.",
        email_from='"Amanda N. Ikari" <<EMAIL>>',
        email_to="WC Submissions <<EMAIL>>",
        attachments=[
            {"name": "Western Saw Acord.pdf", "size": 245844},
            {"name": "WEstern Saw 2023 Supp.pdf", "size": 3557137},
            {"name": "loss runs Western Saw.pdf", "size": 151142},
            {"name": "western saw mod.pdf", "size": 233750},
        ],
        email_body=email_body,
        type=EmailType.ROOT,
        is_processed=True,
        was_sent=True,
    )

    brokerage = brokerage_fixture(
        organization_id=organization.id,
        name="Risk Placement Services, Inc",
        domains=["rpsins.com"],
    )
    broker = broker_fixture(
        organization_id=organization.id,
        brokerage_id=brokerage.id,
        name="Amanda N. Ikari",
        email="<EMAIL>",
    )
    submission.set_brokerage(brokerage)
    submission.set_broker(broker)
    submission.set_brokerage_contact(broker)
    su = submission_user_fixture(user_id=user.id, submission_id=submission.id)
    submission.assigned_underwriters.append(su)

    template_body = None
    with open("tests/data/email_templates/paragon_wc_cleared.html") as fp:
        template_body = fp.read()
    template = email_template_fixture(
        name="Submission Accepted in Clearing Paragon WC",
        owner_id=paragon_user.id,
        type=EmailTemplateType.CLEARING_ACCEPTED,
        html_content=template_body,
    )
    db.session.commit()

    sent_result = get_handler().send_submission_notification(
        user,
        submission,
        add_user_to_cc=False,
        cc_emails=["<EMAIL>"],
        email_type=EmailType.OTHER,
        extra_dynamic_data={"decline_rule": None, "effective_date": "2024-06-01", "submission_type": None},
        override_values={"display_name": "Paragon WC", "reply_to": "<EMAIL>"},
        custom_subject="WESTERN SAW MANUFACTURERS INC - Cleared",
        template_id=template.id,
    )

    assert sent_result
    assert sent_result.email.sendgrid_data
    mail: Mail = pickle.loads(sent_result.email.sendgrid_data.data)

    expected_dynamic_template_data = {
        "submission_name": "WESTERN SAW MANUFACTURERS INC",
        "user_email": "<EMAIL>",
        "broker_email": "<EMAIL>",
        "user_name": "Kelly Scarberry",
        "broker_name": "Amanda N. Ikari",
        "subject": "WESTERN SAW MANUFACTURERS INC - Cleared",
        "primary_naics": "332216",
        "effective_date": "2024-06-01",
    }

    dynamic_template_data = json.loads(sent_result.dynamic_template_data)
    for k, v in expected_dynamic_template_data.items():
        assert dynamic_template_data[k] == v

    assert sent_result.email.email_account == "<EMAIL>"
    assert sent_result.email.email_attachments_count == 0

    with open("tests/data/emails/paragon_wc_cleared_body.html") as fp:
        expected_body = fp.read()
    # using in as we encode email_id which is different each time
    assert expected_body in sent_result.email.email_body.strip()
    assert mail.contents[0].mime_type == "text/html"
    assert BeautifulSoup(sent_result.email.email_body, "html.parser") == BeautifulSoup(
        mail.contents[0].content, "html.parser"
    )

    assert sent_result.email.email_from == "<EMAIL>" == sent_result.email_from == mail.from_email.email
    assert mail.from_email.name == "Paragon WC"

    assert sent_result.email_reply_to == "<EMAIL>"
    assert len(mail.reply_to_list) == 2
    assert mail.reply_to_list[0].email == "<EMAIL>"
    assert mail.reply_to_list[0].name == "Paragon WC"
    assert mail.reply_to_list[1].email == "<EMAIL>"
    assert mail.reply_to_list[1].name == "Kalepa Copilot"

    assert sent_result.email.email_subject == "WESTERN SAW MANUFACTURERS INC - Cleared" == mail.subject.subject

    assert len(mail.personalizations[0].tos) == 1
    assert sent_result.email.email_to == "<EMAIL>" == mail.personalizations[0].tos[0]["email"]
    assert sorted(sent_result.emails_to) == [
        "<EMAIL>",
        "<EMAIL>",
    ]

    assert len(mail.personalizations[0].ccs) == 1
    assert mail.personalizations[0].ccs[0]["email"] == "<EMAIL>"
    assert mail.personalizations[0].ccs[0]["name"] == "<EMAIL>"

    assert len(mail.personalizations[0].bccs) == 1
    assert mail.personalizations[0].bccs[0]["email"] == "<EMAIL>"
    assert mail.personalizations[0].bccs[0]["name"] == "<EMAIL>"

    assert (
        sent_result.email.in_reply_to
        == "<<EMAIL>>"
    )
    assert (
        sent_result.email.email_references
        == "<<EMAIL>>"
        " <<EMAIL>>"
        " <<EMAIL>>"
        " <<EMAIL>>"
        " <<EMAIL>>"
        " <<EMAIL>>"
        " <<EMAIL>>"
        " <<EMAIL>> 18876"
    )  # noqa

    assert sent_result.email.is_processed is False
    assert sent_result.email.was_sent is False
    assert sent_result.scheduled is False

    assert sent_result.tracking_id is not None
    assert mail.custom_args[1].key == "tracking_id"
    assert mail.custom_args[1].value == str(sent_result.email.email_tracking_id) == str(sent_result.tracking_id)
    assert mail.custom_args[0].key == "kalepa_env"
    assert mail.custom_args[0].value == "dev"


def test_send_notification_admiral(app_context):
    organization = organization_fixture(id=ExistingOrganizations.AdmiralInsuranceGroup.value)
    user = user_fixture(
        id=3917, organization_id=organization.id, name="Delinski, Ben", email="<EMAIL>"
    )
    admiral_user = user_fixture(
        id=3190, organization_id=organization.id, name="<EMAIL>", email="<EMAIL>"
    )
    report, submission = report_and_submission_fixture(
        organization_id=organization.id,
        name="CHC MONTGOMERY LLC",
        email_references="<C7VPONYTKMU4.7A2JL91ERBKV1@AWALRELAY07> 2086",  # noqa
        email_message_id="<<EMAIL>>",
        owner_id=admiral_user.id,
        primary_naics_code="NAICS_531110",
    )
    correspondence = report_email_correspondence_fixture(
        thread_id="1794056656602146428", email_account="<EMAIL>"
    )
    report.correspondence = correspondence
    report.correspondence_id = correspondence.id

    email_body = None
    with open("tests/data/emails/admiral_report_email.html") as fp:
        email_body = fp.read()

    email = email_fixture(
        message_id="2086",
        correspondence_id=correspondence.id,
        email_account="<EMAIL>",
        email_subject="FW: (5/15)  CHC Montgomery LLC - GL/HNOA  [EXTERNAL]",
        email_from='"Arthur, Linda" <<EMAIL>>',
        email_to='"<EMAIL>" <<EMAIL>>',
        attachments=[
            {"name": "98453042_Acords - Montgomery.pdf", "size": 454066},
            {
                "name": (
                    "98453044_24-25 Supplemental - Programs Plus - DLP Real Estate Capital, Inc., CHC Montgomery,"
                    " LLC.docx"
                ),
                "size": 43677,
            },
            {
                "name": "98453046_24-25 SOV - DLP Real Estate Capital, Inc., CHC Montgomery, LLC.xlsx",
                "size": 1320625,
            },
            {"name": "98453048_RiskMeter.pdf", "size": 254265},
        ],
        email_body=email_body,
        type=EmailType.ROOT,
        is_processed=True,
        was_sent=True,
    )

    brokerage = brokerage_fixture(
        organization_id=organization.id,
        name="Amwins",
        domains=["usi.com,cobbsallen.com", "amwins.com"],
    )
    broker = broker_fixture(
        organization_id=organization.id,
        brokerage_id=brokerage.id,
        name="Andrew Haynes",
        email="<EMAIL>",
    )
    submission.set_brokerage(brokerage)
    submission.set_broker(broker)
    su = submission_user_fixture(user_id=user.id, submission_id=submission.id)
    submission.assigned_underwriters.append(su)
    db.session.commit()

    custom_template = None
    with open("tests/data/email_templates/admiral.html") as fp:
        custom_template = fp.read()
    custom_subject = "Re: (5/15) CHC Montgomery LLC - GL/HNOA [EXTERNAL] # 05C0WR 001"

    sent_result = get_handler("prod").send_submission_notification(
        user,
        submission,
        custom_subject=custom_subject,
        custom_template=custom_template,
    )

    assert sent_result
    assert sent_result.email.sendgrid_data
    mail: Mail = pickle.loads(sent_result.email.sendgrid_data.data)

    expected_dynamic_template_data = {
        "submission_name": "CHC MONTGOMERY LLC",
        "user_email": "<EMAIL>",
        "broker_email": "<EMAIL>",
        "user_name": "Ben Delinski",
        "broker_name": "Andrew Haynes",
        "subject": "Re: (5/15) CHC Montgomery LLC - GL/HNOA [EXTERNAL] # 05C0WR 001",
        "primary_naics": "531110",
        "uw_name": "Ben Delinski",
        "uw_email": "<EMAIL>",
    }

    dynamic_template_data = json.loads(sent_result.dynamic_template_data)
    for k, v in expected_dynamic_template_data.items():
        assert dynamic_template_data[k] == v

    assert sent_result.email.email_account == "<EMAIL>"
    assert sent_result.email.email_attachments_count == 0

    with open("tests/data/emails/admiral_sent_body.html") as fp:
        expected_body = fp.read()
    # using in as we encode email_id which is different each time
    assert expected_body.strip() in sent_result.email.email_body.strip()
    assert mail.contents[0].mime_type == "text/html"
    assert BeautifulSoup(sent_result.email.email_body, "html.parser") == BeautifulSoup(
        mail.contents[0].content, "html.parser"
    )

    assert sent_result.email.email_from == "<EMAIL>" == sent_result.email_from == mail.from_email.email
    assert mail.from_email.name == "Ben Delinski via Copilot"

    assert sent_result.email_reply_to == "<EMAIL>"
    assert len(mail.reply_to_list) == 1
    assert mail.reply_to_list[0].email == "<EMAIL>"
    assert mail.reply_to_list[0].name == "Delinski, Ben"

    assert (
        sent_result.email.email_subject
        == "Re: (5/15) CHC Montgomery LLC - GL/HNOA [EXTERNAL] # 05C0WR 001"
        == mail.subject.subject
    )

    assert len(mail.personalizations[0].tos) == 1
    assert sent_result.email.email_to == "<EMAIL>" == mail.personalizations[0].tos[0]["email"]
    assert sorted(sent_result.emails_to) == ["<EMAIL>", "<EMAIL>"]

    assert sent_result.email.email_cc == ["<EMAIL>"]
    assert len(mail.personalizations[0].ccs) == 1
    assert mail.personalizations[0].ccs[0]["email"] == "<EMAIL>"
    assert mail.personalizations[0].ccs[0]["name"] == "Delinski, Ben"

    assert len(mail.personalizations[0].bccs) == 1
    assert mail.personalizations[0].bccs[0]["email"] == "<EMAIL>"
    assert mail.personalizations[0].bccs[0]["name"] == "<EMAIL>"

    assert (
        sent_result.email.in_reply_to
        == "<<EMAIL>>"
    )
    assert sent_result.email.email_references == "<C7VPONYTKMU4.7A2JL91ERBKV1@AWALRELAY07> 2086"  # noqa

    assert sent_result.email.is_processed is False
    assert sent_result.email.was_sent is False
    assert sent_result.scheduled is False

    assert sent_result.tracking_id is not None
    assert mail.custom_args[1].key == "tracking_id"
    assert mail.custom_args[1].value == str(sent_result.email.email_tracking_id) == str(sent_result.tracking_id)
    assert mail.custom_args[0].key == "kalepa_env"
    assert mail.custom_args[0].value == "dev"


def test_send_submission_declined_notification_for_arch(app_context, mocker):
    mocker.patch(
        "flask.current_app.knock_client.publish_new_message_in_submission_correspondence_notification", return_value={}
    )

    organization = organization_fixture(id=ExistingOrganizations.Arch.value)
    user = user_fixture(id=1488, organization_id=organization.id, name="Fuchs, Kate", email="<EMAIL>")
    arch_user = user_fixture(id=348, organization_id=organization.id, name="<EMAIL>", email="<EMAIL>")
    with open("tests/data/email_templates/arch_decline.html") as fp:
        decline_template = fp.read()
    report, submission = report_and_submission_fixture(
        organization_id=organization.id,
        name="DECRON PROPERTIES CORP",
        email_references=(  # noqa
            "<<EMAIL>>"
            " <<EMAIL>> 91920"
        ),
        email_message_id="<<EMAIL>>",
        owner_id=arch_user.id,
        primary_naics_code="NAICS_531110",
        decline_custom_subject="DECRON PROPERTIES CORP application has been declined",
        decline_custom_template=decline_template,
    )
    correspondence = report_email_correspondence_fixture(
        thread_id="1797075845848889713", email_account="<EMAIL>"
    )
    report.correspondence = correspondence
    report.correspondence_id = correspondence.id

    email_body = None
    with open("tests/data/emails/arch_report_email.html") as fp:
        email_body = fp.read()

    email = email_fixture(
        message_id="91920",
        correspondence_id=correspondence.id,
        email_account="<EMAIL>",
        email_subject="FW: Decron Properties Corp - Eff 5/31/24-25",
        email_from="'SubmissionsESCasualty' via Arch Support <<EMAIL>>",
        email_to='"Arch-Kalepa.com" <<EMAIL>>',
        attachments=[
            {"name": "~05-31-24 to 25 Decron Casualty X Arizona.docx", "size": 3365786},
            {"name": "05-31-24 to 25 Decron Marketing X AZ SOV 4-11-24.xlsx", "size": 961939},
            {"name": "Decron LR GL Auto PLL Philadelphia 2018-23 Val 4-1-24.xlsx", "size": 46643},
            {"name": "05-31 Decron GL Auto LR Sompo 4-2-24.xlsx", "size": 38484},
            {"name": "05-31 Decron LR UMB ACE 2018-20 Val 3-14-24.pdf", "size": 147612},
            {"name": "05-31 Decron LR UMB CHUBB 2020-21 val 3-29-23.PDF", "size": 105542},
            {"name": "05-31 Decron LR UMB Preferred 2021-24 Val 3-28-24.pdf", "size": 313405},
            {"name": "Decron Loss Summary Submission 4-12-2024.xlsx", "size": 25244},
            {"name": "Decron Loss Summary Submission 4-12-2024.xlsx", "size": 25362},
        ],
        email_body=email_body,
        type=EmailType.ROOT,
        is_processed=True,
        was_sent=True,
    )

    brokerage = brokerage_fixture(
        organization_id=organization.id,
        name="CRC Group",
        domains=["crcroup.com", "crcgroup.com", "crcins.com", "crcinsgroup.com"],
    )
    broker = broker_fixture(
        organization_id=organization.id,
        brokerage_id=brokerage.id,
        name="Fred Khabbaz",
        email="<EMAIL>",
    )
    submission.set_brokerage(brokerage)
    submission.set_broker(broker)
    submission.set_brokerage_contact(broker)
    su = submission_user_fixture(user_id=user.id, submission_id=submission.id)
    submission.assigned_underwriters.append(su)
    db.session.commit()

    sent_result = get_handler("prod").send_submission_declined_notification(
        user,
        submission,
    )

    assert sent_result

    expected_dynamic_template_data = {
        "submission_name": "DECRON PROPERTIES CORP",
        "user_email": "<EMAIL>",
        "broker_email": "<EMAIL>",
        "user_name": "Kate Fuchs",
        "broker_name": "Fred Khabbaz",
        "subject": "DECRON PROPERTIES CORP application has been declined",
        "primary_naics": "531110",
    }

    dynamic_template_data = json.loads(sent_result.dynamic_template_data)
    for k, v in expected_dynamic_template_data.items():
        assert dynamic_template_data[k] == v

    assert sent_result.email.email_account == "<EMAIL>"
    assert sent_result.email.email_attachments_count == 0

    with open("tests/data/emails/arch_sent_decline_body.html") as fp:
        expected_body = fp.read()
    # using in as we encode email_id which is different each time
    assert expected_body.strip() in sent_result.email.email_body.strip()

    assert sent_result.email.email_from == "<EMAIL>" == sent_result.email_from

    assert sent_result.email_reply_to == "<EMAIL>"

    assert sent_result.email.email_subject == "DECRON PROPERTIES CORP application has been declined"

    assert sent_result.email.email_to == "<EMAIL>"
    assert set(sent_result.emails_to) == {"<EMAIL>", "<EMAIL>"}

    assert set(sent_result.email.email_cc) == {"<EMAIL>", "<EMAIL>"}

    assert sent_result.email.in_reply_to == "<<EMAIL>>"
    assert (
        sent_result.email.email_references
        == "<<EMAIL>>"
        " <<EMAIL>> 91920"
    )  # noqa

    assert sent_result.email.is_processed is False
    assert sent_result.email.was_sent is False
    assert sent_result.scheduled is False
    assert sent_result.tracking_id is not None
    assert sent_result.email.type == EmailType.DECLINE


def test_send_via_smtp_when_enabled(app_context):
    smtp_client = MagicMock(spec=SMTPNotificationsClient)
    sendgrid_client = MagicMock()
    arch_api_client = MagicMock(spec=ArchApiClient)

    handler = NotificationHandlerV2(
        env="prod",
        sendgrid_notifications_client=sendgrid_client,
        nationwide_smtp_notifications_client=smtp_client,
        arch_api_client=arch_api_client,
    )

    email_entity = email_fixture()
    email = MagicMock(spec=Email)
    email.id = email_entity.id
    email.sendgrid_data.data = pickle.dumps(
        Mail(from_email=NATIONWIDE_SENDER_EMAIL["prod"], to_emails=["<EMAIL>"])
    )
    email.correspondence.reports = [MagicMock(organization_id=ExistingOrganizations.Nationwide.value)]

    with patch.object(FeatureFlagsClient, "is_feature_enabled", return_value=True):
        sent_email = handler.send_queued_email(email)

    smtp_client.send.assert_called_once()
    sendgrid_client.send.assert_not_called()
    assert sent_email.error is None
    status = (
        EmailStatus.query.filter(EmailStatus.email_id == email_entity.id)
        .order_by(EmailStatus.update_timestamp.desc())
        .first()
    )
    assert status
    assert status.status == "delivered"


def test_send_via_sendgrid_when_smtp_client_missing(app_context):
    sendgrid_client = MagicMock()
    arch_api_client = MagicMock(spec=ArchApiClient)

    handler = NotificationHandlerV2(
        env="prod",
        sendgrid_notifications_client=sendgrid_client,
        nationwide_smtp_notifications_client=None,
        arch_api_client=arch_api_client,
    )

    email_entity = email_fixture()
    email = MagicMock(spec=Email)
    email.id = email_entity.id
    email.sendgrid_data.data = pickle.dumps(
        Mail(from_email=NATIONWIDE_SENDER_EMAIL["prod"], to_emails=["<EMAIL>"])
    )
    email.correspondence.reports = [MagicMock(organization_id=ExistingOrganizations.Nationwide.value)]

    with patch.object(FeatureFlagsClient, "is_feature_enabled", return_value=True):
        sent_email = handler.send_queued_email(email)

    sendgrid_client.send.assert_called_once()
    assert sent_email.error is None
    status = (
        EmailStatus.query.filter(EmailStatus.email_id == email_entity.id)
        .order_by(EmailStatus.update_timestamp.desc())
        .first()
    )
    assert not status or status.status != "delivered"


def test_send_via_sendgrid_when_smtp_fails(app_context):
    smtp_client = MagicMock(spec=SMTPNotificationsClient)
    smtp_client.send.side_effect = Exception("SMTP error")
    sendgrid_client = MagicMock()
    arch_api_client = MagicMock(spec=ArchApiClient)

    handler = NotificationHandlerV2(
        env="prod",
        sendgrid_notifications_client=sendgrid_client,
        nationwide_smtp_notifications_client=smtp_client,
        arch_api_client=arch_api_client,
    )

    email_entity = email_fixture()
    email = MagicMock(spec=Email)
    email.id = email_entity.id
    email.sendgrid_data.data = pickle.dumps(
        Mail(from_email=NATIONWIDE_SENDER_EMAIL["prod"], to_emails=["<EMAIL>"])
    )
    email.correspondence.reports = [MagicMock(organization_id=ExistingOrganizations.Nationwide.value)]

    with patch.object(FeatureFlagsClient, "is_feature_enabled", return_value=True):
        sent_email = handler.send_queued_email(email)

    smtp_client.send.assert_called_once()
    sendgrid_client.send.assert_called_once()
    assert sent_email.error is None


def test_send_fails_when_sendgrid_fails(app_context):
    smtp_client = MagicMock(spec=SMTPNotificationsClient)
    smtp_client.send.side_effect = Exception("SMTP error")
    sendgrid_client = MagicMock()
    sendgrid_client.send.side_effect = Exception("SendGrid error")
    arch_api_client = MagicMock(spec=ArchApiClient)

    handler = NotificationHandlerV2(
        env="prod",
        sendgrid_notifications_client=sendgrid_client,
        nationwide_smtp_notifications_client=smtp_client,
        arch_api_client=arch_api_client,
    )

    email_entity = email_fixture()
    email = MagicMock(spec=Email)
    email.id = email_entity.id
    email.sendgrid_data.data = pickle.dumps(
        Mail(from_email=NATIONWIDE_SENDER_EMAIL["prod"], to_emails=["<EMAIL>"])
    )
    email.correspondence.reports = [MagicMock(organization_id=ExistingOrganizations.Nationwide.value)]

    with patch.object(FeatureFlagsClient, "is_feature_enabled", return_value=True):
        sent_email = handler.send_queued_email(email)

    smtp_client.send.assert_called_once()
    sendgrid_client.send.assert_called_once()
    assert sent_email.error == SendNotificationError.OTHER_ERROR
