from static_common.enums.submission import SubmissionStage
import pytest

from copilot.models import Settings, db
from tests.integration.factories import (
    client_submission_stage_config_fixture,
    organization_fixture,
    recommendation_result_fixture,
    report_and_submission_fixture,
    user_fixture,
)
from tests.integration.utils import <PERSON>on<PERSON><PERSON><PERSON>


def test_update_client_stage_when_updating_stage(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            id=1,
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )

    organization_fixture()
    user_fixture()

    initial_client_stage = client_submission_stage_config_fixture(
        client_stage="non-default", copilot_stage=SubmissionStage.ON_MY_PLATE, default=False
    )

    desired_client_stage = client_submission_stage_config_fixture(
        client_stage="default-one", copilot_stage=SubmissionStage.DECLINED, default=True
    )

    db.session.commit()

    report, submission = report_and_submission_fixture(client_stage_id=initial_client_stage.id)

    db.session.commit()

    submission.stage = SubmissionStage.DECLINED
    db.session.commit()

    assert submission.client_stage.client_stage == desired_client_stage.client_stage


@pytest.mark.parametrize(
    "initial_stage, new_stage, initial_is_bind_likely, score_ml, score, expected_result",
    [
        (SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED_BOUND, None, None, None, True),
        (SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED_BOUND, False, 0.0, 0, True),
        (SubmissionStage.ON_MY_PLATE, SubmissionStage.EXPIRED, None, None, None, False),
        (SubmissionStage.ON_MY_PLATE, SubmissionStage.EXPIRED, True, 0.70, 70, False),
        (SubmissionStage.QUOTED, SubmissionStage.INDICATED, True, 0.70, 70, True),
        (SubmissionStage.EXPIRED, SubmissionStage.INDICATED, False, 0.70, 70, True),
        (SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED, False, 0.70, 10, True),
        (SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED_LOST, True, 0.70, 70, None),
        (SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED, False, 0.69, 69, False),
        (SubmissionStage.ON_MY_PLATE, SubmissionStage.QUOTED_LOST, False, 0.69, 69, None),
    ],
)
def test_update_is_bind_likely_when_updating_stage(
    app_context, mocker, initial_stage, new_stage, initial_is_bind_likely, score_ml, score, expected_result
):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            is_internal_machine_user=False,
            is_machine_user=False,
            organization_id=1,
            email="<EMAIL>",
            id=1,
            is_being_impersonated=False,
            applicable_settings=Settings(),
            has_submission_permission=lambda type, id: True,
        ),
    )

    organization_fixture()
    user_fixture()

    recommendation_result = (
        recommendation_result_fixture(
            score_ml=score_ml,
            score=score,
        )
        if score_ml is not None or score is not None
        else None
    )

    report, submission = report_and_submission_fixture(
        stage=initial_stage, recommendation_result=recommendation_result, is_bind_likely=initial_is_bind_likely
    )
    assert submission.is_bind_likely == initial_is_bind_likely

    submission.stage = new_stage
    db.session.commit()

    assert (
        submission.is_bind_likely == expected_result
    ), f"Expected {expected_result}, but got {submission.is_bind_likely}"
