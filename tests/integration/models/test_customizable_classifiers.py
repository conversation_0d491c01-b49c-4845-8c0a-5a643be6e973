from datetime import datetime, timedelta
import uuid

from marshmallow import ValidationError
from static_common.enums.classification import (
    ClassifierOutputType,
    ClassifierUnits,
    ExtractionType,
    FilterRuleType,
    InputProcessingType,
)
from static_common.enums.file_type import FileType
import pytest

from copilot.models._private import db
from copilot.models.customizable_classifiers import (
    ClassifierConfig,
    ClassifierConfigVersion,
    ClassifierPhrase,
    ClassifierToConfigVersion,
    ClassifierVersion,
    CustomizableClassifierV2,
    FilterRule,
    LLMConfigVersion,
    PhrasesConfigVersion,
    PhrasesWithLLMConfigVersion,
)
from copilot.models.types import CoverageType
from copilot.schemas.customizable_classifiers import (
    ClassifierConfigVersionSchema,
    ClassifierPhraseSchema,
    CustomizableClassifierV2Schema,
)
from copilot.v3.controllers.customizable_classifiers import get_submissions_for_dataset
from tests.integration.factories import (
    classifier_config_fixture,
    classifier_phrase_fixture,
    classifier_to_config_version_fixture,
    classifier_version_fixture,
    coverage_fixture,
    customizable_classifier_v2_fixture,
    file_fixture,
    llm_config_version_fixture,
    organization_fixture,
    phrases_config_version_fixture,
    phrases_with_llm_config_version_fixture,
    report_and_submission_fixture,
    report_fixture,
    submission_business_fixture,
    submission_coverage_fixture,
    submission_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj

classifier_config_version_schema = ClassifierConfigVersionSchema()
customizable_classifier_schema = CustomizableClassifierV2Schema()


def test_create_classifier_with_all_config_types(app_context):
    # Create base classifier with timestamps
    current_time = datetime.utcnow()
    classifier = CustomizableClassifierV2(
        id=uuid.uuid4(),
        name="Test Classifier",
        fact_subtype_id="test_fact",
        output_type=ClassifierOutputType.BOOLEAN,
        output_unit=ClassifierUnits.USD,
        input_types=["document"],
        organization_ids=[1, 2],
        run_in_pds=True,
        is_internal=False,
        created_at=current_time,
        updated_at=current_time,
    )
    db.session.add(classifier)
    db.session.flush()

    # Create a version with timestamps
    version = ClassifierVersion(
        id=uuid.uuid4(),
        name="v1",
        classifier_description="Test version",
        classifier_id=classifier.id,
        created_at=current_time,
        updated_at=current_time,
        is_active=True,
    )
    db.session.add(version)
    db.session.flush()

    # Create configs for different types
    configs = []
    for input_type in ["document", "image", "text"]:
        config = ClassifierConfig(
            id=uuid.uuid4(),
            input_types=[input_type],
            created_at=current_time,
            updated_at=current_time,
        )
        configs.append(config)
        db.session.add(config)
    db.session.flush()

    # Create different types of config versions
    phrases_config = PhrasesConfigVersion(
        id=uuid.uuid4(),
        classifier_config_id=configs[0].id,
        input_processing_type=InputProcessingType.OCR_TEXT.value,
        extraction_type=ExtractionType.PHRASES,
        is_autogenerated=False,
        created_at=current_time,
        updated_at=current_time,
    )
    db.session.add(phrases_config)

    phrases_llm_config = PhrasesWithLLMConfigVersion(
        id=uuid.uuid4(),
        classifier_config_id=configs[1].id,
        input_processing_type=InputProcessingType.IMAGE.value,
        extraction_type=ExtractionType.PHRASES_WITH_LLM,
        is_autogenerated=False,
        llm_model="gpt-4",
        prompt="Extract information",
        created_at=current_time,
        updated_at=current_time,
    )
    db.session.add(phrases_llm_config)

    llm_config = LLMConfigVersion(
        id=uuid.uuid4(),
        classifier_config_id=configs[2].id,
        input_processing_type=InputProcessingType.KV_PAIRS.value,
        extraction_type=ExtractionType.LLM,
        is_autogenerated=True,
        llm_model="gpt-4",
        prompt="Process text",
        created_at=current_time,
        updated_at=current_time,
    )
    db.session.add(llm_config)
    db.session.flush()

    # Add phrases to relevant config versions
    phrase1 = ClassifierPhrase(
        id=uuid.uuid4(),
        classifier_config_version_id=phrases_config.id,
        phrase="test phrase 1",
        weight=0.8,
        excludes=["exclude1"],
        created_at=current_time,
        updated_at=current_time,
    )
    phrase2 = ClassifierPhrase(
        id=uuid.uuid4(),
        classifier_config_version_id=phrases_llm_config.id,
        phrase="test phrase 2",
        weight=0.9,
        excludes=["exclude2"],
        created_at=current_time,
        updated_at=current_time,
    )
    db.session.add_all([phrase1, phrase2])

    # Link config versions to classifier version
    for config_version in [phrases_config, phrases_llm_config, llm_config]:
        link = ClassifierToConfigVersion(
            id=uuid.uuid4(),
            classifier_version_id=version.id,
            classifier_config_id=config_version.classifier_config_id,
            classifier_config_version_id=config_version.id,
            created_at=current_time,
            updated_at=current_time,
        )
        db.session.add(link)

    # Add filter rules
    for filter_type in [FilterRuleType.NAICS, FilterRuleType.COVERAGE]:
        filter_rule = FilterRule(
            id=uuid.uuid4(),
            classifier_id=classifier.id,
            filter_type=filter_type,
            negated=False,
            values=["2137"],
        )
        db.session.add(filter_rule)

    db.session.commit()

    # Test querying and relationships
    loaded_classifier = CustomizableClassifierV2.query.filter_by(id=classifier.id).first()
    assert loaded_classifier.active_version is not None
    assert len(loaded_classifier.active_version.configs) == 3

    # Test polymorphic loading
    config_versions = ClassifierConfigVersion.query.all()
    assert len(config_versions) == 3
    assert any(isinstance(cv, PhrasesConfigVersion) for cv in config_versions)
    assert any(isinstance(cv, PhrasesWithLLMConfigVersion) for cv in config_versions)
    assert any(isinstance(cv, LLMConfigVersion) for cv in config_versions)

    # Test phrases relationships
    phrases_config = PhrasesConfigVersion.query.filter_by(extraction_type=ExtractionType.PHRASES).first()
    assert len(phrases_config.phrases) == 1
    assert phrases_config.phrases[0].weight == 0.8

    # Test filter rules relationships
    assert len(loaded_classifier.filter_rules) == 2
    assert any(fr.filter_type == FilterRuleType.NAICS for fr in loaded_classifier.filter_rules)
    assert any(fr.filter_type == FilterRuleType.COVERAGE for fr in loaded_classifier.filter_rules)

    # Convert to Pydantic models
    classifier_schema = customizable_classifier_schema.dump(loaded_classifier)
    assert classifier_schema["name"] == "Test Classifier"
    assert classifier_schema["active_version"] is not None
    assert len(classifier_schema["active_version"]["configs"]) == 3
    assert len(classifier_schema["filter_rules"]) == 2

    # Test each config version type conversion
    for config_version in config_versions:
        schema = classifier_config_version_schema.dump(config_version)
        if isinstance(config_version, PhrasesConfigVersion):
            assert len(schema["phrases"]) == 1
            assert schema["extraction_type"] == ExtractionType.PHRASES
        elif isinstance(config_version, PhrasesWithLLMConfigVersion):
            assert schema["llm_model"] == "gpt-4"
            assert len(schema["phrases"]) == 1
        elif isinstance(config_version, LLMConfigVersion):
            assert schema["llm_model"] == "gpt-4"
            assert schema["prompt"] == "Process text"


def test_classifier_validation():
    # Test XOR constraint on fact_subtype_id and extracted_value_name
    with pytest.raises(
        ValidationError, match="Exactly one of fact_subtype_id or extracted_value_name must be provided"
    ):
        CustomizableClassifierV2Schema().load(
            {"name": "Test", "fact_subtype_id": "test", "extracted_value_name": "test", "input_types": ["doc"]}
        )

    with pytest.raises(
        ValidationError, match="At least one of fact_subtype_id or extracted_value_name must be provided"
    ):
        CustomizableClassifierV2Schema().load({"name": "Test", "input_types": ["doc"]})

    # Test valid cases
    schema1 = CustomizableClassifierV2Schema().load({"name": "Test", "fact_subtype_id": "test", "input_types": ["doc"]})
    assert schema1.fact_subtype_id == "test"

    schema2 = CustomizableClassifierV2Schema().load(
        {"name": "Test", "extracted_value_name": "test", "input_types": ["doc"]}
    )
    assert schema2.extracted_value_name == "test"


def test_phrase_weight_validation():
    # Test weight constraints
    with pytest.raises(ValidationError):
        ClassifierPhraseSchema().load({"phrase": "test", "weight": 1.5})

    with pytest.raises(ValidationError):
        ClassifierPhraseSchema().load({"phrase": "test", "weight": -0.1})

    # Test valid cases
    schema = ClassifierPhraseSchema().dump({"phrase": "test", "weight": 0.8})
    assert schema["weight"] == 0.8


def test_using_factories(app_context):
    # Create classifier with factories
    classifier = customizable_classifier_v2_fixture(name="Factory Test", fact_subtype_id="test_fact")

    version = classifier_version_fixture(classifier_id=classifier.id, is_active=True)

    config = classifier_config_fixture()

    phrases_config = phrases_config_version_fixture(classifier_config_id=config.id)

    phrase = classifier_phrase_fixture(classifier_config_version_id=phrases_config.id)

    classifier_to_config_version_fixture(
        classifier_version_id=version.id, classifier_config_id=config.id, classifier_config_version_id=phrases_config.id
    )

    db.session.commit()

    # Test loading
    loaded = CustomizableClassifierV2.query.get(classifier.id)
    assert loaded.active_version is not None
    assert len(loaded.active_version.configs) == 1

    config = loaded.active_version.configs[0]
    assert len(config.versions) == 1
    assert isinstance(config.versions[0], PhrasesConfigVersion)
    assert len(config.versions[0].phrases) == 1

    # Test schema conversion
    schema = customizable_classifier_schema.dump(loaded)
    assert schema["name"] == "Factory Test"
    assert schema["active_version"] is not None
    assert len(schema["active_version"]["configs"]) == 1


def test_config_version_schema_discrimination(app_context):
    # Test with PhrasesConfigVersion
    phrases_config = phrases_config_version_fixture(
        input_processing_type=InputProcessingType.OCR_TEXT.value,
        extraction_type=ExtractionType.PHRASES,
    )
    phrase = classifier_phrase_fixture(classifier_config_version_id=phrases_config.id)
    db.session.commit()

    schema = classifier_config_version_schema.dump(phrases_config)
    assert len(schema["phrases"]) == 1
    assert schema["extraction_type"] == ExtractionType.PHRASES

    # Test with PhrasesWithLLMConfigVersion
    phrases_llm_config = phrases_with_llm_config_version_fixture(
        input_processing_type=InputProcessingType.IMAGE.value,
        extraction_type=ExtractionType.PHRASES_WITH_LLM,
        llm_model="gpt-4",
        prompt="test prompt",
    )
    phrase = classifier_phrase_fixture(classifier_config_version_id=phrases_llm_config.id)
    db.session.commit()

    schema = classifier_config_version_schema.dump(phrases_llm_config)
    assert schema["llm_model"] == "gpt-4"
    assert schema["prompt"] == "test prompt"
    assert len(schema["phrases"]) == 1

    # Test with LLMConfigVersion
    llm_config = llm_config_version_fixture(
        input_processing_type=InputProcessingType.KV_PAIRS.value,
        extraction_type=ExtractionType.LLM,
        llm_model="gpt-4",
        prompt="test prompt",
    )
    db.session.commit()

    schema = classifier_config_version_schema.dump(llm_config)
    assert schema["llm_model"] == "gpt-4"
    assert schema["prompt"] == "test prompt"
    # Test with invalid extraction type
    with pytest.raises(ValidationError):
        classifier_config_version_schema.load(
            {"extraction_type": "INVALID", "input_processing_type": InputProcessingType.OCR_TEXT.value}
        )

    # Test with dict input
    dict_input = {
        "extraction_type": ExtractionType.LLM,
        "classifier_config_id": str(uuid.uuid4()),
        "input_processing_type": InputProcessingType.KV_PAIRS.value,
        "llm_model": "gpt-4",
        "prompt": "test prompt",
        "is_autogenerated": False,
    }
    schema = classifier_config_version_schema.load(dict_input)
    assert schema.llm_model == "gpt-4"


def test_get_submissions_for_dataset(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False),
    )

    organization_fixture()
    user = user_fixture()

    # First submission with report
    report1 = report_fixture(user_id=user.id, organization_id=1)
    submission1 = submission_fixture(primary_naics_code="NAICS_213769", is_verified=True)
    file_fixture(
        submission_id=submission1.id,
        user_id=user.id,
        file_type=FileType.SUPPLEMENTAL_FORM,
        is_internal=False,
        s3_key="test_key1",
    )
    file_fixture(
        submission_id=submission1.id, user_id=user.id, file_type=FileType.SOV, is_internal=False, s3_key="test_key2"
    )

    coverage1 = coverage_fixture(
        name="liability",
        display_name="Liability",
        coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS],
        organization_id=1,
    )
    submission_coverage_fixture(
        coverage_id=coverage1.id, submission_id=submission1.id, coverage_type=CoverageType.PRIMARY.value
    )
    submission_business_fixture(submission_id=submission1.id, business_id=uuid.uuid4())
    submission1.report = report1
    submission1.report_id = report1.id

    # Second submission with different values
    report2 = report_fixture(user_id=user.id, organization_id=1)
    submission2 = submission_fixture(primary_naics_code="NAICS_112233", is_verified=True)
    file_fixture(
        submission_id=submission2.id, user_id=user.id, file_type=FileType.EMAIL, is_internal=False, s3_key="test_key3"
    )
    file_fixture(
        submission_id=submission2.id,
        user_id=user.id,
        file_type=FileType.LOSS_RUN,
        is_internal=False,
        s3_key="test_key4",
    )

    coverage2 = coverage_fixture(
        name="property",
        display_name="Property",
        coverage_types=[CoverageType.PRIMARY],
        organization_id=1,
    )
    submission_coverage_fixture(
        coverage_id=coverage2.id, submission_id=submission2.id, coverage_type=CoverageType.PRIMARY.value
    )
    submission_business_fixture(submission_id=submission2.id, business_id=uuid.uuid4())
    submission2.report = report2
    submission2.report_id = report2.id

    classifier: CustomizableClassifierV2 = customizable_classifier_v2_fixture(
        name="Factory Test", fact_subtype_id="test_fact", organization_ids=[report1.organization_id]
    )

    db.session.commit()

    # Set datetime references for consistent testing
    created_at_from = str(datetime.utcnow() - timedelta(days=1))
    created_at_to = str(datetime.utcnow())

    """
    1. No filters should return all submissions
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [],
            "required_file_types": [],
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 2
    submission_ids = [s["id"] for s in submissions_for_dataset]
    assert str(submission1.id) in submission_ids
    assert str(submission2.id) in submission_ids
    file_types = {file["file_type"] for submission in submissions_for_dataset for file in submission["files"]}
    assert file_types == {FileType.SUPPLEMENTAL_FORM, FileType.SOV, FileType.EMAIL, FileType.LOSS_RUN}
    s3_keys = {file["s3_key"] for submission in submissions_for_dataset for file in submission["files"]}
    assert s3_keys == {"test_key1", "test_key2", "test_key3", "test_key4"}

    """
    2. Filter by NAICS should return only submissions with matching NAICS
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [
                {
                    "id": str(uuid.uuid4()),  # Convert UUID to string
                    "filter_type": FilterRuleType.NAICS.value,
                    "negated": False,
                    "values": ["NAICS_213769"],
                },
            ],
            "required_file_types": [],
            "created_at_from": created_at_from,
            "created_at_to": created_at_to,
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 1
    assert submissions_for_dataset[0]["id"] == str(submission1.id)
    assert submissions_for_dataset[0]["primary_naics_code"] == "NAICS_213769"

    """
    3. Filter by NAICS with negation should return submissions without matching NAICS
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [
                {
                    "id": str(uuid.uuid4()),
                    "filter_type": FilterRuleType.NAICS.value,
                    "negated": True,
                    "values": ["NAICS_213769"],
                },
            ],
            "required_file_types": [],
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 1
    assert submissions_for_dataset[0]["id"] == str(submission2.id)
    assert submissions_for_dataset[0]["primary_naics_code"] == "NAICS_112233"

    """
    4. Filter by COVERAGE should return only submissions with matching coverage
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [
                {
                    "id": str(uuid.uuid4()),
                    "filter_type": FilterRuleType.COVERAGE.value,
                    "negated": False,
                    "values": ["liability"],
                },
            ],
            "required_file_types": [],
        },
    )["submissions"]
    assert len(submissions_for_dataset) == 1
    assert submissions_for_dataset[0]["id"] == str(submission1.id)

    """
    5. Filter by COVERAGE with negation should return submissions without matching coverage
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [
                {
                    "id": str(uuid.uuid4()),
                    "filter_type": FilterRuleType.COVERAGE.value,
                    "negated": True,
                    "values": ["liability"],
                },
            ],
            "required_file_types": [],
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 1
    assert submissions_for_dataset[0]["id"] == str(submission2.id)

    """
    6. Filter by NAICS and COVERAGE should return only submissions with matching NAICS and coverage
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [
                {
                    "id": str(uuid.uuid4()),
                    "filter_type": FilterRuleType.NAICS.value,
                    "negated": False,
                    "values": ["NAICS_213769"],
                },
                {
                    "id": str(uuid.uuid4()),
                    "filter_type": FilterRuleType.COVERAGE.value,
                    "negated": False,
                    "values": ["liability"],
                },
            ],
            "required_file_types": [],
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 1
    assert submissions_for_dataset[0]["id"] == str(submission1.id)

    """
    7. Filter by file type should return only submissions containing those files
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [],
            "required_file_types": [FileType.SUPPLEMENTAL_FORM.value],
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 1
    assert submissions_for_dataset[0]["id"] == str(submission1.id)
    assert any(f["file_type"] == FileType.SUPPLEMENTAL_FORM.value for f in submissions_for_dataset[0]["files"])

    """
    8. Filter by multiple file types should return submissions containing any of those files
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [],
            "required_file_types": [FileType.EMAIL.value, FileType.LOSS_RUN.value],
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 1
    assert submissions_for_dataset[0]["id"] == str(submission2.id)

    """
    9. Filter by both file types
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [],
            "required_file_types": [FileType.EMAIL.value, FileType.SOV.value],
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 2

    """
    10. Complex combination of filters should work correctly
    """
    submissions_for_dataset = get_submissions_for_dataset(
        body={
            "organization_id": classifier.organization_ids[0],
            "filter_rules": [
                {
                    "id": str(uuid.uuid4()),
                    "filter_type": FilterRuleType.NAICS.value,
                    "negated": True,
                    "values": ["NAICS_999999"],  # Non-existent NAICS code
                },
                {
                    "id": str(uuid.uuid4()),
                    "filter_type": FilterRuleType.COVERAGE.value,
                    "negated": False,
                    "values": ["property"],
                },
            ],
            "required_file_types": [FileType.EMAIL.value],
        },
    )["submissions"]

    assert len(submissions_for_dataset) == 1
    assert submissions_for_dataset[0]["id"] == str(submission2.id)
