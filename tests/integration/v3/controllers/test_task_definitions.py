from http import HTTPStatus

from static_common.enums.task_model_execution_type import TaskModelExecutionType
from static_common.enums.task_model_processing_type import TaskModelProcessingType
from static_common.enums.task_model_type import TaskModelType
from werkzeug.exceptions import NotFound
import pytest

from copilot.models import db
from copilot.models.tasks import TaskDefinition
from copilot.v3.controllers.task_definitions import (
    create_task_definition,
    delete_task_definition,
    update_task_definition,
)
from tests.integration.factories import task_definition_fixture


@pytest.fixture
def body():
    return {
        "name": "Example Task Definition",
        "code": "example_task_def_code",
        "llm_task_description": "This task handles example LLM execution.",
        "has_large_state": False,
        "timeout": 600,
        "task_definition_models": [
            {
                "task_model": {
                    "name": "Example Task Model",
                    "type": TaskModelType.COMBINED,
                    "execution_config": {"param1": "value1", "param2": 10},
                    "execution_type": TaskModelExecutionType.LAMBDA,
                    "processing_type": TaskModelProcessingType.CONSOLIDATION,
                    "llm_model": "gpt-4",
                    "use_task_output_processor": True,
                    "timeout": 300,
                }
            }
        ],
    }


@pytest.fixture
def updated_body():
    return {
        "name": "Updated Task Definition",
        "code": "example_task_def_code",
        "llm_task_description": "This task handles example LLM execution.",
        "has_large_state": False,
        "timeout": 400,
        "task_definition_models": [
            {
                "task_model": {
                    "name": "Example Task Model",
                    "type": TaskModelType.COMBINED,
                    "execution_config": {"param1": "value1", "param2": 10},
                    "execution_type": TaskModelExecutionType.API,
                    "processing_type": TaskModelProcessingType.CONSOLIDATION,
                    "llm_model": "gpt-4",
                    "use_task_output_processor": True,
                    "timeout": 320,
                }
            }
        ],
    }


def test_task_definition_simple_add(app_context, body) -> None:
    response, status = create_task_definition(body)
    assert status == HTTPStatus.CREATED
    assert TaskDefinition.query.count() == 1


def test_task_definition_multiple_add(app_context, body) -> None:
    for i in range(4):
        body["code"] = f"example_task_def_code_{i}"
        response, status = create_task_definition(body)
        assert status == HTTPStatus.CREATED
        assert TaskDefinition.query.count() == i + 1


def test_task_definition_delete(app_context, body) -> None:
    task_definition = task_definition_fixture()
    db.session.commit()
    task_definition_id = task_definition.id
    assert TaskDefinition.query.count() == 1
    status = delete_task_definition(str(task_definition_id))
    assert status == HTTPStatus.NO_CONTENT
    assert TaskDefinition.query.count() == 0


def test_task_definition_delete_not_found(app_context) -> None:
    with pytest.raises(NotFound):
        delete_task_definition("ac651b8d-a2c5-435d-8b7f-7ad4443283ac")


def test_task_definition_update(app_context, body, updated_body) -> None:
    task_definition = task_definition_fixture()
    db.session.commit()
    record_id = task_definition.id
    assert TaskDefinition.query.count() == 1
    response, status = update_task_definition(str(record_id), updated_body)
    assert status == HTTPStatus.OK
    assert TaskDefinition.query.count() == 1
    assert response["name"] == updated_body["name"]
