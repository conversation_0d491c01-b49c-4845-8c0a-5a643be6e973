from unittest.mock import patch
from uuid import uuid4

from static_common.enums.origin import Origin
from static_common.enums.recommendation import RecommendationActionEnum
from static_common.enums.submission import SubmissionStage
from werkzeug.exceptions import NotFound
import pytest

from copilot.models import db
from copilot.models.reports import Submission
from copilot.v3.controllers.recommendations import sync_submission_recommendations_data
from tests.integration.factories import (
    organization_fixture,
    recommendation_result_fixture,
    report_and_submission_fixture,
    user_fixture,
)


def __verify_external_facing_data_event_called(mock, expected_submissions: list[Submission]) -> None:
    assert 1 == mock.call_count
    actual_submissions = mock.call_args[0][0]
    for expected_submission in expected_submissions:
        assert any(
            actual_submission
            for actual_submission in actual_submissions
            if str(actual_submission.id) == str(expected_submission.id)
            and str(actual_submission.report_id) == str(expected_submission.report_id)
            and actual_submission.organization_id == expected_submission.organization_id
            and actual_submission.origin == expected_submission.origin
            and actual_submission.is_verified == expected_submission.is_verified
        )


def test_sync_submission_recommendations_data_updates_multiple(app_context):
    submission_id_1 = uuid4()
    submission_id_2 = uuid4()

    expected_submission_1_score = 42
    expected_submission_1_action = RecommendationActionEnum.RED_FLAG

    expected_submission_2_score = 45
    expected_submission_2_action = RecommendationActionEnum.NO_ACTION

    organization = organization_fixture(id=1)
    user = user_fixture(id=1, organization_id=organization.id, name="Test User 1")
    recommendation_result_1 = recommendation_result_fixture(action=expected_submission_1_action)
    recommendation_result_2 = recommendation_result_fixture(
        action=RecommendationActionEnum.NO_ACTION,
        score=42,
    )
    _, submission_1 = report_and_submission_fixture(
        id=submission_id_1,
        owner_id=user.id,
        recommendation_v2_action=expected_submission_1_action,
        recommendation_result=recommendation_result_1,
        is_verified=False,
    )
    _, submission_2 = report_and_submission_fixture(
        id=submission_id_2,
        origin=Origin.EMAIL,
        is_verified=True,
        owner_id=user.id,
        recommendation_v2_score=42,
        recommendation_v2_action=RecommendationActionEnum.NO_ACTION,
        recommendation_result=recommendation_result_2,
    )

    db.session.commit()

    requests = [
        {
            "id": str(submission_id_1),
            "recommendation_v2_score": expected_submission_1_score,
            "recommendation_result": {
                "score": expected_submission_1_score,
            },
        },
        {
            "id": str(submission_id_2),
            "recommendation_v2_score": expected_submission_2_score,
            "recommendation_v2_action": str(expected_submission_2_action),
            "recommendation_result": {
                "score": expected_submission_2_score,
                "action": str(expected_submission_2_action),
            },
        },
    ]

    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_external_facing_data_updated_event_bulk"
    ) as send_external_facing_data_updated_event_bulk_mock:
        _, status_code = sync_submission_recommendations_data(body=requests)
        assert 200 == status_code

        updated_submission_1 = Submission.query.get(submission_id_1)

        assert updated_submission_1 is not None
        assert expected_submission_1_score == updated_submission_1.recommendation_v2_score
        assert str(expected_submission_1_action) == updated_submission_1.recommendation_v2_action

        assert updated_submission_1.recommendation_result is not None
        assert expected_submission_1_score == updated_submission_1.recommendation_result.score
        assert str(expected_submission_1_action) == updated_submission_1.recommendation_result.action

        updated_submission_2 = Submission.query.get(submission_id_2)

        assert updated_submission_2 is not None
        assert expected_submission_2_score == updated_submission_2.recommendation_v2_score
        assert str(expected_submission_2_action) == updated_submission_2.recommendation_v2_action

        assert updated_submission_2.recommendation_result is not None
        assert expected_submission_2_score == updated_submission_2.recommendation_result.score
        assert str(expected_submission_2_action) == updated_submission_2.recommendation_result.action

        __verify_external_facing_data_event_called(
            send_external_facing_data_updated_event_bulk_mock, [submission_1, submission_2]
        )


def test_sync_submission_recommendations_data_submission_not_found(app_context):
    submission_id_1 = uuid4()
    requests = [
        {
            "id": str(submission_id_1),
            "recommendation_v2_score": 42,
        },
    ]

    with pytest.raises(NotFound):
        sync_submission_recommendations_data(body=requests)


def test_sync_submission_recommendations_data_only_updates_supported_fields(app_context):
    submission_id_1 = uuid4()
    expected_score = 42
    expected_account_name = "test account"

    organization = organization_fixture(id=1)
    user = user_fixture(id=1, organization_id=organization.id, name="Test User 1")
    report_and_submission_fixture(
        id=submission_id_1,
        owner_id=user.id,
        account_name=expected_account_name,
    )

    db.session.commit()

    requests = [
        {"id": str(submission_id_1), "recommendation_v2_score": expected_score, "account_name": "new account name"},
    ]

    sync_submission_recommendations_data(body=requests)

    updated_submission_1 = Submission.query.get(submission_id_1)
    assert expected_score == updated_submission_1.recommendation_v2_score
    assert expected_account_name == updated_submission_1.account_name


def test_sync_submission_recommendations_data_updates_is_bind_likely_multiple(app_context):
    submission_id_1 = uuid4()
    submission_id_2 = uuid4()
    submission_id_3 = uuid4()

    expected_submission_1_score = 72
    expected_submission_1_score_ml = 0.72
    expected_submission_2_score = 42
    expected_submission_2_score_ml = 0.42
    expected_submission_3_score = 42
    expected_submission_3_score_ml = 0.42

    organization = organization_fixture(id=1)
    user = user_fixture(id=1, organization_id=organization.id, name="Test User 1")
    recommendation_result_1 = recommendation_result_fixture(score_ml=0.49, score=49)
    recommendation_result_2 = recommendation_result_fixture(score_ml=0.8, score=80)
    recommendation_result_3 = recommendation_result_fixture(score_ml=0.8, score=80)

    _, submission_1 = report_and_submission_fixture(
        id=submission_id_1,
        stage=SubmissionStage.QUOTED,
        is_bind_likely=False,
        owner_id=user.id,
        recommendation_result=recommendation_result_1,
        is_verified=False,
    )
    _, submission_2 = report_and_submission_fixture(
        id=submission_id_2,
        stage=SubmissionStage.ON_MY_PLATE,
        is_bind_likely=True,
        origin=Origin.EMAIL,
        is_verified=True,
        owner_id=user.id,
        recommendation_result=recommendation_result_2,
    )

    _, submission_2 = report_and_submission_fixture(
        id=submission_id_3,
        stage=SubmissionStage.QUOTED_LOST,
        is_bind_likely=None,
        origin=Origin.EMAIL,
        is_verified=True,
        owner_id=user.id,
        recommendation_result=recommendation_result_3,
    )

    db.session.commit()

    requests = [
        {
            "id": str(submission_id_1),
            "recommendation_v2_score": expected_submission_1_score,
            "recommendation_result": {
                "score": expected_submission_1_score,
                "score_ml": expected_submission_1_score_ml,
            },
        },
        {
            "id": str(submission_id_2),
            "recommendation_v2_score": expected_submission_2_score,
            "recommendation_result": {
                "score": expected_submission_2_score,
                "score_ml": expected_submission_2_score_ml,
            },
        },
        {
            "id": str(submission_id_3),
            "recommendation_v2_score": expected_submission_3_score,
            "recommendation_result": {
                "score": expected_submission_3_score,
                "score_ml": expected_submission_3_score_ml,
            },
        },
    ]

    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_external_facing_data_updated_event_bulk"
    ) as send_external_facing_data_updated_event_bulk_mock:
        _, status_code = sync_submission_recommendations_data(body=requests)
        assert 200 == status_code

        updated_submission_1 = Submission.query.get(submission_id_1)
        assert updated_submission_1 is not None
        assert updated_submission_1.is_bind_likely is True

        updated_submission_2 = Submission.query.get(submission_id_2)
        assert updated_submission_2 is not None
        assert updated_submission_2.is_bind_likely is False

        updated_submission_3 = Submission.query.get(submission_id_3)
        assert updated_submission_3 is not None
        assert updated_submission_3.is_bind_likely is None

        __verify_external_facing_data_event_called(
            send_external_facing_data_updated_event_bulk_mock, [submission_1, submission_2]
        )
