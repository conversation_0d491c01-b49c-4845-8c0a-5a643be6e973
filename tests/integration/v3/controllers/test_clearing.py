from unittest.mock import MagicMock

from static_common.enums.async_ops import Async<PERSON>peration, AsyncOperationStatus
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from werkzeug.exceptions import BadRequest
import pytest
import werkzeug.exceptions

from copilot.models import File, Loss, Settings, db
from copilot.models.async_ops import AsyncOperationDB
from copilot.models.emails import Email
from copilot.models.reports import Coverage
from copilot.models.types import ClearingStatus, CoverageType
from copilot.v3.controllers.clearing import (
    finish_external_clearing_for_submission,
    merge_correspondence,
    merge_verified_reports,
)
from tests.integration.factories import (
    coverage_fixture,
    email_fixture,
    file_fixture,
    loss_fixture,
    organization_fixture,
    report_and_submission_fixture,
    settings_fixture,
    submission_client_id_fixture,
    submission_coverage_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj


@pytest.fixture
def reports_and_submissions(mocker):
    organization = organization_fixture()
    user = user_fixture()
    settings_fixture(user_id=user.id, loss_runs_enabled=True)
    settings_fixture(organization_id=organization.id)
    report_1, submission_1 = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()

    mocked_user = AnonObj(
        can_light_clear=True,
        can_merge_reports=True,
        is_internal_machine_user=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    email_1_1 = email_fixture(
        attachments=[{"name": "aa.xlsx", "size": 9428}, {"name": "bb.xlsx", "size": 14394}],
        email_body="-1_1-",
    )
    report_1.correspondence_id = email_1_1.correspondence_id
    file_fixture(
        name="email_body.pdf",
        submission_id=submission_1.id,
        checksum="email_body.pdf",
        s3_key=f"uploads/files/x-{submission_1.id}/email_body_1.pdf",
    )
    file_fixture(
        name="aa.xlsx",
        submission_id=submission_1.id,
        checksum="aa.xlsx",
        s3_key=f"uploads/files/x-{submission_1.id}/aa.xlsx",
    )
    file_fixture(
        name="bb.xlsx",
        submission_id=submission_1.id,
        checksum="bb.xlsx",
        s3_key=f"uploads/files/x-{submission_1.id}/bb.xlsx",
    )

    email_1_2 = email_fixture(
        correspondence_id=email_1_1.correspondence_id,
        attachments=[{"name": "cc.xlsx", "size": 10307}],
        email_body="-1_2-",
    )
    file_fixture(
        name="cc.xlsx",
        submission_id=submission_1.id,
        checksum="cc.xlsx",
        s3_key=f"uploads/files/x-{submission_1.id}/cc.xlsx",
    )

    email_2_1 = email_fixture(attachments=[{"name": "dd.xlsx", "size": 10236}], email_body="-2_1-")
    report_2.correspondence_id = email_2_1.correspondence_id
    file_fixture(
        name="email_body.pdf",
        submission_id=submission_2.id,
        checksum="email_body_2.pdf",
        s3_key=f"uploads/files/x-{submission_2.id}/email_body_2.pdf",
        file_type=FileType.EMAIL,
    )
    file_fixture(
        name="dd.xlsx",
        submission_id=submission_2.id,
        checksum="dd.xlsx",
        s3_key=f"uploads/files/x-{submission_2.id}/dd.xlsx",
        file_type=FileType.SOV,
    )

    lr_file_1_1 = file_fixture(
        name="lr.pdf",
        submission_id=submission_1.id,
        checksum="lr.pdf",
        s3_key=f"uploads/files/x-{submission_1.id}/lr.xlsx",
        file_type=FileType.LOSS_RUN,
    )
    loss_fixture(file_id=lr_file_1_1.id, submission_id=submission_1.id)
    lr_file_2_1 = file_fixture(
        name="lr.pdf",
        submission_id=submission_2.id,
        checksum="lr.pdf",
        s3_key=f"uploads/files/x-{submission_2.id}/lr.xlsx",
        file_type=FileType.LOSS_RUN,
    )
    loss_fixture(file_id=lr_file_2_1.id, submission_id=submission_2.id)

    lr_file_2_2 = file_fixture(
        name="lr_2.pdf",
        submission_id=submission_2.id,
        checksum="lr_2.pdf",
        s3_key=f"uploads/files/x-{submission_2.id}/lr_2.xlsx",
        file_type=FileType.LOSS_RUN,
    )
    loss_fixture(
        file_id=lr_file_2_2.id,
        submission_id=submission_2.id,
        file_type=FileType.LOSS_RUN,
    )

    db.session.commit()

    return report_1, submission_1, report_2, submission_2


def test_merge_correspondence(app_context, mocker, reports_and_submissions):
    report_1, submission_1, report_2, submission_2 = reports_and_submissions

    merge_correspondence(str(report_2.id), str(report_1.id))
    target_files = File.query.filter(File.submission_id == submission_1.id).all()
    target_losses = Loss.query.filter(Loss.submission_id == submission_1.id).all()
    emails = Email.query.filter(Email.correspondence_id == report_1.correspondence_id).all()

    assert len(target_files) == 7
    assert len(target_losses) == 1  # The second one will be reprocessed and determined if should be added
    assert len(emails) == 3
    assert submission_2.report.is_deleted


def test_merge_correspondence_bad_request_unique_client_id(app_context, mocker, reports_and_submissions):
    report_1, submission_1, report_2, submission_2 = reports_and_submissions
    submission_client_id_fixture(submission_id=submission_2.id, client_submission_id="unique")

    with pytest.raises(BadRequest, match="Can't merge from submission with unique client id"):
        merge_correspondence(str(report_2.id), str(report_1.id))


def test_merge_verified_reports(app_context, mocker, reports_and_submissions):
    report_1, submission_1, report_2, submission_2 = reports_and_submissions

    with pytest.raises(BadRequest, match="Both submissions must be verified"):
        merge_verified_reports(str(report_2.id), str(report_1.id))

    submission_1.is_verified = True
    submission_2.is_verified = True
    db.session.commit()

    merge_verified_reports(str(report_2.id), str(report_1.id))
    target_files = File.query.filter(File.submission_id == submission_1.id).all()
    target_losses = Loss.query.filter(Loss.submission_id == submission_1.id).all()
    emails = Email.query.filter(Email.correspondence_id == report_1.correspondence_id).all()

    assert len(target_files) == 7
    assert len(target_losses) == 1  # The second one will be reprocessed and determined if should be added
    assert len(emails) == 3
    assert submission_2.report.is_deleted

    sov_file_origin = File.query.filter(File.submission_id == submission_2.id, File.file_type == FileType.SOV).first()
    sov_file = next(f for f in target_files if f.file_type == FileType.SOV)
    assert sov_file.cache_origin_file_id == sov_file_origin.id


def _setup_conifer_finish_clearing_test_environment(mocker, include_cannabis=False):
    org_id = ExistingOrganizations.BishopConifer.value
    organization_fixture(id=org_id)
    db.session.commit()

    user_settings = Settings(is_clearing_enabled=True, client_id_required_for_clearing=True)
    user = user_fixture(organization_id=org_id, email="<EMAIL>", settings=user_settings)
    report_1, submission_1 = report_and_submission_fixture(organization_id=org_id, prevent_clearing_updates=True)

    # Create some coverages
    liability_coverage = coverage_fixture(
        name=Coverage.ExistingNames.Liability,
        organization_id=org_id,
        coverage_types={CoverageType.PRIMARY, CoverageType.EXCESS},
    )
    wc_coverage = coverage_fixture(
        name=Coverage.ExistingNames.WorkersComp,
        organization_id=org_id,
        coverage_types={CoverageType.PRIMARY, CoverageType.EXCESS},
    )
    property_coverage = coverage_fixture(
        name=Coverage.ExistingNames.Property,
        organization_id=org_id,
        coverage_types={CoverageType.PRIMARY, CoverageType.EXCESS},
    )

    cannabis_coverage = None
    if include_cannabis:
        cannabis_coverage = coverage_fixture(
            name=Coverage.ExistingNames.CannabisLiability,
            organization_id=org_id,
            coverage_types={CoverageType.PRIMARY, CoverageType.EXCESS},
        )

    db.session.commit()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    return (
        org_id,
        user,
        report_1,
        submission_1,
        liability_coverage,
        wc_coverage,
        property_coverage,
        cannabis_coverage,
    )


def test_conifer_finish_clearing_when_no_split_requested(app_context, mocker):
    # Sub with WC coverage only. User requested no split but Liability and Property coverages.
    (
        org_id,
        user,
        report_1,
        submission_1,
        liability_coverage,
        wc_coverage,
        property_coverage,
        _,
    ) = _setup_conifer_finish_clearing_test_environment(mocker)

    submission_coverage_fixture(
        coverage_id=wc_coverage.id,
        submission_id=submission_1.id,
        coverage_type=CoverageType.PRIMARY,
        coverage=wc_coverage,
    )
    db.session.commit()

    request = {
        "organization_id": org_id,
        "organization_specific_request_data": {
            "splits": [{"quote_number": "123", "coverages": ["Liability - PRIMARY", "Property - PRIMARY"]}]
        },
    }
    result = finish_external_clearing_for_submission(submission_id=submission_1.id, body=request)
    assert result[1] == 204
    assert len(submission_1.coverages) == 2
    actual_coverages_set = {(cov.coverage.name, cov.coverage_type) for cov in submission_1.coverages}
    assert actual_coverages_set == {
        ("liability", "PRIMARY"),
        ("property", "PRIMARY"),
    }
    assert submission_1.prevent_clearing_updates is False
    assert submission_1.clearing_status == ClearingStatus.CLEARING_IN_PROGRESS

    created_async_op = db.session.query(AsyncOperationDB).all()
    assert len(created_async_op) == 1
    first_async_op = created_async_op[0]
    assert first_async_op.status == AsyncOperationStatus.PENDING
    assert first_async_op.operation == AsyncOperation.FINALIZE_CONIFER_EXTERNAL_CLEARING
    assert first_async_op.organization_id == org_id
    assert first_async_op.logical_identifier == "123"
    assert first_async_op.executing_user_email == "<EMAIL>"

    app_context.event_service.handle_submission_event.assert_called_once_with(
        event="EXTERNAL_CLEARING_FINISHED",
        submission=submission_1,
        additional_data={
            "user_provided_quote_number": "123",
            "user_email": "<EMAIL>",
            "async_op_id": str(first_async_op.id),
        },
    )


def test_conifer_finish_clearing_with_3_splits_requested(app_context, mocker):
    # Submission has 3 initial coverages and user requested 3 splits with various coverages
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    (
        org_id,
        user,
        report_1,
        submission_1,
        liability_coverage,
        wc_coverage,
        property_coverage,
        cannabis_coverage,
    ) = _setup_conifer_finish_clearing_test_environment(mocker, include_cannabis=True)

    submission_coverage_fixture(
        coverage_id=liability_coverage.id,
        submission_id=submission_1.id,
        coverage_type=CoverageType.PRIMARY,
        coverage=liability_coverage,
    )
    submission_coverage_fixture(
        coverage_id=wc_coverage.id,
        submission_id=submission_1.id,
        coverage_type=CoverageType.PRIMARY,
        coverage=wc_coverage,
    )
    submission_coverage_fixture(
        coverage_id=property_coverage.id,
        submission_id=submission_1.id,
        coverage_type=CoverageType.PRIMARY,
        coverage=property_coverage,
    )
    db.session.commit()

    mocked_request = AnonObj(
        form={
            "original_report_id": "file1.csv",
        },
        files={"file": None},
    )
    mocker.patch("connexion.request", mocked_request)

    request = {
        "organization_id": org_id,
        "organization_specific_request_data": {
            "splits": [
                {"quote_number": "123", "coverages": ["Liability - PRIMARY", "CannabisLiability - PRIMARY"]},
                {"quote_number": "456", "coverages": ["WorkersComp - PRIMARY"]},
                {"quote_number": "789", "coverages": ["Property - PRIMARY"]},
            ]
        },
    }
    result = finish_external_clearing_for_submission(submission_id=submission_1.id, body=request)
    assert submission_1.clearing_status == ClearingStatus.CLEARING_IN_PROGRESS
    assert result[1] == 204

    # We should have 3 split submissions which we can capture from event service mock
    split_submissions = []
    for captured_call in app_context.event_service.handle_submission_event.call_args_list:
        if "event" in captured_call.kwargs and captured_call.kwargs["event"] == "EXTERNAL_CLEARING_FINISHED":
            split_sub = captured_call.kwargs["submission"]
            additional_data = captured_call.kwargs["additional_data"]
            split_submissions.append((split_sub, additional_data))
    assert len(split_submissions) == 3

    for split_sub, additional_data in split_submissions:
        actual_coverages_set = {(cov.coverage.name, cov.coverage_type) for cov in split_sub.coverages}
        match additional_data["user_provided_quote_number"]:
            # First submission should be the original one but with 2 user forced coverages
            case "123":
                assert split_sub.id == submission_1.id
                assert len(split_sub.coverages) == 2
                assert actual_coverages_set == {
                    ("liability", "PRIMARY"),
                    ("cannabisLiability", "PRIMARY"),
                }
            # Second submission should have only 1 user forced coverage and be created from the original submission
            case "456":
                assert len(split_sub.coverages) == 1
                assert actual_coverages_set == {
                    ("workersComp", "PRIMARY"),
                }
            # Third submission should have only 1 user forced coverage and be created from the original submission
            case "789":
                assert len(split_sub.coverages) == 1
                assert actual_coverages_set == {
                    ("property", "PRIMARY"),
                }

    created_async_ops = db.session.query(AsyncOperationDB).all()
    assert len(created_async_ops) == 3
    assert {async_op.logical_identifier for async_op in created_async_ops} == {"123", "456", "789"}


def test_conifer_finish_clearing_for_mismatched_organization(app_context, mocker):
    (
        org_id,
        user,
        report_1,
        submission_1,
        liability_coverage,
        wc_coverage,
        property_coverage,
        _,
    ) = _setup_conifer_finish_clearing_test_environment(mocker)

    submission_coverage_fixture(
        coverage_id=wc_coverage.id,
        submission_id=submission_1.id,
        coverage_type=CoverageType.PRIMARY,
        coverage=wc_coverage,
    )
    db.session.commit()

    # Request comes for Paragon org but target submission is Conifer
    request = {
        "organization_id": ExistingOrganizations.Paragon.value,
        "organization_specific_request_data": {
            "splits": [{"quote_number": "123", "coverages": ["Liability - PRIMARY", "Property - PRIMARY"]}]
        },
    }
    try:
        finish_external_clearing_for_submission(submission_id=submission_1.id, body=request)
    except werkzeug.exceptions.Forbidden:
        assert submission_1.clearing_status != ClearingStatus.CLEARING_IN_PROGRESS
        return
    assert False, "Expected Forbidden exception"


def test_conifer_finish_clearing_with_empty_splits_config(app_context, mocker):
    (
        org_id,
        user,
        report_1,
        submission_1,
        liability_coverage,
        wc_coverage,
        property_coverage,
        _,
    ) = _setup_conifer_finish_clearing_test_environment(mocker)

    submission_coverage_fixture(
        coverage_id=wc_coverage.id,
        submission_id=submission_1.id,
        coverage_type=CoverageType.PRIMARY,
        coverage=wc_coverage,
    )
    db.session.commit()

    # This should result in 400 error
    request = {"organization_id": org_id, "organization_specific_request_data": {"splits": []}}
    try:
        finish_external_clearing_for_submission(submission_id=submission_1.id, body=request)
    except werkzeug.exceptions.BadRequest as bad_request:
        assert bad_request.code == 400
        assert bad_request.description == "{'splits': ['At least one split must be provided']}"
        assert submission_1.clearing_status != ClearingStatus.CLEARING_IN_PROGRESS
        return
    assert False, "Expected BadRequest exception"


def test_conifer_finish_clearing_with_duplicate_quote_number(app_context, mocker):
    (
        org_id,
        user,
        report_1,
        submission_1,
        liability_coverage,
        wc_coverage,
        property_coverage,
        _,
    ) = _setup_conifer_finish_clearing_test_environment(mocker)

    submission_coverage_fixture(
        coverage_id=wc_coverage.id,
        submission_id=submission_1.id,
        coverage_type=CoverageType.PRIMARY,
        coverage=wc_coverage,
    )
    db.session.commit()

    request = {
        "organization_id": org_id,
        "organization_specific_request_data": {
            "splits": [
                {"quote_number": "123", "coverages": ["Liability - PRIMARY", "Property - PRIMARY"]},
                {"quote_number": "123", "coverages": ["ManagementLiability - PRIMARY"]},
            ]
        },
    }
    try:
        finish_external_clearing_for_submission(submission_id=submission_1.id, body=request)
    except werkzeug.exceptions.BadRequest as bad_request:
        assert bad_request.code == 400
        assert bad_request.description == "Quote numbers must be unique"
        assert submission_1.clearing_status != ClearingStatus.CLEARING_IN_PROGRESS
        return
    assert False, "Expected BadRequest exception"
