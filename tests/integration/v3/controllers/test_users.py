from datetime import datetime, timed<PERSON>ta
from uuid import uuid4

from marshmallow import ValidationError
from pytest import fixture
from sqlalchemy.orm import load_only
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.enums.workers_compensation import WorkersCompExperienceSource
from werkzeug.exceptions import NotFound
import pytest

from copilot.models import (
    ReportPermission,
    WorkersCompExperience,
    WorkersCompStateRatingInfo,
    db,
)
from copilot.models.emails import ReportEmailCorrespondence
from copilot.models.organization import Organization
from copilot.models.reports import (
    ReportBundle,
    ReportV2,
    Submission,
    SubmissionBusiness,
    SubmissionClearingIssue,
    SubmissionClearingSubStatus,
    SubmissionCoverage,
    SubmissionUser,
)
from copilot.models.types import (
    ClearingStatus,
    CoverageType,
    HubTemplateType,
    PermissionType,
    UserRoleEnum,
)
from copilot.models.user import User
from copilot.v3.controllers.user import get_user_by_attributes
from copilot.v3.controllers.users import (
    add_user_open_report,
    create_hub_template,
    create_user_if_not_present,
    delete_user_open_reports,
    get_all_users,
    get_hub_templates,
    get_reports_by_user,
    get_underwriter,
    get_user_open_reports,
    get_users_by_organization,
)
from copilot.v3.utils.users_common import DEFAULT_CREDITS
from tests.integration.factories import (
    broker_fixture,
    broker_group_fixture,
    broker_group_mapping_fixture,
    brokerage_fixture,
    coverage_fixture,
    email_fixture,
    file_fixture,
    hub_template_fixture,
    organization_fixture,
    recommendation_result_fixture,
    report_and_submission_fixture,
    report_email_correspondence_fixture,
    report_fixture,
    report_with_submissions_fixture,
    submission_bookmark_fixture,
    submission_coverage_fixture,
    submission_fixture,
    user_fixture,
    user_group_fixture,
    user_open_report_fixture,
)


def test_get_user_reports_assignee_groups(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user2 = user_fixture(id=2)
    user1 = user_fixture(redirect_user_id=2)
    mocker.patch("flask_login.utils._get_user", return_value=user1)

    report_1 = report_with_submissions_fixture()
    report_2 = report_with_submissions_fixture()

    group = user_group_fixture()
    group.users.append(user1)
    db.session.commit()

    report_1.submission.assigned_underwriters.append(SubmissionUser(user=user1))
    db.session.commit()

    res = get_reports_by_user(1, assignee_groups=[group.id])

    assert len(res["reports"]) == 1
    submission = res["reports"][0]["submissions"][0]
    assert submission["assigned_underwriters"][0]["id"] == user1.id
    assert submission["assigned_underwriters"][0]["groups"][0]["id"] == str(group.id)
    assert submission["assigned_underwriters"][0]["redirect_user_id"] == 2

    res = get_reports_by_user(1, not_assignee_groups=[group.id])

    assert len(res["reports"]) == 1
    assert res["reports"][0]["id"] == str(report_2.id)


def test_get_user_reports_returns_adjusted_tiv(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user2 = user_fixture(id=2)
    user1 = user_fixture(redirect_user_id=2)
    mocker.patch("flask_login.utils._get_user", return_value=user1)

    id1 = str(uuid4())
    id2 = str(uuid4())
    report_with_submissions_fixture(
        id=id1,
        adjusted_tiv=200,
    )
    report_with_submissions_fixture(
        id=id2,
        adjusted_tiv=100,
    )

    group = user_group_fixture()
    group.users.append(user1)
    db.session.commit()

    res = get_reports_by_user(1, sorting=["ADJUSTED_TIV"], descending=[False])

    assert len(res["reports"]) == 2
    submission = res["reports"][0]["submissions"][0]
    assert submission["adjusted_tiv"] == 100
    submission = res["reports"][1]["submissions"][0]
    assert submission["adjusted_tiv"] == 200


def test_get_user_reports_assignee_groups_and_only_unassigned(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user2 = user_fixture(id=2)
    user1 = user_fixture(redirect_user_id=2)
    mocker.patch("flask_login.utils._get_user", return_value=user1)

    report_1 = report_with_submissions_fixture()
    report_2 = report_with_submissions_fixture()

    group = user_group_fixture()
    group.users.append(user1)
    db.session.commit()

    report_1.submission.assigned_underwriters.append(SubmissionUser(user=user1))
    db.session.commit()

    with pytest.raises(ValidationError):
        get_reports_by_user(1, assignees=[user1.id], only_unassigned=True)


def test_get_user_reports_trusted_user_without_cross_org_access(app_context, mocker):
    """
    Note: This test case is a replication of an issue observed in prod
        https://kalepa.atlassian.net/browse/ENG-17266
    """
    organization_fixture()
    user1 = user_fixture(email="<EMAIL>")
    user2 = user_fixture(id=2)
    mocker.patch("flask_login.utils._get_user", return_value=user1)

    report_1 = report_with_submissions_fixture()
    report_2 = report_with_submissions_fixture()

    report_1.submission.assigned_underwriters.append(SubmissionUser(user=user2))
    db.session.commit()

    res = get_reports_by_user(
        user_id=user1.id,
        assignees=[user2.id],
        date_type="CREATED_AT",
        sorting=["CREATED_AT"],
        descending=[False],
        page=1,
        per_page=10,
    )

    assert len(res["reports"]) == 1


def test_get_user_reports_show_renewals(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user1 = user_fixture()
    user2 = user_fixture(id=2)
    mocker.patch("flask_login.utils._get_user", return_value=user1)

    report1 = report_fixture()
    report2 = report_fixture(owner_id=user2.id)

    report2_1 = report_fixture()
    db.session.commit()

    submission1 = submission_fixture(report=report1)
    submission2 = submission_fixture(is_renewal=True, report=report2)

    submission2_1 = submission_fixture(owner_id=user2.id, is_renewal=True, report=report2_1)
    report1.submission = submission1
    report2.submission = submission2

    report2_1.submission = submission2_1

    db.session.commit()

    res = get_reports_by_user(1, show_renewals="Y")
    assert len(res["reports"]) == 1

    res2 = get_reports_by_user(1, show_renewals="N")
    assert len(res2["reports"]) == 1

    res3 = get_reports_by_user(1)
    assert len(res3["reports"]) == 2


def test_get_report_by_user_unverified(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report_1, submission_1 = report_and_submission_fixture(is_verification_required=True, is_verified=False)
    report_2, submission_2 = report_and_submission_fixture(is_verification_required=True, is_verified=True)
    report_3, submission_3 = report_and_submission_fixture(is_verification_required=False, is_verified=False)
    db.session.commit()

    report_1.submission = submission_1
    report_2.submission = submission_2
    report_3.submission = submission_3
    db.session.commit()

    db.session.add(ReportBundle(reports=[report_1, report_2, report_3]))
    db.session.commit()

    res = get_reports_by_user(user_id=user.id)

    assert len(res["reports"]) == 2
    assert len(res["reports"][0]["bundled_reports"]) == 1

    # this makes him trusted
    user.email = "<EMAIL>"
    res = get_reports_by_user(user_id=user.id)
    assert len(res["reports"]) == 3
    assert len(res["reports"][0]["bundled_reports"]) == 2


def test_get_reports_by_user_account_id(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report_1, submission_1 = report_and_submission_fixture(
        account_id="123", target_premium=1234, expired_premium=4321, sales=42000.0
    )
    report_2, submission_2 = report_and_submission_fixture()
    db.session.commit()

    report_1.submission = submission_1
    report_2.submission = submission_2
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, account_id="123")

    assert len(res["reports"]) == 1
    print(res)
    assert res["reports"][0]["submissions"][0]["account_id"] == "123"
    assert res["reports"][0]["submissions"][0]["target_premium"] == 1234
    assert res["reports"][0]["submissions"][0]["expired_premium"] == 4321
    assert res["reports"][0]["submissions"][0]["sales"] == 42000.0


def test_get_reports_by_user_sort_by_creation_date(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report_1, submission_1 = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()
    report_3, submission_3 = report_and_submission_fixture()
    report_4, submission_4 = report_and_submission_fixture()
    report_5, submission_5 = report_and_submission_fixture()
    db.session.commit()

    submission_1.created_at = datetime.now() - timedelta(days=10)
    submission_2.created_at = datetime.now() - timedelta(days=11)
    submission_3.created_at = datetime.now() - timedelta(days=9)
    submission_4.created_at = datetime.now() - timedelta(days=12)
    submission_5.created_at = datetime.now() - timedelta(days=8)
    report_1.created_at = datetime.now() - timedelta(days=1)
    report_2.created_at = datetime.now() - timedelta(days=2)
    report_3.created_at = datetime.now() - timedelta(days=3)
    report_4.created_at = datetime.now() - timedelta(days=4)
    report_5.created_at = datetime.now() - timedelta(days=5)
    db.session.commit()

    # sorting by report creation date, newest first
    p1 = get_reports_by_user(user.id, per_page=5, page=1, sorting=["CREATED_AT"])
    ids = [r["id"] for r in p1["reports"]]
    assert p1["total_reports"] == 5
    assert ids == [str(report_1.id), str(report_2.id), str(report_3.id), str(report_4.id), str(report_5.id)]


def test_get_reports_by_user_filter_creation_date(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report_1, submission_1 = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()
    report_3, submission_3 = report_and_submission_fixture()
    report_4, submission_4 = report_and_submission_fixture()
    report_5, submission_5 = report_and_submission_fixture()
    db.session.commit()

    submission_1.created_at = datetime.now() - timedelta(days=3)
    submission_2.created_at = datetime.now() - timedelta(days=11)
    submission_3.created_at = datetime.now() - timedelta(days=9)
    submission_4.created_at = datetime.now() - timedelta(days=12)
    submission_5.created_at = datetime.now() - timedelta(days=5)
    report_1.created_at = datetime.now() - timedelta(days=1)
    report_2.created_at = datetime.now() - timedelta(days=2)
    report_3.created_at = datetime.now() - timedelta(days=3)
    report_4.created_at = datetime.now() - timedelta(days=4)
    report_5.created_at = datetime.now() - timedelta(days=5)
    db.session.commit()

    date_2 = report_2.created_at
    date_before = str(date_2.month) + "/" + str(date_2.day) + "/" + str(date_2.year)
    date_4 = report_4.created_at
    date_after = str(date_4.month) + "/" + str(date_4.day) + "/" + str(date_4.year)

    # filtering by report creation date (from report_2 to report_4)
    p1 = get_reports_by_user(user.id, per_page=5, page=1, after=date_after, before=date_before, date_type="CREATED_AT")
    ids = [r["id"] for r in p1["reports"]]
    assert p1["total_reports"] == 3
    assert str(report_2.id) in ids and str(report_3.id) in ids and str(report_4.id) in ids

    p1 = get_reports_by_user(
        user.id, per_page=5, page=1, after_second=date_after, before_second=date_before, date_type_second="CREATED_AT"
    )
    ids = [r["id"] for r in p1["reports"]]
    assert p1["total_reports"] == 3
    assert str(report_2.id) in ids and str(report_3.id) in ids and str(report_4.id) in ids


def test_get_reports_by_user_clearing_issues(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    r1, s1 = report_and_submission_fixture()
    r2, s2 = report_and_submission_fixture()
    r3, s3 = report_and_submission_fixture(processing_state=SubmissionProcessingState.NEEDS_CLEARING)
    r4, s4 = report_and_submission_fixture()
    r5, s5 = report_and_submission_fixture(clearing_status=ClearingStatus.BLOCKED)
    r6, s6 = report_and_submission_fixture(clearing_status=ClearingStatus.BLOCKED)
    r7, s7 = report_and_submission_fixture(processing_state=SubmissionProcessingState.NEEDS_CLEARING)
    s1.clearing_issues = [SubmissionClearingIssue(submission_id=s1.id, is_resolved=True, suspected_report_id=r4.id)]
    s2.clearing_issues = [SubmissionClearingIssue(submission_id=s2.id, is_resolved=False, suspected_report_id=r4.id)]
    s3.clearing_issues = [
        SubmissionClearingIssue(submission_id=s3.id, is_resolved=False, suspected_report_id=r4.id, is_light=True),
        SubmissionClearingIssue(submission_id=s3.id, is_resolved=True, suspected_report_id=r4.id),
    ]
    s5.clearing_issues = [SubmissionClearingIssue(submission_id=s5.id, is_resolved=False, suspected_report_id=r4.id)]
    s7.clearing_issues = [
        SubmissionClearingIssue(submission_id=s7.id, is_resolved=False, suspected_report_id=r4.id, is_light=True),
        SubmissionClearingIssue(submission_id=s7.id, is_resolved=True, suspected_report_id=r4.id),
        SubmissionClearingIssue(submission_id=s7.id, is_resolved=False, suspected_report_id=r4.id),
    ]
    s2.clearing_status = ClearingStatus.IN_CLEARING_CONFLICT
    s7.clearing_status = ClearingStatus.IN_CLEARING_CONFLICT
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, clearing_stage="PENDING_CLEARING")

    assert len(res["reports"]) == 2
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r2.id) in reports_ids
    assert str(r7.id) in reports_ids

    res = get_reports_by_user(user_id=user.id, clearing_stage="POST_CLEARING")

    assert len(res["reports"]) == 3
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r1.id) in reports_ids
    assert str(r3.id) in reports_ids
    assert str(r4.id) in reports_ids

    res = get_reports_by_user(user_id=user.id, clearing_stage="BLOCKED_ON_CLEARING")

    assert len(res["reports"]) == 2
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r5.id) in reports_ids
    assert str(r6.id) in reports_ids

    res = get_reports_by_user(user_id=user.id, clearing_stage="ALL")

    assert len(res["reports"]) == 7

    res = get_reports_by_user(user_id=user.id, clearing_stage="PENDING_LIGHT_CLEARING")

    assert len(res["reports"]) == 2
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r3.id) in reports_ids
    assert str(r7.id) in reports_ids


def test_get_reports_by_user_clearing_sub_status(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    r1, s1 = report_and_submission_fixture()
    r2, s2 = report_and_submission_fixture()

    s1.clearing_status = ClearingStatus.IN_CLEARING_CONFLICT
    s1.clearing_sub_statuses = [
        SubmissionClearingSubStatus(status="Conflict", sub_status="Something"),
        SubmissionClearingSubStatus(status="Conflict", sub_status="Something else"),
        SubmissionClearingSubStatus(status="Missing Details", sub_status="Md"),
    ]

    s2.clearing_status = ClearingStatus.PRE_CLEARING
    s2.clearing_sub_statuses = [
        SubmissionClearingSubStatus(status="Missing Details", sub_status="Ez"),
        SubmissionClearingSubStatus(status="Missing Details", sub_status="Pz"),
    ]

    db.session.commit()

    res = get_reports_by_user(user_id=user.id, clearing_stage="IN_CLEARING_CONFLICT")

    assert len(res["reports"]) == 1
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r1.id) in reports_ids

    res = get_reports_by_user(user_id=user.id, clearing_stage="MISSING_DETAILS")

    assert len(res["reports"]) == 2
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r1.id) in reports_ids
    assert str(r2.id) in reports_ids


def test_get_reports_by_user_in_clearing_conflict_doesnt_find_cleared(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    r1, s1 = report_and_submission_fixture()
    r2, s2 = report_and_submission_fixture()

    s1.clearing_status = ClearingStatus.CLEARED
    s1.clearing_issues = [SubmissionClearingIssue(is_resolved=True, suspected_report_id=r2.id)]
    s1.clearing_sub_statuses = [
        SubmissionClearingSubStatus(
            status="Conflict",
            sub_status="Something X",
            submission_clearing_issue=s1.clearing_issues[0],
        ),
    ]
    s2.clearing_status = ClearingStatus.IN_CLEARING_CONFLICT
    s2.clearing_issues = [SubmissionClearingIssue(is_resolved=False, suspected_report_id=r1.id)]
    s2.clearing_sub_statuses = [
        SubmissionClearingSubStatus(
            status="Conflict",
            sub_status="Something Y",
            submission_clearing_issue=s2.clearing_issues[0],
        ),
    ]
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, clearing_stage="IN_CLEARING_CONFLICT")

    assert len(res["reports"]) == 1
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r2.id) in reports_ids


def test_get_reports_by_user_in_clearing_conflict_doesnt_find_clearing_in_progress(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    r1, s1 = report_and_submission_fixture()
    r2, s2 = report_and_submission_fixture()

    s1.clearing_status = ClearingStatus.CLEARING_IN_PROGRESS
    s1.clearing_issues = [SubmissionClearingIssue(is_resolved=False, suspected_report_id=r2.id)]
    s1.clearing_sub_statuses = [
        SubmissionClearingSubStatus(
            status="Conflict",
            sub_status="Something X",
            submission_clearing_issue=s1.clearing_issues[0],
        ),
    ]
    s2.clearing_status = ClearingStatus.IN_CLEARING_CONFLICT
    s2.clearing_issues = [SubmissionClearingIssue(is_resolved=False, suspected_report_id=r1.id)]
    s2.clearing_sub_statuses = [
        SubmissionClearingSubStatus(
            status="Conflict",
            sub_status="Something Y",
            submission_clearing_issue=s2.clearing_issues[0],
        ),
    ]
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, clearing_stage="IN_CLEARING_CONFLICT")

    assert len(res["reports"]) == 1
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r2.id) in reports_ids


def test_get_reports_by_user_preclearing_doesnt_find_missing_details(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    r1, s1 = report_and_submission_fixture()
    r2, s2 = report_and_submission_fixture()

    s1.clearing_status = ClearingStatus.PRE_CLEARING
    s2.clearing_status = ClearingStatus.PRE_CLEARING
    s2.clearing_sub_statuses = [
        SubmissionClearingSubStatus(status="Missing Details", sub_status="Detail X"),
    ]
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, clearing_stage="PRE_CLEARING")

    assert len(res["reports"]) == 1
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r1.id) in reports_ids


def test_get_reports_by_user_missing_details_doesnt_find_cleared(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    r1, s1 = report_and_submission_fixture()
    r2, s2 = report_and_submission_fixture()

    s1.clearing_status = ClearingStatus.CLEARED
    s1.clearing_sub_statuses = [
        SubmissionClearingSubStatus(status="Missing Details", sub_status="Detail X"),
    ]
    s2.clearing_status = ClearingStatus.PRE_CLEARING
    s2.clearing_sub_statuses = [
        SubmissionClearingSubStatus(status="Missing Details", sub_status="Detail Y"),
    ]
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, clearing_stage="MISSING_DETAILS")

    assert len(res["reports"]) == 1
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r2.id) in reports_ids


def test_get_reports_by_user_missing_details_doesnt_find_blocked(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    r1, s1 = report_and_submission_fixture()
    r2, s2 = report_and_submission_fixture()

    s1.clearing_status = ClearingStatus.BLOCKED
    s1.clearing_sub_statuses = [
        SubmissionClearingSubStatus(status="Missing Details", sub_status="Detail X"),
    ]
    s2.clearing_status = ClearingStatus.PRE_CLEARING
    s2.clearing_sub_statuses = [
        SubmissionClearingSubStatus(status="Missing Details", sub_status="Detail Y"),
    ]
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, clearing_stage="MISSING_DETAILS")

    assert len(res["reports"]) == 1
    reports_ids = [r["id"] for r in res["reports"]]
    assert str(r2.id) in reports_ids


def test_creating_user_with_org(app_context, mocker):
    request = {
        "email": "<EMAIL>",
        "external_id": "foo",
        "full_name": "FooBar",
        "create_organization_if_not_assigned": True,
    }
    response, status_code = create_user_if_not_present(request)

    organization_id = response["organization_id"]
    organization = Organization.query.get(organization_id)
    user = User.query.get(response["user_id"])

    assert status_code == 201
    assert organization_id is not None
    assert organization.name == "FooBar"
    assert organization.settings is not None
    assert organization.settings.whitelisted_emails == ["<EMAIL>", "<EMAIL>"]
    assert organization.settings.support_email == "<EMAIL>"
    assert organization.settings.total_credits == DEFAULT_CREDITS
    assert user.remaining_credits == DEFAULT_CREDITS
    assert user.cross_organization_access is False


def test_create_user_email_domain_lookup_success(app_context):
    org = organization_fixture(id=100, name="Test Organization")
    settings_fixture(organization_id=100, email_domains=["testdomain.com", "anotherdomain.org"])
    db.session.commit()

    request = {
        "email": "<EMAIL>",
        "external_id": "test_external_id",
        "full_name": "Test User",
    }
    response, status_code = create_user_if_not_present(request)

    assert status_code == 201
    assert response["organization_id"] == 100
    assert response["email"] == "<EMAIL>"

    user = User.query.get(response["user_id"])
    assert user.organization_id == 100
    assert user.email == "<EMAIL>"


def test_create_user_email_domain_lookup_case_insensitive(app_context):
    org = organization_fixture(id=101, name="Case Test Organization")
    settings_fixture(organization_id=101, email_domains=["CaseDomain.COM"])
    db.session.commit()

    request = {
        "email": "<EMAIL>",
        "external_id": "test_external_id_case",
        "full_name": "Case Test User",
    }
    response, status_code = create_user_if_not_present(request)

    assert status_code == 201
    assert response["organization_id"] == 101
    assert response["email"] == "<EMAIL>"


def test_create_user_email_domain_lookup_multiple_orgs(app_context):
    org1 = organization_fixture(id=102, name="Organization 1")
    org2 = organization_fixture(id=103, name="Organization 2")
    settings_fixture(organization_id=102, email_domains=["domain1.com"])
    settings_fixture(organization_id=103, email_domains=["domain2.com"])
    db.session.commit()

    request1 = {
        "email": "<EMAIL>",
        "external_id": "test_external_id_1",
        "full_name": "User 1",
    }
    response1, status_code1 = create_user_if_not_present(request1)

    request2 = {
        "email": "<EMAIL>",
        "external_id": "test_external_id_2",
        "full_name": "User 2",
    }
    response2, status_code2 = create_user_if_not_present(request2)

    assert status_code1 == 201
    assert response1["organization_id"] == 102
    assert status_code2 == 201
    assert response2["organization_id"] == 103


def test_create_user_email_domain_lookup_failure_fallback(app_context):
    org = organization_fixture(id=104, name="Fallback Organization")
    settings_fixture(organization_id=104, email_domains=["alloweddomain.com"])
    db.session.commit()

    request = {
        "email": "<EMAIL>",
        "external_id": "test_external_id_unknown",
        "full_name": "Unknown Domain User",
        "create_organization_if_not_assigned": True,
    }
    response, status_code = create_user_if_not_present(request)

    assert status_code == 201
    assert response["organization_id"] != 104

    user = User.query.get(response["user_id"])
    created_org = Organization.query.get(response["organization_id"])
    assert created_org.name == "Unknown Domain User"


def test_create_user_email_domain_lookup_no_settings(app_context):
    org = organization_fixture(id=105, name="No Settings Organization")
    db.session.commit()

    request = {
        "email": "<EMAIL>",
        "external_id": "test_external_id_no_settings",
        "full_name": "No Settings User",
        "create_organization_if_not_assigned": True,
    }
    response, status_code = create_user_if_not_present(request)

    assert status_code == 201
    assert response["organization_id"] != 105

    user = User.query.get(response["user_id"])
    created_org = Organization.query.get(response["organization_id"])
    assert created_org.name == "No Settings User"


def test_create_user_email_domain_lookup_empty_domains(app_context):
    org = organization_fixture(id=106, name="Empty Domains Organization")
    settings_fixture(organization_id=106, email_domains=[])
    db.session.commit()

    request = {
        "email": "<EMAIL>",
        "external_id": "test_external_id_empty",
        "full_name": "Empty Domains User",
        "create_organization_if_not_assigned": True,
    }
    response, status_code = create_user_if_not_present(request)

    assert status_code == 201
    assert response["organization_id"] != 106

    user = User.query.get(response["user_id"])
    created_org = Organization.query.get(response["organization_id"])
    assert created_org.name == "Empty Domains User"


def test_create_user_email_domain_lookup_with_subdomain(app_context):
    org = organization_fixture(id=107, name="Subdomain Organization")
    settings_fixture(organization_id=107, email_domains=["mail.company.com"])
    db.session.commit()

    request = {
        "email": "<EMAIL>",
        "external_id": "test_external_id_subdomain",
        "full_name": "Subdomain User",
    }
    response, status_code = create_user_if_not_present(request)

    assert status_code == 201
    assert response["organization_id"] == 107
    assert response["email"] == "<EMAIL>"


def test_create_user_email_domain_lookup_priority_over_organization_get_id_by_email(app_context, mocker):
    org1 = organization_fixture(id=108, name="Organization from get_id_by_email", email_domain="priority.com")
    org2 = organization_fixture(id=109, name="Organization from Settings")
    settings_fixture(organization_id=109, email_domains=["priority.com"])
    db.session.commit()

    mock_get_id_by_email = mocker.patch.object(Organization, 'get_id_by_email', return_value=108)

    request = {
        "email": "<EMAIL>",
        "external_id": "test_external_id_priority",
        "full_name": "Priority User",
    }
    response, status_code = create_user_if_not_present(request)

    assert status_code == 201
    assert response["organization_id"] == 109
    mock_get_id_by_email.assert_called_once_with("<EMAIL>")


@fixture
def target_uw():
    org = organization_fixture()
    target_uw = User(name="Bozhilov, Ivan", email="<EMAIL>", organization_id=org.id)
    db.session.add(target_uw)
    db.session.commit()
    return target_uw


@fixture
def dummy_submission():
    user_fixture()
    _, submission = report_and_submission_fixture()
    return submission


def test_get_underwriter_doesnt_return_underwriter_from_different_organization(app_context):
    organization_fixture(id=1)
    organization_fixture(id=2)
    user1 = User(
        id=1, name="Bozhilov, Ivan", email="<EMAIL>", role=UserRoleEnum.manager, organization_id=1
    )
    user2 = User(
        id=2, name="Bojilov, Ivan", email="<EMAIL>", role=UserRoleEnum.manager, organization_id=2
    )
    db.session.add(user1)
    db.session.add(user2)
    _, submission = report_and_submission_fixture(owner_id=user1.id)
    db.session.commit()
    request1 = {"name": "bojilov, IVAN", "email": "<EMAIL>", "submission": submission}
    uw = get_underwriter(**request1)
    assert uw is None


def test_get_underwriter(app_context, target_uw, dummy_submission):
    request1 = {"name": "bozhilov, IVAN", "email": " <EMAIL> ", "submission": dummy_submission}
    uw = get_underwriter(**request1)
    assert uw.id == target_uw.id


def test_get_user_by_email(app_context, mocker):
    organization_fixture()
    user1 = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user1)

    user = get_user_by_attributes(email="<EMAIL>")

    assert user is not None
    assert user["email"] == "<EMAIL>"

    # Uppercase email
    user = get_user_by_attributes(email="<EMAIL>")

    assert user is not None
    assert user["email"] == "<EMAIL>"

    with pytest.raises(NotFound):
        get_user_by_attributes(email="<EMAIL>")

    with pytest.raises(NotFound):
        get_user_by_attributes(email=None)


def test_get_users_by_organization(app_context, mocker):
    organization_fixture()
    # Enabled
    user1 = user_fixture(id=1, external_id="external_id_1", is_enabled=True)
    mocker.patch("flask_login.utils._get_user", return_value=user1)

    # No external_id means disabled
    user_fixture(id=2, is_enabled=True)

    # Disabled
    user_fixture(id=3, external_id="external_id_3", is_enabled=False)

    users = get_users_by_organization(organization_id=1)
    assert len(users) == 1
    assert users[0]["id"] == 1


def test_get_all_users(app_context, mocker):
    organization_fixture(id=1)
    organization_fixture(id=2)
    # Enabled
    user1 = user_fixture(id=1, external_id="external_id_1", is_enabled=True, organization_id=1)
    mocker.patch("flask_login.utils._get_user", return_value=user1)

    # No external_id means disabled
    user_fixture(id=2, is_enabled=True, organization_id=1)

    # Disabled
    user_fixture(id=3, external_id="external_id_3", is_enabled=False)

    # Enabled
    user_fixture(id=4, organization_id=2, is_enabled=True, external_id="external_id_4")

    users = get_all_users()

    assert len(users) == 2
    any(user["id"] == 1 for user in users)
    any(user["id"] == 4 for user in users)


def test_get_reports_by_user_internal_account_id(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report, submission = report_and_submission_fixture()
    report_1, submission_1 = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()
    report_3, submission_3 = report_and_submission_fixture()
    report_4, submission_4 = report_and_submission_fixture()
    db.session.commit()

    uuid1 = "********-1111-1111-1111-********1111"
    uuid2 = "*************-2222-2222-************"
    uuid3 = "*************-3333-3333-************"

    submission.businesses.append(
        SubmissionBusiness(business_id=uuid1, named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED)
    )
    submission.businesses.append(SubmissionBusiness(business_id=uuid2))

    submission_1.businesses.append(
        SubmissionBusiness(business_id=uuid1, named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED)
    )
    submission_2.businesses.append(SubmissionBusiness(business_id=uuid2))
    submission_3.businesses.append(SubmissionBusiness(business_id=uuid3))
    submission_4.businesses.append(SubmissionBusiness(business_id=uuid1))
    submission_4.businesses.append(
        SubmissionBusiness(business_id=uuid2, named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED)
    )

    db.session.commit()

    res = get_reports_by_user(user_id=user.id, account_id=f"business-intersection-with:{report.id}")

    assert len(res["reports"]) == 1
    assert res["reports"][0]["id"] == str(report_1.id)


def test_get_report_by_user_coverages(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    c1 = coverage_fixture(name="c1", coverage_types=[])
    c2 = coverage_fixture(name="c2", coverage_types=[])
    c3 = coverage_fixture(name="c3", coverage_types=[CoverageType.PRIMARY])
    c4 = coverage_fixture(name="c4", coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS])

    report_1, submission_1 = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()
    report_3, submission_3 = report_and_submission_fixture()
    report_4, submission_4 = report_and_submission_fixture()
    db.session.commit()

    submission_1.coverages.append(SubmissionCoverage(coverage=c1))
    submission_2.coverages.append(SubmissionCoverage(coverage=c2))
    submission_3.coverages.append(SubmissionCoverage(coverage=c3, coverage_type=CoverageType.PRIMARY))

    submission_4.coverages.append(SubmissionCoverage(coverage=c2))
    submission_4.coverages.append(SubmissionCoverage(coverage=c4, coverage_type=CoverageType.EXCESS))
    db.session.commit()

    # single
    q1 = get_reports_by_user(user.id, coverages=[f"{c1.id}"])
    assert q1["total_reports"] == 1

    # intersection
    q2 = get_reports_by_user(user.id, coverages=[f"{c1.id}", f"{c2.id}"])
    assert q2["total_reports"] == 0
    q4 = get_reports_by_user(user.id, coverages=[f"{c4.id}:EXCESS", f"{c2.id}"])
    assert q4["total_reports"] == 1

    # union
    q5 = get_reports_by_user(user.id, coverages=[f"{c1.id}", f"{c2.id}"], coverage_operator="OR")
    assert q5["total_reports"] == 3

    # by coverage type
    q3 = get_reports_by_user(user.id, coverages=[f"{c3.id}"])
    assert q3["total_reports"] == 1
    q3 = get_reports_by_user(user.id, coverages=[f"{c3.id}:PRIMARY"])
    assert q3["total_reports"] == 1
    q3 = get_reports_by_user(user.id, coverages=[f"{c3.id}:EXCESS"])
    assert q3["total_reports"] == 0


def test_get_user_reports_user_groups(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user1 = user_fixture()
    user2 = user_fixture(id=2, email="<EMAIL>")
    mocker.patch("flask_login.utils._get_user", return_value=user2)

    report_1 = report_with_submissions_fixture()
    report_2 = report_with_submissions_fixture()

    group = user_group_fixture()
    group_2 = user_group_fixture()

    group.users.append(user2)
    group_2.users.append(user2)
    db.session.commit()

    group.report_permissions.append(ReportPermission(report_id=report_1.id, permission_type=PermissionType.VIEWER))
    group.report_permissions.append(ReportPermission(report_id=report_2.id, permission_type=PermissionType.EDITOR))

    group_2.report_permissions.append(ReportPermission(report_id=report_1.id, permission_type=PermissionType.COMMENTER))

    db.session.commit()

    res = get_reports_by_user(2)

    assert len(res["reports"]) == 2

    perms = {
        res["reports"][0]["id"]: res["reports"][0]["current_user_permission_type"],
        res["reports"][1]["id"]: res["reports"][1]["current_user_permission_type"],
    }

    assert perms == {str(report_1.id): PermissionType.COMMENTER.name, str(report_2.id): PermissionType.EDITOR.name}


def test_get_user_reports_emails_and_pagination(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture(id=1)
    mocker.patch("flask_login.utils._get_user", return_value=user)

    _seed_12_reports_with_multiple_emails()
    db.session.commit()

    res1 = get_reports_by_user(1, include_email=True)
    res2 = get_reports_by_user(1, include_email=True, per_page=10, page=1)

    assert len(res1["reports"]) == 12
    assert res1["total_reports"] == 12
    assert len(res2["reports"]) == 10
    assert res2["total_reports"] == 12


def _seed_12_reports_with_multiple_emails():
    for i in range(12):
        correspondence = report_email_correspondence_fixture(db_flush=False)
        for _ in range(7):
            email_fixture(correspondence_id=correspondence.id)
        report_with_submissions_fixture(db_flush=False, correspondence_id=correspondence.id)


def test_get_user_reports_unverified_flag(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user1 = user_fixture(id=1)
    user2 = user_fixture(id=2)
    report_with_submissions_fixture(db_flush=False, is_verified=True)
    report_with_submissions_fixture(db_flush=False, is_verified=True)
    report_with_submissions_fixture(db_flush=True, is_verified=False, is_verification_required=True)
    db.session.commit()

    mocker.patch("flask_login.utils._get_user", return_value=user2)
    res1 = get_reports_by_user(2, include_unverified=True)
    assert len(res1["reports"]) == 3
    mocker.patch("flask_login.utils._get_user", return_value=user1)
    res2 = get_reports_by_user(1, include_unverified=False)
    assert len(res2["reports"]) == 2


def test_get_user_reports_organization_permission_level(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user1 = user_fixture()
    user2 = user_fixture(id=2, email="<EMAIL>")
    mocker.patch("flask_login.utils._get_user", return_value=user2)

    report_with_submissions_fixture(organization_permission_level=PermissionType.EDITOR)

    db.session.commit()

    res = get_reports_by_user(2)

    assert len(res["reports"]) == 1


def test_get_user_reports_organization_permission_level_bundled_submission(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user1 = user_fixture()
    user2 = user_fixture(id=2, email="<EMAIL>")
    mocker.patch("flask_login.utils._get_user", return_value=user2)

    r0, s0 = report_and_submission_fixture()
    r1, s1 = report_and_submission_fixture(organization_permission_level=PermissionType.EDITOR)
    r2, s2 = report_and_submission_fixture(organization_permission_level=PermissionType.COMMENTER)

    db.session.add(ReportBundle(reports=[r0, r1, r2]))

    db.session.commit()

    res = get_reports_by_user(2, per_page=1)

    assert len(res["reports"]) == 1

    perms = [
        res["reports"][0]["bundled_reports"][0]["current_user_permission_type"],
        res["reports"][0]["bundled_reports"][1]["current_user_permission_type"],
        res["reports"][0]["current_user_permission_type"],
    ]

    assert None in perms
    assert "EDITOR" in perms
    assert "COMMENTER" in perms


def test_get_user_reports_organization_permission_level_bundled_submission_read_only(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user1 = user_fixture()
    user2 = user_fixture(id=2, email="<EMAIL>", is_read_only_account=True)
    mocker.patch("flask_login.utils._get_user", return_value=user2)

    r0, s0 = report_and_submission_fixture()
    r1, s1 = report_and_submission_fixture(organization_permission_level=PermissionType.EDITOR)
    r2, s2 = report_and_submission_fixture(organization_permission_level=PermissionType.COMMENTER)

    db.session.add(ReportBundle(reports=[r0, r1, r2]))

    db.session.commit()

    res = get_reports_by_user(2, per_page=1)

    assert len(res["reports"]) == 1

    perms = [
        res["reports"][0]["bundled_reports"][0]["current_user_permission_type"],
        res["reports"][0]["bundled_reports"][1]["current_user_permission_type"],
        res["reports"][0]["current_user_permission_type"],
    ]

    assert set(perms) == {None, "VIEWER"}


def test_get_user_reports_in_flight(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user_fixture()
    user2 = user_fixture(id=254, email="<EMAIL>")
    mocker.patch("flask_login.utils._get_user", return_value=user2)

    report_and_submission_fixture()

    db.session.commit()

    res = get_reports_by_user(
        254, stage=["ON_MY_PLATE", "INDICATED", "WAITING_FOR_OTHERS", "QUOTED"], clearing_stage="POST_CLEARING"
    )

    assert len(res["reports"]) == 0


def test_get_reports_diff_sortings(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report_1, submission_1 = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()
    report_3, submission_3 = report_and_submission_fixture()
    report_4, submission_4 = report_and_submission_fixture()
    report_5, submission_5 = report_and_submission_fixture()
    db.session.commit()

    report_1.name = "a"
    report_2.name = "b"
    report_3.name = "c"
    report_4.name = "d"
    report_5.name = "e"
    submission_1.sales = 10
    submission_2.sales = 9
    submission_3.sales = 8
    submission_4.sales = 7
    submission_5.sales = 6
    db.session.commit()

    # by name, first page
    p1 = get_reports_by_user(user.id, per_page=3, page=1, sorting=["REPORT_NAME"])
    ids = [r["id"] for r in p1["reports"]]
    assert p1["total_reports"] == 5
    assert ids == [str(report_5.id), str(report_4.id), str(report_3.id)]

    # by name, 2nd page
    p1 = get_reports_by_user(user.id, per_page=3, page=2, sorting=["REPORT_NAME"])
    ids = [r["id"] for r in p1["reports"]]
    assert p1["total_reports"] == 5
    assert ids == [str(report_2.id), str(report_1.id)]

    # by sales
    p1 = get_reports_by_user(user.id, per_page=5, page=1, sorting=["SALES"])
    ids = [r["id"] for r in p1["reports"]]
    assert p1["total_reports"] == 5
    assert ids == [str(report_1.id), str(report_2.id), str(report_3.id), str(report_4.id), str(report_5.id)]

    p1 = get_reports_by_user(user.id, per_page=3, page=1, sorting=["EFFECTIVE_AT"])
    assert p1["total_reports"] == 5

    p1 = get_reports_by_user(user.id, per_page=3, page=1, sorting=["NAICS"])
    assert p1["total_reports"] == 5


def test_get_reports_by_user_sort_by_amount(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    cProperty = coverage_fixture(name="property")
    cAuto = coverage_fixture(name="auto")
    report_1, submission_1 = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()
    report_3, submission_3 = report_and_submission_fixture()
    report_4, submission_4 = report_and_submission_fixture()
    report_5, submission_5 = report_and_submission_fixture()
    db.session.commit()

    report_1.name = "a"
    report_2.name = "b"
    report_3.name = "c"
    report_4.name = "d"
    report_5.name = "e"

    submission_1.coverages = [
        submission_coverage_fixture(
            submission_id=submission_1.id, coverage_id=cProperty.id, quoted_premium=10, bound_premium=100
        ),
        submission_coverage_fixture(
            submission_id=submission_1.id, coverage_id=cAuto.id, quoted_premium=1, bound_premium=111
        ),
    ]
    submission_2.coverages = [
        submission_coverage_fixture(
            submission_id=submission_2.id, coverage_id=cProperty.id, quoted_premium=20, bound_premium=200
        )
    ]
    submission_3.coverages = [
        submission_coverage_fixture(
            submission_id=submission_3.id, coverage_id=cProperty.id, quoted_premium=1, bound_premium=1
        )
    ]
    submission_5.coverages = [
        submission_coverage_fixture(
            submission_id=submission_5.id,
            coverage_id=cAuto.id,
            quoted_premium=1000,
            bound_premium=12,
            total_premium=5000,
        )
    ]

    db.session.commit()

    res1 = get_reports_by_user(user.id, per_page=5, page=1, sorting=["AMOUNT_QUOTED"], descending=[False])
    res1ids = [r["id"] for r in res1["reports"]]
    assert res1ids == [str(report_3.id), str(report_1.id), str(report_2.id), str(report_5.id), str(report_4.id)]

    res2 = get_reports_by_user(user.id, per_page=5, page=1, sorting=["AMOUNT_QUOTED"], descending=[True])
    res2ids = [r["id"] for r in res2["reports"]]
    assert res2ids == [str(report_5.id), str(report_2.id), str(report_1.id), str(report_3.id), str(report_4.id)]

    res3 = get_reports_by_user(user.id, per_page=5, page=1, sorting=["AMOUNT_BOUND"], descending=[True])
    res3ids = [r["id"] for r in res3["reports"]]
    assert res3ids == [str(report_1.id), str(report_2.id), str(report_5.id), str(report_3.id), str(report_4.id)]

    res4 = get_reports_by_user(
        user.id, per_page=5, page=1, sorting=["TOTAL_PREMIUM_OR_BOUND_PREMIUM"], descending=[True]
    )
    res4ids = [r["id"] for r in res4["reports"]]
    assert res4ids == [str(report_5.id), str(report_1.id), str(report_2.id), str(report_3.id), str(report_4.id)]

    res5 = get_reports_by_user(user.id, per_page=5, page=1, sorting=["BOUND_PREMIUM"], descending=[True])
    res5ids = [r["id"] for r in res5["reports"]]
    assert res5ids == [str(report_1.id), str(report_2.id), str(report_5.id), str(report_3.id), str(report_4.id)]

    res5 = get_reports_by_user(user.id, per_page=5, page=1, sorting=["QUOTED_PREMIUM"], descending=[True])
    res5ids = [r["id"] for r in res5["reports"]]
    assert res5ids == [str(report_5.id), str(report_2.id), str(report_1.id), str(report_3.id), str(report_4.id)]


def test_close_old_open_reports_on_add_another_user(app_context, mocker):
    organization_fixture()

    user1 = user_fixture(id=1, email="<EMAIL>")
    report1 = report_with_submissions_fixture(organization_permission_level=PermissionType.EDITOR)
    report2 = report_with_submissions_fixture(
        organization_permission_level=PermissionType.EDITOR, created_at="1980-01-01 20:36:00.000000 +00:00"
    )
    report3 = report_with_submissions_fixture(organization_permission_level=PermissionType.EDITOR)

    user2 = user_fixture(id=2, email="<EMAIL>")
    mocker.patch("flask_login.utils._get_user", return_value=user2)
    db.session.commit()

    user_open_report_fixture(user_id=2, report_id=report1.id)
    user_open_report_fixture(user_id=2, report_id=report2.id, created_at=datetime(1980, 1, 1, 20, 37, 0, 0))
    db.session.commit()

    res = get_user_open_reports(2)
    assert len(res) == 2

    user3 = user_fixture(id=3, email="<EMAIL>")
    mocker.patch("flask_login.utils._get_user", return_value=user3)
    add_user_open_report(3, report3.id)

    mocker.patch("flask_login.utils._get_user", return_value=user2)
    res = get_user_open_reports(2)
    assert len(res) == 2  # nothing removed as we open reports for different users


def test_close_old_open_reports_on_add_same_user(app_context, mocker):
    organization_fixture()

    user1 = user_fixture(id=1, email="<EMAIL>")
    report1 = report_with_submissions_fixture(
        organization_permission_level=PermissionType.EDITOR, created_at="1980-01-01 20:36:00.000000 +00:00"
    )
    report2 = report_with_submissions_fixture(
        organization_permission_level=PermissionType.EDITOR, created_at="1980-01-01 20:36:00.000000 +00:00"
    )
    report3 = report_with_submissions_fixture(organization_permission_level=PermissionType.EDITOR)

    user2 = user_fixture(id=2, email="<EMAIL>")
    mocker.patch("flask_login.utils._get_user", return_value=user2)
    db.session.commit()

    user_open_report_fixture(user_id=2, report_id=report1.id, created_at=datetime(1980, 1, 1, 20, 37, 0, 0))
    user_open_report_fixture(user_id=2, report_id=report2.id, created_at=datetime(1980, 1, 1, 20, 37, 0, 0))
    db.session.commit()

    res = get_user_open_reports(2)
    assert len(res) == 2

    add_user_open_report(2, report3.id)

    mocker.patch("flask_login.utils._get_user", return_value=user2)
    res = get_user_open_reports(2)
    assert len(res) == 1  # 2 older reports should be removed


def test_delete_user_open_reports(app_context, mocker):
    organization_fixture()
    user1 = user_fixture(id=1, email="<EMAIL>")
    report1 = report_with_submissions_fixture(organization_permission_level=PermissionType.EDITOR)
    report2 = report_with_submissions_fixture(organization_permission_level=PermissionType.EDITOR)
    user2 = user_fixture(id=2, email="<EMAIL>", open_reports=[report1, report2])
    mocker.patch("flask_login.utils._get_user", return_value=user2)
    db.session.commit()

    res = get_user_open_reports(2)
    assert len(res) == 2

    report_ids = [r["id"] for r in res]

    delete_user_open_reports(2, {"report_ids": report_ids})

    res = get_user_open_reports(2)
    assert len(res) == 0


def test_get_report_by_user_with_unknown(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    brokerage = brokerage_fixture(name="2", organization_id=1)
    broker = broker_fixture(name="2 2", email="<EMAIL>", organization_id=1, brokerage_id=brokerage.id)

    report_1, submission_1 = report_and_submission_fixture(primary_naics_code=None, broker_id=None, brokerage_id=None)
    report_2, submission_2 = report_and_submission_fixture(
        primary_naics_code="NAICS_234567", broker_id=broker.id, brokerage_id=brokerage.id
    )
    report_3, submission_3 = report_and_submission_fixture(primary_naics_code="NAICS_231111")
    db.session.commit()

    report_1.submission = submission_1
    report_2.submission = submission_2
    report_3.submission = submission_3
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, naics_6=["NAICS_234567"])
    assert len(res["reports"]) == 1
    res = get_reports_by_user(user_id=user.id, naics_6=["unknown"])
    assert len(res["reports"]) == 1
    res = get_reports_by_user(user_id=user.id, naics_2=["NAICS_23"])
    assert len(res["reports"]) == 2
    res = get_reports_by_user(user_id=user.id, naics_2=["NAICS_23", "unknown"])
    assert len(res["reports"]) == 3
    res = get_reports_by_user(user_id=user.id, naics_6=["unknown", "NAICS_231111"])
    assert len(res["reports"]) == 2

    res = get_reports_by_user(user_id=user.id, brokerage_ids=["unknown", str(brokerage.id)])
    assert len(res["reports"]) == 3
    res = get_reports_by_user(user_id=user.id, brokerage_ids=["unknown"])
    assert len(res["reports"]) == 2
    res = get_reports_by_user(user_id=user.id, brokerage_ids=[str(brokerage.id)])
    assert len(res["reports"]) == 1

    res = get_reports_by_user(user_id=user.id, broker_ids=["unknown", str(broker.id)])
    assert len(res["reports"]) == 3
    res = get_reports_by_user(user_id=user.id, broker_ids=[str(broker.id)])
    assert len(res["reports"]) == 1


def test_get_report_by_user_with_wc_info(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    f = file_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report, sub = report_and_submission_fixture()
    db.session.commit()

    wc_info_1 = WorkersCompExperience(
        type="ACORD_130", experience_modification=0.7, submission_id=sub.id, risk_name="f", file_id=f.id
    )
    wc_info_2 = WorkersCompExperience(
        type="WCIRB",
        state="CA",
        rating_effective_date="2017-12-12T00:00:00Z",
        experience_modification=0.8,
        submission_id=sub.id,
        risk_name="f1",
        source=WorkersCompExperienceSource.API.value,
    )
    wc_info_3 = WorkersCompExperience(
        type="WCIRB",
        state="NY",
        rating_effective_date=datetime.now(),
        experience_modification=0.9,
        submission_id=sub.id,
        risk_name="f2",
        source=WorkersCompExperienceSource.API.value,
    )
    rating_info = WorkersCompStateRatingInfo(submission_id=sub.id, class_code="45212", file_id=f.id)
    db.session.add(wc_info_1)
    db.session.add(wc_info_2)
    db.session.add(wc_info_3)
    db.session.add(rating_info)
    db.session.commit()
    db.session.close()

    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    res = get_reports_by_user(user_id=user.id, with_wc_info=True)
    assert len(res["reports"]) == 1
    xmods = res["reports"][0]["submissions"][0]["workers_comp_experience"]
    assert {x["experience_modification"] for x in xmods} == {0.7, 0.9}


def test_only_not_bookmarked(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    user_2 = user_fixture(id=2)
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report, submission = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, only_not_bookmarked=True)
    assert len(res["reports"]) == 2

    submission_bookmark_fixture(submission_id=submission.id, user_id=user_2.id)
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, only_not_bookmarked=True)
    assert len(res["reports"]) == 2

    submission_bookmark_fixture(submission_id=submission_2.id, user_id=user.id)
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, only_not_bookmarked=True)
    assert len(res["reports"]) == 1


def test_get_reports_by_broker_group_id(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    user_2 = user_fixture(id=2)
    mocker.patch("flask_login.utils._get_user", return_value=user)

    brokerage = brokerage_fixture(name="1", organization_id=1)
    broker = broker_fixture(name="1 1", email="<EMAIL>", organization_id=1, brokerage_id=brokerage.id)
    db.session.commit()

    report_1, submission_1 = report_and_submission_fixture(broker_id=broker.id, brokerage_id=brokerage.id)
    report_2, submission_2 = report_and_submission_fixture(broker_id=broker.id, brokerage_id=brokerage.id)

    group_1 = broker_group_fixture(
        name="1",
        organization_id=1,
    )
    group_2 = broker_group_fixture(
        name="2",
        organization_id=1,
    )
    db.session.commit()

    broker_group_mapping_fixture(broker_group_id=group_1.id, broker_id=broker.id, user_id=user.id)
    broker_group_mapping_fixture(broker_group_id=group_2.id, broker_id=broker.id, organization_id=1)

    submission_1.assigned_underwriters.append(SubmissionUser(user=user))
    submission_2.assigned_underwriters.append(SubmissionUser(user=user_2))

    db.session.commit()

    res = get_reports_by_user(user_id=user.id, broker_group_ids=[group_1.id])
    assert len(res["reports"]) == 1
    assert res["reports"][0]["id"] == str(report_1.id)

    res = get_reports_by_user(user_id=user.id, broker_group_ids=[group_2.id])
    assert len(res["reports"]) == 1
    assert res["reports"][0]["id"] == str(report_2.id)

    res = get_reports_by_user(user_id=user.id, not_broker_group_ids=[group_1.id])
    assert len(res["reports"]) == 1
    assert res["reports"][0]["id"] == str(report_2.id)

    res = get_reports_by_user(user_id=user.id, not_broker_group_ids=[group_2.id])
    assert len(res["reports"]) == 1
    assert res["reports"][0]["id"] == str(report_1.id)


def test_get_reports_by_broker_group_id_2(app_context, mocker):
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user", return_value=False
    )
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    brokerage = brokerage_fixture(name="1", organization_id=1)
    broker = broker_fixture(name="1 1", email="<EMAIL>", organization_id=1, brokerage_id=brokerage.id)
    broker_2 = broker_fixture(name="1 2", email="<EMAIL>", organization_id=1, brokerage_id=brokerage.id)
    db.session.commit()

    report_1, submission_1 = report_and_submission_fixture(broker_id=broker.id, brokerage_id=brokerage.id)
    report_2, submission_2 = report_and_submission_fixture(broker_id=broker_2.id, brokerage_id=brokerage.id)

    group_1 = broker_group_fixture(
        name="reward",
        organization_id=1,
    )
    group_2 = broker_group_fixture(
        name="develop",
        organization_id=1,
    )
    db.session.commit()

    broker_group_mapping_fixture(broker_group_id=group_1.id, broker_id=broker.id, user_id=user.id)
    broker_group_mapping_fixture(broker_group_id=group_2.id, broker_id=broker_2.id, user_id=user.id)

    submission_1.assigned_underwriters.append(SubmissionUser(user=user))
    submission_2.assigned_underwriters.append(SubmissionUser(user=user))

    group = user_group_fixture()
    group.users.append(user)
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, broker_group_ids=[group_1.id], not_assignee_groups=[group.id])
    assert len(res["reports"]) == 0

    res = get_reports_by_user(user_id=user.id, broker_group_ids=[group_1.id], assignee_groups=[group.id])
    assert len(res["reports"]) == 1
    assert res["reports"][0]["id"] == str(report_1.id)


def test_brokerage_office_filter(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    brokerage = brokerage_fixture(name="1", organization_id=1)
    brokerage_2 = brokerage_fixture(name="2", organization_id=1)
    brokerage_3 = brokerage_fixture(name="3", organization_id=1)

    report_and_submission_fixture(brokerage_id=brokerage.id, brokerage_office="Some, Cool")
    report_and_submission_fixture(brokerage_id=brokerage_2.id, brokerage_office="Not, Cool")
    report_and_submission_fixture(brokerage_id=brokerage_3.id, brokerage_office="Some, Cool")

    db.session.commit()

    res = get_reports_by_user(
        user_id=user.id, brokerage_offices=f'["{brokerage.id}:Some, Cool", "{brokerage_2.id}:Not, Cool"]'
    )

    assert len(res["reports"]) == 2


@pytest.mark.parametrize(
    "filters, expected_count",
    [
        ({"score_ml_min": 0.5}, 2),
        ({"score_ml_min": 1}, 0),
        ({"score_ml_min": 0.25}, 3),
        ({"score_ml_max": 0.5}, 2),
        ({"score_ml_max": 1}, 3),
        ({"score_ml_max": 0.24}, 0),
        ({"score_ml_min": 0.26, "score_ml_max": 0.5}, 1),
    ],
)
def test_score_ml_filter(app_context, mocker, filters, expected_count):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    recommendations_result = recommendation_result_fixture(score_ml=0.25)
    recommendations_result_2 = recommendation_result_fixture(score_ml=0.5)
    recommendations_result_3 = recommendation_result_fixture(score_ml=0.75)

    report_and_submission_fixture(recommendation_result=recommendations_result)
    report_and_submission_fixture(recommendation_result=recommendations_result_2)
    report_and_submission_fixture(recommendation_result=recommendations_result_3)

    db.session.commit()

    res = get_reports_by_user(user_id=user.id, **filters)
    assert len(res["reports"]) == expected_count, f"Expected {expected_count} reports, got {len(res['reports'])}"


def test_only_bind_likely_filter(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    recommendation_result_1 = recommendation_result_fixture(score_ml=0.49, score=49)
    recommendation_result_2 = recommendation_result_fixture(score_ml=0.8, score=80)
    recommendation_result_3 = recommendation_result_fixture(score_ml=0.8, score=80)

    _, submission_1 = report_and_submission_fixture(
        stage=SubmissionStage.QUOTED, recommendation_result=recommendation_result_1
    )  # False
    _, submission_2 = report_and_submission_fixture(
        stage=SubmissionStage.ON_MY_PLATE, recommendation_result=recommendation_result_2
    )  # True
    _, submission_3 = report_and_submission_fixture(
        stage=SubmissionStage.QUOTED_LOST, recommendation_result=recommendation_result_3
    )  # None
    _, submission_4 = report_and_submission_fixture(stage=SubmissionStage.QUOTED_BOUND)  # true

    db.session.commit()

    res = get_reports_by_user(user_id=user.id, **{"only_bind_likely": True})
    assert len(res["reports"]) == 2, f"Expected 2 reports, got {len(res['reports'])}"
    submission_ids = {x["submissions"][0]["id"] for x in res["reports"]}
    assert submission_ids == {
        str(submission_2.id),
        str(submission_4.id),
    }, f"Expected submission ids {submission_ids}, got {submission_ids}"
    res = get_reports_by_user(user_id=user.id)
    assert len(res["reports"]) == 4, f"Expected 4 reports, got {len(res['reports'])}"


def test_clearing_assignee_filter(app_context, mocker):
    organization_fixture()
    user_1 = user_fixture(id=1)
    user_2 = user_fixture(id=2)
    user_3 = user_fixture(id=3)
    mocker.patch("flask_login.utils._get_user", return_value=user_1)

    report_1, sub_1 = report_and_submission_fixture(clearing_assignee_id=user_1.id)
    report_2, sub_2 = report_and_submission_fixture(clearing_assignee_id=user_2.id)
    report_3, sub_3 = report_and_submission_fixture(clearing_assignee_id=user_3.id)
    report_4, sub_4 = report_and_submission_fixture()
    report_5, sub_5 = report_and_submission_fixture(clearing_assignee_id=None)
    db.session.commit()

    res1 = get_reports_by_user(user_id=user_1.id, clearing_assignees=[user_1.id, user_2.id])
    assert {str(report_1.id), str(report_2.id)} == {x["id"] for x in res1["reports"]}

    res2 = get_reports_by_user(
        user_id=user_1.id, clearing_assignees=[user_1.id, user_2.id], include_clearing_user_unassigned=True
    )
    assert {str(report_1.id), str(report_2.id), str(report_4.id), str(report_5.id)} == {
        x["id"] for x in res2["reports"]
    }

    res3 = get_reports_by_user(user_id=user_1.id, include_clearing_user_unassigned=True)
    assert {str(report_4.id), str(report_5.id)} == {x["id"] for x in res3["reports"]}

    res4 = get_reports_by_user(user_id=user_1.id, not_clearing_assignees=[user_3.id])
    assert {str(report_1.id), str(report_2.id), str(report_4.id), str(report_5.id)} == {
        x["id"] for x in res4["reports"]
    }

    res5 = get_reports_by_user(
        user_id=user_1.id, not_clearing_assignees=[user_1.id], exclude_clearing_user_unassigned=True
    )
    assert {str(report_2.id), str(report_3.id)} == {x["id"] for x in res5["reports"]}

    res6 = get_reports_by_user(user_id=user_1.id, exclude_clearing_user_unassigned=True)
    assert {str(report_1.id), str(report_2.id), str(report_3.id)} == {x["id"] for x in res6["reports"]}


def test_not_filters(app_context, mocker):
    organization_fixture()
    user_1 = user_fixture()
    user_2 = user_fixture(id=2)
    mocker.patch("flask_login.utils._get_user", return_value=user_1)

    brokerage = brokerage_fixture(name="1", organization_id=1)
    broker = broker_fixture(name="1 1", email="<EMAIL>", organization_id=1, brokerage_id=brokerage.id)
    brokerage_2 = brokerage_fixture(name="2", organization_id=1)
    broker_2 = broker_fixture(name="2 2", email="<EMAIL>", organization_id=1, brokerage_id=brokerage_2.id)
    db.session.commit()

    c1 = coverage_fixture(name="c1", coverage_types=[])
    c2 = coverage_fixture(name="c2", coverage_types=[CoverageType.PRIMARY, CoverageType.EXCESS])

    report_1, submission_1 = report_and_submission_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, stage=SubmissionStage.QUOTED
    )
    report_2, submission_2 = report_and_submission_fixture(
        broker_id=broker_2.id, brokerage_id=brokerage_2.id, stage=SubmissionStage.DECLINED
    )
    report_3, submission_3 = report_and_submission_fixture(stage=SubmissionStage.ON_MY_PLATE, clearing_assignee_id=123)

    submission_1.assigned_underwriters.append(SubmissionUser(user=user_1))
    submission_2.assigned_underwriters.append(SubmissionUser(user=user_2))

    submission_1.coverages.append(SubmissionCoverage(coverage=c1))
    submission_1.coverages.append(SubmissionCoverage(coverage=c2, coverage_type=CoverageType.PRIMARY))

    submission_2.coverages.append(SubmissionCoverage(coverage=c2, coverage_type=CoverageType.EXCESS))
    submission_3.coverages.append(SubmissionCoverage(coverage=c2, coverage_type=CoverageType.PRIMARY))

    db.session.commit()

    res = get_reports_by_user(user_id=user_1.id, not_broker_ids=[str(broker.id)])
    matched_ids = {x["id"] for x in res["reports"]}
    assert matched_ids == {str(report_2.id), str(report_3.id)}
    res = get_reports_by_user(user_id=user_1.id, not_brokerage_ids=[str(brokerage.id)])
    matched_ids = {x["id"] for x in res["reports"]}
    assert matched_ids == {str(report_2.id), str(report_3.id)}

    res = get_reports_by_user(user_id=user_1.id, only_assigned=True)
    matched_ids = {x["id"] for x in res["reports"]}
    assert matched_ids == {str(report_1.id), str(report_2.id)}

    res = get_reports_by_user(user_id=user_1.id, not_assignees=[str(user_1.id)])
    matched_ids = {x["id"] for x in res["reports"]}
    assert matched_ids == {str(report_2.id), str(report_3.id)}

    res = get_reports_by_user(user_id=user_1.id, not_stage=["QUOTED", "DECLINED"])
    matched_ids = {x["id"] for x in res["reports"]}
    assert matched_ids == {str(report_3.id)}

    res = get_reports_by_user(
        user_id=user_1.id, not_coverages=[str(c1.id), str(c2.id) + ":EXCESS"], not_coverage_operator="OR"
    )
    matched_ids = {x["id"] for x in res["reports"]}
    assert matched_ids == {str(report_3.id)}

    res = get_reports_by_user(user_id=user_1.id, not_clearing_assignees=[123])
    matched_ids = {x["id"] for x in res["reports"]}
    assert matched_ids == {str(report_1.id), str(report_2.id)}


def test_get_reports_by_user_filter_insurance_accounting_date(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    now = datetime.now()
    report_1, submission_1 = report_and_submission_fixture(
        submission=submission_fixture(
            stage=SubmissionStage.QUOTED_LOST,
            quoted_date=now - timedelta(days=3),
            proposed_effective_date=now - timedelta(days=3),
        )
    )
    report_2, submission_2 = report_and_submission_fixture(
        submission=submission_fixture(
            stage=SubmissionStage.QUOTED_BOUND,
            bound_date=now - timedelta(days=3),
            proposed_effective_date=now - timedelta(days=7),
        )
    )
    report_3, submission_3 = report_and_submission_fixture(
        submission=submission_fixture(
            stage=SubmissionStage.QUOTED,
            quoted_date=now - timedelta(days=5),
            proposed_effective_date=now - timedelta(days=5),
        )
    )
    report_4, submission_4 = report_and_submission_fixture(
        submission=submission_fixture(
            stage=SubmissionStage.QUOTED_BOUND,
            bound_date=now - timedelta(days=7),
            proposed_effective_date=now - timedelta(days=7),
        )
    )
    report_5, submission_5 = report_and_submission_fixture(
        submission=submission_fixture(
            stage=SubmissionStage.ON_MY_PLATE, proposed_effective_date=now - timedelta(days=9)
        )
    )
    db.session.commit()

    date_before = (now - timedelta(days=3)).strftime("%m/%d/%Y")
    date_after = (now - timedelta(days=9)).strftime("%m/%d/%Y")

    p1 = get_reports_by_user(
        user.id, insurance_accounting_date_from=date_after, insurance_accounting_date_to=date_before
    )
    ids = [r["id"] for r in p1["reports"]]
    assert p1["total_reports"] == 3
    assert str(report_3.id) in ids and str(report_4.id) in ids and str(report_5.id) in ids

    date_before = (now - timedelta(days=1)).strftime("%m/%d/%Y")
    date_after = (now - timedelta(days=11)).strftime("%m/%d/%Y")
    p1 = get_reports_by_user(
        user.id, insurance_accounting_date_from=date_after, insurance_accounting_date_to=date_before
    )
    ids = [r["id"] for r in p1["reports"]]
    assert p1["total_reports"] == 5
    assert (
        str(report_1.id) in ids
        and str(report_2.id) in ids
        and str(report_3.id) in ids
        and str(report_4.id) in ids
        and str(report_5.id) in ids
    )


def test_get_reports_by_user_include_email(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report_1, submission_1 = report_and_submission_fixture()
    report_2, submission_2 = report_and_submission_fixture()
    db.session.commit()

    report_1.correspondence = ReportEmailCorrespondence()
    report_2.correspondence = ReportEmailCorrespondence()
    db.session.commit()

    email_fixture(correspondence_id=report_1.correspondence.id, message_id="11")
    email_fixture(correspondence_id=report_1.correspondence.id, message_id="22")
    email_fixture(correspondence_id=report_2.correspondence.id, message_id="33")
    email_fixture(correspondence_id=report_2.correspondence.id, message_id="44")
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, include_email=True)
    assert len(res["reports"]) == 2
    assert res["reports"][0]["id"] == str(report_2.id)

    email_fixture(correspondence_id=report_1.correspondence.id, message_id="55")
    db.session.commit()

    res = get_reports_by_user(user_id=user.id, include_email=True)
    assert len(res["reports"]) == 2
    assert res["reports"][0]["id"] == str(report_1.id)


def test_create_hub_template(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    db.session.commit()
    mocker.patch("flask_login.utils._get_user", return_value=user)
    res, status = create_hub_template(
        user.id,
        {
            "name": "Test",
            "is_default": False,
            "template": {},
        },
    )
    assert status == 201
    assert res["name"] == "Test"
    assert res["is_default"] is False
    assert res["template"] == {}
    assert res["template_type"] == HubTemplateType.HUB


def test_get_hub_templates(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    db.session.commit()

    hub_template_1 = hub_template_fixture(user_id=user.id, is_default=True, name="Test")
    hub_template_2 = hub_template_fixture(
        user_id=user.id, is_default=False, name="Test 2", template_type=HubTemplateType.DASHBOARD
    )
    db.session.commit()

    mocker.patch("flask_login.utils._get_user", return_value=user)
    # Empty should return only hub
    res = get_hub_templates(user.id)
    assert len(res["hub_templates"]) == 1
    target_template = res["hub_templates"][0]
    assert target_template["name"] == hub_template_1.name
    assert target_template["is_default"] is hub_template_1.is_default
    assert target_template["template_type"] == HubTemplateType.HUB

    res = get_hub_templates(user.id, template_type=HubTemplateType.DASHBOARD)
    target_template = res["hub_templates"][0]
    assert target_template["name"] == hub_template_2.name
    assert target_template["is_default"] is hub_template_2.is_default
    assert target_template["template_type"] == hub_template_2.template_type


def test_get_reports_by_user_filter_sic_codes(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    mocker.patch("flask_login.utils._get_user", return_value=user)

    report_1, submission_1 = report_and_submission_fixture(submission=submission_fixture(sic_code="SIC_2011"))
    report_2, submission_2 = report_and_submission_fixture(submission=submission_fixture(sic_code="SIC_2834"))
    report_3, submission_3 = report_and_submission_fixture(submission=submission_fixture(sic_code="SIC_2013"))
    report_4, submission_4 = report_and_submission_fixture(submission=submission_fixture(sic_code="SIC_5122"))
    report_5, submission_5 = report_and_submission_fixture(submission=submission_fixture(sic_code=None))
    db.session.commit()

    res = get_reports_by_user(user.id, sic_2=["SIC_20"])
    ids = [r["id"] for r in res["reports"]]
    assert res["total_reports"] == 2
    assert str(report_1.id) in ids
    assert str(report_3.id) in ids

    res = get_reports_by_user(user.id, sic_code=["SIC_2011", "SIC_2013"])
    ids = [r["id"] for r in res["reports"]]
    assert res["total_reports"] == 2
    assert str(report_1.id) in ids
    assert str(report_3.id) in ids

    res = get_reports_by_user(user.id, not_sic_2=["SIC_20"])
    ids = [r["id"] for r in res["reports"]]
    assert res["total_reports"] == 3
    assert str(report_2.id) in ids
    assert str(report_4.id) in ids
    assert str(report_5.id) in ids

    res = get_reports_by_user(user.id, not_sic_code=["SIC_2011", "SIC_2013"])
    ids = [r["id"] for r in res["reports"]]
    assert res["total_reports"] == 3
    assert str(report_2.id) in ids
    assert str(report_4.id) in ids
    assert str(report_5.id) in ids

    res = get_reports_by_user(user.id, sic_2=["SIC_20"], sic_code=["SIC_2011"])
    ids = [r["id"] for r in res["reports"]]
    assert res["total_reports"] == 1
    assert str(report_1.id) in ids

    res = get_reports_by_user(user_id=user.id, sic_2=["unknown"])
    assert len(res["reports"]) == 1
