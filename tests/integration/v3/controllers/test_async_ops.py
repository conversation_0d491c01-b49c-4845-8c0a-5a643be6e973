import time

from static_common.enums.origin import Origin

from copilot.models import db
from copilot.models.async_ops import AsyncOperationDB
from copilot.v3.controllers.async_ops import (
    create_new_async_op,
    get_async_op_by_id,
    get_latest_async_op_by_report_and_operation,
    update_async_op_by_id,
)
from tests.integration.factories import (
    organization_fixture,
    report_and_submission_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj


def _setup_report(mocker):
    organization = organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture(origin=Origin.EMAIL)
    db.session.commit()

    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=user.id,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=lambda x, y: True,
            is_being_impersonated=False,
            organization_id=organization.id,
        ),
    )
    return report, submission, user


def _create_async_op_and_assert(body):
    response = create_new_async_op(body)
    assert response[1] == 201
    data = response[0]
    assert data["id"] is not None
    assert data["created_at"] is not None
    return data


def _init_db_and_prepare_base_body(mocker):
    report, submission, user = _setup_report(mocker)
    base_body = {
        "report_id": str(report.id),
        "submission_id": str(submission.id),
        "organization_id": report.organization_id,
        "executing_user_email": user.email,
        "operation": "FinalizeConiferExternalClearing",
    }
    return base_body, report, user


def test_create_new_async_op_with_success(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)

    body_updates = {
        "status": "Success",
    }

    body = {**base_body, **body_updates}

    data = _create_async_op_and_assert(body)

    db_async_op = db.session.query(AsyncOperationDB).get(data["id"])
    assert db_async_op.report_id == report.id
    assert db_async_op.submission_id == report.submission.id
    assert db_async_op.organization_id == 1
    assert db_async_op.status == "Success"
    assert db_async_op.operation == "FinalizeConiferExternalClearing"
    assert db_async_op.error_details is None
    assert db_async_op.additional_data is None
    assert db_async_op.logical_identifier is None
    assert db_async_op.attempts == 1
    assert db_async_op.executing_user_email == "<EMAIL>"


def test_create_new_async_op_with_error_and_more_details(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)

    body_updates = {
        "status": "Failure",
        "error_details": {"error": "details"},
        "additional_data": {"additional": "data"},
        "logical_identifier": "logical_id",
        "attempts": 3,
        "executing_user_email": "<EMAIL>",
    }

    body = {**base_body, **body_updates}

    data = _create_async_op_and_assert(body)

    db_async_op = db.session.query(AsyncOperationDB).get(data["id"])
    assert db_async_op.report_id == report.id
    assert db_async_op.submission_id == report.submission.id
    assert db_async_op.organization_id == 1
    assert db_async_op.status == "Failure"
    assert db_async_op.operation == "FinalizeConiferExternalClearing"
    assert db_async_op.error_details == {"error": "details"}
    assert db_async_op.additional_data == {"additional": "data"}
    assert db_async_op.logical_identifier == "logical_id"
    assert db_async_op.attempts == 3
    assert db_async_op.executing_user_email == "<EMAIL>"


def test_get_latest_async_op_success(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)
    operation_to_find = "FinalizeConiferExternalClearing"
    initial_body = {
        **base_body,
        "operation": operation_to_find,
        "status": "Success",
    }
    created_log_data = _create_async_op_and_assert(initial_body)

    response = get_latest_async_op_by_report_and_operation(
        report_id=str(report.id), operation=operation_to_find  # Pass the string value
    )

    assert response[1] == 200
    retrieved_data = response[0]
    assert retrieved_data["id"] == created_log_data["id"]
    assert retrieved_data["created_at"] is not None
    assert retrieved_data["status"] == "Success"


def test_get_latest_async_op_really_returns_latest(app_context, mocker):
    # 1. Setup: Create two rows for the same report/operation
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)
    operation_to_find = "FinalizeConiferExternalClearing"

    # First row
    _create_async_op_and_assert(
        {
            **base_body,
            "operation": operation_to_find,
            "status": "Pending",
        }
    )
    # Ensure timestamp difference if DB resolution is low
    time.sleep(0.01)

    # Second (latest) log
    latest_log_data = _create_async_op_and_assert(
        {
            **base_body,
            "operation": operation_to_find,
            "status": "Success",
        }
    )

    # 2. Action: Call the GET endpoint
    response = get_latest_async_op_by_report_and_operation(report_id=str(report.id), operation=operation_to_find)

    # 3. Assertions
    assert response[1] == 200
    retrieved_data = response[0]
    # Crucially, check it's the ID of the *second* row created
    assert retrieved_data["id"] == latest_log_data["id"]
    assert retrieved_data["status"] == "Success"


def test_get_latest_async_op_not_found_report(app_context, mocker):
    _init_db_and_prepare_base_body(mocker)
    non_existent_report_id = "00000000-0000-0000-0000-000000000000"

    response = get_latest_async_op_by_report_and_operation(
        report_id=non_existent_report_id, operation="FinalizeConiferExternalClearing"
    )

    assert response[1] == 404
    assert response[0] == {}


def test_update_async_op_success(app_context, mocker):
    # 1. Setup: Create an initial row
    base_body, report, user = _init_db_and_prepare_base_body(mocker)
    initial_operation = "FinalizeConiferExternalClearing"
    initial_body = {
        **base_body,
        "operation": initial_operation,
        "status": "Pending",
        "attempts": 1,
        "error_details": None,
    }
    created_log_data = _create_async_op_and_assert(initial_body)
    async_op_id_to_update = created_log_data["id"]

    # 2. Action: Define updates and call the PATCH endpoint
    update_body = {
        "status": "Success",  # Required field for update
        "error_details": None,  # Explicitly setting to null if needed
        "attempts": 2,
        "executing_user_email": "<EMAIL>",  # Required field for update
    }
    response = update_async_op_by_id(async_op_id=async_op_id_to_update, body=update_body)

    # 3. Assertions (Response)
    assert response[1] == 200
    updated_response_data = response[0]
    assert updated_response_data["id"] == async_op_id_to_update
    assert updated_response_data["created_at"] is not None  # Should be original creation time

    # 4. Assertions (Database state)
    db.session.expire_all()  # Ensure we fetch fresh data
    updated_db_row = db.session.query(AsyncOperationDB).get(async_op_id_to_update)
    assert updated_db_row is not None
    assert str(updated_db_row.id) == async_op_id_to_update
    assert updated_db_row.status == "Success"  # Updated
    assert updated_db_row.error_details is None  # Updated
    assert updated_db_row.attempts == 2  # Updated
    assert updated_db_row.executing_user_email == "<EMAIL>"  # Updated
    # Check unchanged fields
    assert updated_db_row.report_id == report.id
    assert updated_db_row.operation == initial_operation  # Not updatable via this schema/endpoint


def test_update_async_op_not_found(app_context, mocker):
    _init_db_and_prepare_base_body(mocker)
    non_existent_log_id = "11111111-1111-1111-1111-111111111111"

    update_body = {"status": "Failure", "executing_user_email": "<EMAIL>", "error_details": {"code": "NF"}}
    response = update_async_op_by_id(async_op_id=non_existent_log_id, body=update_body)

    assert response[1] == 404
    assert response[0] == {}


def test_get_by_id_success(app_context, mocker):
    base_body, report, _ = _init_db_and_prepare_base_body(mocker)
    initial_body = {
        **base_body,
        "operation": "FinalizeConiferExternalClearing",
        "status": "Success",
        "error_details": {"error": "details"},
        "additional_data": {"additional": "data"},
        "logical_identifier": "logical_id",
    }
    created_log_data = _create_async_op_and_assert(initial_body)

    response = get_async_op_by_id(async_op_id=created_log_data["id"])

    assert response[1] == 200
    retrieved_data = response[0]
    assert retrieved_data["id"] == created_log_data["id"]
    assert retrieved_data["created_at"] is not None
    assert retrieved_data["updated_at"] is None
    assert retrieved_data["report_id"] == str(report.id)
    assert retrieved_data["submission_id"] == str(report.submission.id)
    assert retrieved_data["status"] == "Success"
    assert retrieved_data["error_details"] == {"error": "details"}
    assert retrieved_data["additional_data"] == {"additional": "data"}
    assert retrieved_data["logical_identifier"] == "logical_id"
    assert retrieved_data["attempts"] == 1
    assert retrieved_data["executing_user_email"] == "<EMAIL>"
