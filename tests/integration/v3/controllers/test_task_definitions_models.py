from http import HTTPStatus
from uuid import UUID

from static_common.enums.task_model_execution_type import TaskModelExecutionType
from static_common.enums.task_model_processing_type import TaskModelProcessingType
from static_common.enums.task_model_type import TaskModelType
from werkzeug.exceptions import NotFound
import pytest

from copilot.models import db
from copilot.models.tasks import TaskDefinition, TaskDefinitionModel
from copilot.schemas.tasks import TaskDefinitionModelSchema, TaskDefinitionSchema
from copilot.v3.controllers.task_definition_models import (
    create_task_definition_model,
    delete_task_definition_model,
    get_task_definition_model,
    update_task_definition_model,
)
from copilot.v3.controllers.task_definitions import create_task_definition
from tests.integration.factories import (
    task_definition_fixture,
    task_definition_model_fixture,
)


@pytest.fixture
def body():
    return {
        "order": 1,
        "is_always_run": False,
        "is_aware_of_previous_runs": True,
        "is_refinement_run": False,
        "is_preprocessing_input_run": False,
        "is_consolidation_run": False,
        "is_validation_run": True,
        "can_self_reflect": True,
        "is_benchmark_run": False,
        "is_disabled": False,
        "task_model": {
            "name": "Primary Task Model",
            "type": TaskModelType.COMBINED,
            "execution_config": {"strategy": "default", "max_steps": 5},
            "execution_type": TaskModelExecutionType.LAMBDA,
            "processing_type": TaskModelProcessingType.CONSOLIDATION,
            "llm_model": "gpt-4",
            "use_task_output_processor": True,
            "timeout": 300,
        },
        "validation_task_model": {
            "name": "Validation Task Model",
            "type": TaskModelType.COMBINED,
            "execution_config": {"check": "basic"},
            "execution_type": TaskModelExecutionType.LAMBDA,
            "processing_type": TaskModelProcessingType.CONSOLIDATION,
            "llm_model": "gpt-3.5",
            "use_task_output_processor": False,
            "timeout": 150,
        },
    }


@pytest.fixture
def task_definition_body():
    return {
        "name": "Example Task Definition",
        "code": "example_task_def_code",
        "llm_task_description": "This task handles example LLM execution.",
        "has_large_state": False,
        "timeout": 600,
        "task_definition_models": [
            {
                "task_model": {
                    "name": "Example Task Model",
                    "type": TaskModelType.COMBINED,
                    "execution_config": {"param1": "value1", "param2": 10},
                    "execution_type": TaskModelExecutionType.LAMBDA,
                    "processing_type": TaskModelProcessingType.CONSOLIDATION,
                    "llm_model": "gpt-4",
                    "use_task_output_processor": True,
                    "timeout": 300,
                }
            }
        ],
    }


@pytest.fixture
def updated_body():
    return {
        "order": 1,
        "is_always_run": False,
        "is_aware_of_previous_runs": True,
        "is_refinement_run": False,
        "is_preprocessing_input_run": False,
        "is_consolidation_run": False,
        "is_validation_run": True,
        "can_self_reflect": True,
        "is_benchmark_run": False,
        "is_disabled": False,
        "task_model": {
            "name": "Primary Task Model",
            "type": TaskModelType.LLM_AGENT,
            "execution_config": {"strategy": "default", "max_steps": 4},
            "execution_type": TaskModelExecutionType.LAMBDA,
            "processing_type": TaskModelProcessingType.CONSOLIDATION,
            "llm_model": "gpt-4",
            "use_task_output_processor": True,
            "timeout": 200,
        },
        "validation_task_model": {
            "name": "Validation Task Model",
            "type": TaskModelType.COMBINED,
            "execution_config": {"check": "basic"},
            "execution_type": TaskModelExecutionType.LAMBDA,
            "processing_type": TaskModelProcessingType.CONSOLIDATION,
            "llm_model": "gpt-3.5",
            "use_task_output_processor": False,
            "timeout": 150,
        },
    }


def test_task_definitions_model_simple_add(app_context, body) -> None:
    task_definition = task_definition_fixture()
    db.session.commit()
    task_definition_id = task_definition.id
    assert TaskDefinition.query.count() == 1
    response, status = create_task_definition_model(body, str(task_definition_id))
    assert status == HTTPStatus.CREATED
    assert TaskDefinitionModel.query.count() == 1


def test_task_model_multiple_add(app_context, body) -> None:
    task_definition = task_definition_fixture()
    db.session.add(task_definition)
    db.session.commit()
    task_definition_id = task_definition.id
    for i in range(4):
        response, status = create_task_definition_model(body, str(task_definition_id))
        assert status == HTTPStatus.CREATED
        assert TaskDefinitionModel.query.count() == i + 1


def test_task_model_delete(app_context, body, task_definition_body) -> None:
    task_definition = task_definition_fixture()
    db.session.commit()
    task_definition_id = task_definition.id
    assert TaskDefinition.query.count() == 1
    response, status = create_task_definition_model(body, str(task_definition_id))
    assert status == HTTPStatus.CREATED
    assert TaskDefinitionModel.query.count() == 1

    status = delete_task_definition_model(response["id"])
    assert status == HTTPStatus.NO_CONTENT
    assert TaskDefinitionModel.query.count() == 0


def test_task_model_delete_not_found(app_context) -> None:
    with pytest.raises(NotFound):
        result = delete_task_definition_model("ac651b8d-a2c5-435d-8b7f-7ad4443283ac")


def test_task_model_simple_get(app_context, body) -> None:
    task_definition = task_definition_fixture()
    db.session.commit()
    task_definition_id = task_definition.id
    assert TaskDefinition.query.count() == 1
    response, status = create_task_definition_model(body, str(task_definition_id))
    assert status == HTTPStatus.CREATED
    assert TaskDefinitionModel.query.count() == 1
    response, status = get_task_definition_model(response["id"])
    assert status == HTTPStatus.OK
    assert TaskDefinitionModel.query.count() == 1
    assert response["task_model"]["type"] == TaskModelType.COMBINED


def test_task_model_update(app_context, body, updated_body) -> None:
    task_definition = task_definition_fixture()
    db.session.commit()
    task_definition_id = task_definition.id
    assert TaskDefinition.query.count() == 1
    response, status = create_task_definition_model(body, str(task_definition_id))
    assert status == HTTPStatus.CREATED
    assert TaskDefinitionModel.query.count() == 1

    response, status = update_task_definition_model(response["id"], updated_body)

    assert status == HTTPStatus.OK
    assert TaskDefinitionModel.query.count() == 1
    assert response["task_model"]["type"] == TaskModelType.LLM_AGENT
