from copy import copy
from http import <PERSON><PERSON><PERSON><PERSON>us
import uuid

from marshmallow.exceptions import ValidationError
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
import pytest

from copilot.models import db
from copilot.models.fact_subtype_selection_benchmark import (
    FactSubtypeSelectionBenchmark,
)
from copilot.schemas.fact_subtype_selection_benchmark import (
    FactSubtypeSelectionBenchmarkSchema,
)
from copilot.v3.controllers.fact_subtype_selection_benchmark import (
    create_fact_subtype_selection_benchmark_bulk,
    get_fact_subtype_selection_benchmark,
    get_latest_fact_subtype_selection_benchmarks,
)
from tests.integration.factories import file_fixture
from tests.integration.utils import AnonObj


@pytest.fixture
def mock_body() -> dict:
    return {
        "benchmark_id": "benchmark_001",
        "field_name": "example_field",
        "field_values": ["value1", "value2", "value3"],
        "fact_subtype_id": FactSubtypeID.BUILDING_TYPE,
        "field_type": FieldType.INTEGER,
        "file_type": FileType.SUPPLEMENTAL_FORM.name,
        "current_fact_subtype_assignment": FactSubtypeID.BUILDING_VALUE,
        "new_fact_subtype_assignment": FactSubtypeID.BUILDING_TYPE,
        "organization_id": ExistingOrganizations.KalepaTest.value,
    }


def test_fact_subtype_selection_benchmark_few_updates(app_context, mock_body: dict, mocker) -> None:
    current_user = AnonObj(
        name="test_user",
        is_kalepa=lambda: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    result = create_fact_subtype_selection_benchmark_bulk([mock_body])
    assert result == HTTPStatus.CREATED
    result2 = create_fact_subtype_selection_benchmark_bulk([mock_body])
    assert result2 == HTTPStatus.CREATED

    assert db.session.query(db.func.count("*")).select_from(FactSubtypeSelectionBenchmark).scalar() == 2


def test_fact_subtype_selection_benchmark_benchmark_id_field_missing(app_context, mocker) -> None:
    current_user = AnonObj(
        name="test_user",
        is_kalepa=lambda: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    with pytest.raises(ValidationError):
        create_fact_subtype_selection_benchmark_bulk(
            [
                {
                    "field_name": "example_field",
                    "field_values": ["value1", "value2", "value3"],
                    "fact_subtype_id": FactSubtypeID.BUILDING_TYPE,
                    "field_type": FieldType.INTEGER,
                    "file_type": FileType.SUPPLEMENTAL_FORM,
                    "current_fact_subtype_assignment": FactSubtypeID.BUILDING_VALUE,
                    "new_fact_subtype_assignment": FactSubtypeID.BUILDING_TYPE,
                }
            ]
        )


def test_get_fact_subtype_selection_benchmark(app_context, mock_body: dict, mocker) -> None:
    current_user = AnonObj(
        name="test_user",
        is_kalepa=lambda: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    result = create_fact_subtype_selection_benchmark_bulk([mock_body])
    assert result == HTTPStatus.CREATED

    result_wrong_benchmark_id = get_fact_subtype_selection_benchmark(
        benchmark_id="wrong_benchmark_id",
        file_type=mock_body["file_type"],
        organization_id=mock_body["organization_id"],
    )
    assert len(result_wrong_benchmark_id) == 0

    result_get_by_benchmark_id = get_fact_subtype_selection_benchmark(
        benchmark_id=mock_body["benchmark_id"],
        file_type=mock_body["file_type"],
        organization_id=mock_body["organization_id"],
    )
    resulty_get = get_fact_subtype_selection_benchmark()
    assert result_get_by_benchmark_id == resulty_get
    assert len(result_get_by_benchmark_id) == 1

    schema = FactSubtypeSelectionBenchmarkSchema(many=True)
    results_serialized = schema.load(result_get_by_benchmark_id)
    assert results_serialized[0].benchmark_id == mock_body["benchmark_id"]
    assert results_serialized[0].field_name == mock_body["field_name"]
    assert results_serialized[0].field_values == mock_body["field_values"]


def test_get_fact_subtype_selection_benchmark_empty_db(app_context, mock_body: dict, mocker) -> None:
    current_user = AnonObj(
        name="test_user",
        is_kalepa=lambda: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    result_get_by_benchmark_id = get_fact_subtype_selection_benchmark()
    assert len(result_get_by_benchmark_id) == 0


def test_get_latest_fact_subtype_selection_benchmarks(app_context, mock_body: dict, mocker) -> None:
    current_user = AnonObj(
        name="test_user",
        is_kalepa=lambda: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    for i in range(10):
        benchmark = copy(mock_body)
        benchmark["benchmark_id"] = "benchmark_" + str(i)
        create_fact_subtype_selection_benchmark_bulk([benchmark])

    results = get_latest_fact_subtype_selection_benchmarks()

    assert len(results) == 5
    assert [result["benchmark_id"] for result in results] == [
        "benchmark_9",
        "benchmark_8",
        "benchmark_7",
        "benchmark_6",
        "benchmark_5",
    ]
