from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, List, Optional
from unittest.mock import MagicMock, patch
from uuid import UUID, uuid4
import json

from entity_resolution_service_client_v3 import (
    Entity,
    EntityName,
    EntityPremises,
    Premises,
    Snapshot,
)
from pytest import raises
from sqlalchemy.orm import selectinload
from static_common.enums.classification import KalepaEmailClassificationLabels
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.entity import EntityInformation
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.metric import MetricType
from static_common.enums.organization import ExistingOrganizations, OrganizationGroups
from static_common.enums.origin import Origin
from static_common.enums.parent import ParentType
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.enums.units import Units
from static_common.enums.user_group import ConiferUserGroup
from static_common.models.submission_level_data import SourceDetails
from werkzeug.exceptions import Forbidden, NotFound
import flask
import pytest
import pytz
import werkzeug

from copilot.clients.ers_v3 import ERSClientV3
from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.logic.org_sharing.org_sharing import share_report_with_org
from copilot.metric_utils import (
    MIXED_FILTERING_MODE,
    PREMISES_BASED_FILTERING_MODE,
    STRUCTURES_BASED_FILTERING_MODE,
)
from copilot.models import File, MetricPreference, MetricV2, Organization, User, db
from copilot.models.emails import EmailClassification
from copilot.models.files import ProcessedFile
from copilot.models.reports import (
    Coverage,
    ReportBundle,
    ReportShadowDependency,
    ReportV2,
    Submission,
    SubmissionBusiness,
    SubmissionIdentifier,
)
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.types import ReportDependencyType, ReportShadowType
from copilot.v3.controllers.reports import (
    auto_set_name,
    bulk_create_metric_v2,
    bundle_reports,
    create_metric_v2,
    create_report,
    delete_report,
    get_emails_for_report,
    get_metrics_v2,
    get_pds_debugger_files_data,
    get_report,
    get_report_external,
    get_report_extract,
    get_report_extracts,
    get_report_id_by_external_id,
    get_report_lite,
    get_reports,
    get_reports_by_dossier,
    get_reports_by_submission,
    get_shadow_or_shadowed_report,
    mark_report_emails_as_not_submission,
    revert_report_to_state,
    revert_to_data_onboarding,
    unbundle_reports,
    update_report,
)
from tests.integration.factories import (
    broker_fixture,
    brokerage_fixture,
    coverage_fixture,
    email_classification_label_fixture,
    email_fixture,
    email_status_fixture,
    file_fixture,
    metric_preference_fixture,
    metric_source_fixture,
    metric_v2_fixture,
    organization_fixture,
    processed_file_fixture,
    report_and_submission_fixture,
    report_bundle_fixture,
    report_email_correspondence_fixture,
    report_fixture,
    report_permission_fixture,
    report_processing_dependency_fixture,
    report_shadow_dependency_fixture,
    report_with_business_fixture,
    report_with_submissions_fixture,
    settings_fixture,
    shadow_submission_fixture,
    submission_business_fixture,
    submission_client_id_fixture,
    submission_coverage_fixture,
    submission_fixture,
    submission_user_fixture,
    user_fixture,
    user_group_fixture,
)
from tests.integration.utils import AnonObj, always_has_permission, with_feature_flag


def _generate_enrichment_data(business_ids: list[UUID]) -> dict[str, Entity]:
    idx = 1
    result = {}
    for bid in business_ids:
        result[str(bid)] = Entity(
            names=[EntityName(value=f"ers_name_{idx}")],
            premises=[EntityPremises(premises=Premises(address_line_1=f"ers_address_{idx}"))],
        )
        idx = idx + 1
    return result


def test_get_report_account_id_collision(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    b_id = "********-d433-4ec2-9d92-44f89b20e415"

    report, submission = report_and_submission_fixture()
    submission.account_id = "123"
    submission.businesses.append(
        SubmissionBusiness(business_id=b_id, named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED)
    )
    db.session.commit()

    res = get_report(str(report.id), None, False)
    assert res["submissions"][0]["account_id"] == "123"

    report_2, submission_2 = report_and_submission_fixture()
    submission_2.businesses.append(
        SubmissionBusiness(business_id=b_id, named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED)
    )
    db.session.commit()

    mocked_user.applicable_settings = AnonObj(show_internal_account_id=True)
    res = get_report(str(report.id), None, False)

    assert res["submissions"][0]["account_id"] == f"business-intersection-with:{report.id}"


def test_get_report_dependent_reports(app, app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        cross_organization_access=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    r1, _ = report_and_submission_fixture()
    r2, _ = report_and_submission_fixture()
    db.session.commit()

    res = get_report(str(r1.id), None, False)
    assert res["id"] == str(r1.id)
    assert not res.get("redirect_url")

    r1.is_deleted = True
    report_processing_dependency_fixture(report_id=r2.id, dependent_report_id=r1.id)
    db.session.commit()

    res = get_report(str(r1.id), None, False)
    assert str(r2.id) in res.get("redirect_url")
    assert str(r1.id) not in res.get("redirect_url")

    r1.is_deleted = False
    r2.is_deleted = True
    db.session.commit()

    res = get_report(str(r2.id), None, False)
    assert str(r1.id) in res.get("redirect_url")
    assert str(r2.id) not in res.get("redirect_url")


def test_get_report_dependent_reports_cross_org(app, app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        cross_organization_access=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    r1, _ = report_and_submission_fixture()
    r2, _ = report_and_submission_fixture()
    db.session.commit()

    res = get_report(str(r1.id), None, False)
    assert res["id"] == str(r1.id)
    assert not res.get("redirect_url")

    r1.is_deleted = True
    dep = report_processing_dependency_fixture(report_id=r2.id, dependent_report_id=r1.id)
    dep.dependency_type = ReportDependencyType.CROSS_ORG

    db.session.commit()

    with pytest.raises(werkzeug.exceptions.NotFound):
        get_report(str(r1.id), None, False)

    r1.is_deleted = False
    r2.is_deleted = True
    db.session.commit()

    with pytest.raises(werkzeug.exceptions.NotFound):
        get_report(str(r2.id), None, False)


def test_get_report_extract(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_report_permission=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )

    naics_code = "NAICS_123456"
    score = 39

    organization_fixture()
    user_fixture()
    report = report_with_submissions_fixture(
        is_verification_required=True,
        is_verified=True,
        primary_naics_code=naics_code,
        is_naics_verified=True,
        recommendation_v2_score=score,
    )
    db.session.commit()

    report_extract = get_report_extract(str(report.id))

    assert report_extract["naics_code"] == naics_code
    assert report_extract["risk_score"] == score
    assert report_extract["url"] == f"https://copilot.kalepa.com/report/{report.id}"


def test_get_report_extract_missing_file(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_report_permission=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )

    created_at = datetime.utcnow() - timedelta(minutes=101)
    missing_naics_code = "NAICS_999999"

    organization_fixture()
    user_fixture()
    report = report_with_submissions_fixture(
        is_verification_required=True,
        created_at=created_at,
    )
    db.session.commit()

    report_extract = get_report_extract(str(report.id))

    assert report_extract["naics_code"] == missing_naics_code
    assert report_extract["risk_score"] is None
    assert report_extract["url"] is None


def test_get_report_extract_with_processing_dependency(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_report_permission=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )

    naics_code = "NAICS_123456"
    score = 39

    organization_fixture()
    user_fixture()
    report = report_with_submissions_fixture(
        is_verification_required=True,
        is_verified=True,
        primary_naics_code=naics_code,
        is_naics_verified=True,
        recommendation_v2_score=score,
    )
    dependent_report = report_with_submissions_fixture(is_verification_required=True)
    report_processing_dependency_fixture(report.id, dependent_report.id)
    db.session.commit()

    report_extract = get_report_extract(str(dependent_report.id))

    assert report_extract["naics_code"] == naics_code
    assert report_extract["risk_score"] is None
    assert report_extract["url"] is None


def test_get_report_extract_risk_score_for_unverified_submission(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_report_permission=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )

    naics_code = "NAICS_123456"
    score = 39
    created_at = datetime.utcnow() - timedelta(minutes=1420)

    organization_fixture()
    user_fixture()
    report = report_with_submissions_fixture(
        is_verification_required=True,
        is_verified=False,
        primary_naics_code=naics_code,
        is_naics_verified=False,
        recommendation_v2_score=score,
        created_at=created_at,
        email_body="test",
    )
    db.session.commit()

    report_extract = get_report_extract(str(report.id))

    assert report_extract["naics_code"] == naics_code
    assert report_extract["risk_score"] == score


def test_get_report_extracts(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_permission_for_reports=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )

    naics_code = "NAICS_123456"
    created_at = datetime.utcnow() - timedelta(minutes=106)

    report_1_id = uuid4()
    report_2_id = uuid4()

    organization_fixture()
    user_fixture()
    report_1 = report_with_submissions_fixture(
        id=report_1_id,
        is_verification_required=True,
        primary_naics_code=naics_code,
        created_at=created_at,
        recommendation_v2_score=78,
        email_body="test",
    )
    report_2 = report_with_submissions_fixture(
        id=report_2_id, is_verification_required=True, primary_naics_code=naics_code, email_body="test"
    )
    db.session.commit()

    report_extracts = get_report_extracts([str(report_1.id), str(report_2.id)])
    extracts_by_id = {re["id"]: re for re in report_extracts}

    assert len(report_extracts) == 2
    assert extracts_by_id[str(report_1_id)]["naics_code"] == naics_code
    assert extracts_by_id[str(report_1_id)]["risk_score"] is None
    assert extracts_by_id[str(report_2_id)]["naics_code"] is None


def test_get_report_with_paragon_client_id(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture(id=ExistingOrganizations.Paragon.value)
    user_fixture(organization_id=ExistingOrganizations.Paragon.value)

    report, submission = report_and_submission_fixture(organization_id=ExistingOrganizations.Paragon.value)
    submission_client_id_fixture(
        submission_id=submission.id,
        client_submission_id=(
            '{"ims_insured_guid": "fa0cd88a-9b84-44ed-8477-8b4324c9c3bb", "ims_submission_id":'
            ' "c72819c0-4b6f-4409-93eb-0a1a8e0e59a0", "ims_quote_guids": {"Casualty E&S Excess (Unsupported)":'
            ' "********-7a45-48fb-b6af-781628e9c2d2"}, "ims_control_numbers": {"Casualty E&S Excess (Unsupported)":'
            " 1060900}}"
        ),
    )
    db.session.commit()

    res = get_report(str(report.id), "organization_id", False)
    assert res["submissions"][0]["client_submission_ids"][0]["client_submission_id"] == "1060900"


def test_bundle_reports(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_report_permission=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )

    organization_fixture()
    user_fixture()
    report_1 = report_fixture()
    report_2 = report_fixture()
    report_3 = report_fixture(is_deleted=True)
    db.session.commit()

    with pytest.raises(werkzeug.exceptions.NotFound):
        bundle_reports({"report_ids": [str(report_1.id), str(report_2.id), str(report_3.id)]})

    bundles = ReportBundle.query.all()
    assert len(bundles) == 0

    bundle_reports({"report_ids": [str(report_1.id), str(report_2.id)]})

    assert report_1.report_bundle == report_2.report_bundle
    assert len(report_1.report_bundle.reports) == 2


def test_unbundle_reports_last_two(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_report_permission=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )

    organization_fixture()
    user_fixture()
    report_1 = report_fixture()
    report_2 = report_fixture()
    bundle = ReportBundle()
    bundle.reports.append(report_1)
    bundle.reports.append(report_2)
    db.session.add(bundle)
    db.session.commit()

    unbundle_reports({"report_ids": [str(report_1.id)]})

    bundles = ReportBundle.query.all()

    assert report_1.report_bundle == report_2.report_bundle == None
    assert len(bundles) == 0


def test_unbundle_reports_1_of_3(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            has_report_permission=lambda type, id: True, email="<EMAIL>", is_being_impersonated=False
        ),
    )

    organization_fixture()
    user_fixture()
    report_1 = report_fixture()
    report_2 = report_fixture()
    report_3 = report_fixture()
    bundle = ReportBundle()
    bundle.reports.append(report_1)
    bundle.reports.append(report_2)
    bundle.reports.append(report_3)
    db.session.add(bundle)
    db.session.commit()

    unbundle_reports({"report_ids": [str(report_1.id)]})

    bundles = ReportBundle.query.all()

    assert report_1.report_bundle == None
    assert report_2.report_bundle == report_3.report_bundle != None
    assert len(bundles) == 1
    assert bundles[0].reports == [report_2, report_3] or bundles[0].reports == [report_3, report_2]


# Given:
#   Report R_1 with Submission S_1 and Submission Business SB_1 which has business id B_1
#   Report R_2 with Submission S_2 and Submission Business SB_2 which has business id B_2
# When: We get reports for B_1
# Then: We get [R_1]
def test_get_reports_by_dossier_without_snapshots(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False),
    )
    organization_fixture()
    user_fixture()

    business_id = uuid4()
    report_1 = report_with_business_fixture(business_id)  # R1
    report_with_business_fixture()  # R2

    reports = get_reports_by_dossier(id=str(business_id)).get("reports")
    assert reports

    report_ids = [r.get("id") for r in reports]
    assert len(report_ids) == 1
    assert str(report_1.id) in report_ids


# Given:
#   Report R with Submission S and Submission Business SB which has business id B_1 and snapshot id SN
#   Snapshot SN which has entity_id B_2
# When: We get reports for B_2 without snapshots
# Then: We get []
def test_get_reports_by_dossier_without_snapshots_when_snapshotted(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False),
    )
    organization_fixture()
    user_fixture()

    business_id_1 = uuid4()
    business_id_2 = uuid4()
    report_with_business_fixture(business_id_1)

    mocker.patch(
        "flask.current_app.ers_client_v3.get_snapshots_for_entity", return_value=Snapshot(entity_id=str(business_id_2))
    )

    reports = get_reports_by_dossier(id=str(business_id_2)).get("reports")
    assert reports == []


# Given:
#   Report R with Submission S and Submission Business SB which has business id B_1 and snapshot id SN
#   Snapshot SN which has entity_id B_2
# When: We get reports for B_2 with snapshots
# Then: We get []
def test_get_reports_by_dossier_with_snapshots_when_snapshotted(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False),
    )
    organization_fixture()
    user_fixture()

    business_id_1 = uuid4()
    business_id_2 = uuid4()
    snapshot = Snapshot(id=str(uuid4()), entity_id=str(business_id_2))
    report_1 = report_with_business_fixture(business_id_1, snapshot_id=UUID(snapshot.id))

    mocker.patch("flask.current_app.ers_client_v3.get_snapshots_for_entity", return_value=[snapshot])

    reports = get_reports_by_dossier(id=str(business_id_2), include_snapshots=True).get("reports")
    assert reports

    report_ids = [r.get("id") for r in reports]
    assert len(report_ids) == 1
    assert str(report_1.id) in report_ids


# Given:
#   Report R with Submission S and Submission Business SB which has business id B_1
# When: We get reports for B_1 with lightweight = True and no expand param
# Then: We get [R] with only ids
def test_get_reports_by_dossier_with_lightweigth_and_no_expand(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False),
    )
    organization_fixture()
    user_fixture()

    business_id_1 = uuid4()
    report_1 = report_with_business_fixture(business_id_1)
    reports = get_reports_by_dossier(id=str(business_id_1), lightweight=True).get("reports")
    assert reports

    report_ids = [r.get("id") for r in reports]
    assert len(report_ids) == 1
    assert str(report_1.id) in report_ids

    submissions = [r.get("submissions") for r in reports][0]
    assert len(submissions) == 1
    assert submissions[0].get("name") is None


# Given:
#   Report R with Submission S and Submission Business SB which has business id B_1
# When: We get reports for B_1 with lightweight = True and expand = ["submissions.name"]
# Then: We get [R] with only ids and submission name
def test_get_reports_by_dossier_with_lightweigth_and_expand(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False),
    )
    organization_fixture()
    user_fixture()

    business_id_1 = uuid4()
    report_1 = report_with_business_fixture(business_id_1)
    reports = get_reports_by_dossier(
        id=str(business_id_1), lightweight=True, expand=["submissions.name", "organization_id"], organization_id=1
    ).get("reports")
    assert reports

    report_ids = [r.get("id") for r in reports]
    assert len(report_ids) == 1
    assert str(report_1.id) in report_ids
    assert reports[0].get("organization_id") == 1

    submissions = [r.get("submissions") for r in reports][0]
    assert len(submissions) == 1
    assert submissions[0].get("name") is not None
    assert submissions[0]["name"] == submissions[0]["id"]

    reports = get_reports_by_dossier(id=str(business_id_1), lightweight=True, organization_id=2).get("reports")
    assert not reports


def test_get_reports_by_dossier_include_in_pds(app_context, mocker):
    current_user = AnonObj(id=1, is_internal_machine_user=True, email="<EMAIL>", is_being_impersonated=False)
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    organization_fixture()
    user_fixture()
    business_id_1 = str(uuid4())
    business_id_2 = str(uuid4())
    report, submission = report_and_submission_fixture()

    submission.files = [File(file_type=FileType.EMAIL, size=123, name="email", origin=Origin.EMAIL)]
    resolution_data = {
        "resolution_data": [
            {
                "entity_id": business_id_1,
            },
            {
                "entity_id": business_id_2,
            },
        ]
    }
    submission.files[0].processed_file = ProcessedFile(business_resolution_data=resolution_data, processed_data={})
    db.session.commit()

    reports = get_reports_by_dossier(id=business_id_1, lightweight=True, include_in_pds=True).get("reports")
    report_ids = [r.get("id") for r in reports]
    assert len(report_ids) == 1
    assert str(report.id) in report_ids

    reports = get_reports_by_dossier(id=business_id_2, lightweight=True, include_in_pds=True).get("reports")
    report_ids = [r.get("id") for r in reports]
    assert len(report_ids) == 1
    assert str(report.id) in report_ids


@pytest.fixture
def report() -> ReportV2:
    organization: Organization = organization_fixture(id=ExistingOrganizations.KalepaTest.value)
    user: User = user_fixture(organization_id=organization.id)
    report, _ = report_and_submission_fixture(owner_id=user.id, organization_id=organization.id)
    return report


@pytest.fixture
def submission_business_id(report: ReportV2) -> str:
    submission = submission_fixture(report=report)
    submission_business = submission_business_fixture(business_id=str(uuid4()), submission_id=submission.id)
    db.session.commit()
    return str(submission_business.id)


@pytest.fixture
def metric_preference(report: ReportV2) -> MetricPreference:
    parent_id: UUID = uuid4()
    parent_type: ParentType = ParentType.REPORT
    children_type: ParentType = ParentType.PREMISES
    summary_config_id: str = "TEST_SUMMARY_CONFIG_ID"

    metric_preference_id: UUID = uuid4()
    metric_preference: MetricPreference = metric_preference_fixture(
        id=metric_preference_id,
        report_id=report.id,
        parent_type=parent_type,
        parent_id=parent_id,
        children_type=children_type,
        summary_config_id=summary_config_id,
    )
    db.session.commit()
    return metric_preference


@pytest.fixture
def enabled_metric_preference(metric_preference: MetricPreference) -> MetricPreference:
    metric_preference.is_applicable = True
    metric_preference.is_enabled = True
    metric_preference.metric_group_name = "Test group"
    db.session.add(metric_preference)
    db.session.commit()

    return metric_preference


@pytest.fixture
def base_request_body(metric_preference: MetricPreference) -> dict:
    return {
        "execution_id": uuid4(),
        "name": "my metric",
        "children_type": "BUSINESS",
        "summary_config_id": metric_preference.summary_config_id,
        "metric_type": "MEAN",
        "parent_id": metric_preference.parent_id,
        "parent_type": metric_preference.parent_type.value,  # type: ignore
        "sources": [
            {
                "type": "WEB_INGESTED",
                "properties": {
                    "url": "https://www.google.com",
                },
            }
        ],
    }


def test_create_metric_v2_mean_and_common_values(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    structure_id_1: UUID = uuid4()
    structure_id_2: UUID = uuid4()
    premises_id_1: UUID = uuid4()
    premises_id_2: UUID = uuid4()

    expected_float_values: list[float] = [42.0, 30.0, 10.0, 20.0]
    base_request_body.update(
        {
            "metric_type": "MEAN",
            "float_values": expected_float_values,
            "value_parent_ids": [premises_id_1, structure_id_1, structure_id_2, premises_id_2],
            "value_parent_types": [
                ParentType.PREMISES.value,
                ParentType.STRUCTURE.value,
                ParentType.STRUCTURE.value,
                ParentType.PREMISES.value,
            ],
            "value_business_ids": [None, None, None, None],
            "units": Units.PERCENTAGE.value,
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .options(selectinload(MetricV2.sources))
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.metric_preference is not None
    assert metric.metric_preference.is_applicable is True
    assert metric.parent_id == metric_preference.parent_id
    assert metric.parent_type == metric_preference.parent_type
    assert metric.summary_config_id == metric_preference.summary_config_id
    assert metric.metric_type == MetricType.MEAN
    assert metric.mean == 20.0
    assert metric.sum == 40.0
    assert metric.min == 20.0
    assert metric.max == 20.0
    assert metric.units == Units.PERCENTAGE.value
    assert metric.has_structures_data is False

    assert metric.business_ids is None
    assert metric.children_ids is not None
    assert len(metric.children_ids) == 2
    assert metric.children_ids[0] == premises_id_1
    assert metric.children_ids[1] == premises_id_2

    assert metric.quintile_distribution is not None
    assert metric.interquintile_numbers is not None
    assert metric.interquintile_numbers == [1, 1]

    assert metric.quintiles is not None
    assert metric.quintiles == [20.0, 20.0, 20.0, 20.0]

    float_values: list[float] | None = results[0].filtered_values
    assert float_values is not None
    assert 2 == len(float_values)
    assert [20.0, 20.0] == float_values

    assert len(metric.sources) == 1


def test_create_metric_v2_sum(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    structure_id_1: UUID = uuid4()
    structure_id_2: UUID = uuid4()
    premises_id_1: UUID = uuid4()
    premises_id_2: UUID = uuid4()

    expected_float_values: list[float] = [42.0, 30.0, 10.0, 20.0]
    base_request_body.update(
        {
            "metric_type": "SUM",
            "float_values": expected_float_values,
            "value_parent_ids": [premises_id_1, structure_id_1, structure_id_2, premises_id_2],
            "value_parent_types": [
                ParentType.PREMISES.value,
                ParentType.STRUCTURE.value,
                ParentType.STRUCTURE.value,
                ParentType.PREMISES.value,
            ],
            "value_business_ids": [None, None, None, None],
            "units": "things",
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .options(selectinload(MetricV2.sources))
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.metric_preference is not None
    assert metric.metric_preference.is_applicable is True
    assert metric.parent_id == metric_preference.parent_id
    assert metric.parent_type == metric_preference.parent_type
    assert metric.summary_config_id == metric_preference.summary_config_id
    assert metric.metric_type == MetricType.SUM
    assert metric.mean == 30.0
    assert metric.sum == 60.0
    assert metric.min == 20.0
    assert metric.max == 40.0
    assert metric.units == "things"
    assert metric.has_structures_data is False

    float_values: list[float] | None = results[0].filtered_values
    assert float_values is not None
    assert 2 == len(float_values)
    assert [40.0, 20.0] == float_values

    assert len(metric.sources) == 1


def test_create_metric_v2_distance_mean_metric(app_context, report: ReportV2, base_request_body: dict) -> None:
    distance_mean_summary_config_id = "DISTANCE_TO_COAST_SUMMARY_CONFIG"
    metric_preference_id: UUID = uuid4()
    metric_preference: MetricPreference = metric_preference_fixture(
        id=metric_preference_id,
        report_id=report.id,
        parent_type="PREMISES",
        parent_id=report.id,
        summary_config_id=distance_mean_summary_config_id,
        is_applicable=False,
    )

    structure_id: UUID = uuid4()
    premises_id: UUID = uuid4()
    expected_float_values: list[float] = [1.5, 4.0]
    base_request_body.update(
        {
            "metric_type": "MEAN",
            "float_values": expected_float_values,
            "value_parent_ids": [structure_id, premises_id],
            "value_parent_types": [ParentType.STRUCTURE.value, ParentType.PREMISES.value],
            "units": "things",
            "summary_config_id": distance_mean_summary_config_id,
            "parent_id": metric_preference.parent_id,
            "parent_type": metric_preference.parent_type,
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.metric_preference is not None
    assert metric.metric_preference.is_applicable is True
    assert metric.quintiles is not None
    assert metric.quintiles == [1.0, 2.0, 3.0, 5.0]
    assert metric.interquintile_numbers is not None
    assert metric.interquintile_numbers == [2, 4]


def test_create_metric_v2_datetime_values(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    now = datetime.utcnow().replace(tzinfo=pytz.UTC)
    expected_date_time_values: list[datetime] = [now, now - timedelta(hours=4)]
    base_request_body.update(
        {
            "metric_type": "DATE_RANGE_SUMMARY",
            "datetime_values": map(lambda x: str(x), expected_date_time_values),
            "value_parent_ids": [uuid4(), uuid4()],
            "value_parent_types": [ParentType.STRUCTURE.value, ParentType.PREMISES.value],
            "value_business_ids": [None, None],
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.metric_type == MetricType.DATE_RANGE_SUMMARY
    assert metric.date_range_quintiles is not None

    datetime_values: list[datetime] | None = results[0].datetime_values
    assert datetime_values is not None
    assert metric.earliest is not None
    assert metric.latest is not None
    assert len(datetime_values) == 2
    assert metric.latest.timestamp() == datetime_values[0].timestamp() == expected_date_time_values[0].timestamp()
    assert metric.earliest.timestamp() == datetime_values[1].timestamp() == expected_date_time_values[1].timestamp()


def test_create_metric_v2_numeric_range_summary(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    expected_minimums = [1, 2]
    expected_maximums = [20, 30]
    base_request_body.update(
        {
            "metric_type": "NUMERIC_RANGE_SUMMARY",
            "minimums": expected_minimums,
            "maximums": expected_maximums,
            "value_parent_ids": [uuid4(), uuid4()],
            "value_parent_types": [ParentType.STRUCTURE.value, ParentType.PREMISES.value],
            "value_business_ids": [None, None],
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.metric_type == MetricType.NUMERIC_RANGE_SUMMARY
    assert metric.minimums == expected_minimums
    assert metric.maximums == expected_maximums
    assert metric.average_min_max == 13.25


def test_create_metric_v2_range_summary(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    expected_inside_business_id: UUID = uuid4()
    expected_outside_business_id: UUID = uuid4()
    expected_unknown_business_id: UUID = uuid4()

    expected_float_values: list[float | None] = [0.4, None, 1.1]
    base_request_body.update(
        {
            "metric_type": "RANGE_SUMMARY",
            "float_values": expected_float_values,
            "value_parent_ids": [uuid4(), uuid4(), uuid4()],
            "value_parent_types": [ParentType.STRUCTURE.value, ParentType.PREMISES.value, ParentType.PREMISES],
            "value_business_ids": [
                expected_inside_business_id,
                expected_unknown_business_id,
                expected_outside_business_id,
            ],
            "distance_threshold": 5280.0,
            "units": "feet",
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.metric_type == MetricType.RANGE_SUMMARY
    assert metric.inside_range_label == "< 5280 feet"
    assert metric.outside_range_label == "> 5280 feet"
    assert metric.threshold == "5280 feet"

    assert metric.inside_range is not None
    assert len(metric.inside_range) == 1
    assert metric.inside_range[0] == expected_inside_business_id

    assert metric.outside_range is not None
    assert len(metric.outside_range) == 1
    assert metric.outside_range[0] == expected_outside_business_id

    assert metric.unknown_range is not None
    assert len(metric.unknown_range) == 1
    assert metric.unknown_range[0] == expected_unknown_business_id

    assert metric.ranges is not None
    assert len(metric.ranges) == 3

    assert metric.range_distribution is not None
    assert len(metric.range_distribution) == 3
    assert metric.range_distribution[0].category == "Inside"
    assert metric.range_distribution[0].frequency == 1
    assert metric.range_distribution[1].category == "Outside"
    assert metric.range_distribution[1].frequency == 1
    assert metric.range_distribution[2].category == "Unknown"
    assert metric.range_distribution[2].frequency == 1


def test_create_metric_v2_grade_summary_label(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    expected_grades = ["C", "F", "D", "F", "A", "A"]
    expected_parent_ids = [uuid4(), uuid4(), uuid4(), uuid4(), uuid4(), uuid4()]
    expected_business_ids = [uuid4(), None, None, uuid4(), uuid4(), uuid4()]
    base_request_body.update(
        {
            "metric_type": "GRADE_SUMMARY",
            "string_values": expected_grades,
            "value_parent_ids": expected_parent_ids,
            "value_parent_types": [
                ParentType.PREMISES.value,
                ParentType.PREMISES.value,
                ParentType.PREMISES.value,
                ParentType.PREMISES.value,
                ParentType.PREMISES.value,
                ParentType.PREMISES.value,
                ParentType.PREMISES.value,
            ],
            "value_business_ids": expected_business_ids,
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]

    assert metric.metric_type == MetricType.GRADE_SUMMARY
    assert metric.score == 0.63
    assert metric.label == "High Risk"
    assert metric.grade_distribution is not None


def test_create_metric_v2_grade_summary_only_unknowns(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    expected_grades = ["Unknown", "Unknown", "Unknown"]
    expected_parent_ids = [uuid4(), uuid4(), uuid4()]
    expeceted_business_ids = [uuid4(), None, None]
    base_request_body.update(
        {
            "metric_type": "GRADE_SUMMARY",
            "string_values": expected_grades,
            "value_parent_ids": expected_parent_ids,
            "value_parent_types": [
                ParentType.STRUCTURE.value,
                ParentType.PREMISES.value,
                ParentType.STRUCTURE.value,
            ],
            "value_business_ids": expeceted_business_ids,
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]

    assert metric.metric_type == MetricType.GRADE_SUMMARY
    assert metric.label == None
    assert metric.score == 0.0
    assert metric.grade_distribution is not None


def test_create_metric_v2_categorical_list_summary(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    expected_urls = ["https://url1.com", "http://url2.com", None]
    expected_list_item_type = "test"
    base_request_body.update(
        {
            "metric_type": "LIST_SUMMARY",
            "string_values": expected_urls,
            "value_parent_ids": [uuid4(), uuid4(), uuid4()],
            "value_parent_types": [
                ParentType.STRUCTURE.value,
                ParentType.STRUCTURE.value,
                ParentType.STRUCTURE.value,
            ],
            "value_business_ids": [None, None, None],
            "list_item_type": expected_list_item_type,
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.metric_type == MetricType.LIST_SUMMARY
    assert metric.list_item_values == expected_urls
    assert metric.list_item_type == expected_list_item_type


def test_create_metric_v2_category_summary(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    category1_uuid1 = uuid4()
    category1_uuid2 = uuid4()
    category2_uuid1 = uuid4()
    category3_uuid1 = uuid4()
    category2_bid1 = uuid4()
    category3_bid1 = uuid4()

    base_request_body.update(
        {
            "metric_type": "CATEGORICAL_DATA_SUMMARY",
            "string_values": ["category1", "category2", "category1", "category3"],
            "value_business_ids": [None, category2_bid1, None, category3_bid1],
            "value_parent_ids": [category1_uuid1, category2_uuid1, category1_uuid2, category3_uuid1],
            "value_parent_types": [
                ParentType.PREMISES.value,
                ParentType.BUSINESS.value,
                ParentType.PREMISES.value,
                ParentType.BUSINESS.value,
            ],
        }
    )

    create_metric_v2(str(report.id), base_request_body)
    db.session.commit()

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.metric_type == MetricType.CATEGORICAL_DATA_SUMMARY
    assert metric.category_summaries is not None
    assert len(metric.category_summaries) == 3
    assert metric.category_summaries[0].display_name == "category1"
    assert metric.category_summaries[0].frequency == 2
    assert metric.category_summaries[0].percentage == 0.5
    assert metric.category_summaries[0].business_ids == [None, None]
    assert metric.category_summaries[0].parent_ids == [category1_uuid1, category1_uuid2]
    assert metric.category_summaries[0].parent_types == [ParentType.PREMISES, ParentType.PREMISES]

    assert metric.category_summaries[1].display_name == "category2"
    assert metric.category_summaries[1].frequency == 1
    assert metric.category_summaries[1].percentage == 0.25
    assert metric.category_summaries[1].business_ids == [category2_bid1]
    assert metric.category_summaries[1].parent_ids == [category2_uuid1]
    assert metric.category_summaries[1].parent_types == [ParentType.BUSINESS]

    assert metric.category_summaries[2].display_name == "category3"
    assert metric.category_summaries[2].frequency == 1
    assert metric.category_summaries[2].percentage == 0.25
    assert metric.category_summaries[2].business_ids == [category3_bid1]
    assert metric.category_summaries[2].parent_ids == [category3_uuid1]
    assert metric.category_summaries[2].parent_types == [ParentType.BUSINESS]


def test_get_metrics_v2(app_context, mocker, enabled_metric_preference: MetricPreference) -> None:
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    sid1 = uuid4()
    sid2 = uuid4()

    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=enabled_metric_preference.report_id,
        summary_config_id=enabled_metric_preference.summary_config_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        metric_type="CATEGORICAL_DATA_SUMMARY",
        string_values=["category1", "category2", "category1", "category3"],
        value_business_ids=[None, None, None, uuid4()],
        value_parent_types=[
            ParentType.BUSINESS.value,
            ParentType.STRUCTURE.value,
            ParentType.BUSINESS.value,
            ParentType.STRUCTURE.value,
        ],
        value_parent_ids=[sid1, uuid4(), sid2, uuid4()],
        units=None,
    )
    metric_source_fixture(metric_v2_id=metric.id)
    db.session.commit()

    result = get_metrics_v2(str(enabled_metric_preference.report_id), {})

    assert result is not None
    metric = result["metrics"][0][0]
    assert MIXED_FILTERING_MODE == metric.get("filtering_mode")
    assert MetricType.CATEGORICAL_DATA_SUMMARY == metric.get("metric_type")
    assert enabled_metric_preference.metric_group_name == metric.get("metric_group_name")

    sources = metric.get("sources")
    assert 1 == len(sources)

    assert Units.OTHER.value == metric.get("computed_units")

    category_summaries = metric.get("category_summaries")
    assert "category1" == category_summaries[0].get("display_name")
    assert 2 == category_summaries[0].get("frequency")
    assert 1.0 == category_summaries[0].get("percentage")

    assert metric.get("business_ids") is None
    assert [str(i) for i in [sid1, sid2]] == metric.get("children_ids")
    assert ["category1", "category1"] == metric.get("values")

    result = get_metrics_v2(str(enabled_metric_preference.report_id), {"exclude": ["values"]})
    values = result["metrics"][0][0].get("values", None)
    assert values is None


def test_get_metrics_v2_with_forced_summary_config_ids(
    app_context, mocker, enabled_metric_preference: MetricPreference
):
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    disabled_metric_preference: MetricPreference = metric_preference_fixture(
        id=uuid4(),
        report_id=enabled_metric_preference.report_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        children_type=enabled_metric_preference.children_type,
        summary_config_id="TEST_SUMMARY_CONFIG_ID_2",
        is_enabled=False,
        is_applicable=True,
    )

    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=enabled_metric_preference.report_id,
        summary_config_id=enabled_metric_preference.summary_config_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        metric_type="CATEGORICAL_DATA_SUMMARY",
        string_values=["sth"],
        value_business_ids=[uuid4()],
        value_parent_types=[
            ParentType.BUSINESS.value,
        ],
        value_parent_ids=[uuid4()],
        units=None,
    )
    metric_source_fixture(metric_v2_id=metric.id)

    metric_2: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=disabled_metric_preference.report_id,
        summary_config_id=disabled_metric_preference.summary_config_id,
        parent_type=disabled_metric_preference.parent_type,
        parent_id=disabled_metric_preference.parent_id,
        metric_type="CATEGORICAL_DATA_SUMMARY",
        string_values=["sth"],
        value_business_ids=[uuid4()],
        value_parent_types=[
            ParentType.BUSINESS.value,
        ],
        value_parent_ids=[uuid4()],
        units=None,
    )
    metric_source_fixture(metric_v2_id=metric_2.id)
    db.session.commit()

    result_default = get_metrics_v2(str(enabled_metric_preference.report_id), {})
    assert len(result_default["metrics"]) == 1
    assert result_default["metrics"][0][0]["summary_config_id"] == "TEST_SUMMARY_CONFIG_ID"

    result_forced = get_metrics_v2(
        str(enabled_metric_preference.report_id), {"summary_config_ids": ["TEST_SUMMARY_CONFIG_ID_2"]}
    )
    assert len(result_forced["metrics"]) == 1
    assert result_forced["metrics"][0][0]["summary_config_id"] == "TEST_SUMMARY_CONFIG_ID_2"


def test_get_metrics_v2_with_forced_summary_config_ids_when_they_are_not_calculated(
    app_context, mocker, enabled_metric_preference: MetricPreference
):
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    disabled_metric_preference: MetricPreference = metric_preference_fixture(
        id=uuid4(),
        report_id=enabled_metric_preference.report_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        children_type=enabled_metric_preference.children_type,
        summary_config_id="TEST_SUMMARY_CONFIG_ID_2",
        is_enabled=False,
        is_applicable=True,
    )

    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=enabled_metric_preference.report_id,
        summary_config_id=enabled_metric_preference.summary_config_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        metric_type="CATEGORICAL_DATA_SUMMARY",
        string_values=["sth"],
        value_business_ids=[uuid4()],
        value_parent_types=[
            ParentType.BUSINESS.value,
        ],
        value_parent_ids=[uuid4()],
        units=None,
    )
    metric_source_fixture(metric_v2_id=metric.id)

    metric_2: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=disabled_metric_preference.report_id,
        summary_config_id=disabled_metric_preference.summary_config_id,
        parent_type=disabled_metric_preference.parent_type,
        parent_id=disabled_metric_preference.parent_id,
        metric_type="CATEGORICAL_DATA_SUMMARY",
        string_values=["sth"],
        value_business_ids=[uuid4()],
        value_parent_types=[
            ParentType.BUSINESS.value,
        ],
        value_parent_ids=[uuid4()],
        units=None,
    )
    metric_source_fixture(metric_v2_id=metric_2.id)
    db.session.commit()

    result_default = get_metrics_v2(str(enabled_metric_preference.report_id), {})
    assert len(result_default["metrics"]) == 1
    assert result_default["metrics"][0][0]["summary_config_id"] == "TEST_SUMMARY_CONFIG_ID"

    result_forced = get_metrics_v2(
        str(enabled_metric_preference.report_id), {"summary_config_ids": ["TEST_SUMMARY_CONFIG_ID_3"]}
    )
    assert len(result_forced["metrics"]) == 0


def test_get_metrics_v2_overrides_custom_quintiles(
    app_context, mocker, report: ReportV2, enabled_metric_preference: MetricPreference
) -> None:
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)
    summary_config_id = "YEAR_FOUNDED"
    summary_config_id_2 = "YEAR_BUILT"
    expected_quintiles = [1933.0, 1975.0, 2009.0, 2020.0]
    mocker.patch(
        "copilot.logic.reports._get_summary_configs_as_dict",
        return_value={
            summary_config_id: AnonObj(
                display_name="Year Founded",
                fact_subtype_id="YEAR_FOUNDED",
                quintiles=expected_quintiles,
                summary_type_id=MetricType.MEAN,
            ),
            summary_config_id_2: AnonObj(
                display_name="Year Built",
                fact_subtype_id="YEAR_BUILT",
                quintiles=None,
                summary_type_id=MetricType.MEAN,
            ),
        },
    )

    metric_preference: MetricPreference = metric_preference_fixture(
        id=str(uuid4()),
        report_id=report.id,
        parent_type="PREMISES",
        parent_id=report.id,
        summary_config_id=summary_config_id,
        is_applicable=True,
        is_enabled=True,
    )
    metric_preference_2: MetricPreference = metric_preference_fixture(
        id=str(uuid4()),
        report_id=report.id,
        parent_type="PREMISES",
        parent_id=report.id,
        summary_config_id=summary_config_id_2,
        is_applicable=True,
        is_enabled=True,
    )
    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=metric_preference.report_id,
        summary_config_id=metric_preference.summary_config_id,
        parent_type=metric_preference.parent_type,
        parent_id=metric_preference.parent_id,
        metric_type=MetricType.MEAN,
        float_values=[1.0],
        value_business_ids=[uuid4()],
        value_parent_types=["PREMISES"],
        value_parent_ids=[uuid4()],
    )
    metric_2: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=metric_preference_2.report_id,
        summary_config_id=metric_preference_2.summary_config_id,
        parent_type=metric_preference_2.parent_type,
        parent_id=metric_preference_2.parent_id,
        metric_type=MetricType.MEAN,
        float_values=[1.0],
        value_business_ids=[uuid4()],
        value_parent_types=["PREMISES"],
        value_parent_ids=[uuid4()],
    )
    db.session.commit()

    result = get_metrics_v2(str(metric_preference.report_id), {})

    assert result is not None

    metric = result["metrics"][0][0]
    assert MetricType.MEAN == metric.get("metric_type")
    assert expected_quintiles == metric.get("quintiles")

    metric_2 = result["metrics"][1][0]
    assert MetricType.MEAN == metric_2.get("metric_type")
    assert [1.0, 1.0, 1.0, 1.0] == metric_2.get("quintiles")


def test_get_metrics_v2_filters_submission_business_id(
    app_context, mocker, report: ReportV2, submission_business_id: str, enabled_metric_preference: MetricPreference
) -> None:
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    expected_string_values = ["category1"]
    sid1 = uuid4()
    expected_value_parent_ids = [sid1]
    expected_value_parent_types = [ParentType.STRUCTURE.value]

    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=enabled_metric_preference.report_id,
        summary_config_id=enabled_metric_preference.summary_config_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        metric_type="CATEGORICAL_DATA_SUMMARY",
        string_values=expected_string_values,
        value_business_ids=[uuid4()],
        value_parent_types=expected_value_parent_types,
        value_parent_ids=expected_value_parent_ids,
        submission_business_id=submission_business_id,
    )
    metric_source_fixture(metric_v2_id=metric.id)
    db.session.commit()

    result = get_metrics_v2(
        str(enabled_metric_preference.report_id), {"submission_business_id": submission_business_id}
    )
    assert 1 == len(result["metrics"])

    # Random submission_business_id
    result = get_metrics_v2(str(enabled_metric_preference.report_id), {"submission_business_id": str(uuid4())})
    assert 0 == len(result["metrics"])

    result = get_metrics_v2(
        str(enabled_metric_preference.report_id), {"submission_business_ids": [submission_business_id]}
    )
    assert 1 == len(result["metrics"])

    # Random submission_business_id
    result = get_metrics_v2(str(enabled_metric_preference.report_id), {"submission_business_ids": [str(uuid4())]})
    assert 0 == len(result["metrics"])


def test_get_metrics_v2_excludes_location_level_metrics_by_default(
    app_context, mocker, report: ReportV2, submission_business_id: str, enabled_metric_preference: MetricPreference
) -> None:
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    expected_string_values = ["category1"]
    sid1 = uuid4()
    expected_value_parent_ids = [sid1]
    expected_value_parent_types = [ParentType.STRUCTURE.value]

    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=enabled_metric_preference.report_id,
        summary_config_id=enabled_metric_preference.summary_config_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        metric_type="CATEGORICAL_DATA_SUMMARY",
        string_values=expected_string_values,
        value_business_ids=[uuid4()],
        value_parent_types=expected_value_parent_types,
        value_parent_ids=expected_value_parent_ids,
        submission_business_id=submission_business_id,
    )
    metric_source_fixture(metric_v2_id=metric.id)
    db.session.commit()

    result = get_metrics_v2(str(enabled_metric_preference.report_id), {})
    assert 0 == len(result["metrics"])

    result = get_metrics_v2(
        str(enabled_metric_preference.report_id), {"submission_business_id": submission_business_id}
    )
    assert 1 == len(result["metrics"])


def test_get_metrics_v2_filters_structures_within_metrics(
    app_context, mocker, report: ReportV2, submission_business_id: str, enabled_metric_preference: MetricPreference
) -> None:
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    expected_string_values = ["category1", "category2", "category3"]
    sid1 = uuid4()
    sid2 = uuid4()
    sid3 = uuid4()
    expected_value_parent_ids = [sid1, sid2, sid3]
    expected_value_parent_types = [ParentType.STRUCTURE.value, ParentType.STRUCTURE.value, ParentType.STRUCTURE.value]

    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=enabled_metric_preference.report_id,
        summary_config_id=enabled_metric_preference.summary_config_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        metric_type="CATEGORICAL_DATA_SUMMARY",
        string_values=expected_string_values,
        value_business_ids=[None, None, None],
        value_parent_types=expected_value_parent_types,
        value_parent_ids=expected_value_parent_ids,
        submission_business_id=submission_business_id,
    )
    metric_source_fixture(metric_v2_id=metric.id)
    db.session.commit()

    result = get_metrics_v2(
        str(enabled_metric_preference.report_id),
        {"submission_business_id": submission_business_id, "restricted_parent_ids": [str(sid1), str(sid3)]},
    )
    assert 1 == len(result.get("metrics", []))
    metric = result.get("metrics")[0][0]
    assert [str(sid1), str(sid3)] == metric.get("children_ids")
    assert ["category1", "category3"] == metric.get("values")


def test_get_metrics_v2_hide_some_metrics(app_context, mocker) -> None:
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)
    mocker.patch("copilot.logic.reports.get_summary_config_ids_to_hide", return_value=["RANDOM_ID"])

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()

    metric_preference = metric_preference_fixture(
        report_id=report.id,
        parent_id=report.id,
        summary_config_id="TEST_SUMMARY_CONFIG_ID",
        is_applicable=True,
        is_enabled=True,
    )
    metric_preference_2 = metric_preference_fixture(
        report_id=report.id,
        parent_id=report.id,
        summary_config_id="TEST_SUMMARY_CONFIG_ID_2",
        is_applicable=True,
        is_enabled=True,
    )
    db.session.commit()

    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=report.id,
        summary_config_id=metric_preference.summary_config_id,
        metric_type=MetricType.SUM,
        float_values=[],
        value_parent_types=[],
        value_parent_ids=[],
        value_business_ids=[],
    )
    metric: MetricV2 = metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=report.id,
        summary_config_id=metric_preference_2.summary_config_id,
        metric_type=MetricType.SUM,
        float_values=[],
        value_parent_types=[],
        value_parent_ids=[],
        value_business_ids=[],
    )
    metric_source_fixture(metric_v2_id=metric.id)
    db.session.commit()

    result = get_metrics_v2(str(report.id), {})
    assert len(result["metrics"]) == 2

    mocker.patch("copilot.logic.reports.get_summary_config_ids_to_hide", return_value=["TEST_SUMMARY_CONFIG_ID"])

    result = get_metrics_v2(str(report.id), {})
    assert len(result["metrics"]) == 1


def test_get_metrics_v2_data_filtering_logic(app_context, mocker, enabled_metric_preference: MetricPreference) -> None:
    current_user = AnonObj(
        is_internal_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=lambda type, id: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    float_values = [1, 2, 3, 5, 7]
    pid1 = uuid4()
    pid2 = uuid4()
    bid1 = uuid4()
    bid2 = uuid4()
    sid1 = uuid4()
    sid2 = uuid4()
    sid3 = uuid4()
    value_parent_ids = [pid1, sid1, sid2, pid2, sid3]
    value_business_ids = [bid1, None, None, bid2, None]
    value_parent_types = [
        ParentType.BUSINESS.value,
        ParentType.STRUCTURE.value,
        ParentType.STRUCTURE.value,
        ParentType.BUSINESS.value,
        ParentType.STRUCTURE.value,
    ]

    metric_v2_fixture(
        execution_id=uuid4(),
        report_v2_id=enabled_metric_preference.report_id,
        summary_config_id=enabled_metric_preference.summary_config_id,
        parent_type=enabled_metric_preference.parent_type,
        parent_id=enabled_metric_preference.parent_id,
        metric_type="SUM",
        float_values=float_values,
        value_business_ids=value_business_ids,
        value_parent_types=value_parent_types,
        value_parent_ids=value_parent_ids,
    )
    db.session.commit()

    result = get_metrics_v2(str(enabled_metric_preference.report_id), {})

    assert result is not None
    assert len(result["metrics"]) == 1

    premises_based_metric = result["metrics"][0][0]
    assert PREMISES_BASED_FILTERING_MODE == premises_based_metric.get("filtering_mode")

    structures_based_metric = result["metrics"][0][1]
    assert STRUCTURES_BASED_FILTERING_MODE == structures_based_metric.get("filtering_mode")

    assert [str(pid1), str(pid2)] == premises_based_metric.get("children_ids")
    assert ["5.0", "7.0"] == premises_based_metric.get("values")

    assert [str(sid1), str(sid2), str(sid3)] == structures_based_metric.get("children_ids")
    assert ["2.0", "3.0", "7.0"] == structures_based_metric.get("values")


def test_auto_set_name(app_context, mocker) -> None:
    sbid_1 = uuid4()
    sbid_2 = uuid4()
    sbid_3 = uuid4()
    mocked_user = AnonObj(
        id=5,
        has_report_permission=lambda type, id: True,
        is_being_impersonated=False,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False, is_cs_manager=False),
        is_trusted=True,
        email="<EMAIL>",
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)
    mocker.patch(
        "flask.current_app.ers_client_v3.get_entities", return_value=_generate_enrichment_data([sbid_1, sbid_2, sbid_3])
    )

    organization_fixture()
    user_fixture()

    report, submission = report_and_submission_fixture()
    submission.account_id = "123"
    submission.businesses.extend(
        [
            SubmissionBusiness(
                id=sbid_1,
                requested_name="req_name_1",
                requested_address="req_address_1",
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                business_id=sbid_1,
            ),
            SubmissionBusiness(
                id=sbid_2,
                requested_name="req_name_2",
                requested_address="req_address_2",
                business_id=sbid_2,
            ),
            SubmissionBusiness(
                id=sbid_3,
                requested_name="req_name_3",
                requested_address="req_address_3",
                business_id=sbid_3,
            ),
        ]
    )
    db.session.commit()

    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    assert r.name == "REQ_NAME_1"

    SubmissionBusiness.query.filter_by(id=sbid_1).update({"entity_role": SubmissionBusinessEntityRole.PROJECT})
    SubmissionBusiness.query.filter_by(id=sbid_2).update(
        {"entity_role": SubmissionBusinessEntityRole.GENERAL_CONTRACTOR}
    )
    db.session.commit()
    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    assert r.name == "GC: REQ_NAME_2, PROJECT: REQ_NAME_1"

    SubmissionBusiness.query.filter_by(id=sbid_1).update({"requested_name": None})
    SubmissionBusiness.query.filter_by(id=sbid_2).update({"requested_name": None})
    db.session.commit()
    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    # should not be updated
    assert r.name == "GC: REQ_NAME_2, PROJECT: REQ_NAME_1"

    SubmissionBusiness.query.filter_by(id=sbid_1).update({"entity_role": None})
    SubmissionBusiness.query.filter_by(id=sbid_2).update({"entity_role": SubmissionBusinessEntityRole.PROJECT})
    db.session.commit()
    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    # should not be updated
    assert r.name == "GC: REQ_NAME_2, PROJECT: REQ_NAME_1"

    SubmissionBusiness.query.filter_by(id=sbid_1).update({"requested_address": None})
    SubmissionBusiness.query.filter_by(id=sbid_2).update({"requested_name": None})
    ReportV2.query.filter_by(id=report.id).update({"name": "Should not be updated"})
    db.session.commit()
    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    assert r.name == "Should not be updated"
    auto_set_name(str(report.id))

    r = ReportV2.query.get(report.id)
    assert r.name == "Should not be updated"

    mocked_user.is_internal_machine_user = False

    auto_set_name(str(report.id), data_onboarding=True)

    r = ReportV2.query.get(report.id)
    assert r.name == "Should not be updated"

    exception_thrown = False
    try:
        auto_set_name(str(report.id))
    except Exception as e:
        assert isinstance(e, Forbidden)
        exception_thrown = True

    assert exception_thrown

    # noinspection PyUnresolvedReferences
    mocked_user.applicable_settings.is_cs_manager = True

    exception_thrown = False
    try:
        auto_set_name(str(report.id))
    except Exception as e:
        assert isinstance(e, Forbidden)
        exception_thrown = True

    assert not exception_thrown


def test_auto_set_name_with_ers_data(app_context, mocker) -> None:
    sbid_1 = UUID("1c626cd6-a53d-458e-a41c-a7dd107f954b")
    sbid_2 = UUID("9c626cd6-a53d-458e-a41c-a7dd107f954b")
    sbid_3 = UUID("2c626cd6-a53d-458e-a41c-a7dd107f954b")

    mocked_user = AnonObj(
        id=5,
        has_report_permission=lambda type, id: True,
        is_being_impersonated=False,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False, is_cs_manager=False),
        is_trusted=True,
        email="<EMAIL>",
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)
    mocker.patch(
        "flask.current_app.ers_client_v3.get_entities", return_value=_generate_enrichment_data([sbid_1, sbid_2, sbid_3])
    )

    organization_fixture()
    user_fixture()

    report, submission = report_and_submission_fixture()
    submission.account_id = "123"
    submission.businesses.extend(
        [
            SubmissionBusiness(
                id=sbid_1,
                requested_name="req_name_1",
                requested_address="req_address_1",
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                business_id=sbid_1,
                serial_id=1,
            ),
            SubmissionBusiness(
                id=sbid_2,
                requested_name=None,
                requested_address="req_address_2",
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                business_id=sbid_2,
                serial_id=2,
            ),
            SubmissionBusiness(
                id=sbid_3,
                requested_name=None,
                requested_address="req_address_3",
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                business_id=sbid_2,
                serial_id=3,
            ),
        ]
    )
    db.session.commit()
    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    assert r.name == "REQ_NAME_1"

    ReportV2.query.filter_by(id=report.id).update({"name": "Original name"})
    db.session.commit()

    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    assert r.name == "REQ_NAME_1"

    ReportV2.query.filter_by(id=report.id).update({"name": "Original name"})
    SubmissionBusiness.query.filter_by(id=sbid_1).update({"requested_name": None})
    db.session.commit()

    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    assert r.name == "ERS_NAME_2"


def test_auto_set_name_for_shadow_report(app_context, mocker) -> None:
    sbid_1 = uuid4()
    sbid_2 = uuid4()
    mocked_user = AnonObj(
        id=5,
        has_report_permission=lambda type, id: True,
        is_being_impersonated=False,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False, is_cs_manager=False),
        is_trusted=True,
        email="<EMAIL>",
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    mocker.patch("flask.current_app.ers_client_v3.get_entities", return_value=_generate_enrichment_data([sbid_1]))

    organization_fixture()
    user_fixture()

    report, submission = report_and_submission_fixture(name="Original name", is_verified=True)
    shadow_submission = shadow_submission_fixture(original_submission=submission, with_files=False)
    shadow_submission.report.name = report.name
    submission.businesses.extend(
        [
            SubmissionBusiness(
                id=sbid_1,
                requested_name="req_name_1",
                requested_address="req_address_1",
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                business_id=sbid_1,
            )
        ]
    )
    shadow_submission.businesses.extend(
        [
            SubmissionBusiness(
                id=sbid_2,
                requested_name="req_name_1",
                requested_address="req_address_1",
                named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
                business_id=sbid_2,
            )
        ]
    )
    db.session.commit()

    auto_set_name(str(shadow_submission.report_id))
    r = ReportV2.query.get(shadow_submission.report_id)
    assert r.name == "Original name"

    auto_set_name(str(report.id))
    r = ReportV2.query.get(report.id)
    assert r.name == "REQ_NAME_1"


def test_get_report_cross_org(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: False,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=True,
        organization_id=6,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()

    report, submission = report_and_submission_fixture()
    db.session.commit()

    res, status = get_report(str(report.id), None, False)
    assert status == 404
    assert res["organization_id"] == 1


def _get_report_id_by_external_id_fixture(
    mocker, report_organization_id: int, user_organization_id: int, client_submission_id: str
) -> str:
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: False,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=True,
        organization_id=user_organization_id,
        is_machine_user=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture(id=report_organization_id)
    user_fixture(organization_id=report_organization_id)

    report, submission = report_and_submission_fixture(organization_id=report_organization_id)
    submission_client_id_fixture(submission_id=submission.id, client_submission_id=client_submission_id)
    db.session.commit()
    return str(report.id)


def test_get_report_id_by_external_id(app_context, mocker):
    report_organization_id = 1
    user_organization_id = report_organization_id
    client_submission_id = "EXTERNAL_ID"

    expected_report_id = _get_report_id_by_external_id_fixture(
        mocker, report_organization_id, user_organization_id, client_submission_id
    )

    actual_report_id = get_report_id_by_external_id(client_submission_id)["report_id"]
    assert actual_report_id == expected_report_id


def test_get_report_id_by_external_id_skip_deleted_reports(app_context, mocker):
    report_organization_id = 1
    user_organization_id = report_organization_id
    client_submission_id = "EXTERNAL_ID"

    report_id_for_delete = _get_report_id_by_external_id_fixture(
        mocker, report_organization_id, user_organization_id, client_submission_id
    )

    ReportV2.query.filter_by(id=report_id_for_delete).update({"is_deleted": True})
    report, submission = report_and_submission_fixture(organization_id=report_organization_id)
    submission_client_id_fixture(submission_id=submission.id, client_submission_id=client_submission_id)
    db.session.commit()
    expected_report_id = str(report.id)

    actual_report_id = get_report_id_by_external_id(client_submission_id)["report_id"]
    assert actual_report_id == expected_report_id


def test_get_report_id_by_external_id_not_found(app_context, mocker):
    report_organization_id = 1
    user_organization_id = report_organization_id
    client_submission_id = "EXTERNAL_ID"

    _get_report_id_by_external_id_fixture(mocker, report_organization_id, user_organization_id, client_submission_id)

    modified_client_submission_id = "MODIFIED_EXTERNAL_ID"
    with raises(NotFound, match="Report not found for given external id"):
        get_report_id_by_external_id(modified_client_submission_id)


def test_get_report_id_by_external_id_for_different_org(app_context, mocker):
    report_organization_id = 1
    user_organization_id = 2
    client_submission_id = "EXTERNAL_ID"

    _get_report_id_by_external_id_fixture(mocker, report_organization_id, user_organization_id, client_submission_id)

    with raises(NotFound, match="Report not found for given external id"):
        get_report_id_by_external_id(client_submission_id)


def test_get_emails_for_report(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()
    email = email_fixture(
        email_body="some body",
    )
    report.correspondence_id = email.correspondence_id
    db.session.commit()
    email_with_status = email_fixture(correspondence_id=email.correspondence_id, email_body="some body 2")
    email_status_fixture(email_id=email_with_status.id)
    db.session.commit()

    res = get_emails_for_report(str(report.id))
    assert res[1] == 200
    assert len(res[0]) == 2
    assert res[0][0]["email_body"] == "some body 2"
    assert len(res[0][0]["status"]) == 1
    assert res[0][1]["email_body"] == "some body"
    assert len(res[0][1]["status"]) == 0


def test_get_shadow_or_shadowed_report(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)
    organization_fixture()
    user_fixture()
    report = report_fixture(shadow_type=ReportShadowType.HAS_ACTIVE_SHADOW)
    shadow_report = report_fixture(shadow_type=ReportShadowType.IS_ACTIVE_SHADOW)
    shadowed_report = report_fixture(shadow_type=ReportShadowType.IS_SHADOW_ORIGIN)
    report_shadow_dependency_fixture(report.id, shadow_report.id)
    report_shadow_dependency_fixture(shadowed_report.id, report.id)
    db.session.commit()
    res = get_shadow_or_shadowed_report(str(report.id))
    assert res["shadow_report_id"] == shadow_report.id
    assert res["shadowed_report_id"] == shadowed_report.id


def test_bulk_create_metric_v2(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    structure_id_1: UUID = uuid4()
    structure_id_2: UUID = uuid4()
    premises_id_1: UUID = uuid4()
    premises_id_2: UUID = uuid4()

    metrics_request_one = base_request_body.copy()
    expected_float_values_one: list[float] = [42.0, 30.0, 10.0, 20.0]
    metrics_request_one.update(
        {
            "metric_type": "SUM",
            "execution_id": str(uuid4()),
            "float_values": expected_float_values_one,
            "value_parent_ids": [premises_id_1, structure_id_1, structure_id_2, premises_id_2],
            "value_parent_types": [
                ParentType.PREMISES,
                ParentType.STRUCTURE,
                ParentType.STRUCTURE,
                ParentType.PREMISES,
            ],
            "value_business_ids": [None, None, None, None],
            "units": "things",
        }
    )

    metrics_request_two = base_request_body.copy()
    expected_float_values_two: list[float] = [42.0, 30.0, 10.0, 21.0]
    metrics_request_two.update(
        {
            "metric_type": "MEAN",
            "execution_id": str(uuid4()),
            "float_values": expected_float_values_two,
            "value_parent_ids": [premises_id_1, structure_id_1, structure_id_2, premises_id_2],
            "value_parent_types": [
                ParentType.PREMISES.value,
                ParentType.STRUCTURE.value,
                ParentType.STRUCTURE.value,
                ParentType.PREMISES.value,
            ],
            "value_business_ids": [None, None, None, None],
            "units": Units.PERCENTAGE.value,
        }
    )

    metrics_request_three = base_request_body
    expected_float_values = [1.2, 5.0]
    metrics_request_three.update(
        {
            "metric_type": "MEAN",
            "execution_id": str(uuid4()),
            "float_values": expected_float_values,
            "value_parent_ids": [structure_id_1, premises_id_1],
            "value_parent_types": [ParentType.STRUCTURE.value, ParentType.PREMISES.value],
            "units": "things2",
        }
    )

    bulk_create_request = {"metric_requests": [metrics_request_one, metrics_request_two, metrics_request_three]}

    bulk_create_metric_v2(str(report.id), bulk_create_request)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .options(selectinload(MetricV2.sources))
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    xr = [r.metric_type for r in results]
    assert len(results) == 3, xr
    assert len([r for r in results if r.metric_type == MetricType.MEAN]) == 2, xr
    assert len([r for r in results if r.metric_type == MetricType.SUM]) == 1, xr

    sum_metric = [r for r in results if r.metric_type == MetricType.SUM][0]
    assert sum_metric.metric_preference is not None
    assert sum_metric.metric_preference.is_applicable is True
    assert sum_metric.parent_id == metric_preference.parent_id
    assert sum_metric.parent_type == metric_preference.parent_type
    assert sum_metric.summary_config_id == metric_preference.summary_config_id
    assert sum_metric.metric_type == MetricType.SUM
    assert sum_metric.mean == 30.0, sum_metric.filtered_values
    assert sum_metric.sum == 60.0, sum_metric.filtered_values
    assert sum_metric.min == 20.0, sum_metric.filtered_values
    assert sum_metric.max == 40.0, sum_metric.filtered_values
    assert sum_metric.units == "things"
    assert sum_metric.has_structures_data is False

    float_values = sum_metric.filtered_values
    assert float_values is not None
    assert 2 == len(float_values)
    assert [40.0, 20.0] == float_values

    assert len(sum_metric.sources) == 1


def test_create_report_with_additional_identifiers(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
        is_nationwide=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)
    mocked_request = AnonObj(
        files={},
    )
    mocker.patch("connexion.request", mocked_request)

    organization_fixture()
    user = user_fixture(email="<EMAIL>")

    requesst_body = {
        "name": " API Report",
        "external_id": "123",
        "brokerage": "CRC",
        "brokerage_office": "Los Angeles, CA",
        "broker": "John Smith",
        "is_renewal": False,
        "proposed_effective_date": "2022-01-01",
        "coverage_type": "primary",
        "user_email": user.email,
        "additional_identifiers": [
            {"type": "quote_number", "identifier": "ABC"},
        ],
    }

    response = create_report(json.dumps(requesst_body))
    assert response[1] == 201

    submission = Submission.query.first()
    assert submission.name == requesst_body["name"]
    assert submission.origin == "API"
    assert submission.client_submission_ids[0].client_submission_id == requesst_body["external_id"]
    assert submission.identifiers[0].identifier == "ABC"
    assert submission.identifiers[0].identifier_type == "quote_number"

    submission_level_data = SubmissionLevelExtractedData.query.all()
    assert len(submission_level_data) == 7
    assert all(sled.submission_id == submission.id for sled in submission_level_data)
    assert all(sled.source_details == SourceDetails.API for sled in submission_level_data)
    assert (
        next(
            sled for sled in submission_level_data if sled.field == EntityInformation.POLICY_EFFECTIVE_START_DATE
        ).value
        == f'"{requesst_body["proposed_effective_date"]}"'
    )


def test_update_report_with_additional_identifiers(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
        id=1,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture(email="<EMAIL>")
    report, submission = report_and_submission_fixture()
    db.session.commit()

    requesst_body = {
        "additional_identifiers": [
            {"type": "quote_number", "identifier": "ABC"},
        ],
    }

    update_report(str(report.id), requesst_body)

    identifiers = SubmissionIdentifier.query.filter_by(submission_id=submission.id).all()
    assert len(identifiers) == 1
    assert identifiers[0].identifier == "ABC"
    assert identifiers[0].identifier_type == "quote_number"

    requesst_body = {
        "additional_identifiers": [
            {"type": "quote_number", "identifier": "123"},
        ],
    }

    update_report(str(report.id), requesst_body)
    identifiers = SubmissionIdentifier.query.filter_by(submission_id=submission.id).all()
    assert len(identifiers) == 1
    assert identifiers[0].identifier == "123"
    assert identifiers[0].identifier_type == "quote_number"


def test_update_report_with_submission_level_data(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
        id=1,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture(email="<EMAIL>")
    report, submission = report_and_submission_fixture()
    db.session.commit()

    requesst_body = {
        "proposed_effective_date": "2022-01-01",
    }

    update_report(str(report.id), requesst_body)

    sled = SubmissionLevelExtractedData.query.filter_by(submission_id=submission.id).all()
    assert len(sled) == 1
    assert sled[0].field == EntityInformation.POLICY_EFFECTIVE_START_DATE
    assert sled[0].value == f'"{requesst_body["proposed_effective_date"]}"'

    requesst_body = {
        "proposed_effective_date": "2023-01-01",
        "policy_expiration_date": "2022-12-31",
    }

    update_report(str(report.id), requesst_body)
    sled = (
        SubmissionLevelExtractedData.query.filter_by(submission_id=submission.id)
        .order_by(SubmissionLevelExtractedData.created_at)
        .all()
    )
    assert len(sled) == 2
    assert sled[0].field == EntityInformation.POLICY_EFFECTIVE_START_DATE
    assert sled[0].value == f'"{requesst_body["proposed_effective_date"]}"'
    assert sled[1].field == EntityInformation.POLICY_END_DATE
    assert sled[1].value == f'"{requesst_body["policy_expiration_date"]}"'


def test_get_report_lite_bundled_reports(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
            is_being_impersonated=False,
            is_trusted=True,
        ),
    )
    organization_fixture(id=6)
    user_fixture(id=6, organization_id=6)
    new_report, new_submission = report_and_submission_fixture(organization_id=6, owner_id=6)
    new_report_2, new_submission_2 = report_and_submission_fixture(organization_id=6, owner_id=6)
    new_report_3, new_submission_3 = report_and_submission_fixture(organization_id=6, owner_id=6)
    id = new_report.id

    bundle = report_bundle_fixture()
    new_report.report_bundle_id = bundle.id
    new_report_2.report_bundle_id = bundle.id
    new_report_3.report_bundle_id = bundle.id

    coverage = coverage_fixture(name=Coverage.ExistingNames.BusinessAuto, organization_id=6)
    coverage_crime = coverage_fixture(name=Coverage.ExistingNames.Crime, organization_id=6)

    submission_coverage_fixture(coverage_id=coverage.id, submission_id=new_submission_2.id)
    submission_coverage_fixture(coverage_id=coverage_crime.id, submission_id=new_submission_3.id)
    db.session.commit()

    response = get_report_lite(str(id), "bundled_reports")

    assert len(response["bundled_reports"]) == 2
    assert len(response["bundled_reports"][0]["submissions"]) == 1
    assert len(response["bundled_reports"][0]["submissions"][0]["coverages"]) == 1
    assert len(response["bundled_reports"][1]["submissions"]) == 1
    assert len(response["bundled_reports"][1]["submissions"][0]["coverages"]) == 1


@pytest.mark.parametrize("include_client_ids", [True, False])
def test_get_reports_returns_reports_with_no_client_id(app_context, mocker, include_client_ids: bool) -> None:
    report_organization_id = 111
    user_organization_id = report_organization_id
    client_submission_id = "EXTERNAL_ID"

    report_id_with_client_id = _get_report_id_fixture(
        mocker, report_organization_id, user_organization_id, client_submission_id
    )
    report_id_no_client_id = _get_report_id_fixture(mocker, report_organization_id, user_organization_id)

    response = get_reports(organization_id=report_organization_id, per_page=100, include_client_ids=include_client_ids)
    assert "reports" in response
    assert any(
        report["id"] == report_id_with_client_id for report in response["reports"]
    ), "Report with client ID not found in the response"
    assert any(
        report["id"] == report_id_no_client_id for report in response["reports"]
    ), "Report without client ID not found in the response"
    assert len(response["reports"]) == 2, "Expected 2 reports in the response"
    assert all(len(report["submissions"]) == 1 for report in response["reports"]), "Expected 1 submission per report"
    if include_client_ids:
        assert all(
            len(submission["client_submission_ids"]) == 1
            for report in response["reports"]
            for submission in report["submissions"]
            if report["id"] == report_id_with_client_id
        ), "Expected client_submission_id to be included in the response"
    else:
        assert all(
            not submission["client_submission_ids"]
            for report in response["reports"]
            for submission in report["submissions"]
        ), "Expected client_submission_id to be excluded from the response"


@pytest.mark.parametrize("include_client_ids", [True, False])
def test_get_reports_only_returns_reports_with_client_id(app_context, mocker, include_client_ids: bool) -> None:
    report_organization_id = 111
    user_organization_id = report_organization_id
    client_submission_id = "EXTERNAL_ID"

    report_id_with_client_id = _get_report_id_fixture(
        mocker, report_organization_id, user_organization_id, client_submission_id
    )
    report_id_no_client_id = _get_report_id_fixture(mocker, report_organization_id, user_organization_id)

    response = get_reports(
        organization_id=report_organization_id,
        per_page=100,
        include_client_ids=include_client_ids,
        with_client_ids_only=True,
    )
    assert "reports" in response
    assert any(
        report["id"] == report_id_with_client_id for report in response["reports"]
    ), "Report with client ID not found in the response"
    assert not any(
        report["id"] == report_id_no_client_id for report in response["reports"]
    ), "Report without client ID found in the response"
    assert len(response["reports"]) == 1, "Expected 1 report in the response"
    assert all(len(report["submissions"]) == 1 for report in response["reports"]), "Expected 1 submission per report"
    assert all(
        len(submission["client_submission_ids"]) == 1
        for report in response["reports"]
        for submission in report["submissions"]
        if report["id"] == report_id_with_client_id
    ), "Expected client_submission_id to be included in the response"


def test_get_reports_effective_date_filtering(app_context, mocker) -> None:
    report_organization_id = 111
    user_organization_id = report_organization_id
    client_submission_id = "EXTERNAL_ID"

    old_report_id = _get_report_id_fixture(
        mocker,
        report_organization_id,
        user_organization_id,
        client_submission_id,
        "2001-09-11",
    )
    new_report_id = _get_report_id_fixture(
        mocker,
        report_organization_id,
        user_organization_id,
        client_submission_id,
        "2024-04-20",
    )

    response = get_reports(
        organization_id=report_organization_id,
        per_page=100,
        with_client_ids_only=True,
        effective_date_from="2024-01-01",
    )

    assert "reports" in response
    assert any(report["id"] == new_report_id for report in response["reports"]), "Report not found in the response"
    assert len(response["reports"]) == 1, "Expected 1 report in the response"
    assert all(len(report["submissions"]) == 1 for report in response["reports"]), "Expected 1 submission per report"


def _get_report_id_fixture(
    mocker,
    report_organization_id: int,
    user_organization_id: int,
    client_submission_id: str | None = None,
    proposed_effective_date: str | datetime | None = None,
) -> str:
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: False,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=True,
        organization_id=user_organization_id,
        is_machine_user=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture(id=report_organization_id)
    user_fixture(organization_id=report_organization_id)

    report, submission = report_and_submission_fixture(
        organization_id=report_organization_id,
        proposed_effective_date=proposed_effective_date,
    )
    if client_submission_id:
        submission_client_id_fixture(submission_id=submission.id, client_submission_id=client_submission_id)
    db.session.commit()
    return str(report.id)


def test_report_shadows_dependency_triggers(app_context) -> None:
    def except_on_commit():
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            assert True
            return
        assert False

    organization_fixture()
    user_fixture()
    parent_report = report_fixture()
    shadow_report = report_fixture()
    db.session.commit()

    parent_report.shadow_type = ReportShadowType.HAS_ACTIVE_SHADOW
    except_on_commit()
    parent_report.shadow_type = ReportShadowType.IS_ACTIVE_SHADOW
    shadow_report.shadow_type = ReportShadowType.IS_ACTIVE_SHADOW
    report_shadow_dependency_fixture(report_id=parent_report.id, shadow_report_id=shadow_report.id)
    except_on_commit()
    parent_report.shadow_type = ReportShadowType.HAS_ACTIVE_SHADOW
    report_shadow_dependency_fixture(report_id=parent_report.id, shadow_report_id=shadow_report.id)
    except_on_commit()
    parent_report.shadow_type = ReportShadowType.HAS_ACTIVE_SHADOW
    shadow_report.shadow_type = ReportShadowType.IS_ACTIVE_SHADOW
    shadow_dependency = report_shadow_dependency_fixture(report_id=parent_report.id, shadow_report_id=shadow_report.id)
    db.session.commit()

    db.session.delete(shadow_dependency)
    except_on_commit()
    db.session.delete(shadow_report)
    except_on_commit()
    parent_report.shadow_type = None
    db.session.delete(shadow_report)
    db.session.commit()


def test_revert_to_data_onboarding(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=lambda x, y: True,
            is_being_impersonated=False,
        ),
    )
    mocker.patch("flask.request", AnonObj(path="/v3/reports/1234/revert_to_data_onboarding"))
    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture()
    submission.processing_state = SubmissionProcessingState.COMPLETED
    file_1 = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.EMAIL,
        file_type=FileType.EMAIL,
        submission_id=submission.id,
        s3_key="test_key",
    )
    file_2 = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.ACORD_125,
        initial_processing_state=FileProcessingState.PROCESSED,
        file_type=FileType.ACORD_FORM,
        submission_id=submission.id,
        s3_key="test_key",
    )
    file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.LOSS_RUN,
        file_type=FileType.LOSS_RUN,
        submission_id=submission.id,
        s3_key="test_key",
    )
    file_fixture(
        processing_state=FileProcessingState.PROCESSING_FAILED,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        file_type=FileType.SOV,
        submission_id=submission.id,
        s3_key="test_key",
    )
    with open("tests/data/data_onboarding/email_entity_mapped_data.json") as file:
        email_entity_mapped_data = json.load(file)
    file_1.processed_file = ProcessedFile(processed_data={}, entity_mapped_data=email_entity_mapped_data)
    file_2.processed_file = ProcessedFile(processed_data=email_entity_mapped_data)
    db.session.commit()
    revert_to_data_onboarding(str(submission.report_id))
    processing_dependency = ReportShadowDependency.query.filter_by(report_id=submission.report_id).first()
    assert processing_dependency is not None
    shadow_submission = Submission.query.filter_by(report_id=processing_dependency.shadow_report_id).first()
    assert shadow_submission is not None
    assert shadow_submission.processing_state == SubmissionProcessingState.DATA_ONBOARDING
    email = File.query.filter_by(
        submission_id=shadow_submission.id, classification=ClassificationDocumentType.EMAIL
    ).first()
    assert email is not None
    assert email.processing_state == FileProcessingState.WAITING_FOR_DATA_ONBOARDING
    acord_125 = File.query.filter_by(
        submission_id=shadow_submission.id, classification=ClassificationDocumentType.ACORD_125
    ).first()
    assert acord_125 is not None
    assert acord_125.processing_state == FileProcessingState.WAITING_FOR_COMPLETION
    loss_run = File.query.filter_by(
        submission_id=shadow_submission.id, classification=ClassificationDocumentType.LOSS_RUN
    ).first()
    assert loss_run is not None
    assert loss_run.processing_state == FileProcessingState.COMPLETED
    sov = File.query.filter_by(
        submission_id=shadow_submission.id, classification=ClassificationDocumentType.SOV_SPREADSHEET
    ).first()
    assert sov is not None
    assert sov.processing_state == FileProcessingState.PROCESSING_FAILED


def test_report_revert_to_state(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=lambda x, y: True,
            is_being_impersonated=False,
        ),
    )
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    organization_fixture()
    user_fixture()
    _, submission = report_and_submission_fixture()
    submission.processing_state = SubmissionProcessingState.COMPLETED
    file = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.EMAIL,
        file_type=FileType.EMAIL,
        submission_id=submission.id,
        s3_key="test_key",
    )
    with open("tests/data/data_onboarding/email_entity_mapped_data.json") as em_file:
        email_entity_mapped_data = json.load(em_file)
    with open("tests/data/data_onboarding/email_business_resolution_data.json") as brd_file:
        business_resolution_data = json.load(brd_file)

    file.processed_file = ProcessedFile(
        processed_data={},
        entity_mapped_data=email_entity_mapped_data,
        business_resolution_data=business_resolution_data,
    )
    db.session.commit()
    revert_report_to_state(str(submission.report_id), SubmissionProcessingState.BUSINESS_CONFIRMATION)
    processing_dependency = ReportShadowDependency.query.filter_by(report_id=submission.report_id).first()
    assert processing_dependency is not None
    shadow_submission = Submission.query.filter_by(report_id=processing_dependency.shadow_report_id).first()
    assert shadow_submission.processing_state == SubmissionProcessingState.BUSINESS_CONFIRMATION
    email = File.query.filter_by(
        submission_id=shadow_submission.id, classification=ClassificationDocumentType.EMAIL
    ).first()
    assert email.processing_state == FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION

    revert_report_to_state(str(shadow_submission.report_id), SubmissionProcessingState.ENTITY_MAPPING)
    shadow_submission = Submission.query.filter_by(report_id=processing_dependency.shadow_report_id).first()
    assert shadow_submission.processing_state == SubmissionProcessingState.ENTITY_MAPPING
    email = File.query.filter_by(
        submission_id=shadow_submission.id, classification=ClassificationDocumentType.EMAIL
    ).first()
    assert email.processing_state == FileProcessingState.WAITING_FOR_ENTITY_MAPPING


def test_get_reports_by_submission_returns_organization_id(app_context):
    organization_fixture(id=5)
    user_fixture(organization_id=5, id=15)
    report_fix, submission = report_and_submission_fixture(owner_id=15, organization_id=5)

    db.session.commit()

    response = get_reports_by_submission(str(submission.id), stages=["ALL"])
    assert response == {
        "reports": [
            {
                "id": str(report_fix.id),
                "name": report_fix.name,
                "organization_id": 5,
                "owner_id": 15,
                "submissions": [{"id": str(submission.id)}],
            }
        ]
    }


def test_submission_period_other_terms(app_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        idp=False,
        applicable_settings=AnonObj(show_internal_account_id=False),
        is_trusted=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        cross_organization_access=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    coverage = coverage_fixture(name="fun")

    report, submission = report_and_submission_fixture()
    submission_coverage_fixture(
        submission_id=submission.id, coverage_id=coverage.id, other_terms="Some other text", period="Period 1\nPeriod 2"
    )
    db.session.commit()

    res = get_report(str(report.id), "organization_id", False)
    coverage = res.get("submissions")[0].get("coverages")[0]
    assert coverage["other_terms"] == "Some other text"
    assert coverage["period"] == "Period 1\nPeriod 2"


@pytest.mark.skip(reason="Skip this test until we enable excluding FNI from the metric")
def test_create_metric_not_filter_fni_when_value_within_range(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    expected_float_values: list[float] = [42.0, 30.0, 10.0, 20.0]
    business_id = uuid4()
    submission_business = submission_business_fixture(
        business_id=str(business_id),
        submission_id=report.submission.id,
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    db.session.commit()
    base_request_body.update(
        {
            "metric_type": "SUM",
            "float_values": expected_float_values,
            "parent_type": ParentType.REPORT.value,
            "parent_id": report.id,
            "value_parent_ids": [uuid4(), uuid4(), uuid4(), uuid4()],
            "value_parent_types": [
                ParentType.PREMISES.value,
                ParentType.STRUCTURE.value,
                ParentType.STRUCTURE.value,
                ParentType.PREMISES.value,
            ],
            "value_business_ids": [None, None, None, business_id],
            "units": "things",
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .options(selectinload(MetricV2.sources))
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.filtered_values == [40.0, 20.0]
    assert metric.sum == 60.0


@pytest.mark.skip(reason="Skip this test until we enable excluding FNI from the metric")
def test_create_metric_filter_fni(
    app_context, report: ReportV2, metric_preference: MetricPreference, base_request_body: dict
) -> None:
    expected_float_values: list[float] = [42.0, 30.0, 10.0, 38.0]
    business_id = uuid4()
    submission_business = submission_business_fixture(
        business_id=str(business_id),
        submission_id=report.submission.id,
        named_insured=SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED,
    )
    db.session.commit()
    base_request_body.update(
        {
            "metric_type": "SUM",
            "float_values": expected_float_values,
            "parent_type": ParentType.REPORT.value,
            "parent_id": report.id,
            "value_parent_ids": [uuid4(), uuid4(), uuid4(), uuid4()],
            "value_parent_types": [
                ParentType.PREMISES.value,
                ParentType.STRUCTURE.value,
                ParentType.STRUCTURE.value,
                ParentType.PREMISES.value,
            ],
            "value_business_ids": [None, None, None, business_id],
            "units": "things",
        }
    )

    create_metric_v2(str(report.id), base_request_body)

    results: list[MetricV2] = (
        db.session.query(MetricV2)
        .options(selectinload(MetricV2.sources))
        .filter(MetricV2.report_v2_id == report.id)
        .filter(MetricV2.summary_config_id == metric_preference.summary_config_id)
        .all()
    )

    assert len(results) == 1
    metric = results[0]
    assert metric.filtered_values == [40.0]
    assert metric.sum == 40.0


def test_get_report_external(app_context, mocker):
    feature_flags_client_mock = MagicMock()

    def variation_mock(*args, **kwargs):
        if args[0] == FeatureType.EXTEND_EXTERNAL_REPORT_API_RESPONSE:
            return True
        return False

    feature_flags_client_mock.variation = variation_mock
    FeatureFlagsClient._client = feature_flags_client_mock

    current_user = AnonObj(
        id=1,
        is_machine_user=True,
        organization_id=1,
        organization=MagicMock(name="Test Agency"),
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=MagicMock(return_value=True),
        name="Test Agency",
        is_internal_machine_user=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    org = organization_fixture()
    user = user_fixture(name="Marian")

    created_at = datetime(2025, 4, 20, 21, 37, 00)
    report_id = "1200b83c-d65d-497c-a4a0-c32f041e6236"

    report, submission = report_and_submission_fixture(id=UUID(report_id))

    report.created_at = created_at
    report.name = "LIS speed services"
    submission.stage = SubmissionStage.WAITING_FOR_OTHERS

    db.session.commit()

    response = get_report_external(str(report.id))

    assert response == {
        "created_at": "2025-04-20T21:37:00+00:00",
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Awaiting Reply",
        "assigned_underwriters": [],
        "broker": None,
        "brokerage": None,
        "fni_state": None,
        "policy_expiration_date": None,
        "primary_naics_code": None,
        "proposed_effective_date": None,
        "received_date": None,
        "recommendation_action": None,
        "recommendation_score": None,
        "sic_code": None,
        "stage": "WAITING_FOR_OTHERS",
    }

    # Then change the stage to QUOTED_BOUND

    submission.stage = SubmissionStage.QUOTED_BOUND
    db.session.commit()

    response = get_report_external(str(report.id))
    assert response == {
        "created_at": "2025-04-20T21:37:00+00:00",
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Bound",
        "assigned_underwriters": [],
        "broker": None,
        "brokerage": None,
        "fni_state": None,
        "policy_expiration_date": None,
        "primary_naics_code": None,
        "proposed_effective_date": None,
        "received_date": None,
        "recommendation_action": None,
        "recommendation_score": None,
        "sic_code": None,
        "stage": "QUOTED_BOUND",
    }

    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=org.id, roles=["AGENT"])

    submission.broker = broker
    submission.brokerage = brokerage
    submission.fni_state = "Greenland"
    submission.policy_expiration_date = datetime(2025, 4, 20, 21, 37, 1)
    submission.primary_naics_code = "NAICS_123456"
    submission.proposed_effective_date = datetime(2025, 4, 20, 21, 37, 2)
    submission.received_date = datetime(2025, 4, 20, 21, 37, 3)
    submission.recommendation_v2_action = "PREFERRED"
    submission.recommendation_v2_score = 82
    submission.sic_code = "SIC_123456"

    db.session.commit()

    response = get_report_external(str(report.id))
    assert response == {
        "created_at": "2025-04-20T21:37:00+00:00",
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Bound",
        "assigned_underwriters": [],
        "broker": {"email": "<EMAIL>", "name": "Test Agency"},
        "brokerage": {"name": "Test Brokerage"},
        "fni_state": "Greenland",
        "policy_expiration_date": "2025-04-20T21:37:01",
        "primary_naics_code": "NAICS_123456",
        "proposed_effective_date": "2025-04-20T21:37:02",
        "received_date": "2025-04-20T21:37:03",
        "recommendation_action": "PREFERRED",
        "recommendation_score": 82,
        "sic_code": "SIC_123456",
        "stage": "QUOTED_BOUND",
    }

    # Test that the response correctly handles broker without email
    broker.email = None
    db.session.commit()

    response = get_report_external(str(report.id))
    assert response == {
        "created_at": "2025-04-20T21:37:00+00:00",
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Bound",
        "assigned_underwriters": [],
        "broker": {"email": None, "name": "Test Agency"},
        "brokerage": {"name": "Test Brokerage"},
        "fni_state": "Greenland",
        "policy_expiration_date": "2025-04-20T21:37:01",
        "primary_naics_code": "NAICS_123456",
        "proposed_effective_date": "2025-04-20T21:37:02",
        "received_date": "2025-04-20T21:37:03",
        "recommendation_action": "PREFERRED",
        "recommendation_score": 82,
        "sic_code": "SIC_123456",
        "stage": "QUOTED_BOUND",
    }

    submission_user_fixture(user_id=user.id, submission_id=submission.id)

    db.session.commit()

    response = get_report_external(str(report.id))
    assert response == {
        "created_at": "2025-04-20T21:37:00+00:00",
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Bound",
        "assigned_underwriters": [{"email": "<EMAIL>", "name": "Marian"}],
        "broker": {"email": None, "name": "Test Agency"},
        "brokerage": {"name": "Test Brokerage"},
        "fni_state": "Greenland",
        "policy_expiration_date": "2025-04-20T21:37:01",
        "primary_naics_code": "NAICS_123456",
        "proposed_effective_date": "2025-04-20T21:37:02",
        "received_date": "2025-04-20T21:37:03",
        "recommendation_action": "PREFERRED",
        "recommendation_score": 82,
        "sic_code": "SIC_123456",
        "stage": "QUOTED_BOUND",
    }


def test_get_report_external_old_schema(app_context, mocker):
    feature_flags_client_mock = MagicMock()

    def variation_mock(*args, **kwargs):
        return False

    feature_flags_client_mock.variation = variation_mock
    FeatureFlagsClient._client = feature_flags_client_mock

    current_user = AnonObj(
        id=1,
        is_machine_user=True,
        organization_id=1,
        organization=MagicMock(name="Test Agency"),
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=MagicMock(return_value=True),
        name="Test Agency",
        is_internal_machine_user=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    org = organization_fixture()
    user_fixture()

    created_at = datetime(2025, 4, 20, 21, 37, 00)
    report_id = "1200b83c-d65d-497c-a4a0-c32f041e6236"

    report, submission = report_and_submission_fixture(id=UUID(report_id))

    report.created_at = created_at
    report.name = "LIS speed services"
    submission.stage = SubmissionStage.WAITING_FOR_OTHERS

    db.session.commit()

    response = get_report_external(str(report.id))

    assert response == {
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Awaiting Reply",
    }

    # Then change the stage to QUOTED_BOUND

    submission.stage = SubmissionStage.QUOTED_BOUND
    db.session.commit()

    response = get_report_external(str(report.id))
    assert response == {
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Bound",
    }

    brokerage = brokerage_fixture(organization_id=org.id)
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=org.id, roles=["AGENT"])

    submission.broker = broker
    submission.brokerage = brokerage
    submission.fni_state = "Greenland"
    submission.policy_expiration_date = datetime(2025, 4, 20, 21, 37, 1)
    submission.primary_naics_code = "NAICS_123456"
    submission.proposed_effective_date = datetime(2025, 4, 20, 21, 37, 2)
    submission.received_date = datetime(2025, 4, 20, 21, 37, 3)
    submission.recommendation_v2_action = "PREFERRED"
    submission.recommendation_v2_score = 82
    submission.sic_code = "SIC_123456"

    db.session.commit()

    response = get_report_external(str(report.id))
    assert response == {
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Bound",
    }

    # Test that the response correctly handles broker without email
    broker.email = None
    db.session.commit()

    response = get_report_external(str(report.id))
    assert response == {
        "id": "1200b83c-d65d-497c-a4a0-c32f041e6236",
        "name": "LIS speed services",
        "status": "Bound",
    }


def test_delete_report_with_correspondence(app_context, mocker):
    current_user = AnonObj(
        id=1,
        is_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=MagicMock(return_value=True),
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    organization_fixture()
    user_fixture()

    email_account = "<EMAIL>"
    correspondence = report_email_correspondence_fixture(email_account=email_account)
    email_fixture(correspondence_id=correspondence.id, message_id="1234", email_account=email_account)
    email_fixture(correspondence_id=correspondence.id, message_id="2345", email_account=email_account)

    report, submission = report_and_submission_fixture(correspondence_id=correspondence.id)

    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_email_without_verified_submission_event"
    ) as send_event_mock:
        delete_report(str(report.id))

        report = ReportV2.query.filter_by(id=report.id).first()
        submission = Submission.query.filter_by(id=submission.id).first()
        assert report.is_deleted == True
        assert submission.is_deleted == True
        assert send_event_mock.called
        assert send_event_mock.call_args[0][0] == report.organization_id
        assert sorted(send_event_mock.call_args[0][1]) == ["1234", "2345"]
        assert send_event_mock.call_args[0][2] == email_account


def test_delete_report_with_shared_correspondence(app_context, mocker):
    current_user = AnonObj(
        id=1,
        is_machine_user=True,
        email="<EMAIL>",
        is_being_impersonated=False,
        has_report_permission=MagicMock(return_value=True),
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)

    organization_fixture()
    user_fixture()

    email_account = "<EMAIL>"
    correspondence = report_email_correspondence_fixture(email_account=email_account)
    email_fixture(correspondence_id=correspondence.id, message_id="1234", email_account=email_account)
    email_fixture(correspondence_id=correspondence.id, message_id="2345", email_account=email_account)

    report, submission = report_and_submission_fixture(correspondence_id=correspondence.id)
    report2, submission2 = report_and_submission_fixture(
        correspondence_id=correspondence.id,
        is_auto_processed=True,
        processing_state=SubmissionProcessingState.PROCESSING,
    )

    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_email_without_verified_submission_event"
    ) as send_event_mock:
        delete_report(str(report.id))

        assert not send_event_mock.called

    correspondence2 = report_email_correspondence_fixture(email_account=email_account)
    email_fixture(correspondence_id=correspondence2.id, message_id="2345", email_account=email_account)
    report3, submission3 = report_and_submission_fixture(
        correspondence_id=correspondence2.id, is_verified=True, is_auto_processed=True, is_deleted=True
    )

    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_email_without_verified_submission_event"
    ) as send_event_mock:
        delete_report(str(report2.id))

        assert send_event_mock.called
        assert send_event_mock.call_args[0] == (report.organization_id, ["1234"], email_account)


def test_update_report_org_group(app_context, mocker):
    current_user = AnonObj(
        id=321,
        is_machine_user=False,
        email="<EMAIL>",
        is_being_impersonated=False,
        is_internal_machine_user=False,
        has_report_permission=MagicMock(return_value=True),
        is_member_of_organization=MagicMock(return_value=True),
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)
    organization = organization_fixture(id=ExistingOrganizations.BishopConifer.value)
    settings_fixture(
        organization_id=organization.id,
        org_groups=[
            OrganizationGroups.CONIFER_MAIN_ST.value,
            OrganizationGroups.CONIFER_HOSPITALITY.value,
            OrganizationGroups.CONIFER_THC.value,
            OrganizationGroups.CONIFER_CANNABIS_SELECT.value,
        ],
    )
    user_fixture(id=1, organization_id=organization.id)
    assigned_user = user_fixture(id=2, organization_id=organization.id)
    core = user_group_fixture(organization_id=organization.id, name=ConiferUserGroup.Conifer_Core.value)
    conifer_all = user_group_fixture(organization_id=organization.id, name=ConiferUserGroup.Conifer_All.value)
    cannabis_select = user_group_fixture(organization_id=organization.id, name=ConiferUserGroup.Cannabis_Select.value)

    report = report_with_submissions_fixture(organization_id=organization.id, is_verified_shell=True)
    submission_user_fixture(assigned_user.id, report.submission.id)
    report_permission_fixture(report_id=report.id, grantee_user_id=assigned_user.id)
    db.session.commit()

    share_report_with_org(report)

    assert len(report.report_permissions) == 2
    assert len([rp for rp in report.report_permissions if rp.grantee_group_id]) == 0
    assert report.organization_permission_level is not None

    update_report(str(report.id), {"org_group": OrganizationGroups.CONIFER_MAIN_ST.value}, return_response=False)

    db.session.refresh(report)
    assert len(report.report_permissions) == 4
    assert len([rp for rp in report.report_permissions if rp.grantee_group_id]) == 2
    assert len([rp for rp in report.report_permissions if rp.grantee_group_id == core.id]) == 1
    assert len([rp for rp in report.report_permissions if rp.grantee_group_id == conifer_all.id]) == 1

    update_report(
        str(report.id), {"org_group": OrganizationGroups.CONIFER_CANNABIS_SELECT.value}, return_response=False
    )

    db.session.refresh(report)
    assert len(report.report_permissions) == 4
    assert len([rp for rp in report.report_permissions if rp.grantee_group_id]) == 2
    assert len([rp for rp in report.report_permissions if rp.grantee_group_id == conifer_all.id]) == 1
    assert len([rp for rp in report.report_permissions if rp.grantee_group_id == cannabis_select.id]) == 1


def test_mark_report_emails_as_not_submission(app_context, mocker):
    organization_fixture()
    settings_fixture(organization_id=1, is_email_classification_enabled=True)
    user_fixture()
    correspondence = report_email_correspondence_fixture()
    current_user = AnonObj(
        name="test_user",
        has_report_permission=lambda x, y: True,
        is_cs_manager_or_internal_machine_user=lambda x: True,
    )
    report = report_fixture(correspondence_id=correspondence.id)
    submission_fixture(report=report)
    mocker.patch("flask_login.utils._get_user", return_value=current_user)
    email_classification_label_fixture(label=KalepaEmailClassificationLabels.NOT_SUBMISSION.value)
    email = email_fixture(correspondence_id=correspondence.id)

    email_classification = EmailClassification.query.filter_by(email_id=email.id).first()
    assert email_classification is None

    reason_to_delete = "I don't like this sub"
    result, status = mark_report_emails_as_not_submission(str(report.id), {"reason": reason_to_delete})
    assert status == 204
    email_classification = EmailClassification.query.filter_by(email_id=email.id).first()
    assert email_classification is not None
    assert email_classification.label.label == KalepaEmailClassificationLabels.NOT_SUBMISSION.value
    assert "Manually set by test_user" in email_classification.explanation
    assert reason_to_delete in email_classification.explanation


def test_get_pds_debugger_files_data(app_context, mocker):
    organization_fixture()
    user_fixture()
    report = report_fixture()
    submission_fixture(report=report)
    file = file_fixture(submission_id=report.submission.id, name="test_name")
    processed_file_fixture(
        file_id=file.id,
        raw_processed_data={"data": "test raw_processed_data"},
        processed_data={"data": "test processed_data"},
        entity_mapped_data={"data": "test entity_mapped_data"},
        onboarded_data={"data": "test onboarded_data"},
    )
    current_user = AnonObj(
        name="test_user",
        has_report_permission=lambda x, y: True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=current_user)
    files = get_pds_debugger_files_data(report.id)
    assert len(files) == 1
    assert files[0]["file_id"] == str(file.id)
    processed_file = files[0]
    assert processed_file["raw_processed_data"] == {"data": "test raw_processed_data"}
    assert processed_file["processed_data"] == {"data": "test processed_data"}
    assert processed_file["entity_mapped_data"] == {"data": "test entity_mapped_data"}
    assert processed_file["onboarded_data"] == {"data": "test onboarded_data"}
    assert processed_file["name"] == "test_name"
