from http import HTTPStatus
from unittest.mock import MagicMock, patch
from uuid import uuid4
import uuid

from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_enhancement_type import FileEnhancementType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.insights_document_type import InsightsDocumentType
from static_common.enums.origin import Origin
from static_common.enums.submission_processing_state import SubmissionProcessingState
from werkzeug.exceptions import BadRequest
import pytest

from copilot.clients.feature_flags import FeatureType
from copilot.models import File, Submission, db
from copilot.models.reports import ReportShadowDependency, ReportV2
from copilot.models.types import ReportShadowType, UserShadowFileState
from copilot.v3.controllers.custom_file_type import (
    get_aggregated_custom_file_types,
    get_customizable_classifiers_v2_input_types,
    get_insights_document_types,
)
from copilot.v3.controllers.files import (
    create_file,
    delete_file,
    find_files_to_download,
    get_aggregated_file_types,
    get_file_stats,
    get_files,
    get_files_for_external_consumers,
    get_upload_url,
    split_pdf_file,
)
from tests.integration.coverages.fixtures import set_up_current_user
from tests.integration.factories import (
    custom_file_type_fixture,
    enhanced_file_fixture,
    file_fixture,
    organization_fixture,
    report_and_submission_fixture,
    report_fixture,
    report_shadow_dependency_fixture,
    submission_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj, with_feature_flag


@pytest.fixture
def mock_current_user(mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        is_support=False,
        id=1,
        is_tier_2_or_internal_machine_user=True,
        is_cs_manager_or_internal_machine_user=False,
        email="<EMAIL>",
        is_kalepa=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)
    return mocked_user


def test_get_files(app_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        is_kalepa=False,
        id=1,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, sub = report_and_submission_fixture()

    file_fixture(
        submission_id=sub.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
    )
    file_fixture(
        submission_id=sub.id,
        name="file2",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
    )
    file_fixture(
        submission_id=sub.id,
        name="file3_deleted",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        is_deleted=True,
    )
    db.session.commit()

    response = get_files(sub.id)
    assert len(response.json) == 2


def test_get_files_with_custom_file_type(app_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        is_kalepa=False,
        id=1,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, sub = report_and_submission_fixture()

    custom_file_type_id = uuid4()

    custom_file_type_fixture(
        organization_id=sub.organization_id, file_type_name="CUSTOM_FILE_TYPE", id=custom_file_type_id
    )
    file_fixture(
        submission_id=sub.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        custom_file_type_id=custom_file_type_id,
    )
    db.session.commit()

    response = get_files(sub.id)
    assert len(response.json) == 1
    assert response.json[0]["custom_file_type_id"] == str(custom_file_type_id)
    assert response.json[0]["custom_file_type"]["file_type_name"] == "CUSTOM_FILE_TYPE"


def test_get_aggregated_file_types(app_context, mocker):
    org = organization_fixture()
    user = user_fixture(organization_id=org.id)

    mocker.patch("flask_login.utils._get_user", return_value=user)

    report, sub = report_and_submission_fixture(
        submission=submission_fixture(processing_state=SubmissionProcessingState.COMPLETED, organization_id=org.id)
    )
    _, sub_not_completed = report_and_submission_fixture(
        submission=submission_fixture(
            processing_state=SubmissionProcessingState.PROCESSING, organization_id=org.id, is_deleted=True
        )
    )

    file_fixture(
        submission_id=sub.id,
        name="file1_correct",
        file_type=FileType.SOV,
        user_file_type=None,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub.id,
        name="file2_acord_correct",
        file_type=FileType.ACORD_FORM,
        user_file_type=None,
        classification=ClassificationDocumentType.ACORD_FORM,
        additional_info={
            "acord": {
                "form_name": "TEST_ACORD_FORM",
            }
        },
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub.id,
        name="file3_unknown",
        file_type=FileType.UNKNOWN,
        user_file_type=None,
        classification=ClassificationDocumentType.UNKNOWN,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub.id,
        name="file4_incorrect",
        file_type=FileType.SOV,
        user_file_type=FileType.EMAIL,
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.UNKNOWN,
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub.id,
        name="file5_excluded",
        file_type=FileType.MERGED,
        user_file_type=FileType.MERGED,
        classification=ClassificationDocumentType.UNKNOWN,
        organization_id=org.id,
        is_deleted=True,
    )
    file_fixture(
        submission_id=sub_not_completed.id,
        name="file6_excluded",
        file_type=FileType.SOV,
        user_file_type=FileType.ACORD_FORM,
        classification=ClassificationDocumentType.ACORD_FORM,
        organization_id=org.id,
    )
    db.session.commit()

    res, status = get_aggregated_file_types(org.id)
    # TODO add more test cases
    assert status == HTTPStatus.OK
    assert len(res) == 3


def test_get_aggregated_custom_file_types(app_context, mocker):
    org = organization_fixture()
    user = user_fixture(organization_id=org.id)

    mocker.patch("flask_login.utils._get_user", return_value=user)

    report, sub = report_and_submission_fixture(
        submission=submission_fixture(
            processing_state=SubmissionProcessingState.COMPLETED, organization_id=org.id, is_deleted=False
        )
    )
    _, sub_not_completed = report_and_submission_fixture(
        submission=submission_fixture(
            processing_state=SubmissionProcessingState.PROCESSING, organization_id=org.id, is_deleted=True
        )
    )

    custom_file_type = custom_file_type_fixture(file_type_name="CUSTOM_FILE_TYPE", organization_id=sub.organization_id)
    custom_file_type_second = custom_file_type_fixture(
        file_type_name="CUSTOM_FILE_TYPE_SECOND", organization_id=sub.organization_id
    )
    custom_file_type_deleted_files = custom_file_type_fixture(
        file_type_name="CUSTOM_FILE_TYPE_DELETED_FILES", organization_id=sub.organization_id
    )

    file_fixture(
        submission_id=sub.id,
        name="file1_correct",
        file_type=FileType.CUSTOM,
        custom_file_type_id=custom_file_type.id,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        organization_id=org.id,
        is_deleted=False,
    )
    file_fixture(
        submission_id=sub.id,
        name="file2_acord_correct",
        file_type=FileType.CUSTOM,
        custom_file_type_id=custom_file_type.id,
        classification=ClassificationDocumentType.ACORD_FORM,
        additional_info={
            "acord": {
                "form_name": "TEST_ACORD_FORM",
            }
        },
        organization_id=org.id,
        is_deleted=False,
    )
    file_fixture(
        submission_id=sub.id,
        name="file3_unknown",
        file_type=FileType.CUSTOM,
        custom_file_type_id=custom_file_type_second.id,
        classification=ClassificationDocumentType.UNKNOWN,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        organization_id=org.id,
        is_deleted=False,
    )
    file_fixture(
        submission_id=sub.id,
        name="file4_incorrect",
        file_type=FileType.SOV,
        user_file_type=FileType.EMAIL,
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.UNKNOWN,
        organization_id=org.id,
        is_deleted=False,
    )
    file_fixture(
        submission_id=sub_not_completed.id,
        name="file6_excluded",
        file_type=FileType.CUSTOM,
        custom_file_type_id=custom_file_type_deleted_files.id,
        classification=ClassificationDocumentType.ACORD_FORM,
        organization_id=org.id,
        is_deleted=False,
    )
    db.session.commit()

    res, status = get_aggregated_custom_file_types(org.id)
    assert status == HTTPStatus.OK
    assert len(res) == 3


def test_get_file_stats(app_context, mocker):
    org = organization_fixture()
    user = user_fixture(organization_id=org.id)

    mocker.patch("flask_login.utils._get_user", return_value=user)

    report, sub = report_and_submission_fixture(
        submission=submission_fixture(processing_state=SubmissionProcessingState.COMPLETED, organization_id=org.id)
    )
    _, sub_not_completed = report_and_submission_fixture(
        submission=submission_fixture(processing_state=SubmissionProcessingState.PROCESSING, organization_id=org.id)
    )

    file_fixture(
        submission_id=sub.id,
        name="file1_correct",
        file_type=FileType.SOV,
        user_file_type=None,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub.id,
        name="file2_acord_correct",
        file_type=FileType.ACORD_FORM,
        user_file_type=None,
        classification=ClassificationDocumentType.ACORD_FORM,
        additional_info={
            "acord": {
                "form_name": "TEST_ACORD_FORM",
            }
        },
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub.id,
        name="file3_unknown",
        file_type=FileType.UNKNOWN,
        user_file_type=None,
        classification=ClassificationDocumentType.UNKNOWN,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub.id,
        name="file4_incorrect",
        file_type=FileType.SOV,
        user_file_type=FileType.EMAIL,
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.UNKNOWN,
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub.id,
        name="file5_excluded",
        file_type=FileType.MERGED,
        user_file_type=FileType.MERGED,
        classification=ClassificationDocumentType.UNKNOWN,
        organization_id=org.id,
    )
    file_fixture(
        submission_id=sub_not_completed.id,
        name="file6_excluded",
        file_type=FileType.SOV,
        user_file_type=FileType.ACORD_FORM,
        classification=ClassificationDocumentType.ACORD_FORM,
        organization_id=org.id,
    )
    db.session.commit()

    response = get_file_stats(sub.organization_id)

    expected_classification_stats = {"correct": 3, "incorrect": 1, "unknown": 2}
    expected_classified_file_type_stats = [
        {"file_type": FileType.SOV, "count": 1},
        {"file_type": FileType.EMAIL, "count": 1},
        {"file_type": FileType.ACORD_FORM, "count": 1},
        {"file_type": FileType.UNKNOWN, "count": 1},
    ]
    expected_acord_form_stats = [{"form_type": "TEST_ACORD_FORM", "count": 1}]
    expected_classification_and_processing_non_acord_stats = [
        {
            "file_type": FileType.SOV,
            "total_files": 1,
            "correctly_classified": 1,
            "incorrectly_classified": 0,
            "classification_accuracy": 1.0,
            "processed": 0,
            "not_applicable": 0,
            "processing_accuracy": 0.0,
        },
        {
            "file_type": FileType.EMAIL,
            "total_files": 1,
            "correctly_classified": 0,
            "incorrectly_classified": 1,
            "classification_accuracy": 0.0,
            "processed": 1,
            "not_applicable": 0,
            "processing_accuracy": 1.0,
        },
        {
            "file_type": FileType.UNKNOWN,
            "total_files": 1,
            "correctly_classified": 1,
            "incorrectly_classified": 0,
            "classification_accuracy": 1.0,
            "processed": 0,
            "not_applicable": 1,
            "processing_accuracy": 0.0,
        },
    ]
    expected_classification_and_processing_acord_stats = [
        {
            "form_type": "TEST_ACORD_FORM",
            "total_files": 1,
            "correctly_classified": 1,
            "incorrectly_classified": 0,
            "classification_accuracy": 1.0,
            "processed": 0,
            "not_applicable": 0,
            "processing_accuracy": 0.0,
        }
    ]
    expected_processing_by_submission_stats = [
        {
            "report_id": str(report.id),
            "name": sub.name,
            "total_files": 4,
            "processed": 1,
            "not_applicable": 1,
            "processing_accuracy": 1 / (4 - 1),
        }
    ]
    body = response.json
    assert body.get("classification_stats") is not None
    assert body.get("classified_file_type_stats") is not None
    assert body.get("acord_form_stats") is not None
    assert body.get("classification_and_processing_non_acord_stats") is not None
    assert body.get("classification_and_processing_acord_stats") is not None
    assert body.get("processing_by_submission_stats") is not None

    classification_stats = body["classification_stats"]
    assert classification_stats == expected_classification_stats, "Incorrect classification_stats values"

    classified_file_type_stats = body["classified_file_type_stats"]
    assert len(classified_file_type_stats) == 4
    assert sorted(classified_file_type_stats, key=lambda x: x["file_type"]) == sorted(
        expected_classified_file_type_stats, key=lambda x: x["file_type"]
    ), "Incorrect classified_file_type_stats values"

    acord_form_stats = body["acord_form_stats"]
    assert len(acord_form_stats) == 1
    assert sorted(acord_form_stats, key=lambda x: x["form_type"]) == sorted(
        expected_acord_form_stats, key=lambda x: x["form_type"]
    ), "Incorrect acord_form_stats values"

    # Not count ACORD there
    classification_and_processing_non_acord_stats = body["classification_and_processing_non_acord_stats"]
    assert len(classification_and_processing_non_acord_stats) == 3
    assert sorted(classification_and_processing_non_acord_stats, key=lambda x: x["file_type"]) == sorted(
        expected_classification_and_processing_non_acord_stats, key=lambda x: x["file_type"]
    ), "Incorrect classification_and_processing_non_acord_stats values"

    classification_and_processing_acord_stats = body["classification_and_processing_acord_stats"]
    assert len(classification_and_processing_acord_stats) == 1
    assert sorted(classification_and_processing_acord_stats, key=lambda x: x["form_type"]) == sorted(
        expected_classification_and_processing_acord_stats, key=lambda x: x["form_type"]
    ), "Incorrect classification_and_processing_acord_stats values"

    processing_by_submission_stats = body["processing_by_submission_stats"]
    assert len(processing_by_submission_stats) == 1
    assert sorted(processing_by_submission_stats, key=lambda x: x["report_id"]) == sorted(
        expected_processing_by_submission_stats, key=lambda x: x["report_id"]
    ), "Incorrect processing_by_submission_stats values"


def test_create_file(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
        is_kalepa_internal=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED, is_auto_processed=True
    )

    new_file_mame = "new_sov.csv"
    mocked_file = AnonObj(
        filename=new_file_mame,
        mimetype="text/csv",
        read=lambda: b"file content",
        seek=lambda x: None,
        stream=AnonObj(
            read=lambda: b"file content",
            seek=lambda x: None,
        ),
    )

    mocked_request = AnonObj(
        form={
            "name": new_file_mame,
            "file_type": "SOV",
            "origin": "COPILOT",
            "submission_id": submission.id,
        },
        files={"file": mocked_file},
    )

    mocker.patch("connexion.request", mocked_request)
    mocker.patch("flask.current_app.submission_s3_client.get_file_checksum", return_value="test_checksum")
    mocker.patch("flask.current_app.submission_s3_client.upload_file", return_value=None)
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )

    body = {
        "submission_id": submission.id,
        "is_internal": True,
    }
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
    ) as send_event_mock:
        create_file(body)

        new_sov_file = File.query.filter_by(submission_id=submission.id, name=new_file_mame).first()

        assert new_sov_file.processing_state == FileProcessingState.NOT_CLASSIFIED
        assert send_event_mock.called


def test_create_file_with_custom_type(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
        is_kalepa_internal=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED, is_auto_processed=True
    )

    custom_file_type = custom_file_type_fixture(
        file_type_name="CUSTOM_FILE_TYPE", organization_id=submission.organization_id
    )

    db.session.commit()

    new_file_mame = "new_sov.csv"
    mocked_file = AnonObj(
        filename=new_file_mame,
        mimetype="text/csv",
        read=lambda: b"file content",
        seek=lambda x: None,
        stream=AnonObj(
            read=lambda: b"file content",
            seek=lambda x: None,
        ),
    )

    mocked_request = AnonObj(
        form={
            "name": new_file_mame,
            "origin": "COPILOT",
            "submission_id": submission.id,
            "custom_file_type_id": custom_file_type.id,
        },
        files={"file": mocked_file},
    )

    mocker.patch("connexion.request", mocked_request)
    mocker.patch("flask.current_app.submission_s3_client.get_file_checksum", return_value="test_checksum")
    mocker.patch("flask.current_app.submission_s3_client.upload_file", return_value=None)
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )

    body = {
        "submission_id": submission.id,
        "is_internal": True,
    }
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_classified_event"
    ) as send_event_mock:
        create_file(body)

        new_sov_file = File.query.filter_by(submission_id=submission.id, name=new_file_mame).first()

        assert new_sov_file.processing_state == FileProcessingState.CLASSIFIED
        assert new_sov_file.custom_file_type_id == custom_file_type.id
        assert new_sov_file.custom_file_type.file_type_name == "CUSTOM_FILE_TYPE"
        assert new_sov_file.custom_classification == "CUSTOM_FILE_TYPE_SPREADSHEET"
        assert send_event_mock.called


def test_replace_sov(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
        is_kalepa_internal=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED, is_auto_processed=True
    )

    old_sov_file = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        submission_id=submission.id,
        s3_key="old_sov",
        is_required_shadow_processing=False,
        checksum="old_sov",
        name="old_sov.csv",
    )
    db.session.commit()

    new_file_mame = "new_sov.csv"
    mocked_file = AnonObj(
        filename=new_file_mame,
        mimetype="text/csv",
        read=lambda: b"file content",
        seek=lambda x: None,
        stream=AnonObj(
            read=lambda: b"file content",
            seek=lambda x: None,
        ),
    )

    mocked_request = AnonObj(
        form={
            "name": new_file_mame,
            "file_type": "SOV",
            "origin": "COPILOT",
            "submission_id": submission.id,
        },
        files={"file": mocked_file},
    )

    mocker.patch("connexion.request", mocked_request)
    mocker.patch("flask.current_app.submission_s3_client.get_file_checksum", return_value="test_checksum")
    mocker.patch("flask.current_app.submission_s3_client.upload_file", return_value=None)
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )

    body = {
        "submission_id": submission.id,
        "is_internal": True,
        "replace_files": [old_sov_file.id],
    }
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_files_clearing_finished_event"
    ) as send_event_mock:
        create_file(body)

        shadow_dependency = ReportShadowDependency.query.filter_by(report_id=report.id).first()
        assert shadow_dependency is not None
        shadow_submission = Submission.query.filter_by(report_id=shadow_dependency.shadow_report_id).first()
        assert shadow_submission is not None
        assert shadow_submission.processing_state == SubmissionProcessingState.FILES_CLEARING

        new_sov_file = File.query.filter_by(submission_id=shadow_submission.id, name=new_file_mame).first()
        old_sov_file = File.query.filter_by(submission_id=shadow_submission.id, name=old_sov_file.name).first()

        assert new_sov_file.processing_state == FileProcessingState.NOT_CLASSIFIED
        assert new_sov_file.is_required_shadow_processing is False
        assert old_sov_file.processing_state == FileProcessingState.REPLACED
        assert send_event_mock.called
        assert shadow_submission.is_processing


def test_replace_sov_chained_shadow(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
        is_kalepa_internal=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED,
        is_auto_processed=True,
    )
    shadow_report, shadow_submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED,
        is_auto_processed=True,
    )
    report.shadow_type = ReportShadowType.HAS_ACTIVE_SHADOW
    shadow_report.shadow_type = ReportShadowType.IS_ACTIVE_SHADOW
    shadow_dependency = report_shadow_dependency_fixture(report.id, shadow_report.id)

    old_sov_file = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        submission_id=shadow_submission.id,
        s3_key="old_sov",
        is_required_shadow_processing=False,
        checksum="old_sov",
        name="old_sov.csv",
    )
    db.session.commit()

    new_file_mame = "new_sov.csv"
    mocked_file = AnonObj(
        filename=new_file_mame,
        mimetype="text/csv",
        read=lambda: b"file content",
        seek=lambda x: None,
        stream=AnonObj(
            read=lambda: b"file content",
            seek=lambda x: None,
        ),
    )

    mocked_request = AnonObj(
        form={
            "name": new_file_mame,
            "file_type": "SOV",
            "origin": "COPILOT",
            "submission_id": shadow_submission.id,
        },
        files={"file": mocked_file},
    )

    mocker.patch("connexion.request", mocked_request)
    mocker.patch("flask.current_app.submission_s3_client.get_file_checksum", return_value="test_checksum")
    mocker.patch("flask.current_app.submission_s3_client.upload_file", return_value=None)
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )

    body = {
        "submission_id": shadow_submission.id,
        "is_internal": True,
        "replace_files": [old_sov_file.id],
    }
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_files_clearing_finished_event"
    ) as send_event_mock:
        create_file(body)

        existing_shadow_dependency = ReportShadowDependency.query.filter_by(
            report_id=report.id, shadow_report_id=shadow_report.id
        ).first()
        assert existing_shadow_dependency is not None
        assert existing_shadow_dependency.is_active is False

        shadow_submission = Submission.query.filter_by(report_id=shadow_report.id).first()
        assert shadow_submission.is_deleted is True
        shadow_report = ReportV2.query.filter_by(id=shadow_report.id).first()
        assert shadow_report.shadow_type == ReportShadowType.CHAINED_SHADOW

        new_shadow_dependency = ReportShadowDependency.query.filter_by(report_id=report.id, is_active=True).first()
        new_shadow_submission = Submission.query.filter_by(report_id=new_shadow_dependency.shadow_report_id).first()
        assert new_shadow_submission is not None
        assert new_shadow_submission.processing_state == SubmissionProcessingState.FILES_CLEARING

        new_sov_file = File.query.filter_by(submission_id=new_shadow_submission.id, name=new_file_mame).first()
        old_sov_file = File.query.filter_by(submission_id=new_shadow_submission.id, name=old_sov_file.name).first()

        assert new_sov_file.processing_state == FileProcessingState.NOT_CLASSIFIED
        assert new_sov_file.is_required_shadow_processing is False
        assert old_sov_file.processing_state == FileProcessingState.REPLACED
        assert send_event_mock.called
        assert new_shadow_submission.is_processing


def test_replace_sov_user_shadow(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=False,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
        is_kalepa=False,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED, is_auto_processed=True, is_verified=True
    )

    old_sov_file = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        submission_id=submission.id,
        s3_key="old_sov",
        is_required_shadow_processing=False,
        checksum="old_sov",
        name="old_sov.csv",
    )
    db.session.commit()

    new_file_mame = "new_sov.csv"
    mocked_file = AnonObj(
        filename=new_file_mame,
        mimetype="text/csv",
        read=lambda: b"file content",
        seek=lambda x: None,
        stream=AnonObj(
            read=lambda: b"file content",
            seek=lambda x: None,
        ),
    )

    mocked_request = AnonObj(
        form={
            "name": new_file_mame,
            "file_type": "SOV",
            "origin": "COPILOT",
            "submission_id": submission.id,
        },
        files={"file": mocked_file},
    )

    mocker.patch("connexion.request", mocked_request)
    mocker.patch("flask.current_app.submission_s3_client.get_file_checksum", return_value="test_checksum")
    mocker.patch("flask.current_app.submission_s3_client.upload_file", return_value=None)
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user",
        return_value=True,
    )

    body = {
        "submission_id": submission.id,
        "is_internal": False,
        "replace_files": [old_sov_file.id],
    }
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_files_clearing_finished_event"
    ) as send_event_mock:
        create_file(body)

        shadow_dependency = ReportShadowDependency.query.filter_by(report_id=report.id).first()
        assert shadow_dependency is not None
        shadow_submission = Submission.query.filter_by(report_id=shadow_dependency.shadow_report_id).first()
        assert shadow_submission is not None
        assert shadow_submission.processing_state == SubmissionProcessingState.FILES_CLEARING

        new_sov_file = File.query.filter_by(submission_id=submission.id, name=new_file_mame).first()
        old_sov_file = File.query.filter_by(submission_id=submission.id, name=old_sov_file.name).first()
        assert new_sov_file.is_required_shadow_processing
        assert new_sov_file.user_shadow_state == UserShadowFileState.CREATED
        assert old_sov_file.is_required_shadow_processing
        assert old_sov_file.user_shadow_state == UserShadowFileState.REPLACED

        new_sov_file_shadow = File.query.filter_by(submission_id=shadow_submission.id, name=new_file_mame).first()
        old_sov_file_shadow = File.query.filter_by(submission_id=shadow_submission.id, name=old_sov_file.name).first()

        assert new_sov_file_shadow.processing_state == FileProcessingState.NOT_CLASSIFIED
        assert new_sov_file_shadow.is_required_shadow_processing is False
        assert old_sov_file_shadow is None
        assert send_event_mock.called
        assert shadow_submission.is_processing


def test_replace_child_sov_user_shadow(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=False,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
        is_kalepa=False,
        email="<EMAIL>",
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED, is_auto_processed=True, is_verified=True
    )

    old_parent_file = file_fixture(
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        file_type=FileType.MERGED,
        classification=ClassificationDocumentType.MERGED,
        submission_id=submission.id,
        s3_key="merged",
        is_required_shadow_processing=False,
        checksum="merged",
        name="merged.xlsx",
    )

    old_sov_file = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        submission_id=submission.id,
        s3_key="old_sov",
        is_required_shadow_processing=False,
        checksum="old_sov",
        name="old_sov.csv",
        parent_file_id=old_parent_file.id,
    )
    db.session.commit()

    new_file_mame = "new_sov.csv"
    mocked_file = AnonObj(
        filename=new_file_mame,
        mimetype="text/csv",
        read=lambda: b"file content",
        seek=lambda x: None,
        stream=AnonObj(
            read=lambda: b"file content",
            seek=lambda x: None,
        ),
    )

    mocked_request = AnonObj(
        form={
            "name": new_file_mame,
            "file_type": "SOV",
            "origin": "COPILOT",
            "submission_id": submission.id,
        },
        files={"file": mocked_file},
    )

    mocker.patch("connexion.request", mocked_request)
    mocker.patch("flask.current_app.submission_s3_client.get_file_checksum", return_value="test_checksum")
    mocker.patch("flask.current_app.submission_s3_client.upload_file", return_value=None)
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user",
        return_value=True,
    )

    body = {
        "submission_id": submission.id,
        "is_internal": False,
        "replace_files": [old_sov_file.id],
    }
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_files_clearing_finished_event"
    ) as send_event_mock:
        create_file(body)

        shadow_dependency = ReportShadowDependency.query.filter_by(report_id=report.id).first()
        assert shadow_dependency is not None
        shadow_submission = Submission.query.filter_by(report_id=shadow_dependency.shadow_report_id).first()
        assert shadow_submission is not None
        assert shadow_submission.processing_state == SubmissionProcessingState.FILES_CLEARING

        new_sov_file = File.query.filter_by(submission_id=submission.id, name=new_file_mame).first()
        old_sov_file = File.query.filter_by(submission_id=submission.id, name=old_sov_file.name).first()
        assert new_sov_file.is_required_shadow_processing
        assert new_sov_file.user_shadow_state == UserShadowFileState.CREATED
        assert old_sov_file.is_required_shadow_processing
        assert old_sov_file.user_shadow_state == UserShadowFileState.REPLACED

        new_sov_file_shadow = File.query.filter_by(submission_id=shadow_submission.id, name=new_file_mame).first()
        old_sov_file_shadow = File.query.filter_by(submission_id=shadow_submission.id, name=old_sov_file.name).first()

        assert new_sov_file_shadow.processing_state == FileProcessingState.NOT_CLASSIFIED
        assert new_sov_file_shadow.is_required_shadow_processing is False
        assert old_sov_file_shadow is not None
        assert old_sov_file_shadow.is_deleted is True
        assert old_sov_file_shadow.is_required_shadow_processing is False
        assert send_event_mock.called
        assert shadow_submission.is_processing


def test_get_upload_url(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_report_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled",
        return_value=True,
    )

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()
    db.session.commit()

    response, status = get_upload_url({"file_name": "test.csv", "report_id": str(report.id), "client_file_type": "SOV"})
    assert "id" in response

    file = File.query.filter_by(external_identifier=response["id"]).one_or_none()
    assert file.submission_id is None
    assert file.client_file_type == "SOV"


def test_delete_file(app_context, mock_current_user):
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()

    file = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
    )
    child_file = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        parent_file_id=file.id,
    )
    enhanced_file = enhanced_file_fixture(
        file_id=file.id, enhancement_type=FileEnhancementType.COLOR_HIGHLIGHT, s3_key="test"
    )

    db.session.commit()
    delete_file(submission.id, file.id)
    assert File.query.all() == []


def test_delete_file_with_replaced_by_file_ids(app_context, mocker, mock_current_user):
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()

    file = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        replaced_by_file_ids=[uuid.uuid4()],
    )

    db.session.commit()
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user",
        return_value=False,
    )
    with pytest.raises(BadRequest):
        delete_file(submission.id, file.id)

    file.replaced_by_file_ids = None
    child_file = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        parent_file_id=file.id,
        replaced_by_file_ids=[uuid.uuid4()],
    )
    db.session.commit()
    with pytest.raises(BadRequest):
        delete_file(submission.id, file.id)

    child_file.replaced_by_file_ids = None
    delete_file(submission.id, file.id)
    assert File.query.all() == []


def test_delete_file_that_replaces_another(app_context, mock_current_user):
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture()

    replacing_file_1 = file_fixture(
        id=uuid4(),
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
    )
    replacing_file_2 = file_fixture(
        id=uuid4(),
        submission_id=submission.id,
        name="file2",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
    )
    replaced_file = file_fixture(
        id=uuid4(),
        submission_id=submission.id,
        name="file2",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        replaced_by_file_ids=[replacing_file_1.id, replacing_file_2.id],
        processing_state=FileProcessingState.REPLACED,
    )
    db.session.commit()

    delete_file(submission.id, replacing_file_1.id)

    file = File.query.filter(File.id == replaced_file.id).first()
    assert file.processing_state == FileProcessingState.REPLACED
    assert file.replaced_by_file_ids == [replacing_file_2.id]

    delete_file(submission.id, replacing_file_2.id)
    file = File.query.filter(File.id == replaced_file.id).first()
    assert file.processing_state == FileProcessingState.IGNORED
    assert file.replaced_by_file_ids is None


def test_delete_file_for_completed_submission(app_context, mocker, mock_current_user):
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(processing_state=SubmissionProcessingState.COMPLETED)

    file_without_data = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
    )

    file_with_data = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.COMPLETED,
    )

    db.session.commit()
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user",
        return_value=False,
    )
    delete_file(submission.id, file_without_data.id)

    with pytest.raises(BadRequest):
        delete_file(submission.id, file_with_data.id)

    assert len(File.query.all()) == 1


def test_delete_file_with_user_shadow(app_context, mocker, mock_current_user):
    mocker.patch("flask.request", MagicMock(path="/v3/reports/1234/test_submission_copy"))
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED, is_verified=True
    )

    file_without_data = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        s3_key="file1",
        checksum="file1",
    )

    file_with_data = file_fixture(
        submission_id=submission.id,
        name="file2",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.COMPLETED,
        s3_key="file2",
        checksum="file2",
    )

    file_with_data_2 = file_fixture(
        submission_id=submission.id,
        name="file3",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.COMPLETED,
        s3_key="file3",
        checksum="file3",
    )

    child_file = file_fixture(
        submission_id=submission.id,
        name="file4",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.COMPLETED,
        parent_file_id=file_with_data_2.id,
        s3_key="file4",
        checksum="file4",
    )

    db.session.commit()
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user",
        return_value=True,
    )
    delete_file(submission.id, file_with_data.id)

    file_with_data = File.query.filter_by(id=file_with_data.id).first()
    assert file_with_data.is_required_shadow_processing
    assert file_with_data.user_shadow_state == UserShadowFileState.DELETED

    report = ReportV2.query.filter_by(id=report.id).first()
    assert report.shadow_type == ReportShadowType.HAS_ACTIVE_SHADOW
    assert report.is_user_waiting_for_shadow

    report_shadow_dependency = ReportShadowDependency.query.filter(
        ReportShadowDependency.report_id == report.id
    ).first()

    shadow_submission = Submission.query.filter_by(report_id=report_shadow_dependency.shadow_report_id).first()
    assert len(shadow_submission.files) == 3

    delete_file(submission.id, file_with_data_2.id)

    submission_files = File.query.filter_by(submission_id=submission.id).all()
    for f in submission_files:
        assert f.is_required_shadow_processing == (f.id != file_without_data.id)
        assert f.user_shadow_state == (UserShadowFileState.DELETED if f.id != file_without_data.id else None)

    shadow_submission = Submission.query.filter_by(report_id=report_shadow_dependency.shadow_report_id).first()
    assert len(shadow_submission.files) == 1


def test_delete_external_file(app_context, mock_current_user):
    mock_current_user.is_support = True
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(processing_state=SubmissionProcessingState.COMPLETED)

    external_file = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        is_internal=False,
    )

    db.session.commit()

    with pytest.raises(BadRequest):
        delete_file(submission.id, external_file.id)

    mock_current_user.is_cs_manager_or_internal_machine_user = True
    delete_file(submission.id, external_file.id)


def test_delete_internal_file(app_context, mock_current_user):
    mock_current_user.is_tier_2_or_internal_machine_user = False
    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(processing_state=SubmissionProcessingState.COMPLETED)

    internal_file = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        is_internal=True,
    )

    db.session.commit()

    with pytest.raises(BadRequest):
        delete_file(submission.id, internal_file.id)

    mock_current_user.is_tier_2_or_internal_machine_user = True
    delete_file(submission.id, internal_file.id)


def test_delete_file_for_submission_with_shadow(app_context, mocker, mock_current_user):
    organization_fixture()
    user_fixture()
    shadowed_report = report_fixture(shadow_type=ReportShadowType.HAS_ACTIVE_SHADOW)
    shadow_report = report_fixture(
        shadow_type=ReportShadowType.IS_ACTIVE_SHADOW,
    )
    report_shadow_dependency_fixture(report_id=shadowed_report.id, shadow_report_id=shadow_report.id)
    _, submission = report_and_submission_fixture()
    submission.report = shadowed_report

    file = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
    )
    db.session.commit()
    mocker.patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled_for_request_user",
        return_value=False,
    )

    with pytest.raises(BadRequest):
        delete_file(submission.id, file.id)


def test_delete_rose_file(app_context, mock_current_user):
    organization_fixture(id=6)
    user_fixture(organization_id=6)
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.COMPLETED, organization_id=6, origin=Origin.API
    )

    rose_file = file_fixture(
        submission_id=submission.id,
        name="file1",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        organization_id=6,
        client_file_tags={"source_type": "Rose"},
    )

    child_rose_file = file_fixture(
        submission_id=submission.id,
        name="file2",
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        organization_id=6,
        parent_file_id=rose_file.id,
    )

    db.session.commit()

    with pytest.raises(BadRequest):
        delete_file(submission.id, rose_file.id)

    with pytest.raises(BadRequest):
        delete_file(submission.id, child_rose_file.id)


def test_download_files(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
    )

    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, sub = report_and_submission_fixture()

    file_fixture(
        submission_id=sub.id,
        name="email_body.pdf",
        file_type=FileType.EMAIL,
        classification=ClassificationDocumentType.EMAIL,
    )
    file_fixture(
        submission_id=sub.id,
        name="file2",
        file_type=FileType.HTML_DOCUMENT,
        classification=ClassificationDocumentType.HTML_DOCUMENT,
    )
    db.session.commit()

    response = find_files_to_download(sub.id)
    assert len(response) == 1


def test_get_files_for_external_consumers(app_context, mocker, monkeypatch):
    org = organization_fixture()
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
        organization_id=org.id,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    user_fixture()
    report, sub = report_and_submission_fixture()

    file_fixture(
        submission_id=sub.id,
        name="file1",
        file_type=FileType.SOV,
        s3_key="file1_s3_key",
    )
    file_fixture(
        submission_id=sub.id,
        name="file2",
        file_type=FileType.SOV,
        s3_key="file2_s3_key",
    )
    file_fixture(
        submission_id=sub.id,
        name="file3",
        file_type=FileType.SOV,
        is_internal=True,
        s3_key="file3_s3_key",
    )

    def _get_presigned_url(s3_key):
        return f"{s3_key}_presigned_url"

    monkeypatch.setattr(File, "presigned_url", property(lambda self: _get_presigned_url(self.s3_key)))
    db.session.commit()

    response, status_code = get_files_for_external_consumers(report.id)
    assert status_code == 200
    assert len(response) == 2
    assert response[0]["name"] == "file1"
    assert response[0]["s3_key"] == "file1_s3_key"
    assert response[0]["presigned_url"] == "file1_s3_key_presigned_url"
    assert response[1]["name"] == "file2"
    assert response[1]["s3_key"] == "file2_s3_key"
    assert response[1]["presigned_url"] == "file2_s3_key_presigned_url"


def test_get_insights_document_types(app_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        is_kalepa=False,
        id=1,
        email="<EMAIL>",
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    org = organization_fixture()
    user_fixture()

    custom_file_type_id = uuid4()

    custom_file_type_fixture(organization_id=org.id, file_type_name="CUSTOM_FILE_TYPE", id=custom_file_type_id)
    db.session.commit()

    response = get_insights_document_types(org.id)
    model_list = response.json
    assert len(model_list) > 1
    assert "Custom - CUSTOM_FILE_TYPE" in model_list


@with_feature_flag(FeatureType.CUSTOMIZABLE_CLASSIFIERS_V2)
def test_get_insights_document_types_new_classifiers(app_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        is_kalepa=False,
        id=1,
        email="<EMAIL>",
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    org = organization_fixture()
    user_fixture()

    custom_file_type_id = uuid4()

    custom_file_type_fixture(organization_id=org.id, file_type_name="CUSTOM_FILE_TYPE", id=custom_file_type_id)
    db.session.commit()

    response = get_insights_document_types(org.id)
    model_list = response.json
    assert "Custom - CUSTOM_FILE_TYPE" in model_list
    assert all(ft.value in model_list for ft in InsightsDocumentType.file_types())
    assert all(ft.value in model_list for ft in InsightsDocumentType.third_party_documents())


def test_split_pdf_file(app_context, request_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        id=1,
        is_kalepa_internal=True,
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    organization_fixture()
    user_fixture()
    report, submission = report_and_submission_fixture(
        processing_state=SubmissionProcessingState.CLASSIFYING, is_auto_processed=True
    )
    file = file_fixture(
        submission_id=submission.id, name="file_for_split.pdf", file_type=FileType.UNKNOWN, origin=Origin.EMAIL
    )
    db.session.commit()

    mocker.patch("flask.current_app.submission_s3_client.get_file_as_bytes", return_value=b"file_content")
    mocker.patch("file_processing.pdf_utils.pdf_splitter.PDFSplitter.page_count", return_value=12)
    mocker.patch("file_processing.pdf_utils.pdf_splitter.PDFSplitter.validate_page_ranges", return_value=True)
    mocker.patch(
        "file_processing.pdf_utils.pdf_splitter.PDFSplitter.split_pdf_with_gaps",
        side_effect=lambda page_ranges: {pr: f"content{i}".encode() for i, pr in enumerate(page_ranges)}
        | {(6, 11): b"additional_file"},
    )

    mocker.patch("flask.current_app.submission_s3_client.upload_file", return_value=None)

    body = {"page_ranges": [{"start": 0, "end": 2}, {"start": 3, "end": 5, "file_type": "ACORD Form"}]}

    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
    ) as send_event_mock:
        result, status = split_pdf_file(file.id, body)
        assert status == 201
        assert len(result["files"]) == 3
        assert result["files"][0]["name"] == "file_for_split_01_03.pdf"
        assert result["files"][1]["name"] == "file_for_split_ACORD Form_04_06.pdf"
        assert result["files"][2]["name"] == "file_for_split_07_12.pdf"
        assert send_event_mock.call_count == 3

    file = File.query.filter_by(id=file.id).first()
    assert file.file_type == FileType.MERGED
    assert file.processing_state == FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING


def test_get_customizable_classifiers_v2_input_types(app_context, mocker):
    mocked_user = AnonObj(
        has_submission_permission=lambda type, id: True,
        is_internal_machine_user=True,
        applicable_settings=None,
        idp=None,
        is_trusted=True,
        is_kalepa=False,
        id=1,
        email="<EMAIL>",
    )
    mocker.patch("flask_login.utils._get_user", return_value=mocked_user)

    org = organization_fixture()
    user_fixture()

    custom_file_type_id = uuid4()

    custom_file_type_fixture(organization_id=org.id, file_type_name="CUSTOM_FILE_TYPE", id=custom_file_type_id)
    db.session.commit()

    response = get_customizable_classifiers_v2_input_types(org.id)
    response_data = response.json

    assert "file_types" in response_data
    assert "document_types" in response_data
    assert isinstance(response_data["file_types"], list)
    assert isinstance(response_data["document_types"], list)

    assert len(response_data["file_types"]) > 0
    assert len(response_data["document_types"]) > 0

    assert any(ft.value in response_data["file_types"] for ft in InsightsDocumentType.file_types())
    assert any(dt.value in response_data["document_types"] for dt in InsightsDocumentType.third_party_documents())

    assert response.status_code == HTTPStatus.OK
