from datetime import datetime, timedelta, timezone
from unittest.mock import <PERSON><PERSON><PERSON>
import uuid

from llm_common.models.llm_model import LLMModel
from static_common.enums.task_definition_codes import TaskDefinitionCodes
from static_common.enums.task_status import TaskStatus
from werkzeug.exceptions import BadRequest
import pytest
import pytz
import werkzeug

from copilot.constants import MI<PERSON><PERSON>
from copilot.models import db
from copilot.models.tasks import Task, TaskDefinition, TaskExecution
from copilot.v3.controllers.task_definitions import get_task_definition
from copilot.v3.controllers.tasks import (
    get_tasks_avg_daily_cost_for_previous_week,
    get_tasks_cost_from_the_last_day,
    handle_pending_tasks,
    init_task,
    store_task_execution,
    update_task,
)
from tests.integration.factories import (
    organization_fixture,
    report_and_submission_fixture,
    task_definition_fixture,
    task_definition_model_fixture,
    task_fixture,
    task_model_fixture,
    user_fixture,
)


def test_get_task_definition(app_context):
    task_definition = task_definition_fixture(code="test_task", has_large_state=True, timeout=300)
    task_model1 = task_model_fixture(llm_model=LLMModel.OPENAI_GPT_4O.value, timeout=150)
    task_model2 = task_model_fixture()
    task_model3 = task_model_fixture()
    task_model4 = task_model_fixture()

    # Model with validation task model and benchmark flag
    task_definition_model1 = task_definition_model_fixture(
        task_definition_id=task_definition.id,
        task_model_id=task_model1.id,
        validation_task_model_id=task_model3.id,
        is_benchmark_run=True,
    )

    # Model with organization filter
    task_definition_model2 = task_definition_model_fixture(
        task_definition_id=task_definition.id,
        task_model_id=task_model2.id,
        only_organization_ids=[1],
        is_disabled=False,
    )

    # Model with organization exclusion
    task_definition_model3 = task_definition_model_fixture(
        task_definition_id=task_definition.id,
        task_model_id=task_model3.id,
        except_organization_ids=[1],
        is_disabled=False,
    )

    # Disabled model - should be filtered out
    task_definition_model4 = task_definition_model_fixture(
        task_definition_id=task_definition.id, task_model_id=task_model4.id, is_disabled=True
    )

    # Cost fallback model
    task_model5 = task_model_fixture()
    task_definition_model5 = task_definition_model_fixture(
        task_definition_id=task_definition.id, task_model_id=task_model5.id, is_cost_fallback=True, is_disabled=False
    )

    # Disabled cost fallback model
    task_model6 = task_model_fixture()
    task_definition_model6 = task_definition_model_fixture(
        task_definition_id=task_definition.id, task_model_id=task_model6.id, is_cost_fallback=True, is_disabled=True
    )

    db.session.commit()

    # Test without organization filter and without use_cost_fallback (default = False)
    response = get_task_definition("test_task")
    assert response[1] == 200
    result = response[0]

    assert result["has_large_state"] is True
    assert result["timeout"] == 300

    # Should include all non-disabled, non-cost-fallback models (model1, model2, model3)
    assert len(result["task_definition_models"]) == 3
    model_ids = {m["id"] for m in result["task_definition_models"]}
    assert str(task_definition_model1.id) in model_ids
    assert str(task_definition_model2.id) in model_ids
    assert str(task_definition_model3.id) in model_ids
    assert str(task_definition_model5.id) not in model_ids  # Excluded: cost fallback

    # Verify model1 flags
    model1 = next(m for m in result["task_definition_models"] if m["id"] == str(task_definition_model1.id))
    assert model1["is_benchmark_run"] is True
    assert model1["is_disabled"] is False
    assert model1["task_model"]["timeout"] == 150

    # Verify disabled model is not included
    assert not any(m["id"] == str(task_definition_model4.id) for m in result["task_definition_models"])
    assert not any(m["id"] == str(task_definition_model6.id) for m in result["task_definition_models"])

    # Test with organization filter and default use_cost_fallback=False
    response = get_task_definition("test_task", organization_id=1)
    assert response[1] == 200
    result = response[0]

    # Check has_large_state is included in the response
    assert result["has_large_state"] is True

    # Should only include model1, model2 (model3 excludes org 1, model4 disabled, model5 cost fallback, model6 disabled cost fallback)
    assert len(result["task_definition_models"]) == 2
    model_ids = {m["id"] for m in result["task_definition_models"]}
    assert str(task_definition_model1.id) in model_ids
    assert str(task_definition_model2.id) in model_ids

    # Test with use_cost_fallback = True
    response = get_task_definition("test_task", use_cost_fallback=True)
    assert response[1] == 200
    result = response[0]

    # Should include only the cost fallback that is not disabled
    assert len(result["task_definition_models"]) == 1
    model_ids = {m["id"] for m in result["task_definition_models"]}
    assert str(task_definition_model5.id) in model_ids  # Included: enabled cost fallback

    # Test with organization filter and use_cost_fallback = True
    response = get_task_definition("test_task", organization_id=1, use_cost_fallback=True)
    assert response[1] == 200
    result = response[0]

    # Should include only the cost fallback that is not disabled
    assert len(result["task_definition_models"]) == 1
    model_ids = {m["id"] for m in result["task_definition_models"]}
    assert str(task_definition_model5.id) in model_ids  # Included: enabled cost fallback


def test_404_init_task(app_context):
    test_task_code = TaskDefinitionCodes.TEST
    task_definition = task_definition_fixture(code=test_task_code)
    task_model = task_model_fixture()
    task_definition_model = task_definition_model_fixture(
        task_definition_id=task_definition.id, task_model_id=task_model.id
    )
    db.session.commit()

    body = {
        "task_definition_id": str(task_definition.id),
        "submission_id": uuid.uuid4(),
        "organization_id": 1,
        "file_id": None,
    }
    with pytest.raises(werkzeug.exceptions.NotFound, match=r"Task related data not found"):
        init_task(body)


def test_init_task(app_context):
    test_task_code = TaskDefinitionCodes.TEST
    task_definition = task_definition_fixture(code=test_task_code)
    task_model = task_model_fixture()
    task_definition_model = task_definition_model_fixture(
        task_definition_id=task_definition.id, task_model_id=task_model.id
    )

    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    db.session.commit()
    business_id = uuid.uuid4()

    body = {
        "task_definition_id": str(task_definition.id),
        "submission_id": str(submission.id),
        "organization_id": str(submission.organization_id),
        "file_id": None,
        "business_id": str(business_id),
    }
    response = init_task(body)
    assert response[1] == 201
    response_dict = response[0]

    task = Task.query.get(response_dict["id"])
    assert task.task_definition_id == task_definition.id
    assert task.submission_id == submission.id
    assert task.file_id is None
    assert task.business_id == business_id
    assert task.status == "PENDING"
    assert task.input == {"test_field": "test_value"}


def test_store_task_execution(app_context, mocker):
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    task_definition = task_definition_fixture(code="test_task")
    task_model = task_model_fixture(use_task_output_processor=True, llm_model=LLMModel.OPENAI_GPT_4O.value)
    task = task_fixture(task_definition_id=task_definition.id, submission_id=submission.id)
    task_definition_model = task_definition_model_fixture(
        task_definition_id=task_definition.id, task_model_id=task_model.id
    )

    mocker.patch(
        "copilot.v3.controllers.tasks.LLMModel",
        return_value=MagicMock(cost_per_1M_input_tokens=10, cost_per_1M_output_tokens=20),
    )

    db.session.commit()

    task_body = {
        "task_id": str(task.id),
        "task_model_id": str(task_model.id),
        "output": {"test_field": "test_output"},
        "used_input_tokens": 1,
        "used_output_tokens": 2,
        "processing_time": 4,
        "confidence": 100,
        "output_evaluation": {"is_valid_output": True},
    }

    response = store_task_execution(task_id=str(task.id), body=task_body)
    assert response[1] == 201
    response_dict = response[0]

    task_execution = TaskExecution.query.get(response_dict["id"])
    assert task_execution.task_id == task.id
    assert task_execution.output == {"test_field": "test_output"}
    assert task_execution.used_input_tokens == 1
    assert task_execution.used_output_tokens == 2
    assert task_execution.processing_cost == 50 / MILLION
    assert task_execution.processing_time == 4
    assert task_execution.confidence == 100
    assert task_execution.processed_output == {"test_field": "test_output"}


def test_update_task_failed(app_context):
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    task_definition = task_definition_fixture(code="test_task")
    task = task_fixture(
        task_definition_id=task_definition.id, submission_id=submission.id, input={"test_field": "test_value"}
    )
    db.session.commit()

    update_task(task_id=task.id, body={"status": "FAILED"})

    task = Task.query.get(task.id)
    assert task.status == "FAILED"
    assert task.input == {"test_field": "test_value"}
    assert task.output is None


def test_update_task_completed(app_context):
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    test_task_code = TaskDefinitionCodes.TEST
    task_definition = task_definition_fixture(code=test_task_code)
    task = task_fixture(
        task_definition_id=task_definition.id,
        submission_id=submission.id,
        input={"test_field": "test_value"},
        created_at=datetime.now(timezone.utc) - timedelta(seconds=5),
        is_valid_input=True,
    )
    db.session.commit()

    task_body = {"status": "COMPLETED", "output": {"test_field": "test_output"}, "is_valid_output": True}
    update_task(task_id=task.id, body=task_body)

    task = Task.query.get(task.id)
    assert task.status == "COMPLETED"
    assert task.input == {"test_field": "test_value"}
    assert task.output is None
    assert task.processed_output == {"test_field": "test_output"}
    assert task.processing_time == 5


def test_update_task_completed_with_processed_output(app_context):
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    test_task_code = TaskDefinitionCodes.TEST
    task_definition = task_definition_fixture(code=test_task_code)
    task = task_fixture(
        task_definition_id=task_definition.id,
        submission_id=submission.id,
        input={"test_field": "test_value"},
        created_at=datetime.now(timezone.utc) - timedelta(seconds=5),
        is_valid_input=True,
    )
    db.session.commit()

    task_body = {"status": "COMPLETED", "processed_output": {"test_field": "test_output"}, "is_valid_output": True}
    update_task(task_id=task.id, body=task_body)

    task = Task.query.get(task.id)
    assert task.status == "COMPLETED"
    assert task.input == {"test_field": "test_value"}
    assert task.processed_output == {"test_field": "test_output"}
    assert task.processing_time == 5


def test_update_task_from_failed_to_completed(app_context):
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    test_task_code = TaskDefinitionCodes.TEST
    task_definition = task_definition_fixture(code=test_task_code)
    task = task_fixture(
        task_definition_id=task_definition.id,
        submission_id=submission.id,
        input={"test_field": "test_value"},
        status=TaskStatus.FAILED,
    )
    db.session.commit()

    task_body = {"status": "COMPLETED", "output": {"test_field": "test_output"}}
    with pytest.raises(BadRequest, match=f"Status of a completed task cannot be updated"):
        update_task(task_id=task.id, body=task_body)


def test_handle_pending_tasks(app_context):
    organization_fixture()
    user = user_fixture()
    report, submission = report_and_submission_fixture()

    task_definition = task_definition_fixture(code="test_task")
    task1 = task_fixture(
        task_definition_id=task_definition.id,
        submission_id=submission.id,
        input={"test_field": "test_value"},
        status=TaskStatus.PENDING,
        created_at=datetime.utcnow() - timedelta(minutes=4),
    )
    task2 = task_fixture(
        task_definition_id=task_definition.id,
        submission_id=submission.id,
        input={"test_field": "test_value"},
        status=TaskStatus.PENDING,
        created_at=datetime.utcnow() - timedelta(minutes=6),
    )
    db.session.commit()

    handle_pending_tasks()

    task1 = Task.query.get(task1.id)
    assert task1.status == TaskStatus.PENDING
    assert task1.is_valid_output is None

    task2 = Task.query.get(task2.id)
    assert task2.status == TaskStatus.FAILED
    assert task2.is_valid_output is False

    task_definition.timeout = 150
    db.session.commit()

    handle_pending_tasks()
    task1 = Task.query.get(task1.id)
    assert task1.status == TaskStatus.FAILED
    assert task1.is_valid_output is False


def test_get_tasks_cost_from_the_last_day(app_context):
    # Create org, task definition with group, and tasks with processing cost
    organization_id = 1
    organization_fixture(id=organization_id)

    # Create another organization for filtering tests
    other_org_id = 2
    organization_fixture(id=other_org_id)

    # Create task definitions with different groups
    group_a = "group_a"
    group_b = "group_b"
    task_def_a = task_definition_fixture(code="task_a", group=group_a)
    task_def_b = task_definition_fixture(code="task_b", group=group_b)

    # Create tasks within the last 24 hours
    task_a1 = task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=organization_id,
        processing_cost=10.0,
        created_at=datetime.now(timezone.utc) - timedelta(hours=12),
    )

    task_a2 = task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=organization_id,
        processing_cost=15.0,
        created_at=datetime.now(timezone.utc) - timedelta(hours=6),
    )

    task_b1 = task_fixture(
        task_definition_id=task_def_b.id,
        organization_id=organization_id,
        processing_cost=5.0,
        created_at=datetime.now(timezone.utc) - timedelta(hours=18),
    )

    # Create a task for different organization
    task_other_org = task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=other_org_id,
        processing_cost=20.0,
        created_at=datetime.now(timezone.utc) - timedelta(hours=10),
    )

    # Create a task that's older than 24 hours (should be excluded)
    old_task = task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=organization_id,
        processing_cost=100.0,
        created_at=datetime.now(timezone.utc) - timedelta(hours=25),
    )

    # Create a test run task (should be excluded from normal results)
    test_run_task = task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=organization_id,
        processing_cost=50.0,
        created_at=datetime.now(timezone.utc) - timedelta(hours=8),
        is_test_run=True,
    )

    db.session.commit()

    # Test getting all costs without filtering
    response = get_tasks_cost_from_the_last_day()
    assert response[1] == 200
    assert response[0]["total_cost"] == 10.0 + 15.0 + 5.0 + 20.0  # All tasks from last 24h

    # Test filtering by organization
    response = get_tasks_cost_from_the_last_day(organization_id=organization_id)
    assert response[1] == 200
    assert response[0]["total_cost"] == 10.0 + 15.0 + 5.0  # Only org 1 tasks

    # Test filtering by task definition group
    response = get_tasks_cost_from_the_last_day(task_definition_group=group_a)
    assert response[1] == 200
    assert response[0]["total_cost"] == 10.0 + 15.0 + 20.0  # Only group_a tasks

    # Test filtering by both organization and group
    response = get_tasks_cost_from_the_last_day(organization_id=organization_id, task_definition_group=group_a)
    assert response[1] == 200
    assert response[0]["total_cost"] == 10.0 + 15.0  # Only org 1 and group_a tasks

    # Test getting test run tasks
    response = get_tasks_cost_from_the_last_day(for_test_run=True)
    assert response[1] == 200
    assert response[0]["total_cost"] == 50.0  # Only test run tasks


def test_get_tasks_avg_daily_cost_for_previous_week(app_context, mocker):
    est = pytz.timezone("US/Eastern")
    # Mock datetime.now to have a fixed reference point
    fixed_now = datetime(2023, 7, 11, 12, 0, 0, tzinfo=est)  # Tuesday, July 11, 2023
    mocker.patch("copilot.v3.controllers.tasks.datetime", wraps=datetime)
    mocker.patch("copilot.v3.controllers.tasks.datetime.now", return_value=fixed_now)

    # Create org and task definition
    organization_id = 1
    organization_fixture(id=organization_id)
    other_org_id = 2
    organization_fixture(id=other_org_id)

    # Create task definitions with different groups
    group_a = "group_a"
    group_b = "group_b"
    task_def_a = task_definition_fixture(code="task_a", group=group_a)
    task_def_b = task_definition_fixture(code="task_b", group=group_b)

    # Create tasks for each day of the previous week (starting Monday)
    # The dates are: 2023-07-03 (Monday) to 2023-07-09 (Sunday)

    # Monday - organization 1, group A - $100
    task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=organization_id,
        processing_cost=100.0,
        created_at=datetime(2023, 7, 3, 12, 0, 0, tzinfo=est),
    )

    # Tuesday - organization 1, group A - $200
    task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=organization_id,
        processing_cost=200.0,
        created_at=datetime(2023, 7, 4, 12, 0, 0, tzinfo=est),
    )

    # Wednesday - organization 1, group B - $50
    task_fixture(
        task_definition_id=task_def_b.id,
        organization_id=organization_id,
        processing_cost=50.0,
        created_at=datetime(2023, 7, 5, 12, 0, 0, tzinfo=est),
    )

    # Thursday - organization 2, group A - $150
    task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=other_org_id,
        processing_cost=150.0,
        created_at=datetime(2023, 7, 6, 12, 0, 0, tzinfo=est),
    )

    # Friday - organization 1, group A - $80
    task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=organization_id,
        processing_cost=80.0,
        created_at=datetime(2023, 7, 7, 12, 0, 0, tzinfo=est),
    )

    # Saturday - organization 1, group B - $30
    task_fixture(
        task_definition_id=task_def_b.id,
        organization_id=organization_id,
        processing_cost=30.0,
        created_at=datetime(2023, 7, 8, 12, 0, 0, tzinfo=est),
    )

    # Sunday - organization 2, group B - $40
    task_fixture(
        task_definition_id=task_def_b.id,
        organization_id=other_org_id,
        processing_cost=40.0,
        created_at=datetime(2023, 7, 9, 12, 0, 0, tzinfo=est),
    )

    # Too old - should be excluded
    task_fixture(
        task_definition_id=task_def_a.id,
        organization_id=organization_id,
        processing_cost=500.0,
        created_at=datetime(2023, 7, 2, 12, 0, 0, tzinfo=est),
    )

    # Current day - should be excluded
    task_fixture(
        task_definition_id=task_def_a.id, organization_id=organization_id, processing_cost=300.0, created_at=fixed_now
    )

    db.session.commit()

    # Test getting average cost without filtering
    no_filter_response = get_tasks_avg_daily_cost_for_previous_week()
    assert no_filter_response[1] == 200
    no_filter_avg = no_filter_response[0]["avg_daily_cost"]

    # Test filtering by organization
    org_filter_response = get_tasks_avg_daily_cost_for_previous_week(organization_id=organization_id)
    assert org_filter_response[1] == 200
    org_filter_avg = org_filter_response[0]["avg_daily_cost"]

    # Test filtering by task definition group
    group_filter_response = get_tasks_avg_daily_cost_for_previous_week(task_definition_group=group_a)
    assert group_filter_response[1] == 200
    group_filter_avg = group_filter_response[0]["avg_daily_cost"]

    # Test filtering by both organization and group
    both_filter_response = get_tasks_avg_daily_cost_for_previous_week(
        organization_id=organization_id, task_definition_group=group_a
    )
    assert both_filter_response[1] == 200
    both_filter_avg = both_filter_response[0]["avg_daily_cost"]

    # Expected values (with zeros padding to 5 entries if needed):
    # No filter: top 5 values are $200, $150, $100, $80, $50 = $116
    # Org filter: top 5 values are $200, $100, $80, $50, $30 = $92
    # Group filter: top values are $200, $150, $100, $80 = $132
    # Both filters: top values are $200, $100, $80 = $127
    assert round(no_filter_avg) == 116
    assert round(org_filter_avg) == 92
    assert round(group_filter_avg) == 132
    assert round(both_filter_avg) == 127
