from http import HTTPStatus

from static_common.enums.task_model_execution_type import TaskModelExecutionType
from static_common.enums.task_model_processing_type import TaskModelProcessingType
from static_common.enums.task_model_type import TaskModelType
from werkzeug.exceptions import NotFound
import pytest

from copilot.models import db
from copilot.models.tasks import TaskModel
from copilot.schemas.tasks import TaskModelSchema
from copilot.v3.controllers.task_models import (
    create_task_model,
    delete_task_model,
    get_task_model,
    update_task_model,
)
from tests.integration.factories import task_model_fixture


@pytest.fixture
def body1():
    return {
        "name": "Example Task",
        "type": TaskModelType.COMBINED,
        "execution_config": {"param1": "value1", "param2": 10},
        "execution_type": TaskModelExecutionType.LAMBDA,
        "processing_type": TaskModelProcessingType.CONSOLIDATION,
        "llm_model": "gpt-4",
        "use_task_output_processor": True,
        "timeout": 300,
    }


@pytest.fixture
def updated_body():
    return {
        "name": "Example Task",
        "type": TaskModelType.COMBINED,
        "execution_config": {"param1": "value1", "param2": 10},
        "execution_type": TaskModelExecutionType.API,
        "processing_type": TaskModelProcessingType.CONSOLIDATION,
        "llm_model": "gpt-4",
        "use_task_output_processor": True,
        "timeout": 250,
    }


def test_task_model_simple_add(app_context, body1) -> None:
    response, status = create_task_model(body1)
    assert status == HTTPStatus.CREATED
    assert TaskModel.query.count() == 1


def test_task_model_multiple_add(app_context, body1) -> None:
    for i in range(4):
        response, status = create_task_model(body1)
        assert status == HTTPStatus.CREATED
        assert TaskModel.query.count() == i + 1


def test_task_model_delete(app_context, body1) -> None:
    task_model = task_model_fixture()
    db.session.commit()
    task_model_id = task_model.id

    assert TaskModel.query.count() == 1
    status = delete_task_model(str(task_model_id))
    assert status == HTTPStatus.NO_CONTENT

    assert TaskModel.query.count() == 0


def test_task_model_delete_not_found(app_context) -> None:
    with pytest.raises(NotFound):
        delete_task_model("ac651b8d-a2c5-435d-8b7f-7ad4443283ac")


def test_task_model_simple_get(app_context, body1) -> None:
    task_model = task_model_fixture()
    db.session.commit()
    assert TaskModel.query.count() == 1

    response, status = get_task_model(str(task_model.id))
    assert status == HTTPStatus.OK
    assert response["execution_type"] == "LAMBDA"


def test_task_model_update(app_context, body1, updated_body) -> None:
    task_model = TaskModelSchema().load(body1, session=db.session)
    db.session.add(task_model)
    db.session.commit()
    record_id = task_model.id
    assert TaskModel.query.count() == 1
    response, status = update_task_model(str(record_id), updated_body)
    assert status == HTTPStatus.OK
    assert TaskModel.query.count() == 1
    assert response["execution_type"] == "API"
