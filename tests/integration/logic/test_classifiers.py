from unittest.mock import patch
import uuid

from flask import abort
from werkzeug.exceptions import Conflict
import pytest

from copilot.logic.classifiers.classifiers import (
    CLASSIFIERS_CONSOLIDATION_TASK_MODEL_ID,
    create_task_definitions,
    get_task_definition_code,
)
from copilot.models import ClassifierConfig, ClassifierVersion, db
from copilot.models.customizable_classifiers import (
    ClassifierVersionTaskDefinition,
    PhrasesConfigVersion,
)
from copilot.models.tasks import TaskDefinition, TaskDefinitionModel
from tests.integration.factories import (
    classifier_config_fixture,
    classifier_to_config_version_fixture,
    classifier_version_fixture,
    customizable_classifier_v2_fixture,
    phrases_config_version_fixture,
    task_model_fixture,
)


@pytest.fixture
def setup_classifier_with_configs(app_context):
    # Create a customizable classifier
    classifier = customizable_classifier_v2_fixture(
        name="Test Classifier",
        fact_subtype_id="test_subtype",
        is_internal=True,
    )

    # Create a version for the classifier
    classifier_version = classifier_version_fixture(
        classifier_id=classifier.id,
        name="Test Version",
        classifier_description="Test description",
        is_active=True,
    )

    # Create configs for the classifier
    config1 = classifier_config_fixture(
        input_types=["document"],
    )

    config2 = classifier_config_fixture(
        input_types=["text"],
    )

    # Create active config versions
    config1_version = phrases_config_version_fixture(
        classifier_config_id=config1.id,
        input_processing_type="OCR_TEXT",
    )

    config2_version = phrases_config_version_fixture(
        classifier_config_id=config2.id,
        input_processing_type="OCR_TEXT",
    )

    # Link phrases config to version 1
    classifier_to_config_version_fixture(
        classifier_version_id=classifier_version.id,
        classifier_config_id=config1.id,
        classifier_config_version_id=config1_version.id,
    )

    classifier_to_config_version_fixture(
        classifier_version_id=classifier_version.id,
        classifier_config_id=config2.id,
        classifier_config_version_id=config2_version.id,
    )

    task_model_fixture(id=CLASSIFIERS_CONSOLIDATION_TASK_MODEL_ID)

    db.session.commit()

    return {
        "classifier": classifier,
        "classifier_version": classifier_version,
        "configs": [config1, config2],
        "config_versions": [config1_version, config2_version],
    }


def test_create_task_definitions_success(app_context, setup_classifier_with_configs):
    # Arrange
    classifier_version = setup_classifier_with_configs["classifier_version"]
    configs = setup_classifier_with_configs["configs"]

    # Act
    create_task_definitions(classifier_version)
    db.session.commit()

    # Assert
    # Check that task definitions were created
    task_definitions = TaskDefinition.query.filter(
        TaskDefinition.code.like(f"CUSTOMIZABLE_CLASSIFIER_{classifier_version.classifier_id}_%")
    ).all()

    assert len(task_definitions) == 2

    for task_def in task_definitions:
        # Check that the naming convention is followed
        assert task_def.name.startswith("CC - Test Classifier -")
        assert task_def.group == "INTERNAL_CUSTOM_CLASSIFIERS"
        assert task_def.llm_task_description is None

        # Check that ClassifierVersionTaskDefinition records were created
        cvtd = ClassifierVersionTaskDefinition.query.filter_by(task_definition_id=task_def.id).first()
        assert cvtd is not None
        assert cvtd.classifier_version_id == classifier_version.id
        assert cvtd.classifier_config_id in [config.id for config in configs]

        # Check that TaskDefinitionModel records were created
        tdms = TaskDefinitionModel.query.filter_by(task_definition_id=task_def.id).all()
        assert len(tdms) == 1
        assert tdms[0].order == 0
        assert tdms[0].is_always_run is False
        assert tdms[0].is_consolidation_run is False


def test_create_task_definitions_with_multiple_config_versions(setup_classifier_with_configs):
    # Arrange
    classifier_version = setup_classifier_with_configs["classifier_version"]
    config = setup_classifier_with_configs["configs"][0]
    config_version = setup_classifier_with_configs["config_versions"][0]

    # Add another config version
    config_version2 = phrases_config_version_fixture(
        classifier_config_id=config.id,
        input_processing_type="RAW_TEXT",
    )
    classifier_to_config_version_fixture(
        classifier_version_id=classifier_version.id,
        classifier_config_id=config.id,
        classifier_config_version_id=config_version2.id,
    )

    db.session.commit()

    # Act
    create_task_definitions(classifier_version)
    db.session.commit()

    # Assert
    # Check that consolidation task definition model was created
    task_definition = TaskDefinition.query.filter(
        TaskDefinition.code == get_task_definition_code(classifier_version, config)
    ).first()

    task_definition_models = TaskDefinitionModel.query.filter_by(task_definition_id=task_definition.id).all()

    # Should have 3 models: 2 for config versions + 1 consolidation
    assert len(task_definition_models) == 3

    # Check that one model is marked as consolidation
    consolidation_models = [m for m in task_definition_models if m.is_consolidation_run]
    assert len(consolidation_models) == 1
    assert consolidation_models[0].order == 1


def test_create_task_definitions_existing_tasks(setup_classifier_with_configs):
    # Arrange
    classifier_version = setup_classifier_with_configs["classifier_version"]
    config = setup_classifier_with_configs["configs"][0]

    # Create task definition first
    task_definition_code = get_task_definition_code(classifier_version, config)
    task_definition = TaskDefinition(
        code=task_definition_code,
        name=f"Existing Task Definition",
    )
    db.session.add(task_definition)
    db.session.commit()

    # Act/Assert
    with pytest.raises(Conflict) as exc_info:
        create_task_definitions(classifier_version)

    assert f"Task definition with code {task_definition_code} already exists" in str(exc_info.value)


def test_create_task_definitions_skip_existing_links(setup_classifier_with_configs):
    # Arrange
    classifier_version = setup_classifier_with_configs["classifier_version"]
    config = setup_classifier_with_configs["configs"][0]

    # Create task definition and link
    task_definition_code = get_task_definition_code(classifier_version, config)
    task_definition = TaskDefinition(
        code=task_definition_code,
        name=f"Existing Task Definition",
    )
    db.session.add(task_definition)
    db.session.flush()

    # Create link
    cvtd = ClassifierVersionTaskDefinition(
        classifier_version_id=classifier_version.id,
        classifier_config_id=config.id,
        task_definition_id=task_definition.id,
    )
    db.session.add(cvtd)
    db.session.commit()

    # Act - should not raise error for config that already has TaskDefinition
    create_task_definitions(classifier_version)
    db.session.commit()

    # Assert
    # Only one task definition should be created (for the second config)
    task_definitions = TaskDefinition.query.filter(
        TaskDefinition.code.like(f"CUSTOMIZABLE_CLASSIFIER_{classifier_version.classifier_id}_%")
    ).all()

    assert len(task_definitions) == 2  # Original + new one


def test_create_task_definitions_with_task_model_id(setup_classifier_with_configs):
    # Arrange
    classifier_version = setup_classifier_with_configs["classifier_version"]
    config_version = setup_classifier_with_configs["config_versions"][0]

    # Create a task model and set its ID on the config version
    task_model = task_model_fixture(name="Existing Task Model")
    config_version.task_model_id = task_model.id
    db.session.commit()

    # Act
    create_task_definitions(classifier_version)
    db.session.commit()

    # Assert
    # Check that the task definition model uses the existing task model ID
    task_definition = TaskDefinition.query.filter(
        TaskDefinition.code == get_task_definition_code(classifier_version, setup_classifier_with_configs["configs"][0])
    ).first()

    task_definition_model = TaskDefinitionModel.query.filter_by(task_definition_id=task_definition.id).first()
    assert task_definition_model.task_model_id == task_model.id
