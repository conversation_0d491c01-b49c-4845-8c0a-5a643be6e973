from copilot.logic.agencies.api_agent_assigner import ApiAgentAssigner
from copilot.logic.agencies.base_agent_assigner import AgentInfo
from copilot.models import db
from tests.integration.factories import (
    broker_fixture,
    brokerage_fixture,
    organization_fixture,
    report_and_submission_fixture,
    user_fixture,
)


def test_assign_agent(app_context):
    organization_fixture()
    user_fixture()

    brokerage_1 = brokerage_fixture(name="1", organization_id=1)
    brokerage_2 = brokerage_fixture(name="2", organization_id=1)

    broker_1 = broker_fixture(
        name="b 1",
        email="<EMAIL>",
        organization_id=1,
        brokerage_id=brokerage_1.id,
        roles=["AGENT", "CORRESPONDENCE_CONTACT"],
    )
    broker_2 = broker_fixture(name="b 2", email="<EMAIL>", organization_id=1, brokerage_id=brokerage_2.id)

    report, submission = report_and_submission_fixture()
    submission.brokerage = brokerage_1
    submission.broker = broker_1
    submission.brokerage_contact = broker_1

    db.session.commit()

    ApiAgentAssigner(AgentInfo(agent_name="b 2", agency_name="2")).assign_agent(submission)

    db.session.refresh(submission)

    assert submission.brokerage.name == "2"
    assert submission.broker.name == "b 2"
    assert submission.brokerage_contact.name == "b 2"
