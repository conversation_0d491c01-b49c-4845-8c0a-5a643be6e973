from datetime import datetime, timedelta
from unittest.mock import <PERSON><PERSON><PERSON>, patch
from uuid import UUID, uuid4
import json

from dateutil.relativedelta import relativedelta
from entity_resolution_service_client_v3 import EntityNameRequest, NameScoreResponse
from sqlalchemy import null
from static_common.enums.classification import KalepaEmailClassification<PERSON>abels
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.coverage_names import CoverageName
from static_common.enums.entity import EntityInformation
from static_common.enums.fields import FieldType
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.origin import Origin
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.models.business_resolution_data import BusinessResolutionData
from static_common.models.coverages import CoverageDetails
from static_common.models.file_onboarding import (
    OnboardedFile,
    ResolvedDataField,
    ResolvedData<PERSON><PERSON>ue,
    SubmissionEntity,
)
from static_common.models.submission_level_data import SourceDetails
from static_common.schemas.business_resolution_data import BusinessResolutionDataSchema
from static_common.schemas.coverages import CoverageDetailsSchema
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
from static_common.taxonomies.industry_classification import NaicsCode
from werkzeug.exceptions import BadRequest
import pytest

from copilot.clients.feature_flags import FeatureType
from copilot.logic.pds.file_handler import FileHandler
from copilot.models import File, Loss, ReportProcessingDependency, Submission, db
from copilot.models.emails import (
    EmailClassification,
    EmailClassificationLabel,
    ReportEmailCorrespondence,
)
from copilot.models.files import LoadedProcessedFile, ProcessedFile
from copilot.models.pds_metrics import PDSStats
from copilot.models.settings import Settings
from copilot.models.submission_consolidation_process import (
    SubmissionConsolidationProcess,
)
from copilot.models.types import (
    CoverageType,
    ReportDependencyType,
    SubmissionConsolidationStatus,
)
from tests.integration.coverages.fixtures import set_up_current_user
from tests.integration.factories import (
    email_fixture,
    file_fixture,
    loss_fixture,
    organization_fixture,
    processed_file_fixture,
    report_and_submission_fixture,
    report_fixture,
    settings_fixture,
    submission_fixture,
    submission_level_extracted_data_fixture,
    user_fixture,
)
from tests.integration.utils import AnonObj

EXAMPLE_RESOLUTION_DATA = {
    "resolution_data": [
        {
            "category": "UNDEFINED",
            "entity_id": None,
            "entity_idx": 0,
            "premises_type": "UNDEFINED",
            "resolved_name": None,
            "requested_name": "Paul Jardin Of Usa Inc",
            "is_autoconfirmed": False,
            "resolved_address": None,
            "requested_address": "60 W Cochran St, Simi Valley CA 93065-6237",
        }
    ]
}


RESOLVED_RESOLUTION_DATA = {
    "resolution_data": [
        {
            "category": "UNDEFINED",
            "entity_id": "d1de151b-cace-46cc-b699-e4680c5fb126",
            "entity_idx": 0,
            "premises_type": "UNDEFINED",
            "resolved_name": None,
            "requested_name": "Paul Jardin Of Usa Inc",
            "is_autoconfirmed": False,
            "resolved_address": None,
            "requested_address": "60 W Cochran St, Simi Valley CA 93065-6237",
        }
    ]
}


@pytest.mark.parametrize(
    "submission_state,submission_auto_process,file_state,file_type,is_published,submission_end_state,going_back,classify_files_for_shells,start_file_processing",
    [
        (
            SubmissionProcessingState.NOT_STARTED,
            True,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.SOV,
            True,
            SubmissionProcessingState.CLASSIFYING,
            False,
            False,
            None,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            False,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.EMAIL,
            True,
            SubmissionProcessingState.NOT_STARTED,
            False,
            False,
            None,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            False,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.SOV,
            True,
            SubmissionProcessingState.NOT_STARTED,
            False,
            False,
            False,
        ),
        (
            SubmissionProcessingState.DATA_ONBOARDING,
            True,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.SOV,
            True,
            SubmissionProcessingState.CLASSIFYING,
            True,
            False,
            None,
        ),
        (
            SubmissionProcessingState.COMPLETED,
            True,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.EMAIL,
            True,
            SubmissionProcessingState.COMPLETED,
            False,
            False,
            None,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            True,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.SOV,
            True,
            SubmissionProcessingState.CLASSIFYING,
            False,
            True,
            None,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            False,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.UNKNOWN,
            True,
            SubmissionProcessingState.NOT_STARTED,
            False,
            True,
            None,
        ),
        (
            SubmissionProcessingState.DATA_ONBOARDING,
            True,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.LOSS_RUN,
            True,
            SubmissionProcessingState.DATA_ONBOARDING,
            False,
            True,
            None,
        ),
    ],
)
def test_handle_new_file_added(
    app_context,
    submission,
    submission_state,
    submission_auto_process,
    file_state,
    file_type,
    is_published,
    submission_end_state,
    going_back,
    classify_files_for_shells,
    start_file_processing,
):
    submission.report.owner.applicable_settings.classify_files_for_shells = classify_files_for_shells
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
    ) as publish_mock:
        submission.processing_state = submission_state
        submission.is_auto_processed = submission_auto_process
        submission.is_processing = False
        file = file_fixture(processing_state=file_state, file_type=file_type, submission_id=submission.id)
        file_handler = FileHandler()
        file_handler.handle_new_file_added(submission, file)
        assert publish_mock.called == is_published
        if is_published and start_file_processing is not None:
            publish_mock.assert_called_once_with(submission, file, start_file_processing=start_file_processing)
        assert submission.processing_state == submission_end_state
        assert submission.report.going_back == going_back
        assert submission.is_processing == (submission_end_state == SubmissionProcessingState.CLASSIFYING)


def test_handle_new_file_added_document_ingestion(app_context, submission, mocker):
    submission.report.owner.applicable_settings.classify_files_for_shells = False
    mocker.patch("copilot.models.files.File.is_document_ingestion", return_value=True)
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
    ) as publish_mock:
        submission.processing_state = SubmissionProcessingState.DATA_ONBOARDING
        submission.is_auto_processed = True
        submission.is_processing = False
        file = file_fixture(
            processing_state=FileProcessingState.NOT_CLASSIFIED, file_type=FileType.SOV, submission_id=submission.id
        )
        file_handler = FileHandler()
        file_handler.handle_new_file_added(submission, file)
        assert publish_mock.called
        publish_mock.assert_called_once_with(submission, file)
        assert submission.processing_state == SubmissionProcessingState.CLASSIFYING
        assert submission.report.going_back is False
        assert submission.is_processing is False


def test_handle_new_file_added_for_classify_files_for_shells(
    app_context,
    submission,
):
    submission.report.owner.applicable_settings.classify_files_for_shells = True
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
    ) as publish_mock:
        submission.processing_state = SubmissionProcessingState.CLASSIFYING
        submission.is_auto_processed = False
        submission.is_processing = False
        file = file_fixture(
            processing_state=FileProcessingState.NOT_CLASSIFIED, file_type=FileType.UNKNOWN, submission_id=submission.id
        )
        file_handler = FileHandler()
        file_handler.handle_new_file_added(submission, file)
        assert publish_mock.call_count == 1
        submission.is_auto_processed = True
        file_handler.handle_new_file_added(submission, file, starting_processing_submission=True)
        assert publish_mock.call_count == 1


@pytest.mark.parametrize(
    "submission_state,submission_auto_process,file_state,file_type,starting_processing_submission,should_process,should_add",
    [
        (
            SubmissionProcessingState.NOT_STARTED,
            False,
            FileProcessingState.NOT_CLASSIFIED,
            FileType.SOV,
            False,
            False,
            True,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            True,
            FileProcessingState.CLASSIFIED,
            FileType.SOV,
            False,
            True,
            False,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            False,
            FileProcessingState.CLASSIFIED,
            FileType.RAW_EMAIL,
            False,
            True,
            False,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            True,
            FileProcessingState.CLASSIFIED,
            FileType.EMAIL,
            True,
            True,
            False,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            True,
            FileProcessingState.CLASSIFICATION_FAILED,
            FileType.UNKNOWN,
            True,
            True,
            False,
        ),
    ],
)
def test_handle_new_file_added_already_classified(
    app_context,
    submission,
    submission_state,
    submission_auto_process,
    file_state,
    file_type,
    starting_processing_submission,
    should_process,
    should_add,
):
    submission.report.owner.applicable_settings.classify_files_for_shells = False
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_classified_event"
        ) as classified_event_mock,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
        ) as added_event_mock,
    ):
        submission.processing_state = submission_state
        submission.is_processing = False
        classification = ClassificationDocumentType.SOV.value if file_state == FileProcessingState.CLASSIFIED else None
        file = file_fixture(
            processing_state=file_state, file_type=file_type, submission_id=submission.id, classification=classification
        )
        submission.is_auto_processed = submission_auto_process
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle_new_file_added(submission, file, starting_processing_submission)
        assert classified_event_mock.called == should_process
        assert added_event_mock.called is should_add
        assert submission.is_processing == (
            (should_process or starting_processing_submission) and submission_auto_process
        )


def test_handle_new_file_added_when_same_org_api_dependency(app_context, submission):
    child_report = report_fixture()
    submission_fixture(report=child_report, origin=Origin.API)
    processing_dependency = ReportProcessingDependency(
        report_id=submission.report.id,
        report=submission.report,
        dependent_report_id=child_report.id,
        dependent_report=child_report,
        dependency_type=ReportDependencyType.SAME_ORG,
    )
    db.session.add(processing_dependency)
    submission.processing_state = SubmissionProcessingState.CLASSIFYING
    submission.is_auto_processed = True
    submission.is_processing = True
    file = file_fixture(
        processing_state=FileProcessingState.NOT_CLASSIFIED,
        file_type=FileType.SOV,
        submission_id=submission.id,
    )
    db.session.commit()
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
    ) as publish_mock:
        file_handler = FileHandler()
        file_handler.handle_new_file_added(submission, file)
        assert publish_mock.called is True
        assert (
            not db.session.query(ReportProcessingDependency)
            .filter(ReportProcessingDependency.report_id == submission.report.id)
            .all()
        )


def test_handle_new_file_added_when_api_dependency_and_raw_email(app_context, submission):
    child_report = report_fixture()
    submission_fixture(report=child_report, origin=Origin.API)
    processing_dependency = ReportProcessingDependency(
        report_id=submission.report.id,
        report=submission.report,
        dependent_report_id=child_report.id,
        dependent_report=child_report,
        dependency_type=ReportDependencyType.CROSS_ORG,
    )
    db.session.add(processing_dependency)
    submission.processing_state = SubmissionProcessingState.CLASSIFYING
    submission.is_auto_processed = True
    submission.is_processing = True
    submission.origin = Origin.API
    file = file_fixture(
        processing_state=FileProcessingState.NOT_CLASSIFIED,
        file_type=FileType.RAW_EMAIL,
        submission_id=submission.id,
    )
    db.session.commit()
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
    ) as publish_mock:
        file_handler = FileHandler()
        file_handler.handle_new_file_added(submission, file)
        assert publish_mock.called is True
        assert (
            db.session.query(ReportProcessingDependency)
            .filter(ReportProcessingDependency.report_id == submission.report.id)
            .first()
        ) is not None

    file_2 = file_fixture(
        processing_state=FileProcessingState.NOT_CLASSIFIED,
        file_type=FileType.SOV,
        submission_id=submission.id,
        parent_file_id=file.id,
    )
    db.session.commit()

    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
    ) as publish_mock:
        file_handler = FileHandler()
        file_handler.handle_new_file_added(submission, file_2)
        assert publish_mock.called is True
        assert (
            db.session.query(ReportProcessingDependency)
            .filter(ReportProcessingDependency.report_id == submission.report.id)
            .first()
        ) is None


def test_handle_new_file_added_when_dependency_and_internal_file_for_verified_sub(app_context, submission):
    child_report = report_fixture()
    submission_fixture(report=child_report, origin=Origin.API)
    processing_dependency = ReportProcessingDependency(
        report_id=submission.report.id,
        report=submission.report,
        dependent_report_id=child_report.id,
        dependent_report=child_report,
        dependency_type=ReportDependencyType.CROSS_ORG,
    )
    db.session.add(processing_dependency)
    submission.processing_state = SubmissionProcessingState.COMPLETED
    submission.is_auto_processed = True
    submission.is_verified = True
    file = file_fixture(
        processing_state=FileProcessingState.NOT_CLASSIFIED,
        file_type=FileType.SOV,
        submission_id=submission.id,
        is_internal=True,
    )
    db.session.commit()
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
        ) as publish_mock,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_start_report_processing_request"
        ) as start_report_processing_mock,
    ):
        file_handler = FileHandler()
        file_handler.handle_new_file_added(submission, file)
        assert publish_mock.called is True
        assert start_report_processing_mock.called is True
        assert (
            db.session.query(ReportProcessingDependency)
            .filter(ReportProcessingDependency.report_id == submission.report.id)
            .first()
        ) is None


def test_already_processed_files_when_start_submission_processing(app_context, submission):
    submission.processing_state = SubmissionProcessingState.NOT_STARTED
    file = file_fixture(
        processing_state=FileProcessingState.PROCESSED,
        file_type=FileType.EMAIL,
        classification=ClassificationDocumentType.EMAIL,
        submission_id=submission.id,
    )
    submission.is_auto_processed = True
    db.session.commit()
    file_handler = FileHandler()
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_classified_event"
        ) as classified_event_mock,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
        ) as added_event_mock,
    ):
        file_handler.handle_new_file_added(submission, file, True)
        assert classified_event_mock.called is False
        assert added_event_mock.called is False
        assert file.processing_state == FileProcessingState.WAITING_FOR_ENTITY_MAPPING


@pytest.mark.parametrize(
    "submission_state, file_state, file_type, is_auto_processed, loss_runs_manual, should_process",
    [
        (
            SubmissionProcessingState.CLASSIFYING,
            FileProcessingState.CLASSIFIED,
            FileType.SOV,
            True,
            False,
            True,
        ),
        (
            SubmissionProcessingState.PROCESSING,
            FileProcessingState.CLASSIFIED,
            FileType.SOV,
            True,
            False,
            True,
        ),
        (
            SubmissionProcessingState.COMPLETED,
            FileProcessingState.CLASSIFIED,
            FileType.EMAIL,
            True,
            False,
            True,
        ),
        (
            SubmissionProcessingState.CLASSIFYING,
            FileProcessingState.CLASSIFIED,
            FileType.EMAIL,
            False,
            False,
            True,
        ),
        (
            SubmissionProcessingState.NOT_STARTED,
            FileProcessingState.CLASSIFIED,
            FileType.SOV,
            False,
            False,
            False,
        ),
        (
            SubmissionProcessingState.COMPLETED,
            FileProcessingState.CLASSIFIED,
            FileType.SOV,
            True,
            False,
            False,
        ),
        (
            SubmissionProcessingState.COMPLETED,
            FileProcessingState.CLASSIFIED,
            FileType.IFTA,
            True,
            False,
            True,
        ),
        (
            SubmissionProcessingState.PROCESSING,
            FileProcessingState.CLASSIFIED,
            FileType.LOSS_RUN,
            True,
            True,
            False,
        ),
        (
            SubmissionProcessingState.PROCESSING,
            FileProcessingState.CLASSIFIED,
            FileType.LOSS_RUN,
            True,
            False,
            True,
        ),
    ],
)
def test_handle_file_classified(
    app_context,
    mocker,
    submission,
    submission_state,
    file_state,
    file_type,
    is_auto_processed,
    loss_runs_manual,
    should_process,
):
    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled")
    with patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing") as processing_mock:
        settings_fixture(user_id=submission.owner_id, loss_runs_manual=loss_runs_manual)
        file = file_fixture(processing_state=file_state, file_type=file_type, submission_id=submission.id)

        submission.processing_state = submission_state
        submission.is_auto_processed = is_auto_processed

        db.session.commit()

        file_handler = FileHandler()
        file_handler.handle(file.id, submission.id)

        assert processing_mock.called == should_process


def test_handle_file_classified_do_not_create_shadow_for_old_subs(
    app_context,
    mocker,
    submission,
):
    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled")
    with (
        patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing") as processing_mock,
        patch("copilot.models.reports.ReportV2.create_shadow_report") as create_shadow_mock,
    ):
        file = file_fixture(
            processing_state=FileProcessingState.CLASSIFIED,
            classification=ClassificationDocumentType.SOV_SPREADSHEET,
            file_type=FileType.SOV,
            submission_id=submission.id,
        )

        submission.processing_state = SubmissionProcessingState.COMPLETED
        submission.is_auto_processed = True

        db.session.commit()

        file_handler = FileHandler()
        file_handler.handle(file.id, submission.id)

        assert processing_mock.called == False
        assert create_shadow_mock.called == True

        submission.created_at = datetime.now() - timedelta(days=200)
        db.session.commit()
        create_shadow_mock.reset_mock()
        processing_mock.reset_mock()
        file_handler = FileHandler()
        file_handler.handle(file.id, submission.id)

        assert processing_mock.called == False
        assert create_shadow_mock.called == False


def test_handle_file_classified_do_not_create_shadow_for_document_ingestion(
    app_context,
    mocker,
    submission,
):
    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled")
    mocker.patch("copilot.models.files.File.is_document_ingestion", return_value=True)
    with (
        patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing") as processing_mock,
        patch("copilot.models.reports.ReportV2.create_shadow_report") as create_shadow_mock,
    ):
        file = file_fixture(
            processing_state=FileProcessingState.CLASSIFIED,
            classification=ClassificationDocumentType.SOV_SPREADSHEET,
            file_type=FileType.SOV,
            submission_id=submission.id,
        )

        submission.processing_state = SubmissionProcessingState.COMPLETED
        submission.is_auto_processed = True
        db.session.commit()

        file_handler = FileHandler()
        file_handler.handle(file.id, submission.id)

        assert processing_mock.called == False
        assert create_shadow_mock.called == False
        assert (
            db.session.query(File.processing_state).filter(File.id == file.id).scalar() == FileProcessingState.IGNORED
        )


def test_handle_file_classified_do_not_process_disabled_files_for_document_ingestion(
    app_context,
    mocker,
    submission,
):
    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled")
    mocker.patch("copilot.models.files.File.is_document_ingestion", return_value=True)
    org_id = submission.report.organization_id
    settings = Settings.query.filter(Settings.organization_id == org_id).first()
    settings.di_disabled_file_types = [FileType.SOV]
    db.session.commit()

    with patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing") as processing_mock:
        file = file_fixture(
            processing_state=FileProcessingState.CLASSIFIED,
            classification=ClassificationDocumentType.SOV_SPREADSHEET,
            file_type=FileType.SOV,
            submission_id=submission.id,
        )

        submission.processing_state = SubmissionProcessingState.PROCESSING
        submission.is_auto_processed = True
        db.session.commit()

        file_handler = FileHandler()
        file_handler.handle(file.id, submission.id)

        assert processing_mock.called == False
        assert (
            db.session.query(File.processing_state).filter(File.id == file.id).scalar() == FileProcessingState.IGNORED
        )


@pytest.mark.parametrize("loss_runs_manual", [True, False])
def test_handle_file_classified_do_not_process_hidden_files(
    app_context,
    mocker,
    submission,
    loss_runs_manual,
):
    mocker.patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled")
    with patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing") as processing_mock:
        settings_fixture(user_id=submission.owner_id, loss_runs_manual=loss_runs_manual)
        file = file_fixture(
            processing_state=FileProcessingState.CLASSIFIED, file_type=FileType.SOV, submission_id=submission.id
        )
        file.hidden = True

        submission.processing_state = SubmissionProcessingState.CLASSIFYING
        submission.is_auto_processed = True

        db.session.commit()

        file_handler = FileHandler()
        file_handler.handle(file.id, submission.id)

        assert processing_mock.called is False
        assert file.processing_state == FileProcessingState.IGNORED


def test_handle_file_classified_do_no_change_submission_state(app_context, mocker, submission):
    mocker.patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing")
    submission.processing_state = SubmissionProcessingState.CLASSIFYING
    submission.is_auto_processed = True
    file_1 = file_fixture(processing_state=FileProcessingState.NOT_CLASSIFIED)
    submission.files = [file_1]
    file_2 = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.SOV,
        submission_id=submission.id,
    )
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_2.id, submission.id)
    assert submission.processing_state == SubmissionProcessingState.CLASSIFYING


def test_handle_file_classified_change_submission_state(app_context, mocker, submission):
    mocker.patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing")
    submission.processing_state = SubmissionProcessingState.CLASSIFYING
    submission.is_auto_processed = True
    file_1 = file_fixture(processing_state=FileProcessingState.PROCESSING)
    submission.files = [file_1]
    file_2 = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.SOV,
        submission_id=submission.id,
    )
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_2.id, submission.id)
    assert submission.processing_state == SubmissionProcessingState.PROCESSING


@pytest.mark.parametrize(
    "file_types, file_classifications, file_processing_states, is_email_classification_enabled, is_cancelled",
    [
        (
            [FileType.EMAIL, FileType.ACORD_FORM, FileType.SOV],
            [ClassificationDocumentType.EMAIL, ClassificationDocumentType.ACORD_35, ClassificationDocumentType.SOV],
            [FileProcessingState.CLASSIFIED, FileProcessingState.CLASSIFIED],
            False,
            True,
        ),
        (
            [FileType.EMAIL, FileType.ACORD_FORM],
            [ClassificationDocumentType.EMAIL, ClassificationDocumentType.ACORD_125],
            [FileProcessingState.CLASSIFIED, FileProcessingState.CLASSIFIED],
            False,
            False,
        ),
        (
            [FileType.EMAIL, FileType.OTHER],
            [ClassificationDocumentType.EMAIL, ClassificationDocumentType.OTHER_PDF],
            [FileProcessingState.CLASSIFIED, FileProcessingState.CLASSIFIED],
            False,
            True,
        ),
        (
            [FileType.EMAIL, FileType.RAW_EMAIL],
            [ClassificationDocumentType.EMAIL, ClassificationDocumentType.RAW_EMAIL],
            [FileProcessingState.CLASSIFIED, FileProcessingState.CLASSIFIED],
            False,
            False,
        ),
        (
            [FileType.EMAIL, FileType.RAW_EMAIL],
            [ClassificationDocumentType.EMAIL, ClassificationDocumentType.RAW_EMAIL],
            [FileProcessingState.CLASSIFIED, FileProcessingState.PROCESSED],
            False,
            True,
        ),
        (
            [FileType.EMAIL, FileType.RAW_EMAIL, FileType.UNKNOWN],
            [
                ClassificationDocumentType.EMAIL,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.RAW_EMAIL,
            ],
            [FileProcessingState.CLASSIFIED, FileProcessingState.CLASSIFYING, FileProcessingState.PROCESSED],
            False,
            False,
        ),
        (
            [FileType.EMAIL, FileType.RAW_EMAIL],
            [ClassificationDocumentType.EMAIL, ClassificationDocumentType.RAW_EMAIL],
            [FileProcessingState.CLASSIFIED, FileProcessingState.PROCESSED],
            True,
            True,
        ),
        (
            [FileType.EMAIL, FileType.RAW_EMAIL, FileType.UNKNOWN],
            [
                ClassificationDocumentType.EMAIL,
                ClassificationDocumentType.UNKNOWN,
                ClassificationDocumentType.RAW_EMAIL,
            ],
            [FileProcessingState.CLASSIFIED, FileProcessingState.CLASSIFYING, FileProcessingState.PROCESSED],
            True,
            False,
        ),
    ],
)
def test_handle_file_classified_cancel_submission(
    app_context,
    mocker,
    file_types,
    file_classifications,
    file_processing_states,
    is_email_classification_enabled,
    is_cancelled,
    set_up_current_user,
):
    mocker.patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing")
    organization = organization_fixture(id=ExistingOrganizations.BishopConifer.value)
    db.session.commit()
    if is_email_classification_enabled:
        settings_fixture(organization_id=organization.id, is_email_classification_enabled=True)
    else:
        settings_fixture(organization_id=organization.id)
    not_sub_email_label = EmailClassificationLabel(
        id=uuid4(),
        label=KalepaEmailClassificationLabels.NOT_SUBMISSION,
        should_process=False,
        organization_id=organization.id,
    )
    db.session.add(not_sub_email_label)
    user_fixture(organization_id=organization.id)
    correspondence = ReportEmailCorrespondence(id=uuid4(), email_account="<EMAIL>", thread_id="thread_id")
    db.session.add(correspondence)
    db.session.commit()
    report, submission = report_and_submission_fixture(
        organization_id=organization.id,
        processing_state=SubmissionProcessingState.CLASSIFYING,
        is_auto_processed=True,
        is_processing=True,
        correspondence_id=correspondence.id,
    )
    email = email_fixture(
        correspondence_id=correspondence.id,
        email_from="<EMAIL>",
        email_to="<EMAIL>",
        email_subject="SUBJECT",
        email_account="<EMAIL>",
    )
    if is_email_classification_enabled:
        new_sub_email_label = EmailClassificationLabel(
            id=uuid4(),
            label=KalepaEmailClassificationLabels.NEW_SUBMISSION,
            should_process=False,
            organization_id=organization.id,
        )
        db.session.add(new_sub_email_label)
        new_sub_classification = EmailClassification(
            email_id=email.id,
            classifier_id=None,
            label_id=new_sub_email_label.id,
            explanation="Its a sub",
            is_confirmed=True,
        )
        db.session.add(new_sub_classification)
    for file_type, classification, processing_state in zip(file_types, file_classifications, file_processing_states):
        file = file_fixture(
            processing_state=processing_state,
            classification=classification,
            file_type=file_type,
            submission_id=submission.id,
        )

    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file.id, submission.id)
    assert (
        submission.processing_state == SubmissionProcessingState.CANCELLED
        if is_cancelled
        else SubmissionProcessingState.PROCESSING
    )
    assert submission.is_processing == (not is_cancelled)
    if is_email_classification_enabled:
        has_not_submission_tag = bool(
            db.session.query(EmailClassification)
            .join(EmailClassificationLabel, EmailClassificationLabel.id == EmailClassification.label_id)
            .filter(
                EmailClassification.email_id == email.id,
                EmailClassificationLabel.label == KalepaEmailClassificationLabels.NOT_SUBMISSION,
            )
            .all()
        )
        has_new_submission_tag = bool(
            db.session.query(EmailClassification)
            .join(EmailClassificationLabel, EmailClassificationLabel.id == EmailClassification.label_id)
            .filter(
                EmailClassification.email_id == email.id,
                EmailClassificationLabel.label == KalepaEmailClassificationLabels.NEW_SUBMISSION,
            )
            .all()
        )
        assert has_not_submission_tag == is_cancelled
        assert has_new_submission_tag is not is_cancelled
    else:
        assert not db.session.query(EmailClassification).filter(EmailClassification.email_id == email.id).all()


def test_handle_file_classified_do_not_override_email_classification(app_context, mocker, set_up_current_user):
    mocker.patch("copilot.logic.pds.file_handler.FileHandler.invoke_file_processing")
    organization = organization_fixture(id=ExistingOrganizations.BishopConifer.value)
    db.session.commit()
    settings_fixture(organization_id=organization.id, is_email_classification_enabled=True)
    email_label = EmailClassificationLabel(
        id=uuid4(),
        label=KalepaEmailClassificationLabels.NOT_SUBMISSION,
        should_process=False,
        organization_id=organization.id,
    )
    email_label_2 = EmailClassificationLabel(
        id=uuid4(),
        label=KalepaEmailClassificationLabels.NEW_SUBMISSION,
        should_process=True,
        organization_id=organization.id,
    )
    db.session.add(email_label)
    db.session.add(email_label_2)
    user_fixture(organization_id=organization.id)
    correspondence = ReportEmailCorrespondence(id=uuid4(), email_account="<EMAIL>", thread_id="thread_id")
    db.session.add(correspondence)
    db.session.commit()
    report, submission = report_and_submission_fixture(
        organization_id=organization.id,
        processing_state=SubmissionProcessingState.CLASSIFYING,
        is_auto_processed=True,
        is_processing=True,
        correspondence_id=correspondence.id,
    )
    email = email_fixture(
        correspondence_id=correspondence.id,
        email_from="<EMAIL>",
        email_to="<EMAIL>",
        email_subject="SUBJECT",
        email_account="<EMAIL>",
    )
    email_classification = EmailClassification(
        email_id=email.id,
        label_id=email_label_2.id,
        explanation="Manually set by Ivan",
        is_confirmed=True,
    )
    db.session.add(email_classification)

    for file_type, classification, processing_state in zip(
        [FileType.EMAIL, FileType.RAW_EMAIL],
        [ClassificationDocumentType.EMAIL, ClassificationDocumentType.RAW_EMAIL],
        [FileProcessingState.CLASSIFIED, FileProcessingState.PROCESSED],
    ):
        file = file_fixture(
            processing_state=processing_state,
            classification=classification,
            file_type=file_type,
            submission_id=submission.id,
        )

    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file.id, submission.id)
    assert submission.processing_state == SubmissionProcessingState.CLASSIFYING
    assert submission.is_processing
    has_not_submission_tag = bool(
        db.session.query(EmailClassification)
        .join(EmailClassificationLabel, EmailClassificationLabel.id == EmailClassification.label_id)
        .filter(
            EmailClassification.email_id == email.id,
            EmailClassificationLabel.label == KalepaEmailClassificationLabels.NOT_SUBMISSION,
        )
        .all()
    )
    assert has_not_submission_tag is False


@pytest.mark.parametrize(
    "file_state, file_classification,file_type,has_processed_file,resolution_data,file_end_state",
    [
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.EMAIL,
            FileType.EMAIL,
            False,
            null(),
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_PDF,
            FileType.SUPPLEMENTAL_FORM,
            False,
            null(),
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.ACORD_125,
            FileType.ACORD_FORM,
            False,
            null(),
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
            FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
            False,
            null(),
            FileProcessingState.PROCESSING_FAILED,
        ),
        (
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
            FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
            False,
            null(),
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.DRIVERS_SPREADSHEET,
            FileType.DRIVERS,
            False,
            null(),
            FileProcessingState.PROCESSING_FAILED,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.VEHICLES_SPREADSHEET,
            FileType.VEHICLES,
            False,
            null(),
            FileProcessingState.PROCESSING_FAILED,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.EQUIPMENT_SPREADSHEET,
            FileType.EQUIPMENT,
            False,
            null(),
            FileProcessingState.PROCESSING_FAILED,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.SOV_SPREADSHEET,
            FileType.SOV,
            False,
            null(),
            FileProcessingState.PROCESSING_FAILED,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.EMAIL,
            FileType.EMAIL,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_PDF,
            FileType.SUPPLEMENTAL_FORM,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.ACORD_126,
            FileType.ACORD_FORM,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.ACORD_126,
            FileType.ACORD_FORM,
            True,
            null(),
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.SOV_SPREADSHEET,
            FileType.SOV,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.VEHICLES_SPREADSHEET,
            FileType.VEHICLES,
            True,
            null(),
            FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.EQUIPMENT_SPREADSHEET,
            FileType.EQUIPMENT,
            True,
            null(),
            FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.DRIVERS_SPREADSHEET,
            FileType.DRIVERS,
            True,
            null(),
            FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.OTHER,
            FileType.OTHER,
            False,
            null(),
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.UNKNOWN,
            FileType.UNKNOWN,
            False,
            null(),
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        ),
        (
            FileProcessingState.CACHE_PROCESSED,
            ClassificationDocumentType.ACORD_125,
            FileType.ACORD_FORM,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
            FileType.NAMED_INSURED_SCHEDULE,
            True,
            RESOLVED_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
            FileType.NAMED_INSURED_SCHEDULE,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION,
        ),
        (
            FileProcessingState.IGNORED,
            ClassificationDocumentType.MERGED,
            FileType.MERGED,
            False,
            null(),
            FileProcessingState.IGNORED,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.EMPLOYEE_HANDBOOK_PDF,
            FileType.EMPLOYEE_HANDBOOK,
            True,
            null(),
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_BENEFITS_PLAN,
            FileType.SUPPLEMENTAL_FORM,
            True,
            null(),
            FileProcessingState.PROCESSING_FAILED,
        ),
    ],
)
def test_handle_file_processed_updates_file_state(
    app_context,
    mocker,
    submission,
    file_state,
    file_classification,
    file_type,
    has_processed_file,
    resolution_data,
    file_end_state,
):
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    )
    mocker.patch("copilot.logic.pds.file_handler.clean_up")
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, has_submission_permission=lambda x, y: True, id=1),
    )
    file_1 = file_fixture(
        processing_state=file_state,
        classification=file_classification,
        file_type=file_type,
        submission_id=submission.id,
    )
    if has_processed_file:
        # Processed_data must not be null as there is a DB level constraint
        file_1.processed_file = ProcessedFile(processed_data={}, business_resolution_data=resolution_data)
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)
    assert file_1.processing_state == file_end_state


@pytest.mark.parametrize(
    "file_state, file_classification,file_type,has_processed_file,resolution_data,file_end_state",
    [
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.EMAIL,
            FileType.EMAIL,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_PDF,
            FileType.SUPPLEMENTAL_FORM,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.ACORD_126,
            FileType.ACORD_FORM,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.ACORD_126,
            FileType.ACORD_FORM,
            True,
            null(),
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.SOV_SPREADSHEET,
            FileType.SOV,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.VEHICLES_SPREADSHEET,
            FileType.VEHICLES,
            True,
            null(),
            FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.EQUIPMENT_SPREADSHEET,
            FileType.EQUIPMENT,
            True,
            null(),
            FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.DRIVERS_SPREADSHEET,
            FileType.DRIVERS,
            True,
            null(),
            FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
        ),
        (
            FileProcessingState.CACHE_PROCESSED,
            ClassificationDocumentType.ACORD_125,
            FileType.ACORD_FORM,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
            FileType.NAMED_INSURED_SCHEDULE,
            True,
            RESOLVED_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.NAMED_INSURED_SCHEDULE_SPREADSHEET,
            FileType.NAMED_INSURED_SCHEDULE,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION,
        ),
        (
            FileProcessingState.IGNORED,
            ClassificationDocumentType.MERGED,
            FileType.MERGED,
            False,
            null(),
            FileProcessingState.IGNORED,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.EMPLOYEE_HANDBOOK_PDF,
            FileType.EMPLOYEE_HANDBOOK,
            True,
            null(),
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
    ],
)
def test_handle_file_processed_updates_file_state(
    app_context,
    mocker,
    submission,
    file_state,
    file_classification,
    file_type,
    has_processed_file,
    resolution_data,
    file_end_state,
):
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    )
    mocker.patch("copilot.logic.pds.file_handler.clean_up")
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, has_submission_permission=lambda x, y: True, id=1),
    )
    with patch("copilot.clients.lambdas.LambdaClient.invoke_file_insights_async") as insights_mock:
        file_1 = file_fixture(
            processing_state=file_state,
            classification=file_classification,
            file_type=file_type,
            submission_id=submission.id,
            organization_id=1,
        )
        if has_processed_file:
            # Processed_data must not be null as there is a DB level constraint
            file_1.processed_file = ProcessedFile(processed_data={}, business_resolution_data=resolution_data)
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_1.id, submission.id)
        assert file_1.processing_state == file_end_state

        if file_end_state == FileProcessingState.WAITING_FOR_FILE_INSIGHTS:
            insights_mock.assert_called_once()
            assert insights_mock.call_args.args[0] == {
                "organization_id": submission.organization_id,
                "submission_id": str(file_1.submission_id),
                "file_id": str(file_1.id),
            }


def test_handle_file_processed_moves_handbook_to_em_for_nw_ml(app_context, mocker, submission):
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    )
    mocker.patch("copilot.logic.pds.file_handler.clean_up")
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, has_submission_permission=lambda x, y: True, id=1),
    )
    file_1 = file_fixture(
        processing_state=FileProcessingState.PROCESSED,
        classification=ClassificationDocumentType.EMPLOYEE_HANDBOOK_PDF,
        file_type=FileType.EMPLOYEE_HANDBOOK,
        submission_id=submission.id,
    )
    file_1.processed_file = ProcessedFile(processed_data={}, business_resolution_data=null())
    db.session.commit()
    mocker.patch("copilot.models.organization.Organization.is_nationwide_ml_for_id", return_value=True)
    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)
    assert file_1.processing_state == FileProcessingState.WAITING_FOR_ENTITY_MAPPING


@pytest.mark.parametrize(
    "file_state, file_classification,file_type,has_processed_file,resolution_data,file_end_state",
    [
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
            FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
            False,
            null(),
            FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
        ),
        (
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
            FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
            False,
            null(),
            FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
        ),
    ],
)
def test_handle_file_processed_updates_file_state_for_financial_proc_active(
    app_context,
    mocker,
    submission,
    file_state,
    file_classification,
    file_type,
    has_processed_file,
    resolution_data,
    file_end_state,
):
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    )
    mocker.patch("copilot.logic.pds.file_handler.clean_up")
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, has_submission_permission=lambda x, y: True, id=1),
    )

    file_1 = file_fixture(
        processing_state=file_state,
        classification=file_classification,
        file_type=file_type,
        submission_id=submission.id,
    )

    mocker.patch("copilot.logic.pds.file_handler.ORGS_TO_PROCESS_FINANCIAL_STATEMENTS", [file_1.organization_id])

    if has_processed_file:
        # Processed_data must not be null as there is a DB level constraint
        file_1.processed_file = ProcessedFile(processed_data={}, business_resolution_data=resolution_data)
    db.session.commit()

    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)

    assert file_1.processing_state == file_end_state


@pytest.mark.parametrize(
    "file_state, file_classification,file_type,has_processed_file,resolution_data,file_end_state",
    [
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.EMAIL,
            FileType.EMAIL,
            False,
            null(),
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.PROJECT_SUPPLEMENTAL_APPLICATION_PDF,
            FileType.SUPPLEMENTAL_FORM,
            False,
            null(),
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.ACORD_125,
            FileType.ACORD_FORM,
            False,
            null(),
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSING_FAILED,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
            FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
            False,
            null(),
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
            ClassificationDocumentType.CONSOLIDATED_FINANCIAL_STATEMENT_PDF,
            FileType.CONSOLIDATED_FINANCIAL_STATEMENT,
            False,
            null(),
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.EMAIL,
            FileType.EMAIL,
            True,
            EXAMPLE_RESOLUTION_DATA,
            FileProcessingState.WAITING_FOR_ENTITY_MAPPING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.UNKNOWN,
            FileType.UNKNOWN,
            True,
            null(),
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        ),
        (
            FileProcessingState.PROCESSED,
            ClassificationDocumentType.UNKNOWN,
            FileType.UNKNOWN,
            False,
            null(),
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        ),
        (
            FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
            ClassificationDocumentType.MERGED,
            FileType.MERGED,
            True,
            null(),
            FileProcessingState.COMPLETED,
        ),
    ],
)
def test_handle_file_processed_updates_file_state_for_document_ingestion(
    app_context,
    mocker,
    submission,
    file_state,
    file_classification,
    file_type,
    has_processed_file,
    resolution_data,
    file_end_state,
):
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    )
    mocker.patch("copilot.logic.pds.file_handler.clean_up")
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, has_submission_permission=lambda x, y: True, id=1),
    )
    mocker.patch("copilot.models.files.File.is_document_ingestion", return_value=True)
    file_1 = file_fixture(
        processing_state=file_state,
        classification=file_classification,
        file_type=file_type,
        submission_id=submission.id,
    )
    if has_processed_file:
        # Processed_data must not be null as there is a DB level constraint
        file_1.processed_file = ProcessedFile(processed_data={}, business_resolution_data=resolution_data)
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)
    assert file_1.processing_state == file_end_state


def test_handle_file_processed_files_without_entities(
    app_context,
    mocker,
    submission,
):
    # (
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    )
    file_1 = file_fixture(
        processing_state=FileProcessingState.PROCESSED, file_type=FileType.LOSS_RUN, submission_id=submission.id
    )
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)
    assert file_1.processing_state == FileProcessingState.COMPLETED


def test_handle_file_processed_not_applicable_for_processing_acords(app_context, submission):
    file_1 = file_fixture(
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        file_type=FileType.ACORD_FORM,
        submission_id=submission.id,
    )
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)
    assert file_1.processing_state == FileProcessingState.WAITING_FOR_ENTITY_MAPPING
    assert db.session.query(ProcessedFile).filter(ProcessedFile.file_id == file_1.id).first()


def test_handle_file_processed_not_applicable_for_processing_acords_blacklisted(app_context, submission):
    file_1 = file_fixture(
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        file_type=FileType.ACORD_FORM,
        submission_id=submission.id,
        additional_info={
            "acord": {
                "form_name": "APPLIED_125",
                "form_year": 2009,
                "version_id": "APPLIED_125_CIS_2009_08",
                "form_version": "08",
                "form_delimiter": "CIS",
            }
        },
    )
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)
    assert file_1.processing_state == FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING


def test_handle_file_processed_not_applicable_for_processing(
    app_context,
    submission,
):
    file_1 = file_fixture(
        processing_state=FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
        file_type=FileType.SOV,
        submission_id=submission.id,
    )
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)
    assert file_1.processing_state == FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING
    assert db.session.query(ProcessedFile).filter(ProcessedFile.file_id == file_1.id).first() is None


def test_handle_file_processed_does_not_update_submission_state(app_context, submission):
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    ) as publish_mock:
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(processing_state=FileProcessingState.PROCESSING, submission_id=submission.id)
        file_2 = file_fixture(
            processing_state=FileProcessingState.PROCESSED,
            classification=ClassificationDocumentType.EMAIL,
            submission_id=submission.id,
        )
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert not publish_mock.called
        assert submission.processing_state == SubmissionProcessingState.PROCESSING


def test_handle_file_processed_updates_submission_state(app_context, submission):
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    ) as publish_mock:
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION, submission_id=submission.id
        )
        file_2 = file_fixture(
            processing_state=FileProcessingState.PROCESSED,
            classification=ClassificationDocumentType.EMAIL,
            submission_id=submission.id,
        )
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert publish_mock.called


def test_handle_file_processed_updates_submission_state_document_ingestion(app_context, submission, mocker):
    mocker.patch("copilot.models.files.File.is_document_ingestion", return_value=True)
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    ) as publish_mock:
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_ONBOARDING, submission_id=submission.id
        )
        file_2 = file_fixture(
            processing_state=FileProcessingState.PROCESSED,
            classification=ClassificationDocumentType.EMAIL,
            submission_id=submission.id,
        )
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert not publish_mock.called
        assert (
            db.session.query(Submission).filter(Submission.id == submission.id).first().processing_state
            == SubmissionProcessingState.PROCESSING
        )


def test_handle_file_processed_updates_submission_state_ignores_lr(app_context, submission):
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    ) as publish_mock:
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            file_type=FileType.LOSS_RUN,
            classification=ClassificationDocumentType.LOSS_RUN_PDF,
            processing_state=FileProcessingState.CLASSIFIED,
            submission_id=submission.id,
        )
        file_2 = file_fixture(
            processing_state=FileProcessingState.PROCESSED,
            classification=ClassificationDocumentType.EMAIL,
            submission_id=submission.id,
        )
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert publish_mock.called


def test_handle_file_processed_going_back(app_context, submission, mocker):
    mocker.patch("copilot.logic.pds.file_handler.clean_up")
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    ) as publish_mock:
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
            classification=ClassificationDocumentType.EMAIL,
            submission_id=submission.id,
        )
        file_1.processed_file = ProcessedFile(processed_data={}, entity_mapped_data={})
        file_2 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
            classification=ClassificationDocumentType.SOV,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data={}, business_resolution_data=EXAMPLE_RESOLUTION_DATA)
        file_3 = file_fixture(
            processing_state=FileProcessingState.PROCESSED,
            classification=ClassificationDocumentType.EMAIL,
            submission_id=submission.id,
        )
        file_3.processed_file = ProcessedFile(processed_data={})
        file_4 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_COMPLETION,
            classification=ClassificationDocumentType.ACORD_125,
            initial_processing_state=FileProcessingState.CACHE_PROCESSED,
            submission_id=submission.id,
        )
        file_4.processed_file = ProcessedFile(processed_data={}, business_resolution_data=EXAMPLE_RESOLUTION_DATA)
        submission.report.going_back = True
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_3.id, submission.id)
        assert publish_mock.called
        assert Submission.query.get(submission.id).report.going_back is False
        assert File.query.get(file_1.id).processing_state == FileProcessingState.WAITING_FOR_ENTITY_MAPPING
        assert File.query.get(file_2.id).processing_state == FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION
        assert File.query.get(file_3.id).processing_state == FileProcessingState.WAITING_FOR_ENTITY_MAPPING
        assert File.query.get(file_4.id).processing_state == FileProcessingState.WAITING_FOR_COMPLETION


def test_handle_file_processed_not_all_files_ready(app_context, submission):
    with patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    ) as publish_mock:
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(processing_state=FileProcessingState.PROCESSED, submission_id=submission.id)
        file_2 = file_fixture(
            processing_state=FileProcessingState.PROCESSED,
            classification=ClassificationDocumentType.EMAIL,
            submission_id=submission.id,
        )
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert not publish_mock.called
        assert submission.processing_state == SubmissionProcessingState.PROCESSING


def test_handle_file_processed_creates_pds_stats(app_context, submission, mocker):
    file_id = uuid4()
    processed_data = OnboardedFile(
        files=[file_id],
        entities=[
            SubmissionEntity(id="123 Main Str", type=SubmissionEntityType.BUSINESS),
            SubmissionEntity(id=str(uuid4()), type=SubmissionEntityType.STRUCTURE, parent_idx=0),
            SubmissionEntity(id="345 Main Str", type=SubmissionEntityType.BUSINESS),
            SubmissionEntity(id=str(uuid4()), type=SubmissionEntityType.STRUCTURE, parent_idx=2),
            SubmissionEntity(id="567 Main Str", type=SubmissionEntityType.BUSINESS),
            SubmissionEntity(id="678 Main Str", type=SubmissionEntityType.BUSINESS),
            SubmissionEntity(id="789 Main Str", type=SubmissionEntityType.BUSINESS),
        ],
    )
    business_resolution_data = [
        BusinessResolutionData(
            entity_id=uuid4(),
            entity_idx=0,
            resolved_name="Kalepa Ltd",
            requested_name="Kalepa Ltd",
            is_autoconfirmed=True,
            resolved_address="711 N UNDERHILL ST, PEORIA, IL 61606, US",
            requested_address="711 N Underhill St, Peoria, IL 61606 2404",
        ),
        BusinessResolutionData(
            entity_id=None,
            entity_idx=2,
            resolved_name=None,
            requested_name="Kalepa Ltd",
            is_autoconfirmed=False,
            resolved_address=None,
            requested_address="711 N Underhill St, Peoria, IL 61606 2404",
        ),
        BusinessResolutionData(
            entity_id=None,
            entity_idx=4,
            resolved_name=None,
            requested_name="Kalepa Ltd",
            is_autoconfirmed=False,
            resolved_address=None,
            requested_address=None,
        ),
        BusinessResolutionData(
            entity_id=None,
            entity_idx=5,
            resolved_name=None,
            requested_name=None,
            is_autoconfirmed=False,
            resolved_address=None,
            requested_address="711 N Underhill St, Peoria, IL 61606 2404",
        ),
    ]
    loaded_pf = LoadedProcessedFile(processed_data=processed_data, business_resolution_data=business_resolution_data)
    mocker.patch("copilot.logic.pds.file_handler.clean_up", return_value=loaded_pf)
    mocker.patch(
        "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_processing_finished"
    )
    file_1 = file_fixture(
        id=file_id,
        processing_state=FileProcessingState.PROCESSED,
        classification=ClassificationDocumentType.SOV,
        file_type=ClassificationDocumentType.SOV,
        submission_id=submission.id,
    )
    file_1.processed_file = ProcessedFile(
        processed_data=LeanOnboardedFileSchema().dump(processed_data),
        business_resolution_data={
            "resolution_data": BusinessResolutionDataSchema().dump(loaded_pf.business_resolution_data, many=True)
        },
    )
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_1.id, submission.id)
    pds_stats = db.session.query(PDSStats).filter(PDSStats.file_id == file_id).first()
    assert pds_stats
    assert pds_stats.number_of_extracted_entities == 7
    assert pds_stats.number_of_autoconfirmed_entities == 1
    assert pds_stats.number_of_not_confirmed_entities == 3
    assert pds_stats.idx_of_entities_without_name == [5]
    assert pds_stats.idx_of_entities_without_address == [4]


@pytest.fixture()
def bishop_submission() -> Submission:
    organization = organization_fixture(id=ExistingOrganizations.BishopConifer.value)
    settings_fixture(organization_id=organization.id)
    user_fixture(organization_id=organization.id)
    correspondence = ReportEmailCorrespondence(id=uuid4(), email_account="<EMAIL>", thread_id="thread_id")
    db.session.add(correspondence)
    db.session.commit()
    report, submission = report_and_submission_fixture(
        organization_id=organization.id,
        processing_state=SubmissionProcessingState.CLASSIFYING,
        is_auto_processed=True,
        is_processing=True,
        correspondence_id=correspondence.id,
    )
    email = email_fixture(
        correspondence_id=correspondence.id,
        email_from="<EMAIL>",
        email_to="<EMAIL>",
        email_subject="SUBJECT",
        email_account="<EMAIL>",
    )
    db.session.commit()

    return submission


@pytest.mark.parametrize(
    ["end_date", "include_additional_file", "should_cancel"],
    [
        (None, False, False),
        (datetime.now(), False, False),
        (datetime.now() + relativedelta(months=6), False, True),
        (datetime.now() + relativedelta(months=6), True, False),
    ],
)
def test_handle_file_processed_should_cancel_submission(
    app_context, bishop_submission, end_date, include_additional_file, should_cancel
):
    file_id = uuid4()

    if end_date:
        entity_information = [
            ResolvedDataField(
                name=EntityInformation.POLICY_END_DATE,
                values=[ResolvedDataValue(value=str(end_date), entity_idx=0)],
                value_type=FieldType.DATETIME,
            )
        ]
    else:
        entity_information = []

    processed_data = OnboardedFile(
        files=[file_id],
        entities=[SubmissionEntity(id=str(bishop_submission.id), type=SubmissionEntityType.SUBMISSION)],
        entity_information=entity_information,
    )
    file_1 = file_fixture(
        id=file_id,
        processing_state=FileProcessingState.PROCESSED,
        classification=ClassificationDocumentType.POLICY,
        file_type=ClassificationDocumentType.POLICY,
        submission_id=bishop_submission.id,
        additional_info={"policy_carrier": "CONIFER"},
        organization_id=bishop_submission.report.organization_id,
    )
    file_1.processed_file = ProcessedFile(
        processed_data=LeanOnboardedFileSchema().dump(processed_data),
    )

    if include_additional_file:
        file_fixture(
            id=uuid4(),
            processing_state=FileProcessingState.PROCESSED,
            classification=ClassificationDocumentType.SOV,
            file_type=ClassificationDocumentType.SOV,
            submission_id=bishop_submission.id,
            organization_id=bishop_submission.report.organization_id,
        )

    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_1.id, bishop_submission.id)

    assert (
        bishop_submission.processing_state == SubmissionProcessingState.CANCELLED
        if should_cancel
        else SubmissionProcessingState.PROCESSING
    )


def test_handle_file_reprocessing_requested(app_context, submission):
    submission.processing_state = SubmissionProcessingState.PROCESSING
    file = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.SOV,
        file_type=FileType.SOV,
        submission_id=submission.id,
    )
    file.processed_file = ProcessedFile(processed_data={}, entity_mapped_data={})
    db.session.commit()

    assert ProcessedFile.query.count() == 1

    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
        ) as added_event_mock,
    ):
        file_handler = FileHandler()
        file_handler.handle_file_reprocessing_requested(submission, file)
        assert added_event_mock.called

    assert ProcessedFile.query.count() == 0


def test_handle_file_reprocessing_requested_for_file_with_child_files(app_context, submission):
    submission.processing_state = SubmissionProcessingState.PROCESSING
    file = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.LOSS_RUN,
        file_type=FileType.LOSS_RUN,
        submission_id=submission.id,
    )
    child_file = file_fixture(
        processing_state=FileProcessingState.COMPLETED,
        classification=ClassificationDocumentType.LOSS_RUN,
        file_type=FileType.LOSS_RUN,
        submission_id=submission.id,
        parent_file_id=file.id,
    )
    db.session.commit()

    file.processed_file = ProcessedFile(processed_data={}, entity_mapped_data={})
    child_file.processed_file = ProcessedFile(processed_data={}, entity_mapped_data={})
    loss_fixture(submission_id=submission.id, file_id=file.id)
    loss_fixture(submission_id=submission.id, file_id=child_file.id)

    assert ProcessedFile.query.count() == 2
    assert Loss.query.count() == 2

    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_file_added_event"
        ) as added_event_mock,
    ):
        file_handler = FileHandler()
        file_handler.handle_file_reprocessing_requested(submission, file)
        assert added_event_mock.called

    assert ProcessedFile.query.count() == 0
    assert Loss.query.count() == 0


@pytest.mark.parametrize(
    "classification,file_type",
    [
        (
            ClassificationDocumentType.ACORD_125,
            FileType.ACORD_FORM,
        ),
        (
            ClassificationDocumentType.EMAIL,
            FileType.EMAIL,
        ),
    ],
)
def test_handle_data_consolidation_not_ready(app_context, submission, classification, file_type):
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
            classification=ClassificationDocumentType.ACORD_126,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        file_1.processed_file = ProcessedFile(processed_data={})
        file_2 = file_fixture(
            processing_state=FileProcessingState.CLASSIFIED,
            classification=classification,
            file_type=file_type,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data={})
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_1.id, submission.id)
        assert not processing_finished_event.called
        assert not data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION


@pytest.mark.parametrize(
    "classification,file_type,should_send_processing_finished_event,should_send_data_consolidated_event,expected_state",
    [
        (
            ClassificationDocumentType.EMAIL,
            FileType.EMAIL,
            True,
            False,
            FileProcessingState.PROCESSED,
        ),
        (
            ClassificationDocumentType.ACORD_125,
            FileType.ACORD_FORM,
            False,
            False,
            FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
        ),
    ],
)
def test_handle_data_consolidation_not_ready_document_ingestion(
    app_context,
    submission,
    classification,
    file_type,
    should_send_data_consolidated_event,
    should_send_processing_finished_event,
    expected_state,
):
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
        patch("copilot.models.reports.ReportV2.is_document_ingestion", return_value=True),
        patch("copilot.models.files.File.is_document_ingestion", return_value=True),
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.CLASSIFIED,
            classification=ClassificationDocumentType.ACORD_126,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        file_1.processed_file = ProcessedFile(processed_data={})
        file_2 = file_fixture(
            processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
            classification=classification,
            file_type=file_type,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data={})
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert processing_finished_event.called == should_send_processing_finished_event
        assert data_consolidated_event.called == should_send_data_consolidated_event
        assert File.query.get(file_2.id).processing_state == expected_state


def test_handle_data_consolidation_ready_document_ingestion(app_context, submission):
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
        patch("copilot.models.reports.ReportV2.is_document_ingestion", return_value=True),
        patch("copilot.models.files.File.is_document_ingestion", return_value=True),
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.CLASSIFIED,
            classification=ClassificationDocumentType.EMAIL,
            file_type=FileType.EMAIL,
            submission_id=submission.id,
        )
        file_1.processed_file = ProcessedFile(processed_data={})
        file_2 = file_fixture(
            processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
            classification=ClassificationDocumentType.ACORD_125,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data={"entities": [{"id": "some", "type": "Business"}]})
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert processing_finished_event.called is True
        assert data_consolidated_event.called is True
        assert File.query.get(file_2.id).processing_state == FileProcessingState.PROCESSED


def test_handle_data_consolidation_ready_no_br_data(app_context, submission):
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
            classification=ClassificationDocumentType.ACORD_125,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        file_1.processed_file = ProcessedFile(processed_data={})
        file_2 = file_fixture(
            processing_state=FileProcessingState.PROCESSING,
            classification=ClassificationDocumentType.SOV_SPREADSHEET,
            file_type=FileType.SOV,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data={})
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_1.id, submission.id)
        assert processing_finished_event.called
        assert not data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.PROCESSED


def test_handle_data_consolidation_ready(app_context, submission):
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
            classification=ClassificationDocumentType.ACORD_125,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        with open("tests/data/data_consolidation/acord_125_onboarded_file_with_entities.json") as f:
            file = f.read()
            processed_data = json.loads(file)
        file_1.processed_file = ProcessedFile(processed_data=processed_data)
        file_2 = file_fixture(
            processing_state=FileProcessingState.PROCESSING,
            classification=ClassificationDocumentType.SOV_SPREADSHEET,
            file_type=FileType.SOV,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data={})
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_1.id, submission.id)

        submission_consolidation_process = SubmissionConsolidationProcess.query.one()
        assert submission_consolidation_process.status == SubmissionConsolidationStatus.COMPLETED
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.DATA_CONSOLIDATED


def test_handle_data_consolidation_failed_acord(app_context, submission, set_up_current_user):
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            classification=ClassificationDocumentType.ACORD_125,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        with open("tests/data/data_consolidation/acord_125_onboarded_file_with_entities.json") as f:
            file = f.read()
            processed_data = json.loads(file)
        file_1.processed_file = ProcessedFile(processed_data=processed_data)
        file_2 = file_fixture(
            processing_state=FileProcessingState.PROCESSING_FAILED,
            classification=ClassificationDocumentType.ACORD_126,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)

        submission_consolidation_process = SubmissionConsolidationProcess.query.one()
        assert submission_consolidation_process.status == SubmissionConsolidationStatus.COMPLETED
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.DATA_CONSOLIDATED


def test_handle_data_consolidation_submission_level_data_before_processing(
    app_context, mocker, submission, set_up_current_user
):
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.is_auto_processed = False
        submission.processing_state = None
        file_1 = file_fixture(
            processing_state=FileProcessingState.NOT_CLASSIFIED,
            submission_id=submission.id,
        )
        file_2 = file_fixture(
            processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
            classification=ClassificationDocumentType.EMAIL,
            file_type=FileType.EMAIL,
            submission_id=submission.id,
        )
        with open("tests/data/processed_data/sov_processed_data.json") as f:
            file = f.read()
            processed_data = json.loads(file)
        file_2.processed_file = ProcessedFile(processed_data=processed_data)
        proposed_effective_date = datetime.today().date() + timedelta(days=1)
        submission_level_extracted_data_fixture(
            submission_id=submission.id, file_id=file_2.id, field=EntityInformation.IS_RENEWAL, value="true"
        )
        submission_level_extracted_data_fixture(
            submission_id=submission.id,
            file_id=file_2.id,
            field=EntityInformation.POLICY_EFFECTIVE_START_DATE,
            value=f'"{proposed_effective_date.strftime("%Y-%m-%d")}"',
        )
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        submission = Submission.query.get(submission.id)
        assert submission.is_renewal
        assert submission.proposed_effective_date.date() == proposed_effective_date


def test_handle_data_consolidation_submission_level_data_during_processing(
    app_context, mocker, submission, set_up_current_user
):
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.PROCESSING,
            classification=ClassificationDocumentType.SOV,
            file_type=FileType.SOV,
            submission_id=submission.id,
        )
        file_2 = file_fixture(
            processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
            classification=ClassificationDocumentType.EMAIL,
            file_type=FileType.EMAIL,
            submission_id=submission.id,
        )
        with open("tests/data/processed_data/sov_processed_data.json") as f:
            file = f.read()
            processed_data = json.loads(file)
        file_2.processed_file = ProcessedFile(processed_data=processed_data)
        file_3 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            classification=ClassificationDocumentType.COVER_SHEET_PDF,
            file_type=FileType.COVER_SHEET,
            submission_id=submission.id,
        )
        file_3.processed_file = ProcessedFile(processed_data=processed_data)
        proposed_effective_date = datetime.today().date() + timedelta(days=1)
        submission_level_extracted_data_fixture(
            submission_id=submission.id, file_id=file_2.id, field=EntityInformation.IS_RENEWAL, value="true"
        )
        submission_level_extracted_data_fixture(
            submission_id=submission.id,
            file_id=file_3.id,
            field=EntityInformation.POLICY_EFFECTIVE_START_DATE,
            value=f'"{proposed_effective_date.strftime("%Y-%m-%d")}"',
        )
        submission_level_extracted_data_fixture(
            submission_id=submission.id, file_id=file_3.id, field=EntityInformation.IS_RENEWAL, value="false"
        )
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_2.id, submission.id)
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.PROCESSING
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        assert File.query.get(file_3.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        submission = Submission.query.get(submission.id)
        assert submission.is_renewal
        assert submission.proposed_effective_date.date() == proposed_effective_date


@pytest.fixture
def email_processed_data_no_fni():
    return {
        "fields": [
            {
                "name": "TOTAL_SALES",
                "fact_subtype_id": "TOTAL_SALES",
                "values": [{"value": 4680000.0, "entity_idx": None}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            }
        ],
        "entity_information": [
            {
                "name": "Broker Name",
                "values": [{"value": "KENDALL HUDAK", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
            {
                "name": "Brokerage Name",
                "values": [{"value": "RT Specialty", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
        ],
        "entities": [{"type": "Submission", "id": "2945ecd5-36c9-45d9-8d94-d0aba6a58987", "resolved": True}],
        "files": ["ea2ae8db-4ea3-4483-9425-7c821c1adff7"],
        "additional_data": {"finished_do_sub_step": None},
    }


@pytest.fixture
def email_processed_data_no_fni_address():
    return {
        "fields": [
            {
                "name": "TOTAL_SALES",
                "fact_subtype_id": "TOTAL_SALES",
                "values": [{"value": 4680000.0, "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            }
        ],
        "entity_information": [
            {
                "name": "Broker Name",
                "values": [{"value": "KENDALL HUDAK", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
            {
                "name": "Brokerage Name",
                "values": [{"value": "RT Specialty", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
            {
                "name": "Name",
                "values": [{"value": "Leavitt's Mortuary", "entity_idx": 1}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
        ],
        "entities": [
            {"type": "Submission", "id": "2945ecd5-36c9-45d9-8d94-d0aba6a58987", "resolved": True},
            {"type": "Primary Insured", "id": "leavitt's mortuary, inc, "},
        ],
        "files": ["ea2ae8db-4ea3-4483-9425-7c821c1adff7"],
        "additional_data": {"finished_do_sub_step": None},
    }


@pytest.fixture
def email_processed_data_no_fni_address_with_fein():
    return {
        "fields": [
            {
                "name": "TOTAL_SALES",
                "fact_subtype_id": "TOTAL_SALES",
                "values": [{"value": 4680000.0, "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            }
        ],
        "entity_information": [
            {
                "name": "Broker Name",
                "values": [{"value": "KENDALL HUDAK", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
            {
                "name": "Brokerage Name",
                "values": [{"value": "RT Specialty", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
            {
                "name": "Name",
                "values": [{"value": "Leavitt's Mortuary", "entity_idx": 1}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
        ],
        "entities": [
            {"type": "Submission", "id": "2945ecd5-36c9-45d9-8d94-d0aba6a58987", "resolved": True},
            {"type": "Primary Insured", "id": "leavitt's mortuary, inc, "},
        ],
        "files": ["ea2ae8db-4ea3-4483-9425-7c821c1adff7"],
        "additional_data": {"finished_do_sub_step": None},
    }


@pytest.fixture
def supplemental_processed_data():
    return {
        "fields": [
            {
                "name": "TOTAL_SALES",
                "fact_subtype_id": "TOTAL_SALES",
                "values": [{"value": 4680000.0, "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            }
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [{"value": "Leavitt's Mortuary", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
            {
                "name": "Address",
                "values": [{"value": "2286 U Road, Strong City KS 66869", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
        ],
        "entities": [{"type": "Primary Insured", "id": "leavitt's mortuary, inc, 2286 u road, strong city ks 66869"}],
        "files": ["ea2ae8db-4ea3-4483-9425-7c821c1adff7"],
        "additional_data": {"finished_do_sub_step": None},
    }


@pytest.fixture
def acord_125_other_named_insured_data():
    return {
        "files": ["1204d0e3-8391-4a7f-b82a-8246b59bad38"],
        "fields": [],
        "entities": [
            {
                "id": "215 star llc 2820 dallas parkway, plano, tx 75093",
                "type": "Business",
                "entity_named_insured": "FIRST_NAMED_INSURED",
            },
            {
                "id": "incline construction inc 133 prospector road, suite 4210, aspen, co 81611",
                "type": "Business",
                "entity_named_insured": "OTHER_NAMED_INSURED",
            },
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [
                    {"value": "215 Star LLC", "entity_idx": 0},
                    {"value": "Incline Construction Inc", "entity_idx": 1},
                ],
                "value_type": "TEXT",
            },
            {
                "name": "Address",
                "values": [
                    {"value": "2820 Dallas Parkway, Plano, TX 75093", "entity_idx": 0},
                    {"value": "133 Prospector Road, Suite 4210, Aspen, CO 81611", "entity_idx": 1},
                ],
                "value_type": "TEXT",
            },
        ],
    }


@pytest.fixture
def supplemental_with_multiple_names_in_fni_name():
    return {
        "fields": [],
        "entity_information": [
            {
                "name": "Name",
                "values": [{"value": "215 Star LLC, Incline Construction Inc", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
            {
                "name": "Address",
                "values": [{"value": "2820 Dallas Parkway, Plano, Texas 75093", "entity_idx": 0}],
                "value_type": "TEXT",
                "fact_subtype_suggestions": [],
            },
        ],
        "entities": [
            {
                "type": "Primary Insured",
                "id": "215 star llc, incline construction inc, 2820 dallas parkway, plano, texas 75093",
            }
        ],
        "files": ["ea2ae8db-4ea3-4483-9425-7c821c1adff7"],
        "additional_data": {"finished_do_sub_step": None},
    }


def _setup_and_consolidate_data(
    submission, email_processed_data, acord_initial_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION
):
    submission.processing_state = SubmissionProcessingState.PROCESSING
    file_1 = file_fixture(
        processing_state=acord_initial_state,
        classification=ClassificationDocumentType.ACORD_125,
        file_type=FileType.ACORD_FORM,
        submission_id=submission.id,
    )
    with open("tests/data/data_consolidation/acord_125_onboarded_file_with_entities.json") as f:
        file = f.read()
        processed_data = json.loads(file)
    file_1.processed_file = ProcessedFile(processed_data=processed_data)
    file_2 = file_fixture(
        processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
        classification=ClassificationDocumentType.EMAIL,
        file_type=FileType.EMAIL,
        submission_id=submission.id,
    )
    file_2.processed_file = ProcessedFile(processed_data=email_processed_data)
    db.session.commit()
    file_handler = FileHandler()
    file_handler.handle(file_2.id, submission.id)
    return file_1, file_2


def test_handle_data_consolidation_email(app_context, submission, mocker, email_processed_data_no_fni):
    mocker.patch("copilot.logic.onboarded_files_transformation._get_fact_subtypes", return_value={})
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        file_1, file_2 = _setup_and_consolidate_data(submission, email_processed_data_no_fni)
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        file_2_processed_data = ProcessedFile.query.filter(ProcessedFile.file_id == file_2.id).first().processed_data
        assert len(file_2_processed_data["entities"]) == 2
        assert file_2_processed_data["entities"][1]["entity_named_insured"] == "FIRST_NAMED_INSURED"
        assert file_2_processed_data["entities"][1]["id"] == "leavitt's mortuary, inc 836 36th st ogden, ut 84403"
        assert len(file_2_processed_data["entity_information"]) == 5
        name_field = file_2_processed_data["entity_information"][2]
        assert name_field["name"] == "Name"
        assert name_field["values"][0]["value"] == "LEAVITT'S MORTUARY, INC"
        assert name_field["values"][0]["entity_idx"] == 1
        assert name_field["values"][0]["external_file_id"] == str(file_1.id)
        address_field = file_2_processed_data["entity_information"][3]
        assert address_field["name"] == "Address"
        assert address_field["values"][0]["value"] == "836 36th ST OGDEN, UT 84403"
        assert address_field["values"][0]["entity_idx"] == 1
        assert address_field["values"][0]["external_file_id"] == str(file_1.id)
        fein_field = file_2_processed_data["entity_information"][4]
        assert fein_field["name"] == "FEIN"
        assert fein_field["values"][0]["value"] == "123456789"
        assert fein_field["values"][0]["entity_idx"] == 1
        assert fein_field["values"][0]["external_file_id"] == str(file_1.id)


@pytest.mark.parametrize(
    "acord_initial_state,acord_end_state",
    [
        (
            FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            FileProcessingState.DATA_CONSOLIDATED,
        ),
        (
            FileProcessingState.WAITING_FOR_COMPLETION,
            FileProcessingState.WAITING_FOR_COMPLETION,
        ),
    ],
)
def test_handle_data_consolidation_email_no_address(
    app_context, submission, mocker, email_processed_data_no_fni_address, acord_initial_state, acord_end_state
):
    mocker.patch("copilot.logic.onboarded_files_transformation._get_fact_subtypes", return_value={})
    score_response = [
        NameScoreResponse(
            name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
            score=1.0,
            reason="For the purpose of this test we want to match the names",
        )
    ]
    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", return_value=score_response)
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        file_1, file_2 = _setup_and_consolidate_data(
            submission, email_processed_data_no_fni_address, acord_initial_state
        )
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == acord_end_state
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        file_2_processed_data = ProcessedFile.query.filter(ProcessedFile.file_id == file_2.id).first().processed_data
        assert len(file_2_processed_data["entities"]) == 2
        assert file_2_processed_data["entities"][1]["id"] == "leavitt's mortuary 836 36th st ogden, ut 84403"
        assert len(file_2_processed_data["entity_information"]) == 5
        name_field = file_2_processed_data["entity_information"][2]
        assert name_field["name"] == "Name"
        assert "external_file_id" not in name_field["values"][0]
        address_field = file_2_processed_data["entity_information"][3]
        assert address_field["name"] == "Address"
        assert address_field["values"][0]["value"] == "836 36th ST OGDEN, UT 84403"
        assert address_field["values"][0]["entity_idx"] == 1
        assert address_field["values"][0]["external_file_id"] == str(file_1.id)
        fein_field = file_2_processed_data["entity_information"][4]
        assert fein_field["name"] == "FEIN"
        assert fein_field["values"][0]["value"] == "123456789"
        assert fein_field["values"][0]["entity_idx"] == 1
        assert fein_field["values"][0]["external_file_id"] == str(file_1.id)


def test_handle_data_consolidation_email_missmatch_names(
    app_context, submission, mocker, email_processed_data_no_fni_address
):
    mocker.patch("copilot.logic.onboarded_files_transformation._get_fact_subtypes", return_value={})
    score_response = [
        NameScoreResponse(
            name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
            score=0.5,
            reason="For the purpose of this test we want the names to mismatch",
        )
    ]
    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", return_value=score_response)
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        email_processed_data_no_fni_address["entity_information"][2]["value"] = "Some other name"
        file_1, file_2 = _setup_and_consolidate_data(submission, email_processed_data_no_fni_address)
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        file_2_processed_data = ProcessedFile.query.filter(ProcessedFile.file_id == file_2.id).first().processed_data
        assert len(file_2_processed_data["entities"]) == 2
        assert len(file_2_processed_data["entity_information"]) == 4
        address_field = file_2_processed_data["entity_information"][3]
        assert address_field["name"] == "Address"
        assert address_field["values"][0]["value"] == ""
        assert address_field["values"][0]["entity_idx"] == 1
        assert "external_file_id" not in address_field["values"][0]


def test_handle_data_consolidation_email_conflicting_identifier(
    app_context, submission, mocker, email_processed_data_no_fni_address_with_fein
):
    mocker.patch("copilot.logic.onboarded_files_transformation._get_fact_subtypes", return_value={})
    score_response = [
        NameScoreResponse(
            name=EntityNameRequest(value="LEAVITT'S MORTUARY, INC"),
            score=1,
            reason="For the purpose of this test we want the names to match",
        )
    ]
    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", return_value=score_response)
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        file_1, file_2 = _setup_and_consolidate_data(submission, email_processed_data_no_fni_address_with_fein)
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        file_2_processed_data = ProcessedFile.query.filter(ProcessedFile.file_id == file_2.id).first().processed_data
        assert len(file_2_processed_data["entities"]) == 2
        assert len(file_2_processed_data["entity_information"]) == 5


def test_handle_data_consolidation_no_acord_fni(
    app_context,
    submission,
    mocker,
    email_processed_data_no_fni_address,
    supplemental_processed_data,
):
    mocker.patch("copilot.logic.onboarded_files_transformation._get_fact_subtypes", return_value={})
    score_response = [
        NameScoreResponse(
            name=EntityNameRequest(value="Leavitt's Mortuary"),
            score=1,
            reason="For the purpose of this test we want the names match",
        )
    ]
    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", return_value=score_response)
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            classification=ClassificationDocumentType.EMAIL,
            file_type=FileType.EMAIL,
            submission_id=submission.id,
        )
        file_1.processed_file = ProcessedFile(processed_data=email_processed_data_no_fni_address)
        file_2 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            classification=ClassificationDocumentType.PRACTICE_SUPPLEMENTAL_APPLICATION_PDF,
            file_type=FileType.SUPPLEMENTAL_FORM,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data=supplemental_processed_data)
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_1.id, submission.id)
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        file_1_processed_data = ProcessedFile.query.filter(ProcessedFile.file_id == file_1.id).first().processed_data
        assert len(file_1_processed_data["entities"]) == 2
        assert file_1_processed_data["entities"][1]["type"] == "Primary Insured"
        assert file_1_processed_data["entities"][1]["id"] == "leavitt's mortuary 2286 u road, strong city ks 66869"
        assert len(file_1_processed_data["entity_information"]) == 4
        address_field = file_1_processed_data["entity_information"][3]
        assert address_field["values"][0]["value"] == "2286 U Road, Strong City KS 66869"
        assert address_field["values"][0]["entity_idx"] == 1
        assert address_field["values"][0]["external_file_id"] == str(file_2.id)


def test_handle_data_consolidation_multiple_names_in_supplemental(
    app_context,
    submission,
    mocker,
    acord_125_other_named_insured_data,
    supplemental_with_multiple_names_in_fni_name,
):
    mocker.patch("copilot.logic.onboarded_files_transformation._get_fact_subtypes", return_value={})
    score_response = [
        NameScoreResponse(
            name=EntityNameRequest(value="215 Star LLC"),
            score=1,
            reason="For the purpose of this test we want the names match",
        ),
        NameScoreResponse(
            name=EntityNameRequest(value="Incline Construction Inc"),
            score=1,
            reason="For the purpose of this test we want the names match",
        ),
    ]
    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", return_value=score_response)
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            classification=ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
            file_type=FileType.SUPPLEMENTAL_FORM,
            submission_id=submission.id,
        )
        file_1.processed_file = ProcessedFile(processed_data=supplemental_with_multiple_names_in_fni_name)
        file_2 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            classification=ClassificationDocumentType.ACORD_125,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data=acord_125_other_named_insured_data)
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_1.id, submission.id)
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        file_1_processed_data = ProcessedFile.query.filter(ProcessedFile.file_id == file_1.id).first().processed_data
        assert len(file_1_processed_data["entities"]) == 1
        assert file_1_processed_data["entities"][0]["type"] == "Primary Insured"
        assert file_1_processed_data["entities"][0]["id"] == "215 star llc 2820 dallas parkway, plano, texas 75093"
        assert len(file_1_processed_data["entity_information"]) == 2
        assert file_1_processed_data["entity_information"][0]["values"][0]["value"] == "215 Star LLC"
        assert (
            file_1_processed_data["entity_information"][1]["values"][0]["value"]
            == "2820 Dallas Parkway, Plano, Texas 75093"
        )


def test_handle_data_consolidation_update_type_in_supplemental(
    app_context,
    submission,
    mocker,
    acord_125_other_named_insured_data,
    supplemental_with_multiple_names_in_fni_name,
):
    mocker.patch("copilot.logic.onboarded_files_transformation._get_fact_subtypes", return_value={})
    score_response = [
        NameScoreResponse(
            name=EntityNameRequest(value="Incline Construction Inc"),
            score=1,
            reason="For the purpose of this test we want the names match",
        )
    ]
    mocker.patch("copilot.clients.ers_v3.ERSClientV3.get_entity_names_score", return_value=score_response)
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        submission.processing_state = SubmissionProcessingState.PROCESSING
        file_1 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            classification=ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
            file_type=FileType.SUPPLEMENTAL_FORM,
            submission_id=submission.id,
        )
        file_1.processed_file = ProcessedFile(processed_data=supplemental_with_multiple_names_in_fni_name)
        file_2 = file_fixture(
            processing_state=FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION,
            classification=ClassificationDocumentType.ACORD_125,
            file_type=FileType.ACORD_FORM,
            submission_id=submission.id,
        )
        file_2.processed_file = ProcessedFile(processed_data=acord_125_other_named_insured_data)
        db.session.commit()
        file_handler = FileHandler()
        file_handler.handle(file_1.id, submission.id)
        file_1_processed_data = ProcessedFile.query.filter(ProcessedFile.file_id == file_1.id).first().processed_data
        assert len(file_1_processed_data["entities"]) == 1
        assert file_1_processed_data["entities"][0]["type"] == "Business"
        assert file_1_processed_data["entities"][0]["entity_named_insured"] == "OTHER_NAMED_INSURED"


def test_handle_data_consolidation_empty_file(app_context, submission, mocker):
    mocker.patch("copilot.logic.onboarded_files_transformation._get_fact_subtypes", return_value={})
    with (
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_file_processing_finished_event"
        ) as processing_finished_event,
        patch(
            "copilot.kalepa_domain_events.kalepa_events_handler.KalepaEventsHandler.send_submission_data_consolidated"
        ) as data_consolidated_event,
    ):
        file_1, file_2 = _setup_and_consolidate_data(submission, LeanOnboardedFileSchema().dump(OnboardedFile()))
        assert not processing_finished_event.called
        assert data_consolidated_event.called
        assert File.query.get(file_1.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        assert File.query.get(file_2.id).processing_state == FileProcessingState.DATA_CONSOLIDATED
        file_2_processed_data = ProcessedFile.query.filter(ProcessedFile.file_id == file_2.id).first().processed_data
        assert LeanOnboardedFileSchema().load(file_2_processed_data).is_empty


emd = {
    "entity_information": [
        {
            "name": "Name",
            "values": [{"value": "abc", "entity_idx": 1}, {"value": "qwe", "entity_idx": 0}],
            "value_type": "TEXT",
        },
        {
            "name": "Address",
            "values": [{"value": "ad1", "entity_idx": 0}, {"value": None, "entity_idx": 1}],
            "value_type": "TEXT",
        },
    ]
}
brd_full = {
    "resolution_data": [
        {"entity_idx": 0, "requested_name": "qwe", "requested_address": "ad1"},
        {"entity_idx": 1, "requested_name": "abc", "requested_address": None},
    ]
}
brd_partial = {
    "resolution_data": [
        {"entity_idx": 0, "requested_name": "qwe", "requested_address": "ad1"},
    ]
}

duplicate_emd = {
    "entity_information": [
        {
            "name": "Name",
            "values": [
                {"value": "Bestmark Express Inc", "entity_idx": 0},
                {"value": "Bestmark Express Inc", "entity_idx": 1},
            ],
            "value_type": "TEXT",
        },
        {
            "name": "Address",
            "values": [
                {"value": "2286 U Road, Strong City KS 66869", "entity_idx": 0},
                {"value": "2286 U Road, Strong City KS 66869", "entity_idx": 1},
            ],
            "value_type": "TEXT",
        },
    ]
}

not_duplicated_brd = {
    "resolution_data": [
        {
            "entity_id": "c867734c-ad58-47da-957e-5ec2094de788",
            "entity_idx": 0,
            "requested_name": "Bestmark Express Inc",
            "requested_address": "2286 U Road, Strong City KS 66869",
        }
    ]
}
brd_full_with_submission_entity = {
    "resolution_data": [
        {"entity_idx": 0, "requested_name": "qwe", "requested_address": "ad1"},
        {"entity_idx": 2, "requested_name": "abc", "requested_address": None},
    ]
}


def test_file_requires_autoconfirmation(app_context):
    # False
    assert FileHandler()._file_requires_autoconfirmation(None) is False
    pf = ProcessedFile(file_id=uuid4(), entity_mapped_data=emd, business_resolution_data=brd_full)
    assert FileHandler()._file_requires_autoconfirmation(pf) is False
    pf = ProcessedFile(file_id=uuid4(), entity_mapped_data=None, business_resolution_data=brd_full)
    assert FileHandler()._file_requires_autoconfirmation(pf) is False

    # True
    pf = ProcessedFile(file_id=uuid4(), entity_mapped_data=emd, business_resolution_data=None)
    assert FileHandler()._file_requires_autoconfirmation(pf) is True
    pf = ProcessedFile(file_id=uuid4(), entity_mapped_data=emd, business_resolution_data=brd_partial)
    assert FileHandler()._file_requires_autoconfirmation(pf) is True

    # Updates brd when EM is changed to empty
    pf = ProcessedFile(
        file_id=uuid4(), entity_mapped_data={"entity_information": []}, business_resolution_data=brd_full
    )
    assert FileHandler()._file_requires_autoconfirmation(pf) is False
    assert pf.business_resolution_data is None

    # Handle duplicated EM
    pf = ProcessedFile(file_id=uuid4(), entity_mapped_data=duplicate_emd, business_resolution_data=not_duplicated_brd)
    assert FileHandler()._file_requires_autoconfirmation(pf) is True

    # Handle removed submission entity
    pf = ProcessedFile(
        file_id=uuid4(), entity_mapped_data=emd, business_resolution_data=brd_full_with_submission_entity
    )
    assert FileHandler()._file_requires_autoconfirmation(pf) is True


def test_file_ignored(app_context, submission):
    parent_file = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.MERGED,
        file_type=FileType.MERGED,
        submission_id=submission.id,
    )
    child_file_1 = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
        file_type=FileType.SUPPLEMENTAL_FORM,
        submission_id=submission.id,
        parent_file_id=parent_file.id,
    )
    child_file_2 = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.MERGED,
        file_type=FileType.MERGED,
        submission_id=submission.id,
        parent_file_id=parent_file.id,
    )
    child_file_3 = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.ACORD_125,
        file_type=FileType.ACORD_FORM,
        submission_id=submission.id,
        parent_file_id=child_file_2.id,
    )
    db.session.commit()
    FileHandler().handle_file_ignored(submission, parent_file)
    assert File.query.get(parent_file.id).processing_state == FileProcessingState.IGNORED
    assert File.query.get(child_file_1.id).processing_state == FileProcessingState.IGNORED
    assert File.query.get(child_file_2.id).processing_state == FileProcessingState.IGNORED
    assert File.query.get(child_file_3.id).processing_state == FileProcessingState.IGNORED


def test_file_ignore_replaced_file(app_context, submission):
    parent_file = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.MERGED,
        file_type=FileType.MERGED,
        submission_id=submission.id,
    )
    child_file_1 = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.SUPPLEMENTAL_APPLICATION_PDF,
        file_type=FileType.SUPPLEMENTAL_FORM,
        submission_id=submission.id,
        parent_file_id=parent_file.id,
    )
    child_file_2 = file_fixture(
        processing_state=FileProcessingState.REPLACED,
        classification=ClassificationDocumentType.ACORD_125,
        file_type=FileType.ACORD_FORM,
        submission_id=submission.id,
        parent_file_id=parent_file.id,
    )
    db.session.commit()
    with (
        pytest.raises(BadRequest),
        patch("copilot.logic.pds.file_handler.FileHandler.remove_file_extracted_data") as remove_data_mock,
    ):
        FileHandler().handle_file_ignored(submission, parent_file)
        assert not remove_data_mock.called
    assert File.query.get(parent_file.id).processing_state == FileProcessingState.CLASSIFIED
    assert File.query.get(child_file_1.id).processing_state == FileProcessingState.CLASSIFIED
    assert File.query.get(child_file_2.id).processing_state == FileProcessingState.REPLACED


def test_file_deleted(app_context, submission):
    replacing_file = file_fixture(
        processing_state=FileProcessingState.CLASSIFIED,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        file_type=FileType.SOV,
        submission_id=submission.id,
    )
    replaced_file = file_fixture(
        processing_state=FileProcessingState.REPLACED,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        file_type=FileType.SOV,
        submission_id=submission.id,
        replaced_by_file_ids=[replacing_file.id],
    )
    file_fixture(
        processing_state=FileProcessingState.WAITING_FOR_COMPLETION,
        classification=ClassificationDocumentType.ACORD_130,
        file_type=FileType.ACORD_FORM,
        submission_id=submission.id,
    )
    db.session.commit()
    FileHandler().handle_file_deleted(submission, replacing_file)
    assert not File.query.get(replacing_file.id)
    replaced_file = File.query.get(replaced_file.id)
    assert replaced_file.processing_state == FileProcessingState.IGNORED
    assert replaced_file.replaced_by_file_ids is None


def test_handle_waiting_for_facts_and_suggestions_resolution(app_context, submission, mocker):
    facts_matching_sf_mock = mocker.patch(
        "copilot.logic.pds.file_handler.current_app.workflows_client.invoke_copilot_workers_facts_matching_and_normalization"
    )

    file = file_fixture(
        processing_state=FileProcessingState.WAITING_FOR_FACTS_AND_SUGGESTIONS_RESOLUTION,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        submission_id=submission.id,
        organization_id=submission.organization_id,
    )
    db.session.commit()

    file_handler = FileHandler()
    file_handler.handle(file.id, submission.id)

    facts_matching_sf_mock.assert_called_once()

    assert facts_matching_sf_mock.call_args.kwargs == {
        "submission_id": file.submission_id,
        "file_id": file.id,
        "file_type": FileType.SOV.value,
        "s3_key": file.s3_key,
        "organization_id": submission.organization_id,
        "classification": file.classification,
        "parent_file_id": file.parent_file_id,
        "parent_classification": None,
        "parent_file_type": None,
    }


# TODO(ENG-27883): Remove the test and rename the other one
def test_handle_facts_and_suggestions_resolution_finished_ff_off(app_context, submission, mocker):
    with patch("copilot.clients.lambdas.LambdaClient.invoke_file_insights_async") as insights_mock:
        file = file_fixture(
            processing_state=FileProcessingState.FACTS_AND_SUGGESTIONS_RESOLUTION_FINISHED,
            file_type=FileType.SOV,
            classification=ClassificationDocumentType.SOV_SPREADSHEET,
            submission_id=submission.id,
            organization_id=submission.organization_id,
        )
        db.session.commit()

        file_handler = FileHandler()
        file_handler.handle(file.id, submission.id)

        insights_mock.assert_not_called()
        assert file.processing_state == FileProcessingState.PROCESSED


@pytest.fixture
def processed_data() -> dict:
    return {
        "files": [],
        "fields": [
            {
                "name": "Current assets",
                "values": [
                    {
                        "value": (
                            '{"units": "USD", "interval": "ANNUAL", "times": ["2023-12-31T00:00:00"], "values":'
                            " [221611.0]}"
                        ),
                        "entity_idx": 0,
                    }
                ],
                "value_type": "TEXT",
                "display_as_fact": True,
                "fact_subtype_id": "CURRENT_ASSETS",
            },
        ],
        "entities": [
            {
                "id": "leavitt's mortuary, inc",
                "type": "Submission",
            }
        ],
        "entity_information": [
            {
                "name": "Name",
                "values": [{"value": "LEAVITT'S MORTUARY, INC", "file_idx": 0, "entity_idx": 0}],
                "value_type": "TEXT",
            },
            {
                "name": "Description",
                "values": [{"value": "Pre existing description", "file_idx": 0, "entity_idx": 0}],
                "value_type": "TEXT",
            },
        ],
    }


def test_enrich_processed_data_with_submission_level_extracted_data(app_context, mocker, submission, processed_data):
    facts_matching_sf_mock = mocker.patch(
        "copilot.logic.pds.file_handler.current_app.workflows_client.invoke_copilot_workers_facts_matching_and_normalization"
    )
    mocker.patch("copilot.models.files.File.is_document_ingestion", return_value=True)
    file = file_fixture(
        processing_state=FileProcessingState.WAITING_FOR_FACTS_AND_SUGGESTIONS_RESOLUTION,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        submission_id=submission.id,
        organization_id=submission.organization_id,
    )
    processed_data["files"] = [str(file.id)]
    db.session.commit()

    processed_file_fixture(file_id=file.id, processed_data=processed_data)

    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=file.id,
        field=EntityInformation.DESCRIPTION,
        value="Some generated description",
        source_details=SourceDetails.GENERATED,
    )
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=file.id,
        field=EntityInformation.DESCRIPTION,
        value="Some description file content raw",
        source_details=SourceDetails.FILE_CONTENT_RAW,
    )
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=file.id,
        field=EntityInformation.NAICS_CODES,
        value=json.dumps({NaicsCode.NAICS_238110: 0.95, NaicsCode.NAICS_111120: 0.58}),
        source_details=SourceDetails.GENERATED,
    )
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=file.id,
        field=EntityInformation.COVERAGES_DETAILS,
        value=json.dumps(
            CoverageDetailsSchema().dumps(
                [
                    CoverageDetails(coverage_name=CoverageName.Liability, coverage_type=CoverageType.PRIMARY),
                    CoverageDetails(coverage_name=CoverageName.Property, coverage_type=CoverageType.PRIMARY),
                ],
                many=True,
            )
        ),
        source_details=SourceDetails.GENERATED,
    )
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=file.id,
        field=EntityInformation.POLICY_END_DATE,
        value="2026-04-28 00:00:00",
    )
    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=file.id,
        field=EntityInformation.POLICY_EFFECTIVE_START_DATE,
        value="2025-04-28 00:00:00",
    )

    file_handler = FileHandler()
    file_handler.handle(file.id, submission.id)

    assert facts_matching_sf_mock.called_once()

    assert file.processed_file.processed_data["entity_information"][1]["name"] == EntityInformation.DESCRIPTION.value
    assert (
        file.processed_file.processed_data["entity_information"][1]["values"][0]["value"] == "Pre existing description"
    )
    assert file.processed_file.processed_data["entity_information"][2]["name"] == EntityInformation.NAICS_CODES.value
    assert (
        file.processed_file.processed_data["entity_information"][3]["name"] == EntityInformation.COVERAGES_DETAILS.value
    )
    coverages_details_value = file.processed_file.processed_data["entity_information"][3]["values"][0]["value"]
    assert file.processed_file.processed_data["entity_information"][3]["values"][0]["value"]
    coverages_details = CoverageDetailsSchema().loads(json.loads(coverages_details_value), many=True)
    assert len(coverages_details) == 2
    assert coverages_details[0].coverage_name == CoverageName.Liability
    assert coverages_details[0].coverage_type == "primary"
    assert coverages_details[1].coverage_name == CoverageName.Property
    assert coverages_details[1].coverage_type == "primary"

    assert (
        file.processed_file.processed_data["entity_information"][4]["name"]
        == EntityInformation.POLICY_EFFECTIVE_START_DATE.value
    )
    assert (
        file.processed_file.processed_data["entity_information"][5]["name"] == EntityInformation.POLICY_END_DATE.value
    )


def test_enrich_processed_data_with_submission_level_extracted_data_2(app_context, mocker, submission, processed_data):
    facts_matching_sf_mock = mocker.patch(
        "copilot.logic.pds.file_handler.current_app.workflows_client.invoke_copilot_workers_facts_matching_and_normalization"
    )
    mocker.patch("copilot.models.files.File.is_document_ingestion", return_value=True)
    file = file_fixture(
        processing_state=FileProcessingState.WAITING_FOR_FACTS_AND_SUGGESTIONS_RESOLUTION,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        submission_id=submission.id,
        organization_id=submission.organization_id,
    )
    processed_data["files"] = [str(file.id)]
    db.session.commit()

    processed_file_fixture(file_id=file.id, processed_data=processed_data)

    submission_level_extracted_data_fixture(
        submission_id=submission.id,
        file_id=file.id,
        field=EntityInformation.NAICS_CODES,
        value=json.dumps([NaicsCode.NAICS_238110]),
        source_details=SourceDetails.GENERATED,
    )

    file_handler = FileHandler()
    file_handler.handle(file.id, submission.id)
    assert facts_matching_sf_mock.called_once()
    assert file.processed_file.processed_data["entity_information"][2]["name"] == EntityInformation.NAICS_CODES.value


def test_handle_file_replaced(app_context, submission):
    replacing_file = file_fixture(
        id=uuid4(),
        is_internal=True,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
    )
    file_to_replace = file_fixture(
        id=uuid4(),
        is_internal=True,
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
    )
    original_file = file_fixture(
        file_type=FileType.SOV,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        replaced_by_file_ids=[file_to_replace.id],
    )
    db.session.commit()
    replace_map = {
        file_to_replace.id: [original_file],
    }

    FileHandler.handle_file_replaced(
        submission=submission,
        replacing_file=replacing_file,
        file_to_replace=original_file,
        file_replace_map=replace_map,
    )
    db.session.commit()
    assert set(File.query.get(original_file.id).replaced_by_file_ids) == {replacing_file.id, file_to_replace.id}

    FileHandler.handle_file_replaced(
        submission=submission,
        replacing_file=replacing_file,
        file_to_replace=file_to_replace,
        file_replace_map=replace_map,
    )
    db.session.commit()
    assert File.query.get(original_file.id).replaced_by_file_ids == [replacing_file.id]
    assert File.query.get(file_to_replace.id).is_deleted is True


def test_move_files_through_di_queue(app_context, mocker, submission):
    mocker.patch("copilot.logic.pds.file_handler.clean_up")
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(is_internal_machine_user=True, has_submission_permission=lambda x, y: True, id=1),
    )
    file_1 = file_fixture(
        processing_state=FileProcessingState.PROCESSED,
        classification=ClassificationDocumentType.SOV_SPREADSHEET,
        file_type=FileType.SOV,
        submission_id=submission.id,
    )
    file_1.processed_file = ProcessedFile(processed_data={}, business_resolution_data=EXAMPLE_RESOLUTION_DATA)
    db.session.commit()
    file_handler = FileHandler()
    with patch("copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled", return_value=""):
        file_handler.handle(file_1.id, submission.id)

    # If there are no files for that org, we proceed as usual, moving the SOV to BC
    assert file_1.processing_state == FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION

    with patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled", return_value="SUPPLEMENTAL, EMAIL"
    ):
        file_handler.handle(file_1.id, submission.id)

    # If there are files to go through DI, but they do not match SOV, again proceed as usual
    assert file_1.processing_state == FileProcessingState.WAITING_FOR_BUSINESS_CONFIRMATION

    with patch(
        "copilot.clients.feature_flags.FeatureFlagsClient.is_feature_enabled", return_value="SUPPLEMENTAL, EMAIL, SOV"
    ):
        file_handler.handle(file_1.id, submission.id)

    # Finally, if the SOV is in the list, we should move it to WAITING_FOR_HUMAN_INPUT
    assert file_1.processing_state == FileProcessingState.WAITING_FOR_HUMAN_INPUT
