from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from datetime import datetime
from unittest.mock import <PERSON><PERSON>ock
from uuid import UUID
import enum
import uuid

from static_common.enums.organization import ExistingOrganizations, OrganizationGroups
from static_common.enums.underwriters import SubmissionUserSource
from static_common.models.paragon import ParagonAgencyConfig
from werkzeug.exceptions import Conflict
import fakeredis
import flask
import pytest
import redis_lock

from copilot.constants import PARAGON_PSP_EMAIL, PARAGON_WC_EMAIL
from copilot.logic.users.admiral_underwriter_assigner import AdmiralUnderwriterAssigner
from copilot.logic.users.aig_underwriter_assigner import AIGUnderwriterAssigner
from copilot.logic.users.bowhead_underwriter_assigner import BowheadUnderwriterAssigner
from copilot.logic.users.conifer_underwriter_assigner import ConiferUnderwriterAssigner
from copilot.logic.users.crc_underwriter_assigner import CrcUnderwriterAssigner
from copilot.logic.users.errors import (
    CrossOrgUserAssignmentError,
    MultipleUsersAssignmentError,
    User<PERSON>lreadyAssignedError,
)
from copilot.logic.users.group_underwriter_assigner import GroupUnderwriterAssigner
from copilot.logic.users.k2ins_underwriter_assigner import (
    AssignCaseRule,
    Email,
    EmailData,
    K2AssignCase,
    K2UnderwriterAssigner,
)
from copilot.logic.users.nec_specialty_underwriter_assigner import (
    NecSpecialtyUnderwriterAssigner,
)
from copilot.logic.users.paragon_psp_underwriter_assigner import (
    ParagonPSPUnderwriterAssigner,
)
from copilot.logic.users.paragon_underwriter_assigner import ParagonUnderwriterAssigner
from copilot.logic.users.paragon_wc_underwriter_assigner import (
    ParagonWCUnderwriterAssigner,
)
from copilot.logic.users.secura_underwriter_assigner import SecuraUnderwriterAssigner
from copilot.logic.users.underwriter_assigner import (
    BaseUnderwriterAssigner,
    UnderwriterAssignerConfig,
)
from copilot.models import ReportPermission, db
from copilot.models.admiral_uw_mappings import AdmiralAssignmentLog
from copilot.models.paragon_wc_uw_mapping import ParagonWCUWMapping
from copilot.models.reports import (
    ReportAlert,
    ReportShadowDependency,
    Submission,
    SubmissionUser,
)
from copilot.models.secura_uw_mappings import SecuraUWMapping
from copilot.models.types import (
    AlertType,
    CoverageType,
    PermissionType,
    ReportShadowType,
)
from copilot.v3.controllers.submissions import update_submission
from tests.integration.coverages.fixtures import set_up_current_user
from tests.integration.factories import (
    admiral_uw_mapping_assignment_fixture,
    admiral_uw_mapping_fixture,
    aig_uw_mapping_fixture,
    boss_uw_mapping_fixture,
    bowhead_uw_mapping_fixture,
    broker_fixture,
    brokerage_fixture,
    conifer_uw_mapping_fixture,
    coverage_fixture,
    email_fixture,
    organization_fixture,
    paragon_underwriter_fixture,
    paragon_wc_uw_mapping_fixture,
    report_bundle_fixture,
    report_fixture,
    report_permission_fixture,
    report_shadow_dependency_fixture,
    report_with_submissions_fixture,
    routing_rule_fixture,
    secura_uw_mapping_fixture,
    settings_fixture,
    submission_coverage_fixture,
    submission_fixture,
    submission_user_fixture,
    user_fixture,
    user_group_fixture,
)
from tests.integration.utils import AnonObj
from tests.integration.v3.controllers.test_onboarded_files import always_has_permission
from tests.unit.logic.pds.test_data_consolidation import submission


def test_base_assigner_happy_path(app_context, set_up_current_user):
    organization_fixture()
    user = user_fixture(id=1)
    report = report_with_submissions_fixture()
    db.session.commit()

    assert SubmissionUser.query.count() == 0

    su = BaseUnderwriterAssigner().assign_underwriters([user.id], report.submission)[0]

    assert SubmissionUser.query.count() == 1
    assert su.user_id == user.id
    assert SubmissionUser.query.first() == su


def test_base_assigner_cross_user_assignment(app_context):
    organization_fixture()
    user_fixture()
    cross_org_user = user_fixture(id=2, cross_organization_access=True)
    report = report_with_submissions_fixture()
    db.session.commit()

    with pytest.raises(CrossOrgUserAssignmentError):
        BaseUnderwriterAssigner().assign_underwriters([cross_org_user.id], report.submission)


def test_base_assigner_already_assigned(app_context):
    organization_fixture()
    user = user_fixture()
    report = report_with_submissions_fixture()
    submission_user_fixture(user_id=user.id, submission_id=report.submission.id)
    db.session.commit()

    with pytest.raises(UserAlreadyAssignedError):
        BaseUnderwriterAssigner().assign_underwriters([user.id], report.submission)


def test_base_assigner_limit_to_one_assign_multiple(app_context):
    org = organization_fixture()
    settings = settings_fixture(organization_id=org.id, allow_multiple_assigned_underwriters_per_submission=False)
    org.settings = settings
    user_1 = user_fixture()
    user_2 = user_fixture(id=2)
    report = report_with_submissions_fixture()
    db.session.commit()

    with pytest.raises(MultipleUsersAssignmentError):
        BaseUnderwriterAssigner().assign_underwriters(
            [user_1.id, user_2.id], report.submission, source=SubmissionUserSource.EMAIL
        )


def test_base_assigner_limit_to_one_assign_one(app_context, set_up_current_user):
    org = organization_fixture()
    settings = settings_fixture(organization_id=org.id, allow_multiple_assigned_underwriters_per_submission=False)
    org.settings = settings
    user_1 = user_fixture()
    user_2 = user_fixture(id=2)
    user_3 = user_fixture(id=3)
    report = report_with_submissions_fixture()
    submission_user_fixture(user_id=user_2.id, submission_id=report.submission.id)
    submission_user_fixture(user_id=user_3.id, submission_id=report.submission.id)
    db.session.commit()

    su = BaseUnderwriterAssigner().assign_underwriters(
        [user_1.id], report.submission, source=SubmissionUserSource.EMAIL
    )[0]

    assert SubmissionUser.query.count() == 1
    assert su.user_id == user_1.id
    assert SubmissionUser.query.first() == su


@pytest.mark.parametrize(
    "existing_source, new_source, expected_source",
    [
        (SubmissionUserSource.AUTO, SubmissionUserSource.EMAIL, SubmissionUserSource.EMAIL),
        (SubmissionUserSource.MANUAL, SubmissionUserSource.EMAIL, SubmissionUserSource.MANUAL),
        (None, SubmissionUserSource.AUTO, SubmissionUserSource.AUTO),
    ],
)
def test_base_assigner_already_assigned_with_source_precedence(
    app_context, existing_source, new_source, expected_source
):
    organization_fixture()
    user = user_fixture()
    report = report_with_submissions_fixture()
    submission_user_fixture(user_id=user.id, submission_id=report.submission.id, source=existing_source)
    db.session.commit()

    BaseUnderwriterAssigner().assign_underwriters(
        [user.id],
        report.submission,
        config=UnderwriterAssignerConfig(already_assigned_error=False),
        source=new_source,
    )

    su = SubmissionUser.query.first()
    assert su.source == expected_source


def test_base_delete_other_assigned(app_context, set_up_current_user):
    organization_fixture()
    user_1 = user_fixture(id=1)
    user_2 = user_fixture(id=2)
    report = report_with_submissions_fixture()
    submission_user_fixture(user_id=user_2.id, submission_id=report.submission.id)
    submission_user_fixture(user_id=user_1.id, submission_id=report.submission.id)
    report_permission_fixture(grantee_user_id=user_2.id, report_id=report.id)
    db.session.commit()

    assert SubmissionUser.query.count() == 2
    # 1 - OWNER when creating report, 2 - added manually
    assert ReportPermission.query.count() == 2

    su = BaseUnderwriterAssigner().assign_underwriters(
        [user_1.id],
        report.submission,
        config=UnderwriterAssignerConfig(delete_other_assigned=True, already_assigned_error=False),
    )[0]

    assert SubmissionUser.query.count() == 1
    assert su.user_id == user_1.id
    assert SubmissionUser.query.first() == su
    assert ReportPermission.query.count() == 1
    permission = ReportPermission.query.first()
    assert permission.grantee_user_id == user_1.id


###############################
#          Conifer            #
###############################


def test_conifer_uw_assigner(app_context, set_up_current_user, mocker):
    organization_fixture(id=58)
    user = user_fixture(organization_id=58)
    user_2 = user_fixture(id=2, organization_id=58)
    user_3 = user_fixture(id=3, organization_id=58)
    default_main_st_user = user_fixture(id=5700, organization_id=58)
    thc1 = user_fixture(id=5698, organization_id=58)
    thc2 = user_fixture(id=5699, organization_id=58)
    thc3 = user_fixture(id=5701, organization_id=58)
    il_user = user_fixture(id=5704, organization_id=58)
    settings_fixture(organization_id=58, org_groups=["THC", "Hospitality", "Main St"])
    brokerage = brokerage_fixture(organization_id=58)
    db.session.commit()
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=58)
    broker_2 = broker_fixture(brokerage_id=brokerage.id, organization_id=58, name="Conifer Test", email="<EMAIL>")
    broker_3 = broker_fixture(brokerage_id=brokerage.id, organization_id=58, name="Conifer Test2", email="<EMAIL>")
    db.session.commit()

    conifer_uw_mapping_fixture(broker_id=broker.id, user_id=user.id)
    conifer_uw_mapping_fixture(user_id=user_2.id, broker_email="<EMAIL>")
    conifer_uw_mapping_fixture(user_id=user_3.id, brokerage_domain="xx.com")
    report = report_with_submissions_fixture(broker_id=broker.id, brokerage_id=brokerage.id, organization_id=58)
    db.session.commit()

    assert SubmissionUser.query.count() == 0

    # no org group set
    ConiferUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 0

    # by broker id
    report.org_group = "Hospitality"
    db.session.commit()
    ConiferUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1
    assert SubmissionUser.query.first().user_id == user.id

    # by email
    broker_2.aliases = ["<EMAIL>"]
    report.submission.broker_id = broker_2.id
    db.session.commit()
    ConiferUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1
    assert SubmissionUser.query.first().user_id == user_2.id

    # by email domains
    SubmissionUser.query.delete()
    report.submission.broker_id = broker_3.id
    db.session.commit()
    ConiferUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1
    assert SubmissionUser.query.first().user_id == user_3.id

    # by email domain of cc
    SubmissionUser.query.delete()
    report.submission.broker_id = None
    report.submission.brokerage_contact_id = broker_3.id
    db.session.commit()
    ConiferUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1
    assert SubmissionUser.query.first().user_id == user_3.id

    # by email domain of the brokerage
    SubmissionUser.query.delete()
    report.submission.broker_id = None
    report.submission.brokerage_contact_id = None
    brokerage.domains = ["xx.com"]
    db.session.commit()
    ConiferUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1
    assert SubmissionUser.query.first().user_id == user_3.id

    # by default
    SubmissionUser.query.delete()
    report.org_group = "Main St"
    report.submission.brokerage_id = None
    brokerage.domains = []
    db.session.commit()
    ConiferUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1
    assert SubmissionUser.query.first().user_id == default_main_st_user.id

    # by FNI state
    SubmissionUser.query.delete()
    report.submission.broker = None
    brokerage.domains = []
    db.session.commit()
    update_submission(str(report.submission.id), {"fni_state": "IL"})
    assert SubmissionUser.query.count() == 1
    assert SubmissionUser.query.first().user_id == il_user.id

    # by THC
    SubmissionUser.query.delete()
    report.org_group = "THC"
    db.session.commit()
    ConiferUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 3


###############################
#         NEC Specialty       #
###############################
def test_nec_specialty_underwriter_assigner_happy_path(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.NecSpecialty.value)
    user_1 = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id)
    user_2 = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id)
    report = report_with_submissions_fixture(organization_id=organization.id)
    db.session.commit()

    assert SubmissionUser.query.count() == 0

    submission_users = NecSpecialtyUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 2
    assert len(submission_users) == 2
    assert any(u.user_id == user_1.id for u in submission_users)
    assert any(u.user_id == user_2.id for u in submission_users)


###############################
#         NSM                 #
###############################
def test_nsm_underwriter_assigner(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.NSM.value)
    user = user_fixture(id=1, organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id)
    user_group.users.append(user)
    report = report_with_submissions_fixture(organization_id=organization.id)
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(user_group.report_permissions) == 0

    submission_users = GroupUnderwriterAssigner().assign_underwriters([user.id], report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert len(user_group.report_permissions) == 1
    assert user_group.report_permissions[0].permission_type == PermissionType.EDITOR


###############################
#         PARAGON             #
###############################
def test_paragon_auto_assigned_underwriter(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="Ally Auto")
    user_group.users.append(user)
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account="<EMAIL>")
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    submission_users = ParagonUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    # Should be shared with group of the user as well.
    assert len(report.report_permissions) == 2


def test_paragon_auto_assigned_underwriter_groups_conflict(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            is_internal_machine_user=False,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="E&S Casualty")
    user_group.users.append(user)
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account="<EMAIL>")
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    with pytest.raises(Conflict) as ex_info:
        ParagonUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert ex_info.value.code == 409


def test_paragon_psp_uw_assigner_renewal(app_context, set_up_current_user):
    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id)
    user2 = user_fixture(id=4777, email="<EMAIL>", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="PSP E3")
    user_group.users.append(user)
    user_group.users.append(user2)
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account="<EMAIL>")
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    report.submission.is_renewal = True
    report.submission.assigned_underwriters = [SubmissionUser(user_id=1, submission_id=report.submission.id)]
    db.session.commit()

    assert SubmissionUser.query.count() == 1
    assert report.submission.assigned_underwriters[0].user_id == 1
    assert len(report.report_permissions) == 1

    submission_users = ParagonPSPUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert report.submission.assigned_underwriters[0].user_id == 4777
    assert len(submission_users) == 1
    # Should be shared with group of the user as well.
    assert len(report.report_permissions) == 2


def test_paragon_psp_uw_assigner_sub_without_broker(app_context, set_up_current_user):
    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="PSP E3")
    user_group.users.append(user)
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account="<EMAIL>")
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    report.submission.assigned_underwriters = [SubmissionUser(user_id=1, submission_id=report.submission.id)]
    db.session.commit()

    assert SubmissionUser.query.count() == 1
    assert report.submission.assigned_underwriters[0].user_id == 1
    assert len(report.report_permissions) == 1

    # Nothing should change, since it's not renewal.
    submission_users = ParagonPSPUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert report.submission.assigned_underwriters[0].user_id == 1
    assert len(submission_users) == 0


@pytest.mark.parametrize(
    "is_broker_blocked",
    [
        (True),
        (False),
    ],
)
def test_paragon_psp_uw_assigner_should_use_broker_uw_mapping(
    app_context, set_up_current_user, mocker, is_broker_blocked
):
    mocker.patch(
        "copilot.logic.users.paragon_underwriter_assigner.get_paragon_agency_config_by_broker_email",
        return_value=ParagonAgencyConfig(
            agency_location_guid="********-1111-1111-1111-************",
            underwriter_guid="*************-2222-2222-************",
            underwriter_assistant_guid="*************-3333-3333-************",
            blocked=is_broker_blocked,
        ),
    )
    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id)
    user_2 = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="PSP E3")
    user_group.users.append(user)
    user_group.users.append(user_2)

    paragon_underwriter_fixture(
        email=user_2.email, kalepa_user_id=user_2.id, ims_underwriter_guid="*************-2222-2222-************"
    )

    brokerage = brokerage_fixture(organization_id=organization.id, domains=["kalepa.com"])
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization.id, email="<EMAIL>")

    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account=PARAGON_PSP_EMAIL)
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    report.submission.broker = broker
    report.submission.brokerage = brokerage
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    submission_users = ParagonPSPUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert user_2.id in [su.user_id for su in submission_users]


def test_paragon_psp_uw_assigner_should_try_match_kalepa_user_for_unsynced_but_present_paragon_uw_guid(
    app_context, set_up_current_user, mocker
):
    mocker.patch(
        "copilot.logic.users.paragon_underwriter_assigner.get_paragon_agency_config_by_broker_email",
        return_value=ParagonAgencyConfig(
            agency_location_guid="********-1111-1111-1111-************",
            underwriter_guid="*************-2222-2222-************",
            underwriter_assistant_guid="*************-3333-3333-************",
            blocked=False,
        ),
    )
    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id)
    user_2 = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="PSP E3")
    user_group.users.append(user)
    user_group.users.append(user_2)

    # Not synced kalepa_user_id and email (stripped, lowercased) matching existing Kalepa user
    paragon_underwriter_fixture(
        email=" <EMAIL>   ",
        kalepa_user_id=None,
        ims_underwriter_guid="*************-2222-2222-************",
    )

    brokerage = brokerage_fixture(organization_id=organization.id, domains=["kalepa.com"])
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization.id, email="<EMAIL>")

    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account=PARAGON_PSP_EMAIL)
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    report.submission.broker = broker
    report.submission.brokerage = brokerage
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    submission_users = ParagonPSPUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert user_2.id in [su.user_id for su in submission_users]


def test_paragon_psp_uw_assigner_should_assign_fallback_user_if_broker_blocked_without_uw_mapping(
    app_context, set_up_current_user, mocker
):
    mocker.patch(
        "copilot.logic.users.paragon_underwriter_assigner.get_paragon_agency_config_by_broker_email",
        return_value=ParagonAgencyConfig(
            agency_location_guid="********-1111-1111-1111-************",
            underwriter_guid=str(UUID(int=0)),
            underwriter_assistant_guid="*************-3333-3333-************",
            blocked=True,
        ),
    )
    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id)
    user_2 = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id)
    fallback_user = user_fixture(id=4781, email="xyz", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="PSP E3")
    user_group.users.append(user)
    user_group.users.append(user_2)
    user_group.users.append(fallback_user)

    paragon_underwriter_fixture(
        email=user_2.email, kalepa_user_id=user_2.id, ims_underwriter_guid="*************-2222-2222-************"
    )

    brokerage = brokerage_fixture(organization_id=organization.id, domains=["kalepa.com"])
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization.id, email="<EMAIL>")

    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account=PARAGON_PSP_EMAIL)
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    report.submission.broker = broker
    report.submission.brokerage = brokerage
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    submission_users = ParagonPSPUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert submission_users[0].user_id == 4781


def test_paragon_wc_sub_cannot_assign_psp_user(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user_wc = user_fixture(id=1, email="<EMAIL>", organization_id=organization.id, name="wc")
    user_group = user_group_fixture(organization_id=organization.id, name="Workers Comp")
    user_group.users.append(user_wc)
    user_psp = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="PSP E3")
    user_group.users.append(user_psp)
    db.session.commit()
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account=PARAGON_WC_EMAIL)
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    config = UnderwriterAssignerConfig(should_share=False, already_assigned_error=False)
    submission_users = ParagonWCUnderwriterAssigner().assign_underwriters(
        [user_psp.id], report.submission, config=config
    )

    assert SubmissionUser.query.count() == 0
    assert len(submission_users) == 0

    submission_users = ParagonWCUnderwriterAssigner().assign_underwriters(
        [user_wc.id], report.submission, config=config
    )
    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert user_wc.id in [su.user_id for su in submission_users]


def test_paragon_wc_auto_assigned_underwriter_by_email_fallback_to_copilot_wc_mapping(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user_1 = user_fixture(
        id=1, email="<EMAIL>", organization_id=organization.id, name="Paragon WC"
    )
    user_2 = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id, name="Test WC UW")
    user_group = user_group_fixture(organization_id=organization.id, name="Workers Comp")
    user_group.users.append(user_1)
    user_group.users.append(user_2)
    brokerage = brokerage_fixture(organization_id=organization.id, domains=["kalepa.com"])
    db.session.commit()
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization.id, email="<EMAIL>")
    paragon_wc_uw_mapping_fixture(
        sheet_broker_email=broker.email, sheet_broker_name=broker.name, sheet_uw_name=user_2.name, user_id=user_2.id
    )
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account=PARAGON_WC_EMAIL)
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    report.submission.broker = broker
    report.submission.brokerage = brokerage
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    submission_users = ParagonWCUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert user_2.id in [su.user_id for su in submission_users]


def test_paragon_wc_auto_assigned_underwriter_by_email(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )
    mocker.patch(
        "copilot.logic.users.paragon_underwriter_assigner.get_paragon_agency_config_by_broker_email",
        return_value=ParagonAgencyConfig(
            agency_location_guid="********-1111-1111-1111-************",
            underwriter_guid="*************-2222-2222-************",
            underwriter_assistant_guid="*************-3333-3333-************",
            blocked=False,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user_1 = user_fixture(
        id=1, email="<EMAIL>", organization_id=organization.id, name="Paragon WC"
    )
    user_2 = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id, name="Test WC UW")
    user_group = user_group_fixture(organization_id=organization.id, name="Workers Comp")
    user_group.users.append(user_1)
    user_group.users.append(user_2)
    brokerage = brokerage_fixture(organization_id=organization.id, domains=["kalepa.com"])
    db.session.commit()
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization.id, email="<EMAIL>")
    paragon_underwriter_fixture(
        email=user_2.email, kalepa_user_id=user_2.id, ims_underwriter_guid="*************-2222-2222-************"
    )
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account=PARAGON_WC_EMAIL)
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    report.submission.broker = broker
    report.submission.brokerage = brokerage
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    submission_users = ParagonWCUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert user_2.id in [su.user_id for su in submission_users]


def test_paragon_wc_auto_assigned_underwriter_by_email_when_paragon_agency_is_blocked(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )
    mocker.patch(
        "copilot.logic.users.paragon_underwriter_assigner.get_paragon_agency_config_by_broker_email",
        return_value=ParagonAgencyConfig(
            agency_location_guid="********-1111-1111-1111-************",
            underwriter_guid="00000000-0000-0000-0000-000000000000",
            underwriter_assistant_guid="*************-3333-3333-************",
            blocked=True,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user_1 = user_fixture(
        id=1, email="<EMAIL>", organization_id=organization.id, name="Paragon WC"
    )
    user_2 = user_fixture(id=3290, email="<EMAIL>", organization_id=organization.id, name="Test WC UW")
    user_group = user_group_fixture(organization_id=organization.id, name="Workers Comp")
    user_group.users.append(user_1)
    user_group.users.append(user_2)
    brokerage = brokerage_fixture(organization_id=organization.id, domains=["kalepa.com"])
    db.session.commit()
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization.id, email="<EMAIL>")
    paragon_underwriter_fixture(
        email=user_2.email, kalepa_user_id=user_2.id, ims_underwriter_guid="*************-2222-2222-************"
    )
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account=PARAGON_WC_EMAIL)
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    report.submission.broker = broker
    report.submission.brokerage = brokerage
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    submission_users = ParagonWCUnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert user_2.id in [su.user_id for su in submission_users]


def test_paragon_wc_other_uw_removed_even_if_config_overwritten(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user_1 = user_fixture(
        id=1, email="<EMAIL>", organization_id=organization.id, name="Paragon WC"
    )
    user_2 = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id, name="Test WC UW")
    user_group = user_group_fixture(organization_id=organization.id, name="Workers Comp")
    user_group.users.append(user_1)
    user_group.users.append(user_2)
    db.session.commit()
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account=PARAGON_WC_EMAIL)
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    config = UnderwriterAssignerConfig(should_share=False, already_assigned_error=False)
    submission_users = ParagonWCUnderwriterAssigner().assign_underwriters([user_1.id], report.submission, config=config)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert user_1.id in [su.user_id for su in submission_users]

    submission_users = ParagonWCUnderwriterAssigner().assign_underwriters([user_2.id], report.submission, config=config)
    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert user_2.id in [su.user_id for su in submission_users]


def test_paragon_assigned_underwriter_with_redirect(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    user = user_fixture(
        id=1, email="<EMAIL>", organization_id=organization.id, name="<EMAIL>"
    )
    # user_2 is redirecting to user_3
    user_2 = user_fixture(id=1676, email="<EMAIL>", organization_id=organization.id)
    user_3 = user_fixture(id=1674, email="<EMAIL>", organization_id=organization.id)
    user_group = user_group_fixture(organization_id=organization.id, name="Ally Auto")
    user_group.users.extend([user, user_2, user_3])
    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account="<EMAIL>")
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    db.session.commit()

    assert SubmissionUser.query.count() == 0
    assert len(report.report_permissions) == 1

    submission_users = ParagonUnderwriterAssigner().assign_underwriters([user_2.id], report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert all(su.user_id == user_3.id for su in submission_users)

    # 1 - OWNER, 1674 - assigned UW and group
    assert len(report.report_permissions) == 3


def test_paragon_assigner_remove_permissions(app_context, mocker):
    # Submission was assigned to E&S user along with permissions and was later reassigned correctly to Ally Auto user
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.Paragon.value)
    paragon_master_user = user_fixture(
        id=1, email="<EMAIL>", organization_id=organization.id, name="<EMAIL>"
    )
    paragon_master_group = user_group_fixture(organization_id=organization.id, name="Paragon")
    paragon_master_group.users.append(paragon_master_user)

    user_1 = user_fixture(id=2, email="<EMAIL>", organization_id=organization.id)
    ally_auto_user = user_fixture(id=3, email="<EMAIL>", organization_id=organization.id)
    ally_auto_group = user_group_fixture(organization_id=organization.id, name="Ally Auto")
    ally_auto_group.users.extend([ally_auto_user, user_1])

    user_2 = user_fixture(id=4, email="<EMAIL>", organization_id=organization.id)
    e_and_s_user = user_fixture(id=5, email="<EMAIL>", organization_id=organization.id)
    e_and_s_group = user_group_fixture(organization_id=organization.id, name="E&S Casualty")
    e_and_s_group.users.extend([e_and_s_user, user_2])

    report = report_with_submissions_fixture(organization_id=organization.id)
    email = email_fixture(email_account="<EMAIL>")
    report.correspondence_id = email.correspondence_id
    report.submission.is_verified = True
    submission_user_fixture(user_id=user_2.id, submission_id=report.submission.id)
    report_permission_fixture(grantee_user_id=user_2.id, permission_type=PermissionType.EDITOR, report_id=report.id)
    report_permission_fixture(
        grantee_group_id=e_and_s_group.id, permission_type=PermissionType.EDITOR, report_id=report.id
    )
    report_permission_fixture(
        grantee_group_id=paragon_master_group.id, permission_type=PermissionType.OWNER, report_id=report.id
    )
    db.session.commit()

    assert SubmissionUser.query.count() == 1
    # Paragon master account as OWNER, Paragon group as OWNER, E&S group as EDITOR, and assigned UW as EDITOR
    assert {rp.grantee_user_id for rp in report.report_permissions if rp.grantee_user_id} == {
        user_2.id,
        paragon_master_user.id,
    }
    assert {rp.grantee_group_id for rp in report.report_permissions if rp.grantee_group_id} == {
        e_and_s_group.id,
        paragon_master_group.id,
    }

    submission_users = ParagonUnderwriterAssigner().assign_underwriters([user_1.id], report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert all(su.user_id == user_1.id for su in submission_users)

    assert {rp.grantee_user_id for rp in report.report_permissions if rp.grantee_user_id} == {
        user_1.id,
        paragon_master_user.id,
    }
    assert {rp.grantee_group_id for rp in report.report_permissions if rp.grantee_group_id} == {
        ally_auto_group.id,
        paragon_master_group.id,
    }
    # Paragon master account as owner, Ally Auto group as EDITOR, and assigned UW as EDITOR
    assert all(
        rp.grantee_user_id in [user_1.id, paragon_master_user.id]
        or rp.grantee_group_id in [ally_auto_group.id, paragon_master_group.id]
        for rp in report.report_permissions
    )


###############################
#           CRC               #
###############################
def test_crc_underwriter_assigner_no_linked_uw(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.CrcGroup.value)
    user = user_fixture(id=1, organization_id=organization.id)
    report = report_with_submissions_fixture(organization_id=organization.id)
    report.submission.is_verified = True
    db.session.commit()

    assert SubmissionUser.query.count() == 0

    submission_users = CrcUnderwriterAssigner().assign_underwriters([user.id], report.submission)

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1


def test_crc_underwriter_assigner_add_linked_uw(app_context, mocker):
    mocker.patch(
        "flask_login.utils._get_user",
        return_value=AnonObj(
            id=1,
            email="<EMAIL>",
            is_internal_machine_user=True,
            has_submission_permission=always_has_permission,
            has_report_permission=always_has_permission,
        ),
    )

    organization = organization_fixture(id=ExistingOrganizations.CrcGroup.value)
    user_1 = user_fixture(id=1, organization_id=organization.id)
    user_2 = user_fixture(id=3421, organization_id=organization.id)
    user_3 = user_fixture(id=3709, organization_id=organization.id)
    report = report_with_submissions_fixture(organization_id=organization.id)
    report.submission.is_verified = True
    db.session.commit()

    assert SubmissionUser.query.count() == 0

    submission_users = CrcUnderwriterAssigner().assign_underwriters([user_1.id, user_2.id], report.submission)

    assert SubmissionUser.query.count() == 3
    assert len(submission_users) == 3
    assert any(su.user_id == user_3.id for su in submission_users)


###############################
#         BOWHEAD             #
###############################
def test_bowhead_assigner(app_context, set_up_current_user):
    organization = organization_fixture(id=ExistingOrganizations.BowheadSpecialty.value)
    user_1 = user_fixture(id=1, organization_id=organization.id)
    user_2 = user_fixture(id=2, organization_id=organization.id)
    user_3 = user_fixture(id=3, organization_id=organization.id)
    user_4 = user_fixture(id=4, organization_id=organization.id)
    report = report_with_submissions_fixture(organization_id=organization.id)
    submission_user_fixture(user_id=user_1.id, submission_id=report.submission.id, source=SubmissionUserSource.EMAIL)
    submission_user_fixture(user_id=user_2.id, submission_id=report.submission.id, source=SubmissionUserSource.AUTO)
    submission_user_fixture(user_id=user_3.id, submission_id=report.submission.id, source=SubmissionUserSource.SYNC)
    db.session.commit()

    assert SubmissionUser.query.count() == 3

    submission_users = BowheadUnderwriterAssigner().assign_underwriters(
        [user_4.id], report.submission, source=SubmissionUserSource.SYNC
    )

    assert SubmissionUser.query.count() == 2
    assert len(submission_users) == 1
    assert submission_users[0].user_id == user_4.id
    all_assigned_users = SubmissionUser.query.all()
    assert all(su.user_id in [user_3.id, user_4.id] for su in all_assigned_users)


def test_bowhead_assigner_update_source(app_context, set_up_current_user):
    organization = organization_fixture(id=ExistingOrganizations.BowheadSpecialty.value)
    user_1 = user_fixture(id=1, organization_id=organization.id)
    report = report_with_submissions_fixture(organization_id=organization.id)
    submission_user_fixture(user_id=user_1.id, submission_id=report.submission.id, source=SubmissionUserSource.EMAIL)
    db.session.commit()

    assert SubmissionUser.query.count() == 1

    submission_users = BowheadUnderwriterAssigner().assign_underwriters(
        [user_1.id], report.submission, source=SubmissionUserSource.SYNC
    )

    assert SubmissionUser.query.count() == 1
    assert len(submission_users) == 1
    assert submission_users[0].user_id == user_1.id
    assert submission_users[0].source == SubmissionUserSource.SYNC


def test_bowhead_assigner_auto_assign_environmental_direct_sender(app_context, set_up_current_user):
    org = organization_fixture(id=ExistingOrganizations.BowheadSpecialty.value)
    org_group = OrganizationGroups.BOWHEAD_ENVIRONMENTAL.value
    settings_fixture(organization_id=org.id, org_groups=[org_group])
    user_1 = user_fixture(id=1, organization_id=org.id, email="<EMAIL>")
    user_2 = user_fixture(id=2, organization_id=org.id, email="<EMAIL>")
    user_3 = user_fixture(id=3, organization_id=org.id, email="<EMAIL>")
    db.session.commit()

    group = user_group_fixture(name="Environmental", organization_id=org.id)
    group.users.append(user_1)
    group.users.append(user_2)
    group.users.append(user_3)
    db.session.commit()

    env_inbox = "Environmental <<EMAIL>>"
    email_1 = email_fixture(email_to=env_inbox, email_from="dude <<EMAIL>>")
    email_2 = email_fixture(email_to=env_inbox, email_from="dude <<EMAIL>>")
    report_1 = report_with_submissions_fixture(organization_id=org.id, correspondence_id=email_1.correspondence_id)
    report_2 = report_with_submissions_fixture(organization_id=org.id, correspondence_id=email_2.correspondence_id)
    report_3 = report_with_submissions_fixture(organization_id=org.id)
    db.session.commit()

    # report 1 and 2 have direct sender, report 3 does not
    report_1.org_group = org_group
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_1.submission)

    assert SubmissionUser.query.count() == 1
    assert report_1.submission.assigned_underwriters[0].user_id == user_2.id
    assert report_1.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO

    report_2.org_group = org_group
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_2.submission)

    assert SubmissionUser.query.count() == 2
    assert report_2.submission.assigned_underwriters[0].user_id == user_3.id
    assert report_2.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO

    report_3.org_group = org_group
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_3.submission)

    assert SubmissionUser.query.count() == 3
    assert report_3.submission.assigned_underwriters[0].user_id == user_1.id
    assert report_3.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO


def test_bowhead_assigner_auto_assign_environmental(app_context, set_up_current_user):
    organization = organization_fixture(id=ExistingOrganizations.BowheadSpecialty.value)
    org_group = OrganizationGroups.BOWHEAD_ENVIRONMENTAL.value
    settings_fixture(organization_id=organization.id, org_groups=[org_group])
    user_1 = user_fixture(id=1, organization_id=organization.id, email="<EMAIL>")
    user_2 = user_fixture(id=2, organization_id=organization.id, email="<EMAIL>")
    user_3 = user_fixture(id=3, organization_id=organization.id, email="<EMAIL>")
    db.session.commit()

    group = user_group_fixture(name="Environmental", organization_id=organization.id)
    group.users.append(user_1)
    group.users.append(user_2)
    group.users.append(user_3)
    db.session.commit()

    report_1 = report_with_submissions_fixture(organization_id=organization.id)
    report_2 = report_with_submissions_fixture(organization_id=organization.id)
    report_3 = report_with_submissions_fixture(organization_id=organization.id)
    db.session.commit()

    report_1.org_group = org_group
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_1.submission)

    assert SubmissionUser.query.count() == 1
    assert report_1.submission.assigned_underwriters[0].user_id == user_1.id
    assert report_1.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO

    report_2.org_group = org_group
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_2.submission)

    assert SubmissionUser.query.count() == 2
    assert report_2.submission.assigned_underwriters[0].user_id == user_2.id
    assert report_2.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO

    report_3.org_group = org_group
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_3.submission)

    assert SubmissionUser.query.count() == 3
    assert report_3.submission.assigned_underwriters[0].user_id == user_3.id
    assert report_3.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO

    BowheadUnderwriterAssigner().auto_assign_underwriters(report_3.submission)

    assert SubmissionUser.query.count() == 3
    assert report_3.submission.assigned_underwriters[0].user_id == user_3.id
    assert report_3.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO


def test_bowhead_assigner_validate_groups(app_context, set_up_current_user):
    organization = organization_fixture(id=ExistingOrganizations.BowheadSpecialty.value)
    org_group = OrganizationGroups.BOWHEAD_PRIMARY.value
    settings_fixture(organization_id=organization.id, org_groups=[org_group])
    user_1 = user_fixture(id=1, organization_id=organization.id, email="<EMAIL>")

    group = user_group_fixture(name="Excess Casualty", organization_id=organization.id)
    group.users.append(user_1)
    db.session.commit()

    report_1 = report_with_submissions_fixture(organization_id=organization.id)
    report_1.org_group = org_group
    db.session.commit()

    BowheadUnderwriterAssigner().assign_underwriters([user_1.id], report_1.submission)
    assert SubmissionUser.query.count() == 0


def test_bowhead_assigner_auto_assign_primary(app_context, set_up_current_user):
    organization = organization_fixture(id=ExistingOrganizations.BowheadSpecialty.value)
    org_group = OrganizationGroups.BOWHEAD_PRIMARY.value
    settings_fixture(organization_id=organization.id, org_groups=[org_group])
    user_1 = user_fixture(id=1, organization_id=organization.id, email="<EMAIL>")
    user_2 = user_fixture(id=2, organization_id=organization.id, email="<EMAIL>")
    user_3 = user_fixture(id=3, organization_id=organization.id, email="<EMAIL>")
    user_4 = user_fixture(id=7880, organization_id=organization.id, email="<EMAIL>")
    db.session.commit()

    brokerage = brokerage_fixture(organization_id=54)
    db.session.commit()
    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=54, email="<EMAIL>")
    broker_2 = broker_fixture(brokerage_id=brokerage.id, organization_id=54, name="bw Test", email="<EMAIL>")
    broker_3 = broker_fixture(brokerage_id=brokerage.id, organization_id=54, name="bw Test2", email="<EMAIL>")
    db.session.commit()

    bowhead_uw_mapping_fixture(user_id=user_1.id, broker_email="<EMAIL>", org_group=org_group)
    bowhead_uw_mapping_fixture(user_id=user_2.id, broker_id=broker_2.id, org_group=org_group)
    bowhead_uw_mapping_fixture(
        user_id=user_3.id, broker_email="<EMAIL>", org_group=OrganizationGroups.BOWHEAD_XS.value
    )

    group = user_group_fixture(name="Primary Casualty", organization_id=organization.id)
    group.users.append(user_1)
    group.users.append(user_2)
    group.users.append(user_4)
    group_2 = user_group_fixture(name="Excess Casualty", organization_id=organization.id)
    group_2.users.append(user_3)
    db.session.commit()

    report_1 = report_with_submissions_fixture(organization_id=organization.id)
    report_2 = report_with_submissions_fixture(organization_id=organization.id)
    report_3 = report_with_submissions_fixture(organization_id=organization.id)
    report_4 = report_with_submissions_fixture(organization_id=organization.id)
    report_5 = report_with_submissions_fixture(organization_id=organization.id)
    db.session.commit()

    # report 1 has UW from email and there's also mapping on CC email - nothing should happen.
    # report 2 has no UW and there's mapping on CC email - we should assign UW from mapping.
    # report 3 has no UW and there's mapping on broker email - we should assign UW from mapping.
    # report 4 has no UW and there's mapping on broker id - we should assign UW from mapping.
    # report 5 has no UW and there's no mapping (it's for XS) - we should assign default UW.

    report_1.org_group = org_group
    report_1.submission.assigned_underwriters.append(
        SubmissionUser(user_id=user_2.id, source=SubmissionUserSource.EMAIL)
    )
    report_1.submission.brokerage_id = brokerage.id
    report_1.submission.brokerage_contact_id = broker.id
    db.session.commit()
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_1.submission)
    assert report_1.submission.assigned_underwriters[0].user_id == user_2.id
    assert report_1.submission.assigned_underwriters[0].source == SubmissionUserSource.EMAIL

    report_2.org_group = org_group
    report_2.submission.brokerage_id = brokerage.id
    report_2.submission.brokerage_contact_id = broker.id
    db.session.commit()
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_2.submission)
    assert report_2.submission.assigned_underwriters[0].user_id == user_1.id
    assert report_2.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO

    report_3.org_group = org_group
    report_3.submission.brokerage_id = brokerage.id
    report_3.submission.broker_id = broker.id
    db.session.commit()
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_3.submission)
    assert report_3.submission.assigned_underwriters[0].user_id == user_1.id
    assert report_3.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO

    report_4.org_group = org_group
    report_4.submission.brokerage_id = brokerage.id
    report_4.submission.broker_id = broker_2.id
    db.session.commit()
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_4.submission)
    assert report_4.submission.assigned_underwriters[0].user_id == user_2.id
    assert report_4.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO

    report_5.org_group = org_group
    report_5.submission.brokerage_id = brokerage.id
    report_5.submission.broker_id = broker_3.id
    db.session.commit()
    BowheadUnderwriterAssigner().auto_assign_underwriters(report_5.submission)
    assert report_5.submission.assigned_underwriters[0].user_id == user_4.id
    assert report_5.submission.assigned_underwriters[0].source == SubmissionUserSource.AUTO


###############################
#           ADMIRAL           #
###############################
def test_admiral_uw_basic_assignment_logic(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    user2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    created_mapping = admiral_uw_mapping_fixture(producer_no="PROD123")  # Added producer_no
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=created_mapping.id, user_email=user.email, for_renewals=False)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=created_mapping.id, user_email=user2.email, for_renewals=False)

    broker = broker_fixture(
        brokerage_id=brokerage.id, organization_id=organization_id, email=created_mapping.broker_email
    )
    db.session.commit()

    report = report_with_submissions_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    db.session.commit()

    assert SubmissionUser.query.count() == 0, "No underwriters should be assigned yet"
    assert AdmiralAssignmentLog.query.count() == 0, "No assignments should be logged yet"

    AdmiralUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert AdmiralAssignmentLog.query.count() == 1, "Assignment should be logged"
    assert SubmissionUser.query.first().user_id in (
        user.id,
        user2.id,
    ), "One of the mapped underwriters should be assigned"


def test_admiral_uw_producer_level_round_robin(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)

    # Create underwriters
    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    uw3 = user_fixture(id=3, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    producer1_mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    producer2_mapping = admiral_uw_mapping_fixture(producer_no="PROD456", broker_email="<EMAIL>")

    # Assign all underwriters to both producers
    for mapping in [producer1_mapping, producer2_mapping]:
        for uw in [uw1, uw2, uw3]:
            admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw.email, for_renewals=False)

    broker1 = broker_fixture(
        brokerage_id=brokerage.id,
        name="Broker no1",
        organization_id=organization_id,
        email=producer1_mapping.broker_email,
    )
    broker2 = broker_fixture(
        brokerage_id=brokerage.id,
        name="Broker no2",
        organization_id=organization_id,
        email=producer2_mapping.broker_email,
    )
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()

    report1a = report_with_submissions_fixture(
        broker_id=broker1.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    report1b = report_with_submissions_fixture(
        broker_id=broker1.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    report1c = report_with_submissions_fixture(
        broker_id=broker1.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    report2a = report_with_submissions_fixture(
        broker_id=broker2.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    report2b = report_with_submissions_fixture(
        broker_id=broker2.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    db.session.commit()

    assigner.auto_assign_underwriters(report1a.submission)
    assigner.auto_assign_underwriters(report1b.submission)
    assigner.auto_assign_underwriters(report1c.submission)
    assigner.auto_assign_underwriters(report2a.submission)
    assigner.auto_assign_underwriters(report2b.submission)

    prod1_assignments = (
        AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.producer_no == "PROD123")
        .order_by(AdmiralAssignmentLog.created_at)
        .all()
    )

    assert len(prod1_assignments) == 3, "Should have 3 assignments for producer 1"

    uw_sequence = [assignment.assigned_underwriter_email for assignment in prod1_assignments]
    assert len(set(uw_sequence)) == 3, "Should have used 3 different underwriters"
    assert uw_sequence[1] != uw_sequence[0], "Second assignment should be different from first"
    assert uw_sequence[2] != uw_sequence[1], "Third assignment should be different from second"

    prod2_assignments = (
        AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.producer_no == "PROD456")
        .order_by(AdmiralAssignmentLog.created_at)
        .all()
    )

    assert len(prod2_assignments) == 2, "Should have 2 assignments for producer 2"
    assert (
        prod2_assignments[0].assigned_underwriter_email != prod2_assignments[1].assigned_underwriter_email
    ), "Should have used different underwriters for producer 2"


def test_admiral_uw_assigner_maintains_idempotency(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    user2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    created_mapping = admiral_uw_mapping_fixture(producer_no="PROD123")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=created_mapping.id, user_email=user.email, for_renewals=False)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=created_mapping.id, user_email=user2.email, for_renewals=False)

    broker = broker_fixture(
        brokerage_id=brokerage.id, organization_id=organization_id, email=created_mapping.broker_email
    )
    db.session.commit()

    report = report_with_submissions_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()

    # Initial assignment
    assigner.auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert AdmiralAssignmentLog.query.count() == 1, "One assignment should be logged"
    initial_assignment = SubmissionUser.query.first()

    # Multiple reassignment attempts
    for _ in range(3):
        SubmissionUser.query.delete()
        db.session.commit()

        assigner.auto_assign_underwriters(report.submission)
        assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
        assert AdmiralAssignmentLog.query.count() == 1, "Should not create additional logs"
        assert SubmissionUser.query.first().user_id == initial_assignment.user_id, "Should assign the same underwriter"


def test_admiral_uw_assigner_handles_invalid_underwriter(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    created_mapping = admiral_uw_mapping_fixture(producer_no="PROD123")
    admiral_uw_mapping_assignment_fixture(
        uw_mapping_id=created_mapping.id,
        user_email="<EMAIL>",  # Non-existent user
        for_renewals=False,
    )
    admiral_uw_mapping_assignment_fixture(
        uw_mapping_id=created_mapping.id,
        user_email="<EMAIL>",  # Non-existent user
        for_renewals=False,
    )

    broker = broker_fixture(
        brokerage_id=brokerage.id, organization_id=organization_id, email=created_mapping.broker_email
    )
    db.session.commit()

    report = report_with_submissions_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()
    assigner.auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert (
        SubmissionUser.query.first().user_id == fallback_user.id
    ), "Fallback underwriter should be assigned when no valid underwriters found"


def test_admiral_uw_assigner_handles_log_failure(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    created_mapping = admiral_uw_mapping_fixture(producer_no="PROD123")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=created_mapping.id, user_email=user.email, for_renewals=False)
    db.session.commit()

    broker = broker_fixture(
        brokerage_id=brokerage.id, organization_id=organization_id, email=created_mapping.broker_email
    )
    db.session.commit()

    report = report_with_submissions_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    db.session.commit()

    assert AdmiralAssignmentLog.query.count() == 0, "No logs should be present initially"

    def mock_add(obj):
        if isinstance(obj, AdmiralAssignmentLog):
            raise Exception("Simulated log failure")
        db.session.add(obj)

    mocker.patch.object(db.session, "add", side_effect=mock_add)

    assigner = AdmiralUnderwriterAssigner()
    assigner.auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1, "Underwriter should be assigned despite log failure"
    assert SubmissionUser.query.first().user_id == user.id, "Correct underwriter should be assigned"
    assert AdmiralAssignmentLog.query.count() == 0, "No log should be created due to simulated failure"


def test_admiral_uw_shared_underwriter_pool(app_context, set_up_current_user, mocker):
    """Tests that multiple producers maintain independent round-robin when sharing the same underwriter pool"""
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    producers = [
        admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>"),
        admiral_uw_mapping_fixture(producer_no="PROD456", broker_email="<EMAIL>"),
        admiral_uw_mapping_fixture(producer_no="PROD789", broker_email="<EMAIL>"),
    ]

    for producer in producers:
        admiral_uw_mapping_assignment_fixture(uw_mapping_id=producer.id, user_email=uw1.email, for_renewals=False)
        admiral_uw_mapping_assignment_fixture(uw_mapping_id=producer.id, user_email=uw2.email, for_renewals=False)

    brokers = [
        broker_fixture(
            brokerage_id=brokerage.id,
            name=f"Broker Test{p.producer_no}",
            organization_id=organization_id,
            email=p.broker_email,
        )
        for p in producers
    ]
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()

    submissions = []
    for broker in brokers:
        for _ in range(2):
            report = report_with_submissions_fixture(
                broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
            )
            submissions.append(report.submission)
    db.session.commit()

    for submission in submissions:
        assigner.auto_assign_underwriters(submission)

    for producer in [p.producer_no for p in producers]:
        assignments = (
            AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.producer_no == producer)
            .order_by(AdmiralAssignmentLog.created_at)
            .all()
        )

        assert len(assignments) == 2, f"Producer {producer} should have 2 assignments"
        assert (
            assignments[0].assigned_underwriter_email != assignments[1].assigned_underwriter_email
        ), f"Producer {producer} should alternate between underwriters"


def test_admiral_uw_renewal_vs_nonrenewal_rotation(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    renewal_uw = user_fixture(organization_id=organization_id, email="<EMAIL>")
    renewal_uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    nonrenewal_uw = user_fixture(id=3, organization_id=organization_id, email="<EMAIL>")
    nonrenewal_uw2 = user_fixture(id=4, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=renewal_uw.email, for_renewals=True)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=renewal_uw2.email, for_renewals=True)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=nonrenewal_uw.email, for_renewals=False)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=nonrenewal_uw2.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()

    renewal_submissions = []
    for _ in range(3):
        report = report_with_submissions_fixture(
            broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
        )
        report.submission.is_renewal = True
        renewal_submissions.append(report.submission)

    nonrenewal_submissions = []
    for _ in range(3):
        report = report_with_submissions_fixture(
            broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
        )
        report.submission.is_renewal = False
        nonrenewal_submissions.append(report.submission)

    db.session.commit()

    for submission in renewal_submissions + nonrenewal_submissions:
        assigner.auto_assign_underwriters(submission)

    renewal_assignments = (
        AdmiralAssignmentLog.query.join(Submission)
        .filter(AdmiralAssignmentLog.producer_no == "PROD123", Submission.is_renewal == True)
        .order_by(AdmiralAssignmentLog.created_at)
        .all()
    )

    assert len(renewal_assignments) == 3, "Should have 3 renewal assignments"
    assert all(
        a.assigned_underwriter_email in [renewal_uw.email, renewal_uw2.email] for a in renewal_assignments
    ), "Should only use renewal underwriters"

    nonrenewal_assignments = (
        AdmiralAssignmentLog.query.join(Submission)
        .filter(AdmiralAssignmentLog.producer_no == "PROD123", Submission.is_renewal == False)
        .order_by(AdmiralAssignmentLog.created_at)
        .all()
    )

    assert len(nonrenewal_assignments) == 3, "Should have 3 non-renewal assignments"
    assert all(
        a.assigned_underwriter_email in [nonrenewal_uw.email, nonrenewal_uw2.email] for a in nonrenewal_assignments
    ), "Should only use non-renewal underwriters"


def test_admiral_uw_missing_producer_no(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no=None, broker_email="<EMAIL>")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=user.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    report = report_with_submissions_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()
    assigner.auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1, "Should assign fallback underwriter"
    assert SubmissionUser.query.first().user_id == fallback_user.id, "Should use fallback underwriter"


def test_admiral_uw_missing_log_entries(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    uw3 = user_fixture(id=3, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    for uw in [uw1, uw2, uw3]:
        admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()

    report1 = report_with_submissions_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    assigner.auto_assign_underwriters(report1.submission)
    first_assignment = AdmiralAssignmentLog.query.first()

    db.session.delete(first_assignment)
    db.session.commit()

    report2 = report_with_submissions_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    assigner.auto_assign_underwriters(report2.submission)

    new_assignment = AdmiralAssignmentLog.query.first()
    assert (
        new_assignment.assigned_underwriter_email == sorted([uw1.email, uw2.email, uw3.email])[0]
    ), "Should restart rotation when log is missing"


def test_admiral_uw_assignment_order_consistency(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw1.email, for_renewals=False)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw2.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    submissions = []
    for _ in range(3):
        report = report_with_submissions_fixture(
            broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
        )
        submissions.append(report.submission)
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()

    first_run_assignments = []
    for submission in submissions:
        assigner.auto_assign_underwriters(submission)
        assignment = AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.submission_id == submission.id).first()
        first_run_assignments.append(assignment.assigned_underwriter_email)

    SubmissionUser.query.delete()
    AdmiralAssignmentLog.query.delete()
    db.session.commit()

    second_run_assignments = []
    for submission in submissions:
        assigner.auto_assign_underwriters(submission)
        assignment = AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.submission_id == submission.id).first()
        second_run_assignments.append(assignment.assigned_underwriter_email)

    assert first_run_assignments == second_run_assignments, "Assignment order should be consistent across runs"


@pytest.mark.parametrize("error_type", [ValueError, RuntimeError, Exception])
def test_admiral_uw_assigner_lock_release_on_error(app_context, set_up_current_user, mocker, error_type):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)

    fake_server = fakeredis.FakeServer()
    flask.current_app.locks_client = fakeredis.FakeStrictRedis(server=fake_server)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=user.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    report = report_with_submissions_fixture(
        broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
    )
    db.session.commit()

    mocker.patch.object(AdmiralUnderwriterAssigner, "find_user_id_by_email", side_effect=error_type("Simulated error"))

    assigner = AdmiralUnderwriterAssigner()

    with pytest.raises(error_type):
        assigner.auto_assign_underwriters(report.submission)

    lock_key = f"admiral_uw_assigner_PROD123"
    assert not flask.current_app.locks_client.get(lock_key), "Lock should be released after error"

    # Verify we can acquire a new lock (if lock wasn't released, this would hang)
    with redis_lock.Lock(
        flask.current_app.locks_client,
        name=f"admiral_uw_assigner_PROD123",
        expire=5,
        auto_renewal=True,
    ):
        assert True, "Should be able to acquire lock after error"


def test_admiral_uw_assigner_concurrent_assignments(app, app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    uw3 = user_fixture(id=3, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    for uw in [uw1, uw2, uw3]:
        admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    fake_server = fakeredis.FakeServer()
    flask.current_app.locks_client = fakeredis.FakeStrictRedis(server=fake_server)

    CONCURRENT_SUBMISSIONS = 9
    submissions = []
    for _ in range(CONCURRENT_SUBMISSIONS):
        report = report_with_submissions_fixture(
            broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
        )
        submissions.append(report.submission)
    db.session.commit()

    submission_ids = [s.id for s in submissions]

    def process_submission(submission_id):
        with app.app_context():
            db.session.remove()
            submission = Submission.query.get(submission_id)
            assigner = AdmiralUnderwriterAssigner()
            time_start = datetime.now()
            result = assigner.auto_assign_underwriters(submission)
            time_end = datetime.now()
            assigned_uw = AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.submission_id == submission_id).first()
            return {
                "submission_id": submission_id,
                "uw_email": assigned_uw.assigned_underwriter_email,
                "time_start": time_start,
                "time_end": time_end,
            }

    results = []
    with ThreadPoolExecutor(max_workers=CONCURRENT_SUBMISSIONS) as executor:
        future_to_submission = {executor.submit(process_submission, sub_id): sub_id for sub_id in submission_ids}
        for future in as_completed(future_to_submission):
            results.append(future.result())

    results.sort(key=lambda x: x["time_end"])
    assignments = [r["uw_email"] for r in results]

    assert (
        len(assignments) == CONCURRENT_SUBMISSIONS
    ), f"Expected {CONCURRENT_SUBMISSIONS} assignments, got {len(assignments)}"

    uw_emails = sorted([uw1.email, uw2.email, uw3.email])
    expected_pattern = uw_emails * (CONCURRENT_SUBMISSIONS // len(uw_emails))
    assert (
        assignments == expected_pattern
    ), f"Wrong assignment order. Expected:\n{expected_pattern}\nGot:\n{assignments}"

    distribution = {email: assignments.count(email) for email in set(assignments)}
    expected_count = CONCURRENT_SUBMISSIONS // 3
    assert all(
        count in (expected_count, expected_count + 1) for count in distribution.values()
    ), f"Uneven distribution. Got counts: {distribution}"

    first_submission = Submission.query.get(submission_ids[0])
    first_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == first_submission.id
    ).first()

    assigner = AdmiralUnderwriterAssigner()
    assigner.auto_assign_underwriters(first_submission)

    new_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == first_submission.id
    ).first()

    assert new_assignment.assigned_underwriter_email == first_assignment.assigned_underwriter_email, (
        f"Idempotency violated. First assignment: {first_assignment.assigned_underwriter_email}, "
        f"New assignment: {new_assignment.assigned_underwriter_email}"
    )

    lock_key = f"admiral_uw_assigner_PROD123"
    assert not flask.current_app.locks_client.get(lock_key), f"Lock not released after operations. Lock key: {lock_key}"


def test_admiral_uw_shadow_submission_assignment(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)

    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw1.email, for_renewals=False)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw2.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    parent_report = report_with_submissions_fixture(
        broker_id=broker.id,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        shadow_type=ReportShadowType.HAS_ACTIVE_SHADOW,
    )
    shadow_report = report_with_submissions_fixture(
        broker_id=broker.id,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        shadow_type=ReportShadowType.IS_ACTIVE_SHADOW,
    )
    shadow_report.submission.is_shadow_processed = True

    report_shadow_dependency_fixture(report_id=parent_report.id, shadow_report_id=shadow_report.id)
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()
    assigner.auto_assign_underwriters(parent_report.submission)
    parent_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == parent_report.submission.id
    ).first()

    assert parent_assignment is not None, "Parent submission should have an assignment"

    SubmissionUser.query.delete()
    db.session.commit()

    assigner.auto_assign_underwriters(shadow_report.submission)

    shadow_user = SubmissionUser.query.filter(SubmissionUser.submission_id == shadow_report.submission.id).first()
    assert shadow_user is None, "Shadow submission should not have an assigned underwriter"

    assert AdmiralAssignmentLog.query.count() == 1, "No new assignment log should be created for shadow submission"


def test_admiral_uw_shadow_submission_with_inactive_dependency(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)

    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw1.email, for_renewals=False)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw2.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    parent_report = report_with_submissions_fixture(
        broker_id=broker.id,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        shadow_type=ReportShadowType.HAS_ACTIVE_SHADOW,
    )
    shadow_report = report_with_submissions_fixture(
        broker_id=broker.id,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        shadow_type=ReportShadowType.IS_ACTIVE_SHADOW,
    )
    shadow_report.submission.is_shadow_processed = True

    dependency = report_shadow_dependency_fixture(report_id=parent_report.id, shadow_report_id=shadow_report.id)
    db.session.commit()

    dependency.is_active = False

    assigner = AdmiralUnderwriterAssigner()
    assigner.auto_assign_underwriters(parent_report.submission)
    parent_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == parent_report.submission.id
    ).first()
    parent_uw_id = assigner.find_user_id_by_email(parent_assignment.assigned_underwriter_email)

    SubmissionUser.query.delete()
    db.session.commit()

    assigner.auto_assign_underwriters(shadow_report.submission)

    shadow_user = SubmissionUser.query.filter(SubmissionUser.submission_id == shadow_report.submission.id).first()
    shadow_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == shadow_report.submission.id
    ).first()

    assert shadow_user is not None, "Shadow submission should have an assigned underwriter"
    assert shadow_assignment is not None, "Shadow submission should have a new assignment log"
    assert (
        shadow_user.user_id != parent_uw_id
    ), "Shadow submission should get different underwriter with inactive dependency"


def test_admiral_uw_deleted_mapping_assignment(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)
    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    uw3 = user_fixture(id=3, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    assignment1 = admiral_uw_mapping_assignment_fixture(
        uw_mapping_id=mapping.id, user_email=uw1.email, for_renewals=False
    )
    assignment2 = admiral_uw_mapping_assignment_fixture(
        uw_mapping_id=mapping.id, user_email=uw2.email, for_renewals=False
    )
    assignment3 = admiral_uw_mapping_assignment_fixture(
        uw_mapping_id=mapping.id, user_email=uw3.email, for_renewals=False
    )

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    submissions = []
    for _ in range(5):
        report = report_with_submissions_fixture(
            broker_id=broker.id, brokerage_id=brokerage.id, organization_id=organization_id
        )
        submissions.append(report.submission)
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()

    assigner.auto_assign_underwriters(submissions[0])
    first_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == submissions[0].id
    ).first()

    assigner.auto_assign_underwriters(submissions[1])
    second_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == submissions[1].id
    ).first()

    db.session.delete(assignment2)
    db.session.commit()

    assigner.auto_assign_underwriters(submissions[2])
    third_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == submissions[2].id
    ).first()

    assigner.auto_assign_underwriters(submissions[3])
    fourth_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == submissions[3].id
    ).first()

    assigner.auto_assign_underwriters(submissions[4])
    fifth_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == submissions[4].id
    ).first()

    assert all(
        AdmiralAssignmentLog.query.filter(AdmiralAssignmentLog.submission_id == sub.id).first() for sub in submissions
    ), "All submissions should have assignments"

    later_assignments = [third_assignment.assigned_underwriter_email, fourth_assignment.assigned_underwriter_email]

    assert (
        assignment2.user_email not in later_assignments
    ), "Deleted underwriter should not be used in later assignments"
    assert all(
        email in [uw1.email, uw3.email] for email in later_assignments
    ), "Only remaining underwriters should be used"
    assert (
        len({a.assigned_underwriter_email for a in [third_assignment, fourth_assignment]}) == 2
    ), "Should still rotate between remaining underwriters"


def test_admiral_existing_uw_assignment_in_bundled_report(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.AdmiralInsuranceGroup.value
    organization_fixture(id=organization_id)

    uw1 = user_fixture(organization_id=organization_id, email="<EMAIL>")
    uw2 = user_fixture(id=2, organization_id=organization_id, email="<EMAIL>")
    fallback_user = user_fixture(
        id=669, organization_id=organization_id, email=AdmiralUnderwriterAssigner.FALLBACK_UW_EMAIL
    )
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    mapping = admiral_uw_mapping_fixture(producer_no="PROD123", broker_email="<EMAIL>")
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw1.email, for_renewals=False)
    admiral_uw_mapping_assignment_fixture(uw_mapping_id=mapping.id, user_email=uw2.email, for_renewals=False)

    broker = broker_fixture(brokerage_id=brokerage.id, organization_id=organization_id, email=mapping.broker_email)
    db.session.commit()

    parent_report = report_with_submissions_fixture(
        broker_id=broker.id,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
    )
    bundled_report = report_with_submissions_fixture(
        broker_id=broker.id,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
    )
    db.session.commit()

    bundle = report_bundle_fixture()
    parent_report.report_bundle_id = bundle.id
    bundled_report.report_bundle_id = bundle.id
    db.session.commit()

    assigner = AdmiralUnderwriterAssigner()
    assigner.auto_assign_underwriters(parent_report.submission)
    parent_assignment = AdmiralAssignmentLog.query.filter(
        AdmiralAssignmentLog.submission_id == parent_report.submission.id
    ).first()

    assert parent_assignment is not None, "Parent submission should have an assignment"

    SubmissionUser.query.delete()
    db.session.commit()

    assigner.auto_assign_underwriters(bundled_report.submission)

    bundled_user = SubmissionUser.query.filter(SubmissionUser.submission_id == bundled_report.submission.id).first()
    assert bundled_user is not None, "Bundled submission should have an assigned underwriter"

    parent_uw_id = assigner.find_user_id_by_email(parent_assignment.assigned_underwriter_email)
    assert AdmiralAssignmentLog.query.count() == 1, "No new assignment log should be created for bundled submission"
    assert bundled_user.user_id == parent_uw_id, "Bundled submission should be assigned same underwriter as parent"


###############################
#             K2              #
###############################
def test_k2_uw_basic_assignment_by_recipient(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    target_email = "<EMAIL>"
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    email = email_fixture(email_to=target_email, email_cc=[], email_account="<EMAIL>")
    report = report_with_submissions_fixture(
        broker_id=None,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        correspondence_id=email.correspondence_id,
    )
    db.session.commit()

    assert SubmissionUser.query.count() == 0, "No underwriters should be assigned yet"

    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user.id, "Correct underwriter should be assigned"


def test_k2_uw_basic_assignment_by_cc(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    target_email = "<EMAIL>"
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    email = email_fixture(
        email_to="<EMAIL>", email_cc=[target_email], email_account="<EMAIL>"
    )
    report = report_with_submissions_fixture(
        broker_id=None,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        correspondence_id=email.correspondence_id,
    )
    db.session.commit()

    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user.id, "Correct underwriter should be assigned"


def test_k2_uw_empty_cc_rule(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    email = email_fixture(email_to="<EMAIL>", email_cc=[], email_account="<EMAIL>")
    report = report_with_submissions_fixture(
        broker_id=None,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        correspondence_id=email.correspondence_id,
    )
    db.session.commit()

    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user.id, "Correct underwriter should be assigned"


def test_k2_uw_no_matching_rules(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    email = email_fixture(
        email_to="<EMAIL>",
        email_cc=["<EMAIL>"],
        email_account="<EMAIL>",
    )
    report = report_with_submissions_fixture(
        broker_id=None,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        correspondence_id=email.correspondence_id,
    )
    db.session.commit()

    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "Fallback underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user.id, "Correct fallback underwriter should be assigned"


def test_k2_uw_missing_report_or_correspondence(app_context, set_up_current_user, mocker):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    report = report_with_submissions_fixture(
        broker_id=None, brokerage_id=brokerage.id, organization_id=organization_id, correspondence_id=None
    )
    db.session.commit()

    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 0, "No underwriters should be assigned"


@pytest.mark.parametrize(
    "rule_case,invalid_data",
    [
        (K2AssignCase.RECIPIENT_OR_CC_VALUE, ""),
        (K2AssignCase.RECIPIENT_OR_CC_VALUE, None),
        (K2AssignCase.IS_RENEWAL, "not_a_bool"),
        (K2AssignCase.IS_RENEWAL, None),
    ],
)
def test_k2_uw_invalid_rule_data(app_context, set_up_current_user, rule_case, invalid_data):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    email = email_fixture(email_account="<EMAIL>")
    report = report_with_submissions_fixture(
        broker_id=None, brokerage_id=None, organization_id=organization_id, correspondence_id=email.correspondence_id
    )
    db.session.commit()

    assigner = K2UnderwriterAssigner()
    with pytest.raises(ValueError):
        assigner._check_rule_applies(
            rule=AssignCaseRule(rule_case, invalid_data),
            submission=report.submission,
            emails_data=[EmailData(email_to="<EMAIL>", email_cc=[], email_from="")],
        )


def test_k2_uw_unimplemented_rule_case(app_context, set_up_current_user):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    email = email_fixture(email_account="<EMAIL>")
    report = report_with_submissions_fixture(
        broker_id=None, brokerage_id=None, organization_id=organization_id, correspondence_id=email.correspondence_id
    )
    db.session.commit()

    class UnimplementedCase(enum.Enum):
        NEW_CASE = enum.auto()

    assigner = K2UnderwriterAssigner()
    with pytest.raises(NotImplementedError):
        assigner._check_rule_applies(
            rule=AssignCaseRule(UnimplementedCase.NEW_CASE),
            submission=report.submission,
            emails_data=[EmailData(email_to="<EMAIL>", email_cc=[], email_from="")],
        )


def test_k2_uw_missing_user(app_context, set_up_current_user):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    email = email_fixture(email_to="<EMAIL>", email_cc=[], email_account="<EMAIL>")
    report = report_with_submissions_fixture(
        broker_id=None, brokerage_id=None, organization_id=organization_id, correspondence_id=email.correspondence_id
    )
    db.session.commit()

    with pytest.raises(ValueError):
        K2UnderwriterAssigner().auto_assign_underwriters(report.submission)


def test_k2_uw_email_case_insensitivity(app_context, set_up_current_user):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    email = email_fixture(email_to="<EMAIL>", email_cc=[], email_account="<EMAIL>")
    report = report_with_submissions_fixture(
        broker_id=None,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        correspondence_id=email.correspondence_id,
    )
    db.session.commit()

    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user.id, "Correct underwriter should be assigned"


def test_k2_uw_multiple_emails(app_context, set_up_current_user):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    email1 = email_fixture(email_to="<EMAIL>", email_cc=[], email_account="<EMAIL>")
    correspondence_id = email1.correspondence_id
    email2 = email_fixture(
        correspondence_id=correspondence_id,
        email_to="<EMAIL>",
        email_cc=["<EMAIL>"],
        email_account="<EMAIL>",
    )
    db.session.commit()

    report = report_with_submissions_fixture(
        broker_id=None, brokerage_id=brokerage.id, organization_id=organization_id, correspondence_id=correspondence_id
    )
    db.session.commit()

    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user.id, "Correct underwriter should be assigned"


def test_k2_uw_default_assignment(app_context, set_up_current_user):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user = user_fixture(organization_id=organization_id, email="<EMAIL>")
    target_email = "<EMAIL>"
    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    email = email_fixture(email_to=target_email, email_cc=[], email_account="<EMAIL>")
    report = report_with_submissions_fixture(
        broker_id=None,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        correspondence_id=email.correspondence_id,
    )
    db.session.commit()

    assert SubmissionUser.query.count() == 0, "No underwriters should be assigned yet"

    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user.id, "Correct underwriter should be assigned"


def test_k2_uw_routing_tags_assignment(app_context, set_up_current_user):
    organization_id = ExistingOrganizations.K2.value
    organization_fixture(id=organization_id)

    user1 = user_fixture(organization_id=organization_id, email="<EMAIL>", user_id=1)
    user2 = user_fixture(organization_id=organization_id, email="<EMAIL>", user_id=2)

    brokerage = brokerage_fixture(organization_id=organization_id)
    db.session.commit()

    email = email_fixture(
        email_to="<EMAIL>", email_cc=[], email_account="<EMAIL>"
    )
    report = report_with_submissions_fixture(
        broker_id=None,
        brokerage_id=brokerage.id,
        organization_id=organization_id,
        correspondence_id=email.correspondence_id,
    )
    db.session.commit()

    assert SubmissionUser.query.count() == 0, "No underwriters should be assigned yet"

    # No routing tags
    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user1.id, "Correct underwriter should be assigned"

    routing_rule_fixture(
        tag="k2_best_uw",
        user_id=user2.id,
        organization_id=organization_id,
        is_case_sensitive=False,
        only_email_body=False,
    )
    report.routing_tags = ["k2_best_uw"]
    db.session.commit()

    # Routing tags present, but user doesn't belong to the submission's user group
    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user1.id, "Correct underwriter should be assigned"

    user_group = user_group_fixture(organization_id=organization_id, name=OrganizationGroups.K2_AEGIS.value)
    user_group.users.append(user2)
    db.session.commit()

    # Routing tags present and user belongs to the submission's user group
    K2UnderwriterAssigner().auto_assign_underwriters(report.submission)

    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user2.id, "Correct underwriter should be assigned"


###############################
#          Secura             #
###############################
def test_secura_uw_assigner(app_context, set_up_current_user, mocker):
    # This test is really simple because we rely on (tested in different integration test) SecuraService.
    # So here only test that assigner uses mapping correctly.
    secura_org_id = 64
    organization_fixture(id=secura_org_id)
    user = user_fixture(organization_id=secura_org_id)
    db.session.commit()
    report = report_with_submissions_fixture(organization_id=secura_org_id)
    db.session.commit()

    assert SubmissionUser.query.count() == 0

    mocker.patch("copilot.logic.users.secura_underwriter_assigner.SecuraService.find_kalepa_mapping", return_value=None)
    SecuraUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 0, "No underwriter should be assigned because there is no mapping"

    mocker.patch(
        "copilot.logic.users.secura_underwriter_assigner.SecuraService.find_kalepa_mapping",
        return_value=SecuraUWMapping(mapped_kalepa_user_id=user.id),
    )
    SecuraUnderwriterAssigner().auto_assign_underwriters(report.submission)
    assert SubmissionUser.query.count() == 1, "One underwriter should be assigned"
    assert SubmissionUser.query.first().user_id == user.id, "Correct underwriter should be assigned"


def test_aig_underwriter_assigner_basic_assignment(app_context, set_up_current_user):
    org = organization_fixture(id=ExistingOrganizations.AIG.value)
    user = user_fixture(organization_id=org.id, email="<EMAIL>", name="Over Writer")
    brokerage = brokerage_fixture(organization_id=ExistingOrganizations.AIG.value)
    broker = broker_fixture(
        brokerage_id=brokerage.id, organization_id=ExistingOrganizations.AIG.value, email="<EMAIL>"
    )
    mapping = aig_uw_mapping_fixture(broker_contact_email=broker.email, ww_uw_name="Over Writer", user_id=user.id)
    report = report_with_submissions_fixture(organization_id=org.id, broker_id=broker.id, brokerage_id=brokerage.id)
    db.session.commit()
    submission = report.submission

    assert SubmissionUser.query.count() == 0

    assigner = AIGUnderwriterAssigner()
    result = assigner.auto_assign_underwriters(submission)

    assert len(result) == 1
    su = SubmissionUser.query.first()
    assert su.user_id == user.id
    assert su.submission_id == submission.id


def test_aig_underwriter_assigner_fallback_to_name(app_context, set_up_current_user):
    org = organization_fixture(id=ExistingOrganizations.AIG.value)
    user = user_fixture(organization_id=org.id, email="<EMAIL>", name="Over Writer")
    brokerage = brokerage_fixture(organization_id=ExistingOrganizations.AIG.value)
    broker = broker_fixture(
        brokerage_id=brokerage.id, organization_id=ExistingOrganizations.AIG.value, email="<EMAIL>"
    )
    mapping = aig_uw_mapping_fixture(broker_contact_email=broker.email, ww_uw_name=user.name, user_id=None)
    report = report_with_submissions_fixture(organization_id=org.id, broker_id=broker.id, brokerage_id=brokerage.id)
    db.session.commit()
    submission = report.submission

    assert SubmissionUser.query.count() == 0

    assigner = AIGUnderwriterAssigner()
    result = assigner.auto_assign_underwriters(submission)

    assert len(result) == 1
    su = SubmissionUser.query.first()
    assert su.user_id == user.id
    assert su.submission_id == submission.id
