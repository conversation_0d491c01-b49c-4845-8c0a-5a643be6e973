from copilot.models.classification_observation import ClassificationObservationFactory


def test_get_class_idx_when_full_bar_expect_0():
    assert ClassificationObservationFactory.get_class_idx("full_bar") == 0


def test_get_class_idx_when_none_expect_2():
    assert ClassificationObservationFactory.get_class_idx("None") == 2


def test_get_class_idx_when_no_expect_2():
    assert ClassificationObservationFactory.get_class_idx("No") == 2
