from unittest.mock import patch

from copilot.clients.knock import (
    KnockClient,
    _extract_paragon_control_numbers_for_notification,
    append_idp_param,
    build_report_url,
)
from copilot.models.emails import Email

REPORT_URL = "https://copilot.kalepa.com/report/e4b8629b-d628-4de7-af38-094cc9c8a74d"


def test_append_signout_redirect_url_when_signout_redirect_is_none_expect_report_url():
    assert append_idp_param(REPORT_URL, None) == REPORT_URL


def test_append_signout_redirect_url_when_signout_redirect_is_not_none_expect_query_param():
    expected = f"{REPORT_URL}?idp=QBE-AD"
    assert append_idp_param(REPORT_URL, "QBE-AD") == expected


def test_append_navigation_source():
    expected = f"{REPORT_URL}?navigationSource=source"
    assert build_report_url(REPORT_URL, None, "source") == expected


def test_append_navigation_source_for_shared_notification():
    expected = f"{REPORT_URL}?idp=test-ad&navigationSource=source"
    assert build_report_url(REPORT_URL, "test-ad", "source") == expected


def test_extract_control_numbers_for_notification_none():
    with patch("copilot.clients.knock.extract_paragon_control_numbers", return_value=[]):
        result = _extract_paragon_control_numbers_for_notification(None)
        assert result == ""


def test_extract_control_numbers_for_notification_single():
    with patch("copilot.clients.knock.extract_paragon_control_numbers", return_value=[12345678]):
        result = _extract_paragon_control_numbers_for_notification("some_policy_id")
        assert result == "12345678"


def test_extract_control_numbers_for_notification_multiple():
    with patch("copilot.clients.knock.extract_paragon_control_numbers", return_value=[12345678, 98765432]):
        result = _extract_paragon_control_numbers_for_notification("some_policy_id")
        assert result == "12345678, 98765432"


def test_extract_control_numbers_for_notification_empty():
    with patch("copilot.clients.knock.extract_paragon_control_numbers", return_value=[]):
        result = _extract_paragon_control_numbers_for_notification("some_policy_id")
        assert result == ""


def test_extract_name_and_email():
    client = KnockClient("", "", env="test", interact_with_external_resources=False)
    name, email = client._extract_name_and_email(Email(email_from="<julie akin <<EMAIL>>>"))
    assert name == "julie akin"
    assert email == "<EMAIL>"
