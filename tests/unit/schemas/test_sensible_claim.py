from datetime import datetime
from unittest.mock import patch
from uuid import uuid4
import json

from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.models.file_onboarding import (
    PremisesInformationTableRow,
    PremisesInformationTableWithEntity,
)
from static_common.schemas.file_onboarding import LeanOnboardedFileSchema
import pytest

from copilot.clients.response_configuration.sensible import (
    ACORD_125_CONFIG,
    ACORD_126_CONFIG,
    ACORD_130_CONFIG,
    ACORD_131_CONFIG,
    ACORD_139_CONFIG,
    ACORD_140_CONFIG,
    ACORD_160_CONFIG,
)
from copilot.clients.response_mappers import (
    AcordMapper,
    _is_value_valid,
    _resolve_value_same_using_fallback,
)
from copilot.models.sensible_claim import (
    Acord125Form,
    Acord126Form,
    Acord130Form,
    Acord131Form,
    Acord139Form,
    Acord139PropertyEntry,
    Acord140Form,
    Acord160Form,
    AcordConfiguration,
    Applied130Form,
    SensibleClaim,
)
from copilot.schemas.sensible_claim import (
    Acord125Schema,
    Acord126Schema,
    Acord130Schema,
    Acord131Schema,
    Acord139FormSchema,
    Acord140Schema,
    Acord160FormSchema,
    Applied130Schema,
    SensibleClaimSchema,
    SensibleEntityPartValueSchema,
)
from tests.fact_subtypes.fact_subtypes import get_fact_subtypes_dump


@pytest.fixture
def sensible_claim_schema():
    return SensibleClaimSchema()


@pytest.fixture
def acord_125_schema():
    return Acord125Schema()


@pytest.fixture
def acord_126_schema():
    return Acord126Schema()


@pytest.fixture
def acord_140_schema():
    return Acord140Schema()


@pytest.fixture
def acord_131_schema():
    return Acord131Schema()


@pytest.fixture
def acord_130_schema():
    return Acord130Schema()


@pytest.fixture
def acord_139_schema():
    return Acord139FormSchema()


@pytest.fixture
def applied_130_schema():
    return Applied130Schema()


@pytest.fixture
def acord_160_schema():
    return Acord160FormSchema()


def test_acord_126_2007_05_multiple_docs_response(acord_126_schema):
    with open("tests/data/responses/acord_126_2007_05_multiple.json") as file:
        d = json.loads(file.read())
        acord_form: Acord126Form = acord_126_schema.load(d)
    assert acord_form.products_completed_operations


def test_acord_126_2016_09_response(acord_126_schema):
    with open("tests/data/responses/acord_126_2016_09.json") as file:
        d = json.loads(file.read())
        acord_form: Acord126Form = acord_126_schema.load(d)
    assert acord_form.application_information
    assert acord_form.schedule_of_hazards[0].location_number.value == 1
    assert acord_form.schedule_of_hazards[0].premium_basis.value == "Area"
    assert acord_form.schedule_of_hazards[1].location_number.value == 2
    assert acord_form.schedule_of_hazards[1].premium_basis.value == "Payroll"
    assert acord_form.schedule_of_hazards[2].location_number.value == 3
    assert acord_form.schedule_of_hazards[2].premium_basis is None


def test_acord_140_2016_03_response(acord_140_schema):
    with open("tests/data/responses/acord_140_2016_03.json") as file:
        d = json.loads(file.read())
        acord_form: Acord140Form = acord_140_schema.load(d)
    assert acord_form.building_information_list


def test_acord_140_2014_12_response(acord_140_schema):
    with open("tests/data/responses/acord_140_2014_12.json") as file:
        d = json.loads(file.read())
        acord_form: Acord140Form = acord_140_schema.load(d)
    assert acord_form.building_information_list


def test_acord_131_2009_10_response(acord_131_schema):
    with open("tests/data/responses/acord_131_2009_10.json") as file:
        d = json.loads(file.read())
        acord_form: Acord131Form = acord_131_schema.load(d)
    assert acord_form.premises_information


def test_acord_131_2011_11_response(acord_131_schema):
    with open("tests/data/responses/acord_131_2011_11.json") as file:
        d = json.loads(file.read())
        acord_form: Acord131Form = acord_131_schema.load(d)
    assert acord_form.premises_information


def test_acord_131_2013_12_response(acord_131_schema):
    with open("tests/data/responses/acord_131_2013_12.json") as file:
        d = json.loads(file.read())
        acord_form: Acord131Form = acord_131_schema.load(d)
    assert acord_form.premises_information


def test_acord_131_2016_04_response(acord_131_schema):
    with open("tests/data/responses/acord_131_2016_04.json") as file:
        d = json.loads(file.read())
        acord_form: Acord131Form = acord_131_schema.load(d)
    assert acord_form.premises_information


def test_acord_131_2017_11_response(acord_131_schema):
    with open("tests/data/responses/acord_131_2017_11.json") as file:
        d = json.loads(file.read())
        acord_form: Acord131Form = acord_131_schema.load(d)
    assert acord_form.premises_information


def test_acord_131_2009_10_polygon_response(acord_131_schema):
    with open("tests/data/responses/acord_131_2009_10_polygons.json") as file:
        d = json.loads(file.read())
        acord_form: Acord131Form = acord_131_schema.load(d)
    assert acord_form.premises_information


def test_acord_130_2017_05_response(acord_130_schema):
    with open("tests/data/responses/acord_130_2017_05.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d)
    assert acord_form.policy_information
    assert "Rice Milling" in acord_form.nature_of_business[0].description_of_operations.value
    assert acord_form.premium_information[0].state.value == "MA"
    assert acord_form.premium_information[0].experience_mod.value == 0.91

    mapping_config = AcordConfiguration(Acord130Schema(), ACORD_130_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 1, submission_id=uuid4())
    assert next(f for f in mapping_result.raw_processed_data.fields if f.fact_subtype_id == FactSubtypeID.OPERATIONS)


def test_applied_130_api_2013_01_response(applied_130_schema):
    with open("tests/data/responses/applied_130_api_2013_01.json") as file:
        d = json.loads(file.read())
        acord_form: Applied130Form = applied_130_schema.load(d)
    assert acord_form.locations


def test_applied_130_api_2005_08_response(applied_130_schema):
    with open("tests/data/responses/applied_130_api_2005_08.json") as file:
        d = json.loads(file.read())
        acord_form: Applied130Form = applied_130_schema.load(d)
    assert acord_form.locations


def test_accord_130_2017_05_table_approach(acord_130_schema):
    with open("tests/data/responses/accord_130_2017_05_table_approach.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d["documents"][0]["output"]["parsedDocument"])
    assert acord_form.locations
    assert len(acord_form.rating_information) == 1
    assert len(acord_form.rating_information[0].table) == 5
    assert acord_form.rating_information[0].state is None


def test_accord_130_2013_08_table_approach(acord_130_schema):
    with open("tests/data/responses/accord_130_2013_09_table_approach.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d["parsed_document"])
    assert acord_form.locations
    assert len(acord_form.rating_information) == 1
    assert len(acord_form.rating_information[0].table) == 3
    assert acord_form.rating_information[0].state.value == "NJ"


def test_accord_130_2013_08_table_approach_in_house(acord_130_schema):
    with open("tests/data/responses/accord_130_2013_09_table_approach_in_house.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d["parsed_document"])
    assert acord_form.locations
    assert len(acord_form.rating_information) == 1
    assert len(acord_form.rating_information[0].table) == 2
    assert acord_form.rating_information[0].state.value == "CA"


def test_acord_130_2019_07_fl_response(acord_130_schema):
    with open("tests/data/responses/acord_130_2019_07_fl.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d)
    assert acord_form.locations
    assert len(acord_form.rating_information) == 1
    assert len(acord_form.rating_information[0].table) == 5
    assert acord_form.rating_information[0].state is None


def test_acord_130_ca_2023_01_response(acord_130_schema):
    with open("tests/data/responses/acord_130_ca_2023_01.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d)
    assert acord_form.locations
    assert len(acord_form.rating_information) == 1
    assert len(acord_form.rating_information[0].table) == 3
    assert acord_form.rating_information[0].state is None


def test_acord_130_ca_2019_01_response(acord_130_schema):
    with open("tests/data/responses/acord_130_ca_2019_01.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d)
    assert acord_form.locations
    assert len(acord_form.rating_information) == 1
    assert len(acord_form.rating_information[0].table) == 3
    assert acord_form.rating_information[0].state.value == "CA"


def test_acord_130_2013_01_response(acord_130_schema):
    with open("tests/data/responses/acord_130_2013_01.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d)
    assert acord_form.locations
    assert len(acord_form.rating_information) == 1
    assert len(acord_form.rating_information[0].table) == 8
    assert acord_form.rating_information[0].state is None


def test_acord_130_2000_08_response(acord_130_schema):
    with open("tests/data/responses/acord_130_2000_08.json") as file:
        d = json.loads(file.read())
        acord_form: Acord130Form = acord_130_schema.load(d)
    assert acord_form.locations
    assert len(acord_form.rating_information) == 1
    assert len(acord_form.rating_information[0].table) == 1
    assert acord_form.rating_information[0].state is None


def test_is_valid_str_value():
    assert _is_value_valid(
        "See attached loss runs for all claim data. Latest claim statuses also attached. Insured is on Pay as you Go"
        " plan."
    )
    assert not _is_value_valid("See attached aa")


def test_acord_125_2013_01_response(acord_125_schema):
    with open("tests/data/responses/acord_125_2013_01.json") as file:
        d = json.loads(file.read())
        acord_form: Acord125Form = acord_125_schema.load(d)
    assert acord_form


@pytest.mark.parametrize(
    "address, is_valid",
    [
        ("Hawkins Service Company, LLC 10517 Riverview Drive Riverview, FL 33578", True),
        ("Hawkins Service Comp any, LLC 10517 Riverview Drive Riverview, FL 33578", False),
        ("Hawkins Service Comp+any, LLC 10517 Riverview Drive Riverview, FL 33578", False),
        ("any Hawkins Service Company, LLC 10517 Riverview Drive Riverview, FL 33578", False),
        ("Hawkins Service Company, LLC 10517 Riverview Drive Riverview, FL 33578 any", False),
    ],
)
def test_marking_addresses_as_invalid(address: str, is_valid: bool):
    data = {"value": address}
    cls = SensibleEntityPartValueSchema()
    cls.mark_invalid_address_part(data)
    result = bool(data.get("value"))
    assert result == is_valid


def test_loss_run_with_polygons(sensible_claim_schema):
    with open("tests/data/responses/loss_run_with_polygons.json") as file:
        d = json.loads(file.read())
        loss_runs: list[SensibleClaim] = sensible_claim_schema.load(d["claims"], many=True)
    assert len(loss_runs[0].claim_number.lines) == 1


def test_acord_160_2011_10_response_base(acord_160_schema):
    with open("tests/data/responses/acord_160_2011_10.json") as file:
        d = json.loads(file.read())
        acord_form: Acord160Form = acord_160_schema.load(d)
    assert acord_form.agency_information
    assert acord_form.agency_information[0].agency.value == "World Insurance Associates, LLC"
    assert acord_form.agency_information[0].policy_type.value == "policy_type_special"
    assert acord_form.named_insured.value == "PRAMUKH CORP DBA QUALITY INN AND SUITES"

    mapping_config = AcordConfiguration(Acord160FormSchema(), ACORD_160_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 1, submission_id=uuid4())

    assert len(mapping_result.raw_processed_data.entities) == 3
    business = mapping_result.raw_processed_data.entities[0]
    assert business.is_business
    assert business.acord_location_number == 1
    assert business.acord_building_number is None
    structure = mapping_result.raw_processed_data.entities[1]
    assert not structure.is_business
    assert structure.type == SubmissionEntityType.STRUCTURE
    assert structure.acord_location_number == 1
    assert structure.acord_building_number == 1
    submission = mapping_result.raw_processed_data.entities[2]
    assert not submission.is_business
    assert submission.type == SubmissionEntityType.SUBMISSION

    property_description = next(
        f for f in mapping_result.raw_processed_data.fields if f.fact_subtype_id == FactSubtypeID.PROPERTY_DESCRIPTION
    )
    assert property_description is not None
    assert len(property_description.values) == 1
    assert property_description.values[0].value == "89 Unit Hotel"
    assert property_description.values[0].entity_idx == 1

    distance_to_hydrant = next(
        f
        for f in mapping_result.raw_processed_data.fields
        if f.fact_subtype_id == FactSubtypeID.DISTANCE_TO_FIRE_HYDRANT
    )
    assert distance_to_hydrant is not None
    assert len(distance_to_hydrant.values) == 1
    assert distance_to_hydrant.values[0].value == "999 FT"
    assert distance_to_hydrant.values[0].entity_idx == 1

    asbestos_removal = next(
        (
            f
            for f in mapping_result.raw_processed_data.fields
            if f.fact_subtype_id == FactSubtypeID.PROJECT_ASBESTOS_OTHER_HAZARDOUS_REMOVAL
        ),
        None,
    )
    assert asbestos_removal is None

    year_built = next(
        f for f in mapping_result.raw_processed_data.fields if f.fact_subtype_id == FactSubtypeID.YEAR_BUILT
    )
    assert year_built is not None
    assert len(year_built.values) == 1
    assert year_built.values[0].value == 1966
    assert year_built.values[0].entity_idx == 1

    building_value = next(
        f for f in mapping_result.raw_processed_data.fields if f.fact_subtype_id == FactSubtypeID.BUILDING_VALUE
    )
    assert building_value is not None
    assert len(building_value.values) == 1
    assert building_value.values[0].value == 8026
    assert building_value.values[0].entity_idx == 1

    building_value_method = next(
        f
        for f in mapping_result.raw_processed_data.fields
        if f.fact_subtype_id == FactSubtypeID.BUILDING_VALUATION_METHOD
    )
    assert building_value_method is not None
    assert len(building_value_method.values) == 1
    assert building_value_method.values[0].value == "RC"
    assert building_value_method.values[0].entity_idx == 1

    bpp = next(f for f in mapping_result.raw_processed_data.fields if f.fact_subtype_id == FactSubtypeID.BPP)
    assert bpp is not None
    assert len(bpp.values) == 1
    assert bpp.values[0].value == 700000
    assert bpp.values[0].entity_idx == 1


def test_acord_160_2011_10_response_structures(acord_160_schema):
    with open("tests/data/responses/acord_160_2011_10_with_structures.json") as file:
        d = json.loads(file.read())
        acord_form: Acord160Form = acord_160_schema.load(d)
    assert acord_form.agency_information
    assert acord_form.agency_information[0].agency.value == "First Casualty Insurance Agency Inc."
    assert acord_form.agency_information[0].policy_type.value == "policy_type_special"
    assert acord_form.named_insured.value == "WHITLEY POWER EQUIPMENT INC"

    mapping_config = AcordConfiguration(Acord160FormSchema(), ACORD_160_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 1, submission_id=uuid4())

    assert len(mapping_result.raw_processed_data.entities) == 6
    assert mapping_result.raw_processed_data.entities[0].is_business
    assert mapping_result.raw_processed_data.entities[0].acord_location_number == 1
    assert mapping_result.raw_processed_data.entities[0].acord_building_number is None
    assert mapping_result.raw_processed_data.entities[1].type == SubmissionEntityType.STRUCTURE
    assert mapping_result.raw_processed_data.entities[1].parent_idx == 0
    assert mapping_result.raw_processed_data.entities[1].acord_location_number == 1
    assert mapping_result.raw_processed_data.entities[1].acord_building_number == 1
    assert mapping_result.raw_processed_data.entities[2].type == SubmissionEntityType.STRUCTURE
    assert mapping_result.raw_processed_data.entities[2].parent_idx == 0
    assert mapping_result.raw_processed_data.entities[2].acord_location_number == 1
    assert mapping_result.raw_processed_data.entities[2].acord_building_number == 2
    assert mapping_result.raw_processed_data.entities[3].type == SubmissionEntityType.STRUCTURE
    assert mapping_result.raw_processed_data.entities[3].parent_idx == 0
    assert mapping_result.raw_processed_data.entities[3].acord_location_number == 1
    assert mapping_result.raw_processed_data.entities[3].acord_building_number == 3
    assert mapping_result.raw_processed_data.entities[4].type == SubmissionEntityType.STRUCTURE
    assert mapping_result.raw_processed_data.entities[4].parent_idx == 0
    assert mapping_result.raw_processed_data.entities[4].acord_location_number == 1
    assert mapping_result.raw_processed_data.entities[4].acord_building_number == 4
    assert mapping_result.raw_processed_data.entities[5].type == SubmissionEntityType.SUBMISSION


def test_acord_160_2011_10_response_premises(acord_160_schema):
    with open("tests/data/responses/acord_160_2011_10_with_premises.json") as file:
        d = json.loads(file.read())
        acord_form: Acord160Form = acord_160_schema.load(d)
    assert acord_form.agency_information
    assert acord_form.agency_information[0].agency.value == "Spectrum Insurance Group Wisconsin Valley"
    assert acord_form.agency_information[0].policy_type is None
    assert acord_form.named_insured.value == "Ace's Pro Shop LLC"

    mapping_config = AcordConfiguration(Acord160FormSchema(), ACORD_160_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 1, submission_id=uuid4())

    assert len(mapping_result.raw_processed_data.entities) == 5
    assert mapping_result.raw_processed_data.entities[0].is_business
    assert mapping_result.raw_processed_data.entities[1].type == SubmissionEntityType.STRUCTURE
    assert mapping_result.raw_processed_data.entities[1].parent_idx == 0
    assert mapping_result.raw_processed_data.entities[2].is_business
    assert mapping_result.raw_processed_data.entities[3].type == SubmissionEntityType.STRUCTURE
    assert mapping_result.raw_processed_data.entities[3].parent_idx == 2
    assert mapping_result.raw_processed_data.entities[4].type == SubmissionEntityType.SUBMISSION

    bpp = next(f for f in mapping_result.raw_processed_data.fields if f.fact_subtype_id == FactSubtypeID.BPP)
    assert bpp
    assert len(bpp.values) == 2
    assert bpp.values[0].value == 40000
    assert bpp.values[0].entity_idx == 1
    assert bpp.values[1].value == 100000
    assert bpp.values[1].entity_idx == 3


def test_acord_140_2016_03_response_premises(acord_140_schema):
    with open("tests/data/responses/acord_140_2016_03_response_premises.json") as file:
        # extraction_id: a5f249f5-c273-4745-a194-2ad2b809b302
        d = json.loads(file.read())
        acord_form: Acord140Form = acord_140_schema.load(d)
    assert acord_form.agency_information
    assert acord_form.agency_information[0].agency.value == "Benner Insurance"
    assert acord_form.named_insured.value == "Great Lakes Commercial Roofing LLC"

    mapping_config = AcordConfiguration(Acord140Schema(), ACORD_140_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 64, submission_id=uuid4())

    assert len(mapping_result.raw_processed_data.entities) == 3
    expected_entities = [
        {"type": SubmissionEntityType.BUSINESS, "acord_building_number": None, "acord_location_number": 2},
        {
            "type": SubmissionEntityType.STRUCTURE,
            "parent_idx": 0,
            "acord_building_number": None,  # it should be 1 but did not get extracted
            "acord_location_number": 2,
        },
        {"type": SubmissionEntityType.SUBMISSION, "acord_building_number": None, "acord_location_number": None},
    ]
    for i, expected in enumerate(expected_entities):
        entity = mapping_result.raw_processed_data.entities[i]
        for attr, expected_value in expected.items():
            actual_value = getattr(entity, attr)
            assert (
                actual_value == expected_value
            ), f"Entity {i} attribute '{attr}' mismatch: expected {expected_value}, got {actual_value}"
    assert mapping_result.transient_data["named_insured"] == "Great Lakes Commercial Roofing LLC"
    assert (
        mapping_result.transient_data["premises_info"][0]["premises_information_table"][0]["subject_of_insurance"]
        == "Business Pers Property"
    )
    assert mapping_result.transient_data["premises_info"][0]["premises_information_table"][0]["amount"] == 70000.0
    assert (
        mapping_result.transient_data["premises_info"][0]["premises_information_table"][0]["coins_percentage"] == 100.0
    )
    assert mapping_result.transient_data["premises_info"][0]["premises_information_table"][0]["deductible"] == 1000.0
    assert mapping_result.transient_data["premises_info"][0]["premises_information_table"][0]["valuation"] == "RC"
    assert mapping_result.transient_data["premises_info"][0]["requested_address"] is None
    assert mapping_result.transient_data["premises_info"][0]["location_number"] == 2
    assert mapping_result.transient_data["premises_info"][0]["building_number"] is None


def test_acord_140_2014_12_response_premises(acord_140_schema):
    with open("tests/data/responses/acord_140_2014_12_response_premises.json") as file:
        # extraction_id: 4518e60f-1e13-424c-b216-eb01463ebc9e
        d = json.loads(file.read())
        acord_form: Acord140Form = acord_140_schema.load(d)
    assert acord_form.agency_information
    assert acord_form.agency_information[0].agency.value == "Belmont Insurance Brokerage, Inc."
    assert acord_form.named_insured.value == "Polish American Association"

    mapping_config = AcordConfiguration(Acord140Schema(), ACORD_140_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 64, submission_id=uuid4())

    assert len(mapping_result.raw_processed_data.entities) == 14
    expected_entities = [
        {"type": SubmissionEntityType.BUSINESS, "acord_building_number": None, "acord_location_number": 1},
        {
            "type": SubmissionEntityType.STRUCTURE,
            "parent_idx": 0,
            "acord_building_number": 1,
            "acord_location_number": 1,
        },
        {
            "type": SubmissionEntityType.STRUCTURE,
            "parent_idx": 0,
            "acord_building_number": 2,
            "acord_location_number": 1,
        },
        {
            "type": SubmissionEntityType.BUSINESS,
            "acord_building_number": None,
            "acord_location_number": 2,
        },
        {
            "type": SubmissionEntityType.STRUCTURE,
            "parent_idx": 3,
            "acord_building_number": 1,
            "acord_location_number": 2,
        },
        {
            "type": SubmissionEntityType.BUSINESS,
            "acord_building_number": None,
            "acord_location_number": 3,
        },
        {
            "type": SubmissionEntityType.STRUCTURE,
            "parent_idx": 5,
            "acord_building_number": 1,
            "acord_location_number": 3,
        },
        {
            "type": SubmissionEntityType.BUSINESS,
            "acord_building_number": None,
            "acord_location_number": 4,
        },
        {
            "type": SubmissionEntityType.STRUCTURE,
            "parent_idx": 7,
            "acord_building_number": 1,
            "acord_location_number": 4,
        },
        {
            "type": SubmissionEntityType.BUSINESS,
            "acord_building_number": None,
            "acord_location_number": 5,
        },
        {
            "type": SubmissionEntityType.STRUCTURE,
            "parent_idx": 9,
            "acord_building_number": 1,
            "acord_location_number": 5,
        },
        {
            "type": SubmissionEntityType.BUSINESS,
            "acord_building_number": None,
            "acord_location_number": 6,
        },
        {
            "type": SubmissionEntityType.STRUCTURE,
            "parent_idx": 11,
            "acord_building_number": 1,
            "acord_location_number": 6,
        },
        {"type": SubmissionEntityType.SUBMISSION, "acord_building_number": None, "acord_location_number": None},
    ]
    for i, expected in enumerate(expected_entities):
        entity = mapping_result.raw_processed_data.entities[i]
        for attr, expected_value in expected.items():
            actual_value = getattr(entity, attr)
            assert (
                actual_value == expected_value
            ), f"Entity {i} attribute '{attr}' mismatch: expected {expected_value}, got {actual_value}"

    assert mapping_result.transient_data["named_insured"] == "Polish American Association"

    expected_values = [
        # table 0
        {
            "premises_index": 0,
            "table_index": 0,
            "building_number": 1,
            "location_number": 1,
            "subject": "Building",
            "amount": 3092400.0,
        },
        {
            "premises_index": 0,
            "table_index": 1,
            "building_number": 1,
            "location_number": 1,
            "subject": "Business Personal Property",
            "amount": 400000.0,
        },
        # table 1
        {
            "premises_index": 1,
            "table_index": 0,
            "building_number": 2,
            "location_number": 1,
            "subject": "Personal Property of Others - Condo Unit Owner",
            "amount": 10000.0,
        },
        # table 2
        {
            "premises_index": 2,
            "table_index": 0,
            "building_number": 1,
            "location_number": 2,
            "subject": "Building",
            "amount": 2006000.0,
        },
        {
            "premises_index": 2,
            "table_index": 1,
            "subject": "Business Personal Property",
            "building_number": 1,
            "location_number": 2,
            "amount": 200000.0,
        },
        {
            "premises_index": 2,
            "table_index": 2,
            "building_number": 1,
            "location_number": 2,
            "subject": "Personal Property of Others - Condo Unit Owne",
            "amount": 10000.0,
        },
        # table 3
        {
            "premises_index": 3,
            "table_index": 0,
            "building_number": 1,
            "location_number": 3,
            "subject": "Building",
            "amount": 1229000.0,
        },
        {
            "premises_index": 3,
            "table_index": 1,
            "building_number": 1,
            "location_number": 3,
            "subject": "Business Personal Property",
            "amount": 200000.0,
        },
        {
            "premises_index": 3,
            "table_index": 2,
            "building_number": 1,
            "location_number": 3,
            "subject": "Personal Property of Others - Condo Unit Owner",
            "amount": 10000.0,
        },
        # table 4
        {
            "premises_index": 4,
            "table_index": 0,
            "building_number": 1,
            "location_number": 4,
            "subject": "Business Personal Property",
            "amount": 25000.0,
        },
        # table 5
        {
            "premises_index": 5,
            "table_index": 0,
            "building_number": 1,
            "location_number": 5,
            "subject": "Business Personal Property",
            "amount": 75000.0,
        },
    ]

    for item in expected_values:
        actual_entry_table = mapping_result.transient_data["premises_info"][item["premises_index"]]
        actual_entry_row = actual_entry_table["premises_information_table"][item["table_index"]]

        assert actual_entry_table["location_number"] == item["location_number"], (
            f'Expected location_number "{item["location_number"]}", '
            f'got "{actual_entry_table["location_number"]}" '
            f'at premises {item["premises_index"]}, table {item["table_index"]}'
        )
        assert actual_entry_table["building_number"] == item["building_number"], (
            f'Expected building_number "{item["building_number"]}", '
            f'got "{actual_entry_table["building_number"]}" '
            f'at premises {item["premises_index"]}, table {item["table_index"]}'
        )
        assert actual_entry_row["subject_of_insurance"] == item["subject"], (
            f'Expected subject "{item["subject"]}", '
            f'got "{actual_entry_row["subject_of_insurance"]}" '
            f'at premises {item["premises_index"]}, table {item["table_index"]}'
        )
        assert actual_entry_row["amount"] == item["amount"], (
            f'Expected amount {item["amount"]}, '
            f'got {actual_entry_row["amount"]} '
            f'at premises {item["premises_index"]}, table {item["table_index"]}'
        )


@pytest.mark.parametrize(
    "value, fallback, result",
    [
        ("same", "Address 1", "Address 1"),
        ("SAME", "Address 1", "Address 1"),
        ("same same", "Address 1", "Address 1"),
        ("samesame", "Address 1", "samesame"),
        ("Some Address same", "Address 1", "Some Address same"),
    ],
)
def test_resolve_value_same_using_fallback(value: str, fallback: str, result: str):
    with patch("copilot.logic.onboarded_files_transformation.logger") as mock_logger:
        assert _resolve_value_same_using_fallback(value, fallback, mock_logger) == result


def test_acord_125_same_as_requested_address(acord_125_schema: Acord125Schema):
    with open("tests/data/responses/acord_125_2016_03_address_as_same_result_in_house.json") as file:
        # file_id: '03937615-6507-444f-8806-b262ed70dc05'
        d = json.loads(file.read())
        acord_form: Acord125Form = acord_125_schema.load(d)

    assert acord_form.premises_info[0].requested_address.value == "SAME"

    mapping_config = AcordConfiguration(Acord125Schema(), ACORD_125_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 64, submission_id=uuid4())

    for entity_name in mapping_result.raw_processed_data.entity_information[0].values:
        assert entity_name.value == "STRAKE, LLC DBA: STRAKE STUDIO"
    for entity_address in mapping_result.raw_processed_data.entity_information[1].values:
        assert entity_address.value == "3600 LEASON RD STERLING HEIGHTS, MI 48310"


def test_acord_126_with_not_null_fields_as_null(acord_126_schema):
    with open("tests/data/responses/not_null_fields_as_null_acord_126_2025_03.json") as file:
        # extraction_id: 68841243-c879-4227-b9df-54e3102291ff
        d = json.loads(file.read())
        acord_form: Acord126Form = acord_126_schema.load(d)

    assert d["general_information"][0]["equipment_loaned_or_rented_list"] is None
    assert d["general_information"][0]["lease_employees_other_employers_lease_from_list"] is None
    acord_configuration = AcordConfiguration(Acord126Schema(), ACORD_126_CONFIG, True)
    schema = acord_configuration.schema
    acord_data = schema.load(d)
    assert acord_data.general_information[0].equipment_loaned_or_rented_list is None
    assert acord_data.general_information[0].lease_employees_other_employers_lease_from_list is None


def test_acord_139_response(acord_139_schema):
    with open("tests/data/responses/acord_139_2015_12.json") as file:
        # file_id: 58a7d9d4-d2c5-4c3b-8865-a5cc710bfacb
        d = json.loads(file.read())
        acord_form: Acord139Form = acord_139_schema.load(d)

    assert acord_form.statement_of_values_table[0].building_number.value == 1
    assert acord_form.statement_of_values_table[0].location_number.value == 1
    assert acord_form.statement_of_values_table[0].subject.value == "B"
    assert acord_form.statement_of_values_table[0].valuation.value == "RC"
    assert acord_form.statement_of_values_table[0].value_100_percent.value == 225000.0

    mapping_config = AcordConfiguration(Acord139FormSchema(), ACORD_139_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 64, submission_id=uuid4())

    assert mapping_result.transient_data["premises_info"][0] == {
        "location_number": 1,
        "building_number": 1,
        "subject": "B",
        "value_100_percent": 225000.0,
        "valuation": "RC",
        "requested_name": None,
        "requested_address": None,
    }

    assert mapping_result.transient_data_object.premises_info[0] == PremisesInformationTableWithEntity(
        location_number=1,
        building_number=1,
        premises_information_table=[
            PremisesInformationTableRow(
                subject_of_insurance="B",
                amount=225000.0,
                valuation="RC",
            ),
            PremisesInformationTableRow(
                subject_of_insurance="BPP",
                amount=20000.0,
                valuation="RC",
            ),
            PremisesInformationTableRow(
                subject_of_insurance="Stock/INV",
                amount=15000.0,
                valuation="RC",
            ),
            PremisesInformationTableRow(
                subject_of_insurance="Sign",
            ),
        ],
    )


def test_acord_131_save_to_json(acord_131_schema):
    with open("tests/data/responses/acord_131_2017_11_with_ts_values.json") as file:
        # extraction_id: 26a151ab-afa3-4d2a-b345-bdc1d03b7de8, file_id: d3377eae-beb8-43aa-bff2-d349abb1209f
        d = json.loads(file.read())
        acord_form: Acord131Form = acord_131_schema.load(d)

    mapping_config = AcordConfiguration(Acord131Schema(), ACORD_131_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 64, submission_id=uuid4())

    today = datetime.now()
    last_year = today.year - 1
    start_of_last_year = datetime(year=last_year, month=1, day=1)
    end_of_last_year = datetime(year=last_year, month=12, day=31)

    expected_timeseries_150k = json.dumps(
        {
            "values": [150000.0],
            "units": "USD",
            "interval": "ANNUAL",
            "times": [str(end_of_last_year)],
            "start_dates": [str(start_of_last_year)],
        }
    )

    expected_timeseries_500k = json.dumps(
        {
            "values": [500000.0],
            "units": "USD",
            "interval": "ANNUAL",
            "times": [str(end_of_last_year)],
            "start_dates": [str(start_of_last_year)],
        }
    )

    assert mapping_result.raw_processed_data.fields[0].fact_subtype_id == FactSubtypeID.PAYROLL
    assert mapping_result.raw_processed_data.fields[0].values[0].value == expected_timeseries_150k
    assert mapping_result.raw_processed_data.fields[1].fact_subtype_id == FactSubtypeID.TOTAL_SALES
    assert mapping_result.raw_processed_data.fields[1].values[0].value == expected_timeseries_500k


def test_acord_140_with_no_building_and_location_number(acord_140_schema):
    with open("tests/data/responses/acord_140_2016_03_with_no_bld_and_loc_number.json") as file:
        # extraction_id: b176ec72-1be4-43c5-b06e-3f13b9562c9c; file_id: ca0cd86b-fe42-4fc6-9145-765216cebb6b
        d = json.loads(file.read())
        acord_form: Acord140Form = acord_140_schema.load(d)

    mapping_config = AcordConfiguration(Acord140Schema(), ACORD_140_CONFIG, True)
    acord_mapper = AcordMapper()
    fact_subtypes = get_fact_subtypes_dump()
    acord_mapper._fact_subtypes = {fs.id: fs for fs in fact_subtypes}
    acord_mapper._get_available_coverages = lambda x: []
    mapping_result = acord_mapper.process_response(mapping_config.mapping_config, acord_form, 64, submission_id=uuid4())

    # with following asserts we see that the parsing result is saved
    assert mapping_result.raw_processed_data.fields[0].name == "Property Description"
    assert mapping_result.raw_processed_data.fields[0].values[0].value == "Bldg, BPP, Sign, Food Spoilage"
    assert mapping_result.transient_data["named_insured"] == "Sauce Bar, Inc"
    assert mapping_result.transient_data["premises_info"][0]["requested_name"] == "Sauce Bar, Inc"
    assert mapping_result.transient_data["premises_info"][0]["premises_information_table"][0] == {
        "subject_of_insurance": "BUILDING",
        "amount": 125000.0,
        "coins_percentage": 90.0,
        "causes_of_loss": "REPL COST",
        "deductible": 2500.0,
        "forms_and_conditions_to_apply": "Special Form",
    }
