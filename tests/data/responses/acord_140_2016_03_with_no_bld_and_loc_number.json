{"named_insured": {"type": "string", "value": "Sauce Bar, Inc", "lines": [{"text": "Sauce Bar, Inc", "page": 0, "boundingPolygon": [{"x": 4.227, "y": 1.462}, {"x": 4.96, "y": 1.461}, {"x": 4.96, "y": 1.565}, {"x": 4.228, "y": 1.566}], "confidence": 0.9972882843017579}], "anchorConfidence": 0.9968543243408203, "valueConfidence": 0.9972882843017579}, "agency_information": [{"agency_customer_id": null, "date": {"source": "5/9/2025", "value": "2025-05-09T00:00:00.000Z", "type": "date", "lines": [{"text": "DATE (MM/DD/YYYY)", "page": 0, "boundingPolygon": [{"x": 7.142, "y": 0.63}, {"x": 7.956, "y": 0.629}, {"x": 7.956, "y": 0.719}, {"x": 7.142, "y": 0.72}], "confidence": 0.995745849609375}, {"text": "5/9/2025", "page": 0, "boundingPolygon": [{"x": 7.331, "y": 0.794}, {"x": 7.764, "y": 0.793}, {"x": 7.764, "y": 0.886}, {"x": 7.332, "y": 0.887}], "confidence": 0.9964375305175781}], "anchorConfidence": 0.9994456481933593, "valueConfidence": 0.9964375305175781}, "agency": {"type": "string", "value": "Shears II Insurance Agency, Inc.", "lines": [{"text": "Shears II Insurance Agency, Inc.", "page": 0, "boundingPolygon": [{"x": 0.235, "y": 1.127}, {"x": 1.833, "y": 1.124}, {"x": 1.833, "y": 1.239}, {"x": 0.236, "y": 1.242}], "confidence": 0.9459770965576172}], "anchorConfidence": 0.9985862731933594, "valueConfidence": 0.9459770965576172}, "policy_number": null, "carrier": null, "naics_code": null, "effective_date": null, "named_insured": {"type": "string", "value": "Sauce Bar, Inc", "lines": [{"text": "Sauce Bar, Inc", "page": 0, "boundingPolygon": [{"x": 4.227, "y": 1.462}, {"x": 4.96, "y": 1.461}, {"x": 4.96, "y": 1.565}, {"x": 4.228, "y": 1.566}], "confidence": 0.9972882843017579}], "anchorConfidence": 0.9968543243408203, "valueConfidence": 0.9972882843017579}}, {"agency_customer_id": null, "date": null, "agency": null, "policy_number": null, "carrier": null, "naics_code": null, "effective_date": null, "named_insured": null}, {"agency_customer_id": null, "date": null, "agency": null, "policy_number": null, "carrier": null, "naics_code": null, "effective_date": null, "named_insured": null}], "building_information_list": [{"premises_information_table": [{"subject_of_insurance": {"value": "BUILDING", "type": "string", "lines": [{"text": "BUILDING", "page": 0, "boundingPolygon": [{"x": 0.168, "y": 2.746}, {"x": 1.803, "y": 2.746}, {"x": 1.803, "y": 3.073}, {"x": 0.168, "y": 3.073}], "confidence": 0.998658676147461}], "valueConfidence": 0.998658676147461}, "amount": {"source": "125,000", "value": 125000, "unit": "$", "type": "currency", "lines": [{"text": "125,000", "page": 0, "boundingPolygon": [{"x": 1.803, "y": 2.746}, {"x": 2.811, "y": 2.746}, {"x": 2.811, "y": 3.073}, {"x": 1.803, "y": 3.073}], "confidence": 0.9989112091064453}], "valueConfidence": 0.9989112091064453}, "coins_percentage": {"source": "90", "value": 90, "type": "number", "lines": [{"text": "90%", "page": 0, "boundingPolygon": [{"x": 2.811, "y": 2.746}, {"x": 3.214, "y": 2.747}, {"x": 3.214, "y": 3.073}, {"x": 2.811, "y": 3.073}], "confidence": 0.999439697265625}], "valueConfidence": 0.999439697265625}, "valuation": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 3.214, "y": 2.747}, {"x": 3.513, "y": 2.747}, {"x": 3.513, "y": 3.073}, {"x": 3.214, "y": 3.073}], "confidence": null}], "valueConfidence": null}, "causes_of_loss": {"value": "REPL COST", "type": "string", "lines": [{"text": "REPL COST", "page": 0, "boundingPolygon": [{"x": 3.513, "y": 2.747}, {"x": 4.409, "y": 2.747}, {"x": 4.409, "y": 3.073}, {"x": 3.513, "y": 3.073}], "confidence": 0.9973800277709961}], "valueConfidence": 0.9973800277709961}, "inflation_guard_percentage": null, "deductible": {"source": "2500.00", "value": 2500, "unit": "$", "type": "currency", "lines": [{"text": "2500.00", "page": 0, "boundingPolygon": [{"x": 4.909, "y": 2.747}, {"x": 5.458, "y": 2.747}, {"x": 5.459, "y": 3.073}, {"x": 4.91, "y": 3.073}], "confidence": 0.9994342041015625}], "valueConfidence": 0.9994342041015625}, "deductible_type": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 5.458, "y": 2.747}, {"x": 5.903, "y": 2.747}, {"x": 5.903, "y": 3.074}, {"x": 5.459, "y": 3.073}], "confidence": null}], "valueConfidence": null}, "blanket_number": null, "forms_and_conditions_to_apply": {"value": "Special Form", "type": "string", "lines": [{"text": "Special Form", "page": 0, "boundingPolygon": [{"x": 6.153, "y": 2.747}, {"x": 8.143, "y": 2.747}, {"x": 8.143, "y": 3.074}, {"x": 6.154, "y": 3.074}], "confidence": 0.9995691299438476}], "valueConfidence": 0.9995691299438476}}, {"subject_of_insurance": {"value": "Contents", "type": "string", "lines": [{"text": "Contents", "page": 0, "boundingPolygon": [{"x": 0.168, "y": 3.073}, {"x": 1.803, "y": 3.073}, {"x": 1.803, "y": 3.407}, {"x": 0.169, "y": 3.406}], "confidence": 0.9989628601074219}], "valueConfidence": 0.9989628601074219}, "amount": {"source": "180,000", "value": 180000, "unit": "$", "type": "currency", "lines": [{"text": "180,000", "page": 0, "boundingPolygon": [{"x": 1.803, "y": 3.073}, {"x": 2.811, "y": 3.073}, {"x": 2.811, "y": 3.407}, {"x": 1.803, "y": 3.407}], "confidence": 0.9985995483398438}], "valueConfidence": 0.9985995483398438}, "coins_percentage": {"source": "90", "value": 90, "type": "number", "lines": [{"text": "90", "page": 0, "boundingPolygon": [{"x": 2.811, "y": 3.073}, {"x": 3.214, "y": 3.073}, {"x": 3.214, "y": 3.407}, {"x": 2.811, "y": 3.407}], "confidence": 0.9997299194335938}], "valueConfidence": 0.9997299194335938}, "valuation": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 3.214, "y": 3.073}, {"x": 3.513, "y": 3.073}, {"x": 3.513, "y": 3.407}, {"x": 3.214, "y": 3.407}], "confidence": null}], "valueConfidence": null}, "causes_of_loss": {"value": "<PERSON>l Cost", "type": "string", "lines": [{"text": "<PERSON>l Cost", "page": 0, "boundingPolygon": [{"x": 3.513, "y": 3.073}, {"x": 4.409, "y": 3.073}, {"x": 4.409, "y": 3.407}, {"x": 3.513, "y": 3.407}], "confidence": 0.9971208572387695}], "valueConfidence": 0.9971208572387695}, "inflation_guard_percentage": null, "deductible": {"source": "2500.00", "value": 2500, "unit": "$", "type": "currency", "lines": [{"text": "2500.00", "page": 0, "boundingPolygon": [{"x": 4.91, "y": 3.073}, {"x": 5.459, "y": 3.073}, {"x": 5.459, "y": 3.407}, {"x": 4.91, "y": 3.407}], "confidence": 0.9997321319580078}], "valueConfidence": 0.9997321319580078}, "deductible_type": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 5.459, "y": 3.073}, {"x": 5.903, "y": 3.074}, {"x": 5.904, "y": 3.407}, {"x": 5.459, "y": 3.407}], "confidence": null}], "valueConfidence": null}, "blanket_number": null, "forms_and_conditions_to_apply": {"value": "Special Form", "type": "string", "lines": [{"text": "Special Form", "page": 0, "boundingPolygon": [{"x": 6.154, "y": 3.074}, {"x": 8.143, "y": 3.074}, {"x": 8.144, "y": 3.407}, {"x": 6.154, "y": 3.407}], "confidence": 0.999379768371582}], "valueConfidence": 0.999379768371582}}, {"subject_of_insurance": {"value": "Bus OVerHEAD", "type": "string", "lines": [{"text": "Bus OVerHEAD", "page": 0, "boundingPolygon": [{"x": 0.169, "y": 3.406}, {"x": 1.803, "y": 3.407}, {"x": 1.804, "y": 3.74}, {"x": 0.169, "y": 3.74}], "confidence": 0.9966899871826171}], "valueConfidence": 0.9966899871826171}, "amount": {"source": "120,000", "value": 120000, "unit": "$", "type": "currency", "lines": [{"text": "120,000", "page": 0, "boundingPolygon": [{"x": 1.803, "y": 3.407}, {"x": 2.811, "y": 3.407}, {"x": 2.811, "y": 3.74}, {"x": 1.804, "y": 3.74}], "confidence": 0.9982467651367187}], "valueConfidence": 0.9982467651367187}, "coins_percentage": null, "valuation": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 3.214, "y": 3.407}, {"x": 3.513, "y": 3.407}, {"x": 3.513, "y": 3.74}, {"x": 3.214, "y": 3.74}], "confidence": null}], "valueConfidence": null}, "causes_of_loss": {"value": "Rpl Cost", "type": "string", "lines": [{"text": "Rpl Cost", "page": 0, "boundingPolygon": [{"x": 3.513, "y": 3.407}, {"x": 4.409, "y": 3.407}, {"x": 4.41, "y": 3.741}, {"x": 3.513, "y": 3.74}], "confidence": 0.9867559432983398}], "valueConfidence": 0.9867559432983398}, "inflation_guard_percentage": null, "deductible": null, "deductible_type": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 5.459, "y": 3.407}, {"x": 5.904, "y": 3.407}, {"x": 5.904, "y": 3.741}, {"x": 5.459, "y": 3.741}], "confidence": null}], "valueConfidence": null}, "blanket_number": null, "forms_and_conditions_to_apply": {"value": "331/3", "type": "string", "lines": [{"text": "331/3", "page": 0, "boundingPolygon": [{"x": 6.154, "y": 3.407}, {"x": 8.144, "y": 3.407}, {"x": 8.144, "y": 3.741}, {"x": 6.154, "y": 3.741}], "confidence": 0.9991154479980469}], "valueConfidence": 0.9991154479980469}}, {"subject_of_insurance": {"value": "Water back Up Food Spoilage 20.000", "type": "string", "lines": [{"text": "Water back Up Food Spoilage 20.000", "page": 0, "boundingPolygon": [{"x": 0.169, "y": 3.74}, {"x": 1.804, "y": 3.74}, {"x": 1.804, "y": 4.074}, {"x": 0.169, "y": 4.074}], "confidence": 0.9872580337524414}], "valueConfidence": 0.9872580337524414}, "amount": {"source": "25,000", "value": 25000, "unit": "$", "type": "currency", "lines": [{"text": "25,000", "page": 0, "boundingPolygon": [{"x": 1.804, "y": 3.74}, {"x": 2.811, "y": 3.74}, {"x": 2.811, "y": 4.074}, {"x": 1.804, "y": 4.074}], "confidence": 0.9992613983154297}], "valueConfidence": 0.9992613983154297}, "coins_percentage": null, "valuation": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 3.214, "y": 3.74}, {"x": 3.513, "y": 3.74}, {"x": 3.513, "y": 4.074}, {"x": 3.214, "y": 4.074}], "confidence": null}], "valueConfidence": null}, "causes_of_loss": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 3.513, "y": 3.74}, {"x": 4.41, "y": 3.741}, {"x": 4.41, "y": 4.074}, {"x": 3.513, "y": 4.074}], "confidence": null}], "valueConfidence": null}, "inflation_guard_percentage": null, "deductible": null, "deductible_type": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 5.459, "y": 3.741}, {"x": 5.904, "y": 3.741}, {"x": 5.904, "y": 4.074}, {"x": 5.459, "y": 4.074}], "confidence": null}], "valueConfidence": null}, "blanket_number": null, "forms_and_conditions_to_apply": {"value": "Special form special Form", "type": "string", "lines": [{"text": "Special form special Form", "page": 0, "boundingPolygon": [{"x": 6.154, "y": 3.741}, {"x": 8.144, "y": 3.741}, {"x": 8.144, "y": 4.075}, {"x": 6.154, "y": 4.074}], "confidence": 0.9994207191467285}], "valueConfidence": 0.9994207191467285}}, {"subject_of_insurance": {"value": "Sign", "type": "string", "lines": [{"text": "Sign", "page": 0, "boundingPolygon": [{"x": 0.169, "y": 4.074}, {"x": 1.804, "y": 4.074}, {"x": 1.804, "y": 4.399}, {"x": 0.169, "y": 4.399}], "confidence": 0.9996115112304688}], "valueConfidence": 0.9996115112304688}, "amount": {"source": "10,000", "value": 10000, "unit": "$", "type": "currency", "lines": [{"text": "10,000", "page": 0, "boundingPolygon": [{"x": 1.804, "y": 4.074}, {"x": 2.811, "y": 4.074}, {"x": 2.812, "y": 4.399}, {"x": 1.804, "y": 4.399}], "confidence": 0.9980536651611328}], "valueConfidence": 0.9980536651611328}, "coins_percentage": null, "valuation": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 3.214, "y": 4.074}, {"x": 3.513, "y": 4.074}, {"x": 3.514, "y": 4.399}, {"x": 3.215, "y": 4.399}], "confidence": null}], "valueConfidence": null}, "causes_of_loss": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 3.513, "y": 4.074}, {"x": 4.41, "y": 4.074}, {"x": 4.41, "y": 4.399}, {"x": 3.514, "y": 4.399}], "confidence": null}], "valueConfidence": null}, "inflation_guard_percentage": null, "deductible": null, "deductible_type": {"value": "", "type": "string", "lines": [{"text": "", "page": 0, "boundingPolygon": [{"x": 5.459, "y": 4.074}, {"x": 5.904, "y": 4.074}, {"x": 5.904, "y": 4.4}, {"x": 5.459, "y": 4.4}], "confidence": null}], "valueConfidence": null}, "blanket_number": null, "forms_and_conditions_to_apply": {"value": "RPL Cost", "type": "string", "lines": [{"text": "RPL Cost", "page": 0, "boundingPolygon": [{"x": 6.154, "y": 4.074}, {"x": 8.144, "y": 4.075}, {"x": 8.144, "y": 4.4}, {"x": 6.154, "y": 4.4}], "confidence": 0.9983868408203125}], "valueConfidence": 0.9983868408203125}}], "location_number": null, "street_address": null, "building_number": null, "building_description": null, "business_income_extra_expense": null, "value_reporting_information": null, "spoilage_coverage": {"type": "string", "value": "Y", "lines": [{"text": "Y", "page": 0, "boundingPolygon": [{"x": 0.42, "y": 5.192}, {"x": 0.496, "y": 5.192}, {"x": 0.497, "y": 5.281}, {"x": 0.42, "y": 5.281}], "confidence": 0.9180558013916016}], "anchorConfidence": 0.9970720672607422, "valueConfidence": 0.9180558013916016}, "description_of_property_covered": {"type": "string", "value": "Bldg, BPP, Sign, Food Spoilage", "lines": [{"text": "Bldg, BPP, Sign, Food Spoilage", "page": 0, "boundingPolygon": [{"x": 0.827, "y": 4.932}, {"x": 2.418, "y": 4.929}, {"x": 2.419, "y": 5.046}, {"x": 0.827, "y": 5.048}], "confidence": 0.9788966369628906}], "anchorConfidence": 0.9984925079345703, "valueConfidence": 0.9788966369628906}, "limit": null, "deductible": null, "refrig_maint_agreement": null, "breakdown_or_contamination": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9967905426025391, "valueConfidence": null}, "power_outage": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9986051177978515, "valueConfidence": null}, "selling_price": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9963833618164063, "valueConfidence": null}, "sinkhole_coverage_accept": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9981742858886719, "valueConfidence": null}, "sinkhole_coverage_limit": null, "mine_subsidence_coverage_accept": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9981949615478516, "valueConfidence": null}, "mine_subsidence_coverage_limit": null, "designates_historical_landmark": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9976020050048828, "valueConfidence": null}, "number_of_open_sides_on_structure": null, "policy_inclusions": {"type": "string", "value": "NA", "lines": [{"text": "NA", "page": 0, "boundingPolygon": [{"x": 1.486, "y": 5.917}, {"x": 1.646, "y": 5.917}, {"x": 1.646, "y": 6.004}, {"x": 1.486, "y": 6.004}], "confidence": 0.9994044494628906}], "anchorConfidence": 0.9872883605957031, "valueConfidence": 0.9994044494628906}, "construction_type": {"type": "string", "value": "Brick", "lines": [{"text": "Brick", "page": 0, "boundingPolygon": [{"x": 0.231, "y": 6.514}, {"x": 0.481, "y": 6.514}, {"x": 0.481, "y": 6.608}, {"x": 0.231, "y": 6.608}], "confidence": 0.998211669921875}], "anchorConfidence": 0.9948968505859375, "valueConfidence": 0.998211669921875}, "distance_to_hydrant": null, "distance_to_fire_station": null, "fire_district": {"type": "string", "value": "Det Fire, Dept", "lines": [{"text": "Det Fire, Dept", "page": 0, "boundingPolygon": [{"x": 3.33, "y": 6.509}, {"x": 4.024, "y": 6.508}, {"x": 4.024, "y": 6.622}, {"x": 3.33, "y": 6.623}], "confidence": 0.988381118774414}], "anchorConfidence": 0.9966329193115234, "valueConfidence": 0.988381118774414}, "fire_district_code_number": null, "fire_protection_class": null, "number_of_stories": null, "number_of_basements": null, "year_built": null, "total_area": null, "building_improvements_wiring": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9902999114990234, "valueConfidence": null}, "building_improvements_wiring_year": {"source": "2023", "value": 2023, "type": "number", "lines": [{"text": "2023", "page": 0, "boundingPolygon": [{"x": 0.404, "y": 6.848}, {"x": 1.223, "y": 6.847}, {"x": 1.223, "y": 6.962}, {"x": 0.404, "y": 6.963}], "confidence": 0.9902999114990234}], "anchorConfidence": 0.9902999114990234, "valueConfidence": 0.9902999114990234}, "building_improvements_plumbing": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9797752380371094, "valueConfidence": null}, "building_improvements_plumbing_year": {"source": "2024", "value": 2024, "type": "number", "lines": [{"text": "2024", "page": 0, "boundingPolygon": [{"x": 1.58, "y": 6.845}, {"x": 2.47, "y": 6.844}, {"x": 2.471, "y": 6.96}, {"x": 1.58, "y": 6.961}], "confidence": 0.9797752380371094}], "anchorConfidence": 0.9797752380371094, "valueConfidence": 0.9797752380371094}, "building_improvements_roofing": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9753534698486328, "valueConfidence": null}, "building_improvements_roofing_year": {"source": "2023", "value": 2023, "type": "number", "lines": [{"text": "2023", "page": 0, "boundingPolygon": [{"x": 0.406, "y": 7.014}, {"x": 1.264, "y": 7.013}, {"x": 1.264, "y": 7.127}, {"x": 0.406, "y": 7.128}], "confidence": 0.9753534698486328}], "anchorConfidence": 0.9753534698486328, "valueConfidence": 0.9753534698486328}, "building_improvements_heating": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9864617919921875, "valueConfidence": null}, "building_improvements_heating_year": {"source": "2024", "value": 2024, "type": "number", "lines": [{"text": "2024", "page": 0, "boundingPolygon": [{"x": 1.583, "y": 7.011}, {"x": 2.412, "y": 7.01}, {"x": 2.412, "y": 7.124}, {"x": 1.583, "y": 7.125}], "confidence": 0.9864617919921875}], "anchorConfidence": 0.9864617919921875, "valueConfidence": 0.9864617919921875}, "building_improvements_other": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9966588592529297, "valueConfidence": null}, "building_improvements_other_year": null, "building_code_grade": null, "tax_code": null, "roof_type": {"type": "string", "value": "Membrane/ Gravel", "lines": [{"text": "Membrane/ Gravel", "page": 0, "boundingPolygon": [{"x": 3.864, "y": 6.84}, {"x": 4.781, "y": 6.839}, {"x": 4.781, "y": 6.933}, {"x": 3.864, "y": 6.934}], "confidence": 0.9883680725097657}], "anchorConfidence": 0.9981638336181641, "valueConfidence": 0.9883680725097657}, "other_occupancies": null, "wind_class_resistive": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.994488754272461, "valueConfidence": null}, "wind_class_semi_resistive": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.97042236328125, "valueConfidence": null}, "heating_source_incl_woodburning_stove": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9973622131347656, "valueConfidence": null}, "heating_source_incl_woodburning_stove_manufacturer": null, "heating_source_incl_woodburning_stove_date_installed": null, "primary_heat_boiler": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9944430541992187, "valueConfidence": null}, "primary_heat_solid_fuel": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9974063873291016, "valueConfidence": null}, "primary_heat_boiler_insurance_placed_elsewhere": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9938789367675781, "valueConfidence": null}, "secondary_heat_boiler": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9963542938232421, "valueConfidence": null}, "secondary_heat_solid_fuel": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9973146057128907, "valueConfidence": null}, "seecondary_heat_boiler_insurance_placed_elsewhere": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9929544067382813, "valueConfidence": null}, "right_exposure_and_distance": {"type": "string", "value": "Comm bldg", "lines": [{"text": "Comm bldg", "page": 0, "boundingPolygon": [{"x": 0.226, "y": 8.01}, {"x": 0.791, "y": 8.009}, {"x": 0.791, "y": 8.121}, {"x": 0.226, "y": 8.122}], "confidence": 0.9749101257324219}], "anchorConfidence": 0.9978012084960938, "valueConfidence": 0.9749101257324219}, "left_exposure_and_distance": {"type": "string", "value": "Comm Bldg", "lines": [{"text": "Comm Bldg", "page": 0, "boundingPolygon": [{"x": 2.22, "y": 8.007}, {"x": 2.801, "y": 8.006}, {"x": 2.801, "y": 8.118}, {"x": 2.22, "y": 8.119}], "confidence": 0.9941936492919922}], "anchorConfidence": 0.9982513427734375, "valueConfidence": 0.9941936492919922}, "front_exposure_and_distance": {"type": "string", "value": "Street", "lines": [{"text": "Street", "page": 0, "boundingPolygon": [{"x": 4.221, "y": 8.006}, {"x": 4.521, "y": 8.006}, {"x": 4.521, "y": 8.095}, {"x": 4.221, "y": 8.095}], "confidence": 0.99923828125}], "anchorConfidence": 0.9979300689697266, "valueConfidence": 0.99923828125}, "rear_exposure_and_distance": {"type": "string", "value": "parking Lot", "lines": [{"text": "parking Lot", "page": 0, "boundingPolygon": [{"x": 6.22, "y": 8.002}, {"x": 6.771, "y": 8.002}, {"x": 6.771, "y": 8.113}, {"x": 6.22, "y": 8.113}], "confidence": 0.999017105102539}], "anchorConfidence": 0.9959178924560547, "valueConfidence": 0.999017105102539}, "burglar_alarm_type": {"type": "string", "value": "Central Stat/ W Cameras", "lines": [{"text": "Central Stat/ W Cameras", "page": 0, "boundingPolygon": [{"x": 0.226, "y": 8.343}, {"x": 1.434, "y": 8.341}, {"x": 1.434, "y": 8.434}, {"x": 0.226, "y": 8.435}], "confidence": 0.93399658203125}], "anchorConfidence": 0.9909529113769531, "valueConfidence": 0.93399658203125}, "burglar_alarm_certificate_number": null, "burglar_alarm_expiration_date": null, "central_station": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.997520980834961, "valueConfidence": null}, "with_keys": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9978288269042969, "valueConfidence": null}, "local_gong": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9985651397705078, "valueConfidence": null}, "burglar_alarm_serviced_by": null, "burglar_alarm_extent": null, "burglar_alarm_grade": null, "number_of_guards_and_watchmen": null, "clock_hourly": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9982368469238281, "valueConfidence": null}, "premises_fire_protection": null, "sprinkler_percentage": null, "fire_alarm_manufacturer": null, "fire_protection_central_station": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9989405059814453, "valueConfidence": null}, "fire_protection_local_gong": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9992668914794922, "valueConfidence": null}, "additional_interest": [{"rank": null, "name_and_address": null, "reference_number": null, "certificate_required": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9964381408691406, "valueConfidence": null}, "interest_lenders_loss_payable": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9983323669433594, "valueConfidence": null}, "interest_loss_payee": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.998155746459961, "valueConfidence": null}, "interest_mortgagee": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9920500183105468, "valueConfidence": null}, "location_number": null, "building_number": null, "scheduled_item_number": null, "item_class": null, "item_description": null}]}], "remarks": null, "signature.producers_signature": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9811705017089843, "valueConfidence": null}, "signature.producers_name": {"type": "string", "value": "<PERSON>", "lines": [{"text": "<PERSON>", "page": 2, "boundingPolygon": [{"x": 3.576, "y": 10.035}, {"x": 4.374, "y": 10.034}, {"x": 4.375, "y": 10.152}, {"x": 3.576, "y": 10.152}], "confidence": 0.99930908203125}], "anchorConfidence": 0.952757339477539, "valueConfidence": 0.99930908203125}, "signature.state_producer_license_number": null, "signature.applicants_signature": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.858638916015625, "valueConfidence": null}, "signature.date": {"type": "string", "value": "5/9/2025", "lines": [{"text": "5/9/2025", "page": 2, "boundingPolygon": [{"x": 5.879, "y": 10.369}, {"x": 6.317, "y": 10.369}, {"x": 6.317, "y": 10.467}, {"x": 5.879, "y": 10.467}], "confidence": 0.9963385772705078}], "anchorConfidence": 0.9991260528564453, "valueConfidence": 0.9963385772705078}, "signature.national_producers_number": null, "informations": null}