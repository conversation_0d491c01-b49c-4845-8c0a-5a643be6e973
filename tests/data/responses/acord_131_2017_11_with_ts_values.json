{"named_insured": {"type": "string", "value": "JWC Steel Co. LLC", "lines": [{"text": "JWC Steel Co. LLC", "page": 0, "boundingPolygon": [{"x": 4.31, "y": 1.692}, {"x": 5.182, "y": 1.691}, {"x": 5.182, "y": 1.776}, {"x": 4.311, "y": 1.777}], "confidence": 0.9978971862792969}], "anchorConfidence": 0.9966947174072266, "valueConfidence": 0.9978971862792969}, "agency_information": [{"agency_customer_id": {"type": "string", "value": "00011617", "lines": [{"text": "00011617", "page": 0, "boundingPolygon": [{"x": 3.895, "y": 0.365}, {"x": 5.7, "y": 0.363}, {"x": 5.7, "y": 0.466}, {"x": 3.895, "y": 0.468}], "confidence": 0.9976646423339843}], "anchorConfidence": 0.9976646423339843, "valueConfidence": 0.9976646423339843}, "date": {"source": "03/14/2025", "value": "2025-03-14T00:00:00.000Z", "type": "date", "lines": [{"text": "DATE (MM/DD/YYYY)", "page": 0, "boundingPolygon": [{"x": 7.254, "y": 0.582}, {"x": 8.042, "y": 0.581}, {"x": 8.042, "y": 0.667}, {"x": 7.254, "y": 0.668}], "confidence": 0.9920513916015625}, {"text": "03/14/2025", "page": 0, "boundingPolygon": [{"x": 7.396, "y": 0.737}, {"x": 7.899, "y": 0.736}, {"x": 7.899, "y": 0.82}, {"x": 7.397, "y": 0.821}], "confidence": 0.9992679595947266}], "anchorConfidence": 0.9991134643554688, "valueConfidence": 0.9992679595947266}, "agency": {"type": "string", "value": "Wentworth-DeAngelis, Inc.", "lines": [{"text": "Wentworth-DeAngelis, Inc.", "page": 0, "boundingPolygon": [{"x": 0.309, "y": 1.36}, {"x": 1.497, "y": 1.358}, {"x": 1.497, "y": 1.463}, {"x": 0.309, "y": 1.464}], "confidence": 0.9820292663574218}], "anchorConfidence": 0.9997759246826172, "valueConfidence": 0.9820292663574218}, "carrier": {"type": "string", "value": "Ascot Specialty Insurance Company", "lines": [{"text": "Ascot Specialty Insurance Company", "page": 0, "boundingPolygon": [{"x": 4.311, "y": 1.359}, {"x": 5.928, "y": 1.357}, {"x": 5.928, "y": 1.461}, {"x": 4.311, "y": 1.463}], "confidence": 0.9963625335693359}], "anchorConfidence": 0.9995235443115235, "valueConfidence": 0.9963625335693359}, "naic_code": null, "policy_number": {"type": "string", "value": "ESXS2410002201-02", "lines": [{"text": "ESXS2410002201-02", "page": 0, "boundingPolygon": [{"x": 0.318, "y": 1.693}, {"x": 1.292, "y": 1.692}, {"x": 1.292, "y": 1.777}, {"x": 0.318, "y": 1.778}], "confidence": 0.95952392578125}], "anchorConfidence": 0.9977938079833985, "valueConfidence": 0.95952392578125}, "effective_date": {"source": "05/15/2025", "value": "2025-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2025", "page": 0, "boundingPolygon": [{"x": 3.597, "y": 1.696}, {"x": 4.099, "y": 1.696}, {"x": 4.099, "y": 1.775}, {"x": 3.597, "y": 1.776}], "confidence": 0.9991344451904297}], "anchorConfidence": 0.998155517578125, "valueConfidence": 0.9991344451904297}}], "policy_information": [{"new": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.99957763671875, "valueConfidence": null}, "renew": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9994104766845703, "valueConfidence": null}, "umbrella": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9983466339111328, "valueConfidence": null}, "excess": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9989974212646484, "valueConfidence": null}, "occurrence": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9862394714355469, "valueConfidence": null}, "claims_made": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9934508514404297, "valueConfidence": null}, "voluntary": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9986276245117187, "valueConfidence": null}, "proposed_retroactive_date": null, "current_retroactive_date": null, "retained_limit": null, "limits_occurrence": {"source": "$ 5,000,000", "value": 5000000, "unit": "$", "type": "currency", "lines": [{"text": "NEW", "page": 0, "boundingPolygon": [{"x": 0.501, "y": 2.215}, {"x": 0.689, "y": 2.215}, {"x": 0.689, "y": 2.281}, {"x": 0.501, "y": 2.282}], "confidence": 0.99957763671875}, {"text": "UMBRELLA", "page": 0, "boundingPolygon": [{"x": 1.249, "y": 2.215}, {"x": 1.684, "y": 2.215}, {"x": 1.684, "y": 2.282}, {"x": 1.249, "y": 2.282}], "confidence": 0.9983466339111328}, {"text": "OCCURRENCE", "page": 0, "boundingPolygon": [{"x": 2.053, "y": 2.215}, {"x": 2.618, "y": 2.214}, {"x": 2.619, "y": 2.282}, {"x": 2.053, "y": 2.283}], "confidence": 0.9862394714355469}, {"text": "VOLUNTARY", "page": 0, "boundingPolygon": [{"x": 2.948, "y": 2.216}, {"x": 3.431, "y": 2.216}, {"x": 3.431, "y": 2.28}, {"x": 2.948, "y": 2.28}], "confidence": 0.9986276245117187}, {"text": "RETROACTIVE DATE", "page": 0, "boundingPolygon": [{"x": 3.899, "y": 2.215}, {"x": 4.695, "y": 2.214}, {"x": 4.695, "y": 2.281}, {"x": 3.899, "y": 2.282}], "confidence": 0.9980460357666016}, {"text": "$ 5,000,000", "page": 0, "boundingPolygon": [{"x": 5.128, "y": 2.198}, {"x": 5.708, "y": 2.197}, {"x": 5.708, "y": 2.288}, {"x": 5.128, "y": 2.289}], "confidence": 0.9966791534423828}], "anchorConfidence": 0.9905948638916016, "valueConfidence": 0.9966791534423828}, "limits_general_aggregate": {"source": "5,000,000", "value": 5000000, "type": "number", "lines": [{"text": "RENEWAL", "page": 0, "boundingPolygon": [{"x": 0.504, "y": 2.382}, {"x": 0.897, "y": 2.381}, {"x": 0.897, "y": 2.451}, {"x": 0.504, "y": 2.451}], "confidence": 0.9994104766845703}, {"text": "EXCESS", "page": 0, "boundingPolygon": [{"x": 1.252, "y": 2.381}, {"x": 1.572, "y": 2.381}, {"x": 1.573, "y": 2.447}, {"x": 1.253, "y": 2.447}], "confidence": 0.9989974212646484}, {"text": "CLAIMS MADE", "page": 0, "boundingPolygon": [{"x": 2.05, "y": 2.38}, {"x": 2.598, "y": 2.38}, {"x": 2.598, "y": 2.448}, {"x": 2.05, "y": 2.449}], "confidence": 0.9934508514404297}, {"text": "PROPOSED", "page": 0, "boundingPolygon": [{"x": 3.671, "y": 2.382}, {"x": 4.119, "y": 2.381}, {"x": 4.12, "y": 2.448}, {"x": 3.671, "y": 2.448}], "confidence": 0.9986331176757812}, {"text": "CURRENT", "page": 0, "boundingPolygon": [{"x": 4.507, "y": 2.38}, {"x": 4.895, "y": 2.38}, {"x": 4.895, "y": 2.448}, {"x": 4.507, "y": 2.449}], "confidence": 0.9995612335205079}, {"text": "$ 5,000,000", "page": 0, "boundingPolygon": [{"x": 5.128, "y": 2.365}, {"x": 5.709, "y": 2.364}, {"x": 5.709, "y": 2.454}, {"x": 5.128, "y": 2.455}], "confidence": 0.9968311309814453}], "anchorConfidence": 0.9990351867675781, "valueConfidence": 0.9968311309814453}, "first_dollar_defense": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9958087158203125, "valueConfidence": null}, "expiring_policy_number": null}], "employee_benefits_liability": [{"employee_benefits_limit_of_insurance": null, "employee_benefits_aggregate_limit": null, "employee_benefits_retained_limit": null, "employee_benefits_retroactive_date": null, "employee_benefits_name_of_program": null}], "premises_information": [{"location_number": {"source": "1", "value": 1, "type": "number", "lines": [{"text": "1", "page": 0, "boundingPolygon": [{"x": 0.378, "y": 3.946}, {"x": 0.415, "y": 3.946}, {"x": 0.415, "y": 4.023}, {"x": 0.378, "y": 4.023}], "confidence": 0.9969317626953125}], "anchorConfidence": 0.9953785705566406, "valueConfidence": 0.9969317626953125}, "name": null, "location": {"type": "string", "value": "540 Ledyard Rd.", "lines": [{"text": "540 Ledyard Rd.", "page": 0, "boundingPolygon": [{"x": 1.263, "y": 4.027}, {"x": 2.003, "y": 4.026}, {"x": 2.003, "y": 4.127}, {"x": 1.263, "y": 4.128}], "confidence": 0.998939437866211}], "anchorConfidence": 0.9935400390625, "valueConfidence": 0.998939437866211}, "description": null, "annual_payroll": {"source": "150,000", "value": 150000, "type": "number", "lines": [{"text": "150,000", "page": 0, "boundingPolygon": [{"x": 4.918, "y": 4.029}, {"x": 5.276, "y": 4.029}, {"x": 5.276, "y": 4.12}, {"x": 4.918, "y": 4.12}], "confidence": 0.9975040435791016}], "anchorConfidence": 0.9953785705566406, "valueConfidence": 0.9975040435791016}, "annual_gross_sales": {"source": "500,000", "value": 500000, "type": "number", "lines": [{"text": "500,000", "page": 0, "boundingPolygon": [{"x": 5.862, "y": 4.03}, {"x": 6.226, "y": 4.03}, {"x": 6.226, "y": 4.117}, {"x": 5.862, "y": 4.118}], "confidence": 0.9989167022705078}], "anchorConfidence": 0.9953785705566406, "valueConfidence": 0.9989167022705078}, "foreign_gross_sales": {"source": "0", "value": 0, "type": "number", "lines": [{"text": "0", "page": 0, "boundingPolygon": [{"x": 6.862, "y": 4.03}, {"x": 6.918, "y": 4.03}, {"x": 6.918, "y": 4.106}, {"x": 6.862, "y": 4.106}], "confidence": 0.9927690887451172}], "anchorConfidence": 0.9953785705566406, "valueConfidence": 0.9927690887451172}, "employees": null}, {"location_number": null, "name": null, "location": null, "description": null, "annual_payroll": null, "annual_gross_sales": null, "foreign_gross_sales": null, "employees": null}, {"location_number": null, "name": null, "location": null, "description": null, "annual_payroll": null, "annual_gross_sales": null, "foreign_gross_sales": null, "employees": null}, {"location_number": null, "name": null, "location": null, "description": null, "annual_payroll": null, "annual_gross_sales": null, "foreign_gross_sales": null, "employees": null}, {"location_number": null, "name": null, "location": null, "description": null, "annual_payroll": null, "annual_gross_sales": null, "foreign_gross_sales": null, "employees": null}, {"location_number": null, "name": null, "location": null, "description": null, "annual_payroll": null, "annual_gross_sales": null, "foreign_gross_sales": null, "employees": null}], "underlying_insurance": [{"auto_liability_carrier_and_policy": {"type": "string", "value": "CNA Insurance 6049983041", "lines": [{"text": "CNA Insurance", "page": 0, "boundingPolygon": [{"x": 1.013, "y": 7.527}, {"x": 1.687, "y": 7.526}, {"x": 1.687, "y": 7.609}, {"x": 1.013, "y": 7.61}], "confidence": 0.997606201171875}, {"text": "6049983041", "page": 0, "boundingPolygon": [{"x": 1.012, "y": 7.696}, {"x": 1.559, "y": 7.696}, {"x": 1.559, "y": 7.778}, {"x": 1.012, "y": 7.778}], "confidence": 0.9990054321289062}], "anchorConfidence": 0.975583724975586, "valueConfidence": 0.9983058166503906}, "auto_policy_effective_date": {"source": "05/15/2024", "value": "2024-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2024", "page": 0, "boundingPolygon": [{"x": 3.199, "y": 7.611}, {"x": 3.7, "y": 7.611}, {"x": 3.7, "y": 7.692}, {"x": 3.199, "y": 7.692}], "confidence": 0.99900146484375}], "anchorConfidence": 0.975583724975586, "valueConfidence": 0.99900146484375}, "auto_policy_expiration_date": {"source": "05/15/2025", "value": "2025-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2025", "page": 0, "boundingPolygon": [{"x": 4.1, "y": 7.612}, {"x": 4.599, "y": 7.611}, {"x": 4.599, "y": 7.691}, {"x": 4.1, "y": 7.692}], "confidence": 0.9992372894287109}], "anchorConfidence": 0.975583724975586, "valueConfidence": 0.9992372894287109}, "auto_limits_csl_acc": null, "auto_limits_bi_acc": null, "auto_limits_bi_per": null, "auto_limits_pd_acc": null, "auto_csl_annual_renewal_premium": null, "auto_bi_annual_renewal_premium": null, "auto_pd_annual_renewal_premium": null, "general_liability_occurence": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9993616485595703, "valueConfidence": null}, "general_liability_claims_made": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.99786376953125, "valueConfidence": null}, "general_liability_carrier_and_policy": {"type": "string", "value": "CNA Insurance 6049983041", "lines": [{"text": "CNA Insurance", "page": 0, "boundingPolygon": [{"x": 1.014, "y": 8.36}, {"x": 1.687, "y": 8.359}, {"x": 1.687, "y": 8.443}, {"x": 1.014, "y": 8.444}], "confidence": 0.9979753112792968}, {"text": "6049983041", "page": 0, "boundingPolygon": [{"x": 1.013, "y": 8.53}, {"x": 1.561, "y": 8.53}, {"x": 1.561, "y": 8.609}, {"x": 1.013, "y": 8.61}], "confidence": 0.9989631652832032}], "anchorConfidence": 0.9988654327392578, "valueConfidence": 0.99846923828125}, "general_policy_effective_date": {"source": "05/15/2024", "value": "2024-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2024", "page": 0, "boundingPolygon": [{"x": 3.2, "y": 8.444}, {"x": 3.7, "y": 8.443}, {"x": 3.7, "y": 8.525}, {"x": 3.2, "y": 8.526}], "confidence": 0.9990043640136719}], "anchorConfidence": 0.9988654327392578, "valueConfidence": 0.9990043640136719}, "general_policy_expiration_date": {"source": "05/15/2025", "value": "2025-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2025", "page": 0, "boundingPolygon": [{"x": 4.1, "y": 8.444}, {"x": 4.599, "y": 8.443}, {"x": 4.599, "y": 8.524}, {"x": 4.1, "y": 8.525}], "confidence": 0.9992604064941406}], "anchorConfidence": 0.9988654327392578, "valueConfidence": 0.9992604064941406}, "general_limits_each_occurrence": {"source": "1,000,000", "value": 1000000, "type": "number", "lines": [{"text": "$ 1,000,000", "page": 0, "boundingPolygon": [{"x": 5.797, "y": 8.032}, {"x": 6.358, "y": 8.031}, {"x": 6.358, "y": 8.153}, {"x": 5.797, "y": 8.154}], "confidence": 0.9989373779296875}], "anchorConfidence": 0.9946958160400391, "valueConfidence": 0.9989373779296875}, "general_limits_general_agg": {"source": "2,000,000", "value": 2000000, "type": "number", "lines": [{"text": "$ 2,000,000", "page": 0, "boundingPolygon": [{"x": 5.797, "y": 8.197}, {"x": 6.359, "y": 8.196}, {"x": 6.359, "y": 8.319}, {"x": 5.797, "y": 8.319}], "confidence": 0.9989649963378906}], "anchorConfidence": 0.9953385162353515, "valueConfidence": 0.9989649963378906}, "general_limits_product_and_comp": {"source": "2,000,000", "value": 2000000, "type": "number", "lines": [{"text": "$ 2,000,000", "page": 0, "boundingPolygon": [{"x": 5.796, "y": 8.363}, {"x": 6.366, "y": 8.362}, {"x": 6.366, "y": 8.487}, {"x": 5.796, "y": 8.488}], "confidence": 0.999081039428711}], "anchorConfidence": 0.9960840606689453, "valueConfidence": 0.999081039428711}, "general_limits_personal_injury": {"source": "1,000,000", "value": 1000000, "type": "number", "lines": [{"text": "$ 1,000,000", "page": 0, "boundingPolygon": [{"x": 5.797, "y": 8.529}, {"x": 6.358, "y": 8.529}, {"x": 6.359, "y": 8.653}, {"x": 5.797, "y": 8.653}], "confidence": 0.9986113739013672}], "anchorConfidence": 0.9970731353759765, "valueConfidence": 0.9986113739013672}, "general_limits_damage_to_rented_premises": {"source": "100,000", "value": 100000, "type": "number", "lines": [{"text": "$ 100,000", "page": 0, "boundingPolygon": [{"x": 5.797, "y": 8.697}, {"x": 6.276, "y": 8.696}, {"x": 6.276, "y": 8.82}, {"x": 5.797, "y": 8.821}], "confidence": 0.9992890930175782}], "anchorConfidence": 0.9993869018554687, "valueConfidence": 0.9992890930175782}, "general_limits_medical_expense": {"source": "5,000", "value": 5000, "type": "number", "lines": [{"text": "$ 5,000", "page": 0, "boundingPolygon": [{"x": 5.797, "y": 8.863}, {"x": 6.164, "y": 8.863}, {"x": 6.164, "y": 8.987}, {"x": 5.797, "y": 8.987}], "confidence": 0.99825927734375}], "anchorConfidence": 0.9986514282226563, "valueConfidence": 0.99825927734375}, "general_prem_ops_annual_renewal_premium": null, "general_products_annual_renewal_premium": null, "general_other_annual_renewal_premium": null, "employers_liability_carrier_and_policy": {"type": "string", "value": "BerkleyNet", "lines": [{"text": "BerkleyNet", "page": 0, "boundingPolygon": [{"x": 1.015, "y": 9.109}, {"x": 1.507, "y": 9.109}, {"x": 1.507, "y": 9.211}, {"x": 1.015, "y": 9.212}], "confidence": 0.9945323944091797}], "anchorConfidence": 0.9992125701904296, "valueConfidence": 0.9945323944091797}, "employers_policy_effective_date": {"source": "05/15/2024", "value": "2024-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2024", "page": 0, "boundingPolygon": [{"x": 3.2, "y": 9.195}, {"x": 3.703, "y": 9.194}, {"x": 3.703, "y": 9.275}, {"x": 3.2, "y": 9.275}], "confidence": 0.999010238647461}], "anchorConfidence": 0.9992125701904296, "valueConfidence": 0.999010238647461}, "employers_policy_expiration_date": {"source": "05/15/2025", "value": "2025-05-15T00:00:00.000Z", "type": "date", "lines": [{"text": "05/15/2025", "page": 0, "boundingPolygon": [{"x": 4.098, "y": 9.196}, {"x": 4.599, "y": 9.195}, {"x": 4.599, "y": 9.276}, {"x": 4.098, "y": 9.276}], "confidence": 0.9990606689453125}], "anchorConfidence": 0.9992125701904296, "valueConfidence": 0.9990606689453125}, "employers_limits_each_accident": {"source": "1,000,000", "value": 1000000, "type": "number", "lines": [{"text": "$ 1,000,000", "page": 0, "boundingPolygon": [{"x": 5.797, "y": 9.029}, {"x": 6.367, "y": 9.028}, {"x": 6.367, "y": 9.154}, {"x": 5.797, "y": 9.155}], "confidence": 0.998562240600586}], "anchorConfidence": 0.9987126159667968, "valueConfidence": 0.998562240600586}, "employers_limits_disease_each_employee": {"source": "1,000,000", "value": 1000000, "type": "number", "lines": [{"text": "$ 1,000,000", "page": 0, "boundingPolygon": [{"x": 5.797, "y": 9.196}, {"x": 6.363, "y": 9.195}, {"x": 6.363, "y": 9.32}, {"x": 5.797, "y": 9.321}], "confidence": 0.9987803649902344}], "anchorConfidence": 0.9982288360595704, "valueConfidence": 0.9987803649902344}, "employers_limits_disease_policy": {"source": "1,000,000", "value": 1000000, "type": "number", "lines": [{"text": "$ 1,000,000", "page": 0, "boundingPolygon": [{"x": 5.797, "y": 9.362}, {"x": 6.362, "y": 9.362}, {"x": 6.362, "y": 9.486}, {"x": 5.797, "y": 9.487}], "confidence": 0.9990667724609374}], "anchorConfidence": 0.9989169311523437, "valueConfidence": 0.9990667724609374}, "employers_annual_renewal_premium": null, "one_before_last_row_type": null, "one_before_last_row_carrier_and_policy": null, "one_before_last_row_effective_date": null, "one_before_last_row_expiration_date": null, "one_before_last_row_limit": null, "one_before_last_row_annual_renewal_premium": null, "last_row_type": null, "last_row_carrier_and_policy": null, "last_row_effective_date": null, "last_row_expiration_date": null, "last_row_limit": null, "last_row_annual_renewal_premium": null, "defense_costs_in_aggregate_limits": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9954280090332032, "valueConfidence": null}, "defense_costs_separate_limit": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9976612091064453, "valueConfidence": null}, "defense_costs_unlimited": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9964947509765625, "valueConfidence": null}, "iso_form_effective_date": null, "exclusions_from_previous_coverage": {"value": "N", "type": "string", "lines": [{"text": "N", "page": 1, "boundingPolygon": [{"x": 7.918, "y": 1.522}, {"x": 7.989, "y": 1.522}, {"x": 7.989, "y": 1.61}, {"x": 7.918, "y": 1.61}], "confidence": 0.9903000640869141}], "anchorConfidence": 0.9641905975341797, "valueConfidence": 0.9903000640869141}, "exclusions_from_previous_coverage_details": {"type": "string", "value": "N", "lines": [{"text": "N", "page": 1, "boundingPolygon": [{"x": 7.918, "y": 1.522}, {"x": 7.989, "y": 1.522}, {"x": 7.989, "y": 1.61}, {"x": 7.918, "y": 1.61}], "confidence": 0.9903000640869141}], "anchorConfidence": 0.9641905975341797, "valueConfidence": 0.9903000640869141}, "claims_made_retroactive_date": null, "claims_made_uninterrupted_coverage_date": null, "claims_made_tail_coverage_purchased": null, "claims_made_tail_coverage_effective_date": null, "any_auto": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9856557464599609, "valueConfidence": null}, "cgl_claims_made": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9874713134765625, "valueConfidence": null}, "cgl_occurrence": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9722489929199218, "valueConfidence": null}, "aircraft_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9987184143066407, "valueConfidence": null}, "aircraft_liability_exposure": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9987184143066407, "valueConfidence": null}, "aircraft_passenger_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9980571746826172, "valueConfidence": null}, "aircraft_passenger_liability_exposure": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9980571746826172, "valueConfidence": null}, "additional_interest_coverage": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9985342407226563, "valueConfidence": null}, "additional_interest_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9985342407226563, "valueConfidence": null}, "care_custody_control_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9426443481445312, "valueConfidence": null}, "care_custody_control_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9426443481445312, "valueConfidence": null}, "employee_benefit_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9957395935058594, "valueConfidence": null}, "employee_benefit_liability_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9957395935058594, "valueConfidence": null}, "foreign_liability_travel_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.8245712280273437, "valueConfidence": null}, "foreign_liability_travel_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.8245712280273437, "valueConfidence": null}, "garagekeepers_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9969957733154297, "valueConfidence": null}, "garagekeepers_liability_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9969957733154297, "valueConfidence": null}, "incidental_medical_malpractice_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9963994598388672, "valueConfidence": null}, "incidental_medical_malpractice_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9963994598388672, "valueConfidence": null}, "liquor_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.*********2070312, "valueConfidence": null}, "liquor_liability_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.*********2070312, "valueConfidence": null}, "pollution_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9982112884521485, "valueConfidence": null}, "pollution_liability_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9982112884521485, "valueConfidence": null}, "professional_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.996523666381836, "valueConfidence": null}, "professional_liability_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.996523666381836, "valueConfidence": null}, "vendors_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9981998443603516, "valueConfidence": null}, "vendors_liability_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9981998443603516, "valueConfidence": null}, "watercraft_liability_coverage": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9951161956787109, "valueConfidence": null}, "watercraft_liability_exposure": {"type": "boolean", "value": true, "lines": [], "anchorConfidence": 0.9951161956787109, "valueConfidence": null}, "underlying_insurance_coverage_information": null, "underlying_insurance_previous_experience": null, "no_claims": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9977668762207031, "valueConfidence": null}}], "care_custody_control": [{"property_type_real": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.996623764038086, "valueConfidence": null}, "property_type_personal": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9979339599609375, "valueConfidence": null}, "value": null, "occupied_building_square_footage": null, "occupancy_description": null}], "vehicles": [{"private_passenger": [{"owned": null, "non_owned": null, "leased": null, "property_hauled": null, "local_radius": null, "intermediate_radius": null, "long_distance_radius": null}], "light_truck": [{"owned": null, "non_owned": null, "leased": null, "property_hauled": null, "local_radius": null, "intermediate_radius": null, "long_distance_radius": null}], "medium_trucks": [{"owned": null, "non_owned": null, "leased": null, "property_hauled": null, "local_radius": null, "intermediate_radius": null, "long_distance_radius": null}], "heavy_truck": [{"owned": null, "non_owned": null, "leased": null, "property_hauled": null, "local_radius": null, "intermediate_radius": null, "long_distance_radius": null}], "extra_heavy_trucks": [{"owned": null, "non_owned": null, "leased": null, "property_hauled": null, "local_radius": null, "intermediate_radius": null, "long_distance_radius": null}, {"owned": null, "non_owned": null, "leased": null, "property_hauled": null, "local_radius": null, "intermediate_radius": null, "long_distance_radius": null}], "buses": [{"owned": null, "non_owned": null, "leased": null, "property_hauled": null, "local_radius": null, "intermediate_radius": null, "long_distance_radius": null}]}], "additional_exposures": [{"media_used": null, "media_annual_cost": null, "advertising_agency_services_used": null, "advertising_agency_coverage": null, "aircraft": null, "dangerous_cargo": null, "passengers_for_fee": null, "units_not_insured_by_underlying": null, "vehicles_leased_to_others": null, "hired_coverages": null, "bridge_dam_marine_work": null, "typical_jobs_performed": null, "agreement": null, "crane_work": null, "subcontractors_limit_less_than_applicant": null, "applicant_self_insured": null, "hospital_or_first_aid": null, "doctor_nurse_coverage": null, "number_of_doctors": null, "number_of_nurses": null, "number_of_beds": null, "hazardous_material_products": null, "pollution_coverage_standard_iso_exclusion": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9983425140380859, "valueConfidence": null}, "pollution_coverage_standard_sudden_only": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9985845947265625, "valueConfidence": null}, "pollution_coverage_endorsement": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9977542877197265, "valueConfidence": null}, "pollution_coverage_separate": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9987059020996094, "valueConfidence": null}, "aircraft_installation": null, "foreign_products_operations": null, "product_liability_past_three_years": null, "product_liability_past_three_years_details": null, "independent_contractors_details": null, "has_watercrafts": null, "watercraft_location_1": null, "watercraft_owned_1": null, "watercraft_length_1": null, "watercraft_horsepower_1": null, "watercraft_location_2": null, "watercraft_owned_2": null, "watercraft_length_2": null, "watercraft_horsepower_2": null, "apartments_hotels_location_number_1": null, "apartments_hotels_number_stories_1": null, "apartments_hotels_number_units_1": null, "apartments_hotels_number_pools_1": null, "apartments_hotels_number_diving_boards_1": null, "apartments_hotels_location_number_2": null, "apartments_hotels_number_stories_2": null, "apartments_hotels_number_units_2": null, "apartments_hotels_number_pools_2": null, "apartments_hotels_number_diving_boards_2": null}], "signature": [{"producers_signature": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9988980102539062, "valueConfidence": null}, "producers_name": null, "state_producer_license_number": null, "applicants_signature": {"type": "boolean", "value": false, "lines": [], "anchorConfidence": 0.9992835235595703, "valueConfidence": null}, "date": null, "national_producers_number": null}]}