from __future__ import annotations

from marshmallow import EXCLUDE, ValidationError, fields, validate, validates_schema
from marshmallow_enum import Enum<PERSON><PERSON>
from marshmallow_oneofschema import OneOfSchema
from marshmallow_sqlalchemy import auto_field
from static_common.enums.classification import (
    ClassifierOutputType,
    ClassifierUnits,
    ExtractionType,
    FilterRuleType,
)

from copilot.models.customizable_classifiers import (
    ClassifierConfig,
    ClassifierConfigVersion,
    ClassifierPhrase,
    ClassifierVersion,
    ClassifierVersionTaskDataset,
    ClassifierVersionTaskDefinition,
    CustomizableClassifierV2,
    FilterRule,
    LLMConfigVersion,
    PhrasesConfigVersion,
    PhrasesWithLLMConfigVersion,
)
from copilot.schemas.base import BaseSchema


class ClassifierPhraseSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ClassifierPhrase
        unknown = EXCLUDE

    id = auto_field()
    classifier_config_version_id = auto_field(dump_only=True)
    phrase = auto_field()
    weight = auto_field(validate=validate.Range(min=0.0, max=1.0), load_default=1.0)
    excludes = fields.List(fields.String(), required=False, missing=list)
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)


class ClassifierConfigVersionBaseSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ClassifierConfigVersion
        unknown = EXCLUDE

    id = auto_field()
    classifier_config_id = auto_field(required=False)
    input_processing_type = auto_field()
    extraction_type = EnumField(ExtractionType, by_value=True, required=True)
    is_autogenerated = auto_field()
    task_model_id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)


class PhrasesConfigVersionSchema(ClassifierConfigVersionBaseSchema):
    class Meta(ClassifierConfigVersionBaseSchema.Meta):
        model = PhrasesConfigVersion

    phrases = fields.Nested(ClassifierPhraseSchema, many=True, required=False)


class PhrasesWithLLMConfigVersionSchema(ClassifierConfigVersionBaseSchema):
    class Meta(ClassifierConfigVersionBaseSchema.Meta):
        model = PhrasesWithLLMConfigVersion

    llm_model = auto_field()
    prompt = auto_field()
    phrases = fields.Nested(ClassifierPhraseSchema, many=True, required=False)


class LLMConfigVersionSchema(ClassifierConfigVersionBaseSchema):
    class Meta(ClassifierConfigVersionBaseSchema.Meta):
        model = LLMConfigVersion

    llm_model = auto_field()
    prompt = auto_field()


class ClassifierConfigVersionSchema(OneOfSchema):
    type_field = "extraction_type"
    type_field_remove = False

    type_schemas = {
        ExtractionType.PHRASES.value: PhrasesConfigVersionSchema,
        ExtractionType.PHRASES_WITH_LLM.value: PhrasesWithLLMConfigVersionSchema,
        ExtractionType.LLM.value: LLMConfigVersionSchema,
    }

    def get_obj_type(self, obj):
        if isinstance(obj, PhrasesConfigVersion):
            return ExtractionType.PHRASES.value
        elif isinstance(obj, PhrasesWithLLMConfigVersion):
            return ExtractionType.PHRASES_WITH_LLM.value
        elif isinstance(obj, LLMConfigVersion):
            return ExtractionType.LLM.value
        elif obj is None:
            return None
        raise TypeError(f"Unknown object type for ClassifierConfigVersionSchema: {obj.__class__.__name__}")


class ClassifierConfigSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ClassifierConfig
        unknown = EXCLUDE

    id = auto_field()
    input_types = fields.List(fields.String(), required=True)
    task_dataset_id = auto_field()
    versions = fields.Nested(ClassifierConfigVersionSchema, many=True, required=False)
    active_versions = fields.Nested(ClassifierConfigVersionSchema, many=True, dump_only=True)
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)


class ClassifierVersionTaskDefinitionSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ClassifierVersionTaskDefinition
        unknown = EXCLUDE

    id = auto_field()
    classifier_version_id = auto_field(dump_only=True)
    classifier_config_id = auto_field()
    task_definition_id = auto_field()
    created_at = auto_field(
        dump_only=True,
    )
    updated_at = auto_field(dump_only=True)


class ClassifierVersionSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ClassifierVersion
        unknown = EXCLUDE

    id = auto_field()
    classifier_id = auto_field(dump_only=True)
    name = auto_field()
    comment = auto_field()
    classifier_description = auto_field()
    is_active = auto_field()

    configs = fields.Nested(ClassifierConfigSchema, many=True)

    classifier_task_definitions = fields.Nested(ClassifierVersionTaskDefinitionSchema, many=True, required=False)
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)


class FilterRuleSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = FilterRule
        unknown = EXCLUDE

    id = auto_field()
    classifier_id = auto_field(dump_only=True)
    filter_type = EnumField(FilterRuleType, by_value=True, required=True)
    negated = auto_field()
    values = fields.List(fields.String(), required=True)
    created_at = auto_field(dump_only=True, required=False)
    updated_at = auto_field(dump_only=True, required=False)


class CustomizableClassifierV2Schema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = CustomizableClassifierV2
        unknown = EXCLUDE

    id = auto_field()
    name = auto_field()
    fact_subtype_id = auto_field()
    extracted_value_name = auto_field()
    output_type = EnumField(ClassifierOutputType, by_value=True, allow_none=True, required=False)
    output_unit = EnumField(ClassifierUnits, by_value=True, allow_none=True, required=False)
    tags = auto_field()
    input_types = fields.List(fields.String(), required=True)
    organization_ids = fields.List(fields.Integer(), missing=list)
    run_in_pds = auto_field()
    is_internal = auto_field()
    versions = fields.Nested(ClassifierVersionSchema, many=True)
    active_version = fields.Nested(ClassifierVersionSchema, allow_none=True, dump_only=True)
    filter_rules = fields.Nested(FilterRuleSchema, many=True)
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)

    @validates_schema
    def validate_exactly_one_field(self, data, **kwargs):
        fact_subtype_id = data.get("fact_subtype_id")
        extracted_value_name = data.get("extracted_value_name")

        if fact_subtype_id is not None and extracted_value_name is not None:
            raise ValidationError(
                "Exactly one of fact_subtype_id or extracted_value_name must be provided (both found).",
                field_names=["fact_subtype_id", "extracted_value_name"],
            )
        if fact_subtype_id is None and extracted_value_name is None:
            raise ValidationError(
                "At least one of fact_subtype_id or extracted_value_name must be provided.",
                field_names=["fact_subtype_id", "extracted_value_name"],
            )


class ClassifierVersionTaskDatasetSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = ClassifierVersionTaskDataset
        unknown = EXCLUDE

    id = auto_field()
    classifier_version_id = auto_field()
    task_dataset_id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
