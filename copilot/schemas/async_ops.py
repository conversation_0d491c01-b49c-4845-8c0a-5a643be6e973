from marshmallow_enum import <PERSON><PERSON><PERSON><PERSON>
from marshmallow_sqlalchemy import auto_field
from static_common.enums.async_ops import AsyncOperation, AsyncOperationStatus

from copilot.models.async_ops import AsyncOperationDB
from copilot.schemas.base import BaseSchema


class AsyncOpRequestBaseSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = AsyncOperationDB

    status = EnumField(AsyncOperationStatus, by_value=True, required=True, allow_none=False)
    error_details = auto_field(required=False, allow_none=True)
    additional_data = auto_field(required=False, allow_none=True)
    logical_identifier = auto_field(required=False, allow_none=True)
    attempts = auto_field(required=False, allow_none=True)
    executing_user_email = auto_field(required=True, allow_none=False)


class AsyncOpCreateRequestSchema(AsyncOpRequestBaseSchema):
    organization_id = auto_field(required=True, allow_none=False)
    report_id = auto_field(required=False, allow_none=True)
    submission_id = auto_field(required=False, allow_none=True)
    created_at = auto_field(required=False, allow_none=True)
    operation = EnumField(AsyncOperation, by_value=True, required=True, allow_none=False)


class AsyncOpUpdateRequestSchema(AsyncOpRequestBaseSchema):
    # Meta and all fields are inherited.
    pass


class AsyncOpCreateResponseSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = AsyncOperationDB

    id = auto_field(dump_only=True)
    created_at = auto_field(dump_only=True)


class AsyncOpLowDetailsSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = AsyncOperationDB

    id = auto_field(dump_only=True)
    created_at = auto_field(dump_only=True)
    status = EnumField(AsyncOperationStatus, by_value=True, required=True, allow_none=False)
    error_details = auto_field(required=False, allow_none=True)
    additional_data = auto_field(required=False, allow_none=True)
    logical_identifier = auto_field(required=False, allow_none=True)


class AsyncOpFullDetailsSchema(AsyncOpCreateRequestSchema):
    id = auto_field(dump_only=True)
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
