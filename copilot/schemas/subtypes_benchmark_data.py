from marshmallow import EXCLUDE, fields
from marshmallow_sqlalchemy import auto_field

from copilot.models.subtypes_benchmark_data import SubtypesBenchmarkData
from copilot.schemas.base import BaseSchema


class SubtypesBenchmarkDataSchema(BaseSchema):
    class Meta:
        model = SubtypesBenchmarkData
        load_instance = True
        unknown = EXCLUDE

    id = auto_field()
    fact_subtype_id = auto_field(required=False)
    name = auto_field(required=False)
    values = auto_field(required=False)
    updated_at = auto_field(dump_only=True)
    created_at = auto_field(dump_only=True)
    file_type = auto_field(required=False)
    organization_id = auto_field(required=False)


class SubtypesBenchmarkDataEnvelopeSchema(BaseSchema):
    class Meta:
        unknown = EXCLUDE

    total_pages = fields.Integer(dump_only=True)
    items = fields.Nested(SubtypesBenchmarkDataSchema, many=True, dump_only=True)
