from marshmallow import EXCLUDE, Schema, fields, post_dump, post_load
from marshmallow_sqlalchemy import auto_field

from copilot.models.broker_groups import (
    BrokerGroup,
    BrokerGroupMapping,
    BrokerGroupPatch,
    BrokerGroupUpdateBrokers,
)
from copilot.schemas.base import BaseSchema


class BrokerGroupMappingSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = BrokerGroupMapping
        unknown = EXCLUDE

    id = auto_field(required=False)
    broker_group_id = auto_field(required=False)
    broker_id = auto_field(required=True)
    organization_id = auto_field(required=False)
    user_id = auto_field(required=False)


class BrokerGroupSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = BrokerGroup
        unknown = EXCLUDE

    id = auto_field(required=False)
    name = auto_field(required=False)
    organization_id = auto_field(required=True)
    broker_group_mappings = fields.List(fields.Nested(BrokerGroupMappingSchema), required=False)

    @post_dump(pass_many=True)
    def wrap_with_envelope(self, data, many, **kwargs):
        if many:
            data = {"broker_groups": data}
        return data


class BrokerGroupPatchSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    name = fields.Str(required=True)

    @post_load
    def make_object(self, data, *args, **kwargs) -> BrokerGroupPatch:
        return BrokerGroupPatch(**data)


class BrokerGroupUpdateBrokersSchema(Schema):
    class Meta:
        unknown = EXCLUDE

    brokers_to_add = fields.List(fields.UUID(), required=False)
    brokers_to_remove = fields.List(fields.UUID(), required=False)

    @post_load
    def make_object(self, data, *args, **kwargs) -> BrokerGroupUpdateBrokers:
        return BrokerGroupUpdateBrokers(**data)
