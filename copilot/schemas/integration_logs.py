from marshmallow_enum import <PERSON><PERSON><PERSON><PERSON>
from marshmallow_sqlalchemy import auto_field
from static_common.enums.clients.integrations import (
    IntegrationFlow,
    IntegrationOperation,
    IntegrationStatus,
)

from copilot.models.integration_logs import IntegrationLog
from copilot.schemas.base import BaseSchema


class IntegrationLogsRequestBaseSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = IntegrationLog

    status = EnumField(IntegrationStatus, by_value=True, required=True, allow_none=False)
    payload_json = auto_field(required=False, allow_none=True)
    response_json = auto_field(required=False, allow_none=True)
    error_details = auto_field(required=False, allow_none=True)
    additional_data = auto_field(required=False, allow_none=True)
    logical_identifier = auto_field(required=False, allow_none=True)
    attempts = auto_field(required=False, allow_none=True)
    executing_user_email = auto_field(required=True, allow_none=False)


class IntegrationLogsCreateRequestSchema(IntegrationLogsRequestBaseSchema):
    organization_id = auto_field(required=True, allow_none=False)
    report_id = auto_field(required=False, allow_none=True)
    submission_id = auto_field(required=False, allow_none=True)
    created_at = auto_field(required=False, allow_none=True)
    flow = EnumField(IntegrationFlow, by_value=True, required=True, allow_none=False)
    operation = EnumField(IntegrationOperation, by_value=True, required=True, allow_none=False)


class IntegrationLogsUpdateRequestSchema(IntegrationLogsRequestBaseSchema):
    # Meta and all fields are inherited.
    pass


class IntegrationLogsCreateResponseSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = IntegrationLog

    id = auto_field(dump_only=True)
    created_at = auto_field(dump_only=True)
    payload_json_hash = auto_field(dump_only=True)
    response_json_hash = auto_field(dump_only=True)


class IntegrationLogsLowDetailsSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = IntegrationLog

    id = auto_field(dump_only=True)
    created_at = auto_field(dump_only=True)
    status = EnumField(IntegrationStatus, by_value=True, required=True, allow_none=False)
    error_details = auto_field(required=False, allow_none=True)
    additional_data = auto_field(required=False, allow_none=True)
    logical_identifier = auto_field(required=False, allow_none=True)


class IntegrationLogsFullDetailsSchema(IntegrationLogsCreateRequestSchema):
    id = auto_field(dump_only=True)
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    payload_json_hash = auto_field(dump_only=True)
    response_json_hash = auto_field(dump_only=True)
