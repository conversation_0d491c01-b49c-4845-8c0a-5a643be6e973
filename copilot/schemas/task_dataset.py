from __future__ import annotations

from marshmallow import EXCLUDE, fields
from marshmallow_enum import <PERSON>um<PERSON><PERSON>
from marshmallow_sqlalchemy import auto_field

from copilot.models.task_dataset import (
    TaskDataset,
    TaskDatasetGroundTruth,
    TaskDatasetInput,
    TaskDatasetModelOutcome,
)
from copilot.models.types import PredictionResult
from copilot.schemas.base import BaseSchema


class TaskDatasetSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = TaskDataset
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    description = auto_field()
    organization_id = auto_field(required=False)
    processing_type = auto_field()
    output_type = auto_field()
    dataset_inputs_id = auto_field()


class TaskDatasetInputSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = TaskDatasetInput
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    dataset_inputs_id = auto_field()
    submission_id = auto_field(required=False)
    file_id = auto_field(required=False)
    business_id = auto_field(required=False)
    context = fields.Dict(required=False)
    input = fields.Dict(required=False)


class TaskDatasetGroundTruthSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = TaskDatasetGroundTruth
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    task_dataset_id = auto_field()
    task_dataset_input_id = auto_field()
    value = fields.Dict(required=False)
    has_value = auto_field()


class TaskDatasetModelOutcomeSchema(BaseSchema):
    class Meta(BaseSchema.Meta):
        model = TaskDatasetModelOutcome
        unknown = EXCLUDE

    id = auto_field()
    created_at = auto_field(dump_only=True)
    updated_at = auto_field(dump_only=True)
    task_dataset_input_id = auto_field()
    task_model_id = auto_field()
    task_definition_id = auto_field()
    output = fields.Dict(required=False)
    is_valid_output = auto_field()
    prediction_result = EnumField(PredictionResult, by_value=True, required=False)
    score = fields.Float(required=False)
    input_tokens = fields.Integer(required=False)
    output_tokens = fields.Integer(required=False)
    validation_input_tokens = fields.Integer(required=False)
    validation_output_tokens = fields.Integer(required=False)
    processing_time = fields.Integer(required=False)
