from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy.orm import Session
from static_common.enums.async_ops import AsyncOperation, AsyncOperationStatus
from structlog.stdlib import BoundLogger

from copilot.models import db
from copilot.models.async_ops import AsyncOperationDB
from copilot.schemas.async_ops import (
    AsyncOpCreateRequestSchema,
    AsyncOpUpdateRequestSchema,
)
from copilot.utils import convert_to_uuid

logger = get_logger()

async_op_create_schema = AsyncOpCreateRequestSchema()
async_op_update_schema = AsyncOpUpdateRequestSchema()


class AsyncOpsService:
    @staticmethod
    def get_async_op_by_id(async_op_id: str, db_session: Session | None = None) -> AsyncOperationDB | None:
        db_session = AsyncOpsService._get_db_session(db_session)
        query = db_session.query(AsyncOperationDB).filter(AsyncOperationDB.id == convert_to_uuid(async_op_id))
        async_ops = query.first()
        return async_ops

    @staticmethod
    def create_new_async_op(body: dict, db_session: Session | None = None, commit: bool = False) -> AsyncOperationDB:
        db_session = AsyncOpsService._get_db_session(db_session)
        log = AsyncOpsService._get_logger()
        async_op = async_op_create_schema.load(body)
        log.info(
            "Creating new async op",
            report_id=async_op.report_id,
            submission_id=async_op.submission_id,
            operation=async_op.operation,
        )

        if not async_op.attempts:
            async_op.attempts = 1

        db_session.add(async_op)
        if commit:
            db_session.commit()

        return async_op

    @staticmethod
    def add_pending_async_op(
        organization_id: int,
        report_id: str,
        submission_id: str,
        operation: AsyncOperation,
        logical_identifier: str | None = None,
        executing_user_email: str | None = None,
        db_session: Session | None = None,
        commit: bool = False,
    ) -> AsyncOperationDB:
        body = {
            "organization_id": organization_id,
            "report_id": report_id,
            "submission_id": submission_id,
            "status": AsyncOperationStatus.PENDING.value,
            "operation": operation.value,
            "error_details": None,
            "additional_data": None,
            "logical_identifier": logical_identifier,
            "attempts": 1,
            "executing_user_email": executing_user_email,
        }
        return AsyncOpsService.create_new_async_op(body, db_session=db_session, commit=commit)

    @staticmethod
    def get_latest_async_op_by_report_and_operation(
        report_id: str, operation: AsyncOperation, db_session: Session | None = None
    ) -> AsyncOperationDB | None:
        db_session = AsyncOpsService._get_db_session(db_session)
        log = AsyncOpsService._get_logger()
        query = db_session.query(AsyncOperationDB).filter(
            AsyncOperationDB.report_id == convert_to_uuid(report_id),
            AsyncOperationDB.operation == operation,
        )
        query = query.order_by(AsyncOperationDB.created_at.desc()).limit(1)
        async_op = query.first()
        if async_op:
            if current_user.organization_id != async_op.organization_id:
                log.warn(
                    "User does not have access to this async op",
                    async_op_id=async_op.id,
                    user_org_id=current_user.organization_id,
                    async_op_org_id=async_op.organization_id,
                )
                return None
        return async_op

    @staticmethod
    def update_async_op_by_id(
        async_op_id: str,
        body: dict,
        db_session: Session | None = None,
        commit: bool = False,
    ) -> AsyncOperationDB | None:
        db_session = AsyncOpsService._get_db_session(db_session)
        log = AsyncOpsService._get_logger()
        existing_async_op = (
            db_session.query(AsyncOperationDB).filter(AsyncOperationDB.id == convert_to_uuid(async_op_id)).first()
        )
        if not existing_async_op:
            log.error("Async op not found", async_op_id=async_op_id)
            return None

        async_op = async_op_update_schema.load(body, instance=existing_async_op)

        db_session.add(async_op)
        if commit:
            db_session.commit()

        return async_op

    @staticmethod
    def _get_db_session(db_session: Session) -> Session:
        if db_session is None:
            db_session = db.session
        return db_session

    @staticmethod
    def _get_logger(bound_log: BoundLogger | None = None) -> BoundLogger:
        if bound_log is None:
            bound_log = logger
        return bound_log
