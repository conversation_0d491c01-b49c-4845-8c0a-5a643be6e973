from datetime import datetime
from functools import partial
from uuid import UUID
import json
import os

from common.utils.aws import (
    build_step_function_arn,
    detect_current_aws_account_id,
    detect_current_aws_region,
)
from infrastructure_common.logging import get_logger
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.file_type import FileType
from static_common.enums.parent import ParentType
from static_common.enums.task_definition_codes import TaskDefinitionCodes
from static_common.schemas.on_demand import (
    OnDemandIngestionRequestSchema,
    OnDemandSearchRequestSchema,
)
import boto3

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType

logger = get_logger()

on_demand_ingestion_schema = OnDemandIngestionRequestSchema()
on_demand_search_schema = OnDemandSearchRequestSchema()


class WorkflowClient:
    def __init__(self, interact_with_external_resources=True):
        self.interact_with_external_resources = interact_with_external_resources

        step_function_arn = partial(
            build_step_function_arn, detect_current_aws_region(), detect_current_aws_account_id()
        )
        self.customizable_classifier_test_run_arn = step_function_arn(
            os.environ.get("CUSTOMIZABLE_CLASSIFIER_TEST_RUN_STATE_MACHINE_NAME")
        )
        self.force_ingest_osha_inspection_from_url_arn = step_function_arn(
            os.environ.get("FORCE_INGEST_OSHA_INSPECTION_FROM_URL_STATE_MACHINE_NAME")
        )
        # TODO(ENG-27678): remove once migrated to MDA fully
        self.copilot_workers_process_file_sf_arn = step_function_arn("copilot-workers-process-file")
        self.copilot_workers_execute_task_sf_arn = step_function_arn("copilot-workers-execute-task")
        self.mda_engine_execute_task_sf_arn = step_function_arn("mda-engine-execute-task")
        self.copilot_workers_facts_matching_and_normalization_sf_arn = step_function_arn(
            "copilot-workers-facts-matching-and-normalization"
        )

        self.client = boto3.client("stepfunctions", region_name="us-east-1")

    def _start_execution(self, state_machine_arn: str, name: str, input: str):
        if self.interact_with_external_resources:
            return self.client.start_execution(stateMachineArn=state_machine_arn, name=name, input=input)

    @staticmethod
    def _get_now_str():
        return str(datetime.now()).replace("-", "").replace(":", "").replace(".", "").replace(" ", "")

    def start_customizable_classifier_test_run(self, test_run_id: UUID):
        test_run_request = {"customizable_classifier_test_run_id": str(test_run_id)}
        self._start_execution(
            self.customizable_classifier_test_run_arn,
            self._get_now_str(),
            json.dumps(test_run_request),
        )

    def invoke_force_ingest_osha_inspection_from_url(self, submission_id: UUID, url: str):
        try:
            force_ingest_request = {
                "kalepa_id": str(submission_id),
                "url": url,
                "parent_type": ParentType.SUBMISSION.value,
            }
            name = f"{submission_id!s}-{self._get_now_str()}"
            self._start_execution(
                self.force_ingest_osha_inspection_from_url_arn, name, json.dumps(force_ingest_request)
            )
        except:
            logger.exception("Failed to invoke force ingest osha inspection from url")

    def invoke_copilot_workers_process_file(self, submission_id: UUID, file_id: UUID):
        try:
            input = {
                "submission_id": str(submission_id),
                "file_id": str(file_id),
            }
            name = f"{file_id!s}-{self._get_now_str()}"
            self._start_execution(self.copilot_workers_process_file_sf_arn, name, json.dumps(input))
        except:
            logger.exception("Failed to invoke copilot workers process file Step Function")

    def invoke_copilot_workers_facts_matching_and_normalization(
        self,
        submission_id: UUID,
        file_id: UUID,
        s3_key: str,
        file_type: FileType,
        organization_id: int,
        classification: ClassificationDocumentType,
        parent_file_id: UUID | None,
        parent_classification: ClassificationDocumentType | None,
        parent_file_type: FileType | None,
    ) -> None:
        try:
            input = {
                "submission_id": str(submission_id),
                "file_id": str(file_id),
                "parent_file_id": str(parent_file_id) if parent_file_id else None,
                "s3_key": s3_key,
                "file_type": file_type.value,
                "organization_id": organization_id,
                "classification": str(classification) if classification else None,
                "parent_classification": str(parent_classification) if parent_classification else None,
                "parent_file_type": str(parent_file_type) if parent_file_type else None,
            }
            name = f"{file_id!s}-{self._get_now_str()}"
            self._start_execution(self.copilot_workers_facts_matching_and_normalization_sf_arn, name, json.dumps(input))
        except:
            logger.exception("Failed to invoke copilot workers facts matching and normalization Step Function")

    def invoke_execute_task(
        self,
        task_code: TaskDefinitionCodes,
        organization_id: int,
        submission_id: UUID,
        file_id: UUID | None = None,
        context: dict | None = None,
        force_copilot_workers: bool = False,
    ):
        try:
            input = {
                "task_code": task_code.value,
                "organization_id": organization_id,
                "submission_id": str(submission_id),
            }
            if file_id:
                input["file_id"] = str(file_id)
            if context:
                input["context"] = context
            name = f"{submission_id!s}-{task_code.value[:20]}-{self._get_now_str()}"

            # TODO(ENG-27678): remove the condition, log, FF and force_copilot_workers once done with the migration
            use_mda_engine = (
                FeatureFlagsClient.is_feature_enabled(FeatureType.USE_MDA_ENGINE) and not force_copilot_workers
            )
            logger.info(
                "Invoking MDA task",
                use_mda_engine=use_mda_engine,
                ff=FeatureFlagsClient.is_feature_enabled(FeatureType.USE_MDA_ENGINE),
                force_copilot_workers=force_copilot_workers,
            )
            if use_mda_engine:
                self._start_execution(self.mda_engine_execute_task_sf_arn, name[:80], json.dumps(input))
            else:
                self._start_execution(self.copilot_workers_execute_task_sf_arn, name[:80], json.dumps(input))

        except:
            logger.exception(
                "Failed to invoke copilot workers execute task Step Function",
                submission_id=submission_id,
                file_id=file_id,
            )

    def stop_execution_arn(self, execution_arn: str, error: str = None, cause: str = None):
        if self.interact_with_external_resources:
            self.client.stop_execution(executionArn=execution_arn, error=error, cause=cause)
        else:
            logger.info("Skipping stopping execution ARN", execution_arn=execution_arn)
