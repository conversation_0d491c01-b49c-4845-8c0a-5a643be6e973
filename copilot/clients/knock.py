from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
import base64
import os

from flask_login import current_user
from infrastructure_common.logging import get_logger
from knockapi import <PERSON><PERSON>
from retrying import retry
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
import flask
import jwt
import nh3

from copilot.clients.amplitude import AmplitudeClient
from copilot.clients.ers_v3 import ERSClientV3
from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.logic.paragon import extract_paragon_control_numbers
from copilot.models import (
    Organization,
    ReportPermission,
    ReportV2,
    Submission,
    User,
    db,
)
from copilot.models.emails import Email
from copilot.models.reports import SaveSubmissionNoteOptions, SubmissionNote
from copilot.schemas.report import SubmissionBusinessSchema
from copilot.utils import get_main_name_from_entity, get_most_suitable_premises

ONE_SECOND_IN_MS = 1000
NOTIFICATION_TIMESTAMP_FORMAT = "%b %d, %Y %H:%M:%S"


logger = get_logger()


def _fetch_submissions_primary_naics_code_description(submission: Submission) -> str:
    if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
        classes = flask.current_app.facts_client.get_classification_task(
            fact_subtype=FactSubtypeID.MULTI_LABEL_NAICS_CODES
        ).classes
        el = [desc.display_name for desc in classes if desc.fact_subtype_id == submission.primary_naics_code]
        if len(el) > 0:
            return el[0]
    return None


def append_idp_param(report_url: str, idp: str | None) -> str:
    """
    Append the `idp` query parameter to a Report's URL.
    This assumes that the Report's URL does not include any query parameters already.

    The value of `idp` should not be URL-encoded;
    the web app does not decode it (https://github.com/Kalepa/copilot_web_app/blob/env/stage/src/AuthRoute.jsx#L21).
    """
    if idp:
        return f"{report_url}?idp={idp}"
    return report_url


def build_report_url(report_url: str, user_idp: str | None, navigation_source: str) -> str | None:
    url = append_idp_param(report_url, user_idp) if report_url else None
    return f"{url}{'&' if '?' in url else '?'}navigationSource={navigation_source}" if url else None


@dataclass(frozen=True)
class NotificationType:
    name: str
    shared_notification: bool
    recipient_notification: bool
    disabled: bool

    @classmethod
    def recipient_only(cls, name: str):
        return cls(name=name, recipient_notification=True, shared_notification=False, disabled=False)

    @classmethod
    def shared_notification_only(cls, name: str):
        return cls(name=name, recipient_notification=False, shared_notification=True, disabled=False)

    @classmethod
    def create_disabled(cls, name: str):
        return cls(name=name, recipient_notification=True, shared_notification=False, disabled=True)


class KnockClient:
    REPORT_SHARED_NOTIFICATION_RECIPIENT_ONLY = NotificationType.recipient_only("report-shared")
    SUBMISSION_STAGE_CHANGED = NotificationType.recipient_only("submission-stage-changed")
    SUBMISSION_REFERRED_NOTIFICATION_RECIPIENT_ONLY = NotificationType.recipient_only("submission-referred-to-you")
    SUBMISSION_REFERRAL_CLOSED_NOTIFICATION_RECIPIENT_ONLY = NotificationType.recipient_only(
        "submission-referral-closed"
    )

    USER_TAGGED_IN_NOTE = NotificationType.recipient_only("user-tagged-in-note")
    MANUAL_LOSS_RUN_PROCESSING_COMPLETE = NotificationType.recipient_only("manual-loss-run-processing-complete")
    NEW_MESSAGE_IN_SUBMISSION_CORRESPONDENCE = NotificationType.recipient_only(
        "new-message-in-submission-correspondence"
    )

    def __init__(self, api_key, signing_key, env, interact_with_external_resources):
        self.client = Knock(api_key=api_key)
        self.env = env
        self.interact_with_external_resources = interact_with_external_resources
        # Note: We base64 code/decode signing key to avoid issues with newline characters
        # in AWS secrets / terraform. In secrets, we should store base64-coded string.
        try:
            self.signing_key = base64.b64decode(signing_key)
        except Exception:
            if self.env in {"prod", "stage"}:
                logger.exception("Issue parsing Knock signing key")

    def sign_jwt(self, user_id: int) -> str:
        data = {
            "sub": self._prepare_knock_user_id(user_id),
            "iat": int(datetime.utcnow().timestamp()),
            "exp": int((datetime.utcnow() + timedelta(hours=48)).timestamp()),
        }
        return jwt.encode(data, self.signing_key, algorithm="RS256")

    @retry(stop_max_attempt_number=3, wait_fixed=ONE_SECOND_IN_MS)
    def _identify(self, user_id: str, user: User) -> None:
        if not self.interact_with_external_resources:
            return
        self.client.users.identify(id=user_id, data={"name": user.name or user.email, "email": user.email})

    def identify_user(self, user: User) -> None:
        user_id = self._prepare_knock_user_id(user.id)
        try:
            self._identify(user_id, user)
            user.registered_in_knock = True
            db.session.commit()
        except:
            db.session.rollback()
            logger.exception("Failed to identify", user_id=user_id)

    @retry(stop_max_attempt_number=3, wait_fixed=ONE_SECOND_IN_MS)
    def _notify(
        self, sender_id_or_email: int | str, receiver: User, notification: NotificationType, data: dict
    ) -> None:
        log = logger.bind(
            sender_id_or_email=sender_id_or_email,
            receiver_id=receiver.id,
            receiver_email=receiver.email,
            notification=notification.name,
        )
        if not self.interact_with_external_resources:
            return
        if notification.disabled:
            return

        recipients = []
        if notification.recipient_notification:
            recipients.append(self._prepare_knock_user_id(receiver.id))
        if notification.shared_notification:
            if receiver.shared_notification_address:
                log.info(
                    "Appending shared notification address",
                    shared_notification_address=receiver.shared_notification_address,
                )
                recipients.append(self._prepare_knock_user_id(receiver.shared_notification_address))

        if len(recipients) == 0:
            log.warning("No recipients. Skipping sending notification")
            return

        self.client.notify(
            name=notification.name,
            actor=self._prepare_knock_user_id(sender_id_or_email),
            recipients=recipients,
            data=data,
        )

    def _publish(self, sender_id_or_email: int | str, receiver: User, notification: NotificationType, data: dict):
        log = logger.bind(
            sender_id_or_email=sender_id_or_email,
            receiver_id=receiver.id,
            receiver_email=receiver.email,
            notification=notification.name,
            data=data,
        )
        try:
            if receiver.applicable_settings and receiver.applicable_settings.user_notifications_enabled_after:
                current_time = datetime.now(timezone.utc)
                if current_time < receiver.applicable_settings.user_notifications_enabled_after:
                    log.info("User notifications are disabled until a later date", current_time=current_time)
                    return
            log.info("Publishing notification")
            self._notify(sender_id_or_email, receiver, notification, data)
            AmplitudeClient.track_notification_sent(receiver, notification.name)
        except Exception:
            log.exception("Failed to publish notification")

    def _prepare_knock_user_id(self, user_id_or_email: int | str) -> str:
        return f"{user_id_or_email}-{self.env}"

    def _broker_name(self, submission: Submission) -> str | None:
        return submission.broker_name

    def _brokerage_name(self, submission: Submission) -> str | None:
        return submission.brokerage_name

    def publish_submission_referral_closed_notification(
        self,
        sender: User,
        receiver: User,
        submission: Submission,
    ) -> None:
        data = {
            "senderName": sender.name or "-",
            "submissionName": submission.name,
            "submissionId": str(submission.id),
            "reportId": str(submission.report_id),
        }
        self._publish(sender.id, receiver, self.SUBMISSION_REFERRAL_CLOSED_NOTIFICATION_RECIPIENT_ONLY, data)

    def publish_user_tagged_in_note_notification(
        self,
        sender: User,
        receiver: User,
        note: SubmissionNote,
        options: SaveSubmissionNoteOptions,
        submission: Submission,
    ) -> None:
        last_modified_at: datetime = note.updated_at or note.created_at or datetime.utcnow()
        effective_date = submission.proposed_effective_date
        data = {
            "noteId": str(note.id),
            "html": nh3.clean(options.note_html) if options.note_html is not None else None,
            "lastModifiedTimestamp": last_modified_at.strftime(NOTIFICATION_TIMESTAMP_FORMAT),
            "reportId": str(submission.report.id),
            "reportName": submission.report.name,
            "linkToNotes": f"{submission.report.get_url()}?openNotes=true",
            "effectiveDate": effective_date.strftime("%m/%d/%Y") if effective_date else None,
            "agent": self._broker_name(submission),
            "agency": self._brokerage_name(submission),
            "recommendation": (
                submission.recommendation_v2_action.replace("_", " ").capitalize()
                if submission.recommendation_v2_action
                else None
            ),
            "isRenewal": submission.is_renewal,
        }

        primary_naics = _fetch_submissions_primary_naics_code_description(submission)
        if primary_naics:
            data["primaryNaics"] = primary_naics
        self._publish(sender.id, receiver, self.USER_TAGGED_IN_NOTE, data)

    def publish_manual_loss_run_processing_complete_notification(
        self,
        sender_id: int,
        receiver: User,
        submission: Submission,
    ) -> None:
        data = {
            "reportId": str(submission.report.id),
            "reportName": submission.report.name,
        }
        self._publish(sender_id, receiver, self.MANUAL_LOSS_RUN_PROCESSING_COMPLETE, data)

    def publish_report_shared_notification(
        self,
        report: ReportV2,
        owner_user: User,
        grantee_user: User,
        permission: ReportPermission,
        submission: Submission,
        policy_id: str,
        notification_type: NotificationType,
        is_underwriter_being_assigned: bool = False,
    ) -> None:
        submission_first_business = submission.businesses[0] if len(submission.businesses) else None
        if submission_first_business:
            submission_first_business = SubmissionBusinessSchema().dump(submission_first_business)
        fni_business = next(
            (
                b
                for b in submission.businesses
                if b.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
            ),
            None,
        )
        fni_entity = None
        if fni_business:
            ers_client: ERSClientV3 = flask.current_app.ers_client_v3
            fni_entity = ers_client.get_entity(entity_id=str(fni_business.business_id))

        fni_address = None
        fni_name = None
        fni_state = None
        if fni_entity:
            premises = get_most_suitable_premises(fni_entity.premises)
            fni_name = get_main_name_from_entity(fni_entity) or None
            if premises:
                fni_address = premises.premises.formatted_address
                fni_state = premises.premises.state

        effective_date = submission.proposed_effective_date
        expiration_date = submission.policy_expiration_date
        received_date = submission.received_date

        # If the user is not active, identify them first so Knock can deliver email.
        if not grantee_user.is_active or not grantee_user.registered_in_knock:
            self.identify_user(grantee_user)

        notification_source = (
            "rshared+shared+email+notification"
            if notification_type.shared_notification
            else "rshared+direct+email+notification"
        )

        cc_email_address = None
        show_more_details_in_email = False
        if submission.organization_id == 3 and FeatureFlagsClient.is_feature_enabled(
            FeatureType.EXTENDED_SHARED_EMAIL_FOR_KALEPA_ORG
        ):
            cc_email_address = "<EMAIL>"
            show_more_details_in_email = True
        elif submission.organization_id == 54 and FeatureFlagsClient.is_feature_enabled(
            FeatureType.EXTENDED_SHARED_EMAIL_FOR_BOWHEAD_ORG
        ):
            # TODO(ENG_21356): Update email address to the correct one once it's available
            cc_email_address = None
            show_more_details_in_email = True

        is_paragon = Organization.is_paragon_for_id(report.organization_id)
        data = {
            "reportName": report.name,
            "reportId": str(report.id),
            "inviterName": owner_user.name or owner_user.email,
            "ownerName": owner_user.name,
            "ownerEmail": owner_user.email,
            "reportUrl": build_report_url(report.url, grantee_user.idp, notification_source) if report.url else None,
            "message": nh3.clean(permission.message) if permission.message is not None else None,
            "lastModifiedTimestamp": datetime.utcnow().strftime(NOTIFICATION_TIMESTAMP_FORMAT),
            "permissionLevel": permission.permission_type.value,
            "businessCount": len(submission.businesses),
            "firstBusinessDetails": submission_first_business,
            "fniName": fni_name,
            "fniAddress": fni_address,
            "fniState": fni_state,
            "isGranteeCopilotUser": True if grantee_user.is_active else None,
            "effectiveDate": effective_date.strftime("%m/%d/%Y") if effective_date else None,
            "expirationDate": expiration_date.strftime("%m/%d/%Y") if expiration_date else None,
            "receivedDate": received_date.strftime("%m/%d/%Y") if received_date else None,
            "agent": self._broker_name(submission),
            "agency": self._brokerage_name(submission),
            "policy_id": _extract_paragon_control_numbers_for_notification(policy_id) if is_paragon else policy_id,
            "is_underwriter_being_assigned": is_underwriter_being_assigned,
            "recommendation": (
                submission.recommendation_v2_action.replace("_", " ").capitalize()
                if submission.recommendation_v2_action
                else None
            ),
            "isRenewal": submission.is_renewal,
            "organizationId": submission.organization_id,
            "showMoreDetails": show_more_details_in_email,
            "ccEmailAddress": cc_email_address,
        }

        primary_naics = _fetch_submissions_primary_naics_code_description(submission)
        if primary_naics:
            data["primaryNaics"] = primary_naics

        self._publish(owner_user.id, grantee_user, notification_type, data)

    def maybe_publish_report_shared_to_recipient(
        self,
        report: ReportV2,
        grantee_user: User,
        permission: ReportPermission,
        submission: Submission,
        external_id: str | None,
        is_underwriter_being_assigned: bool = False,
        is_referred: bool = False,
    ) -> None:
        log = logger.bind(submission_id=submission.id, external_id=external_id)
        if not submission.is_shareable:
            log.info("Submission is not shareable. Skipping sending shared notification")
            return
        if not grantee_user:
            return
        try:
            owner_user = current_user if is_referred else User.query.get(report.owner_id)
            self.publish_report_shared_notification(
                report=report,
                owner_user=owner_user,
                grantee_user=grantee_user,
                permission=permission,
                submission=submission,
                policy_id=external_id or "",
                notification_type=(
                    KnockClient.SUBMISSION_REFERRED_NOTIFICATION_RECIPIENT_ONLY
                    if is_referred
                    else KnockClient.REPORT_SHARED_NOTIFICATION_RECIPIENT_ONLY
                ),
                is_underwriter_being_assigned=is_underwriter_being_assigned,
            )
        except:
            log.exception("Could not send publish report shared notification")

    def publish_submission_stage_changed_notification(
        self, submission: Submission, prev_stage: str, new_stage: str
    ) -> None:
        if submission.is_verification_required and not submission.is_verified:
            return
        try:
            report = submission.reports[0]
            for recipient in submission.assigned_underwriters:
                if recipient.user.id != current_user.id:
                    data = {
                        "userName": recipient.user.name or recipient.user.email,
                        "submissionName": submission.name,
                        "submissionId": str(submission.id),
                        "newStage": new_stage,
                        "previousStage": prev_stage,
                        "effectiveDate": (
                            submission.proposed_effective_date.strftime("%m/%d/%Y")
                            if submission.proposed_effective_date
                            else None
                        ),
                        "agent": self._broker_name(submission).title() if self._broker_name(submission) else None,
                        "agency": self._brokerage_name(submission),
                        "isMachineUser": current_user.is_machine_user or current_user.cross_organization_access,
                        "reportId": str(report.id) if report else None,
                        "reportUrl": (
                            build_report_url(report.url, recipient.user.idp, "sschanged+email+notification")
                            if report.url
                            else None
                        ),
                    }
                    if self.env != "prod":
                        self.identify_user(current_user)
                        self.identify_user(recipient.user)
                    self._publish(current_user.id, recipient.user, self.SUBMISSION_STAGE_CHANGED, data)
        except:
            logger.exception(
                "Could not send publish submission stage changed notification", submission_id=submission.id
            )

    def _extract_name_and_email(self, email: Email) -> tuple[str, str]:
        if "<" in email.email_from:
            name_and_email_parts = [part.strip() for part in email.email_from.split("<") if part.strip()]
            sender_name, sender_email = name_and_email_parts
        else:
            sender_name, sender_email = (email.email_from, email.email_from)
        sender_name = sender_name.strip()
        sender_email = sender_email.strip(">").strip()
        return sender_name, sender_email

    def publish_new_message_in_submission_correspondence_notification(
        self, report: ReportV2, submission: Submission, email: Email, naics: str
    ) -> None:
        assigned_uw = submission.assigned_underwriters and submission.assigned_underwriters[0].user
        if not assigned_uw:
            return

        sender_name, sender_email = self._extract_name_and_email(email)

        sendEmail = "true" if not [x for x in email.email_cc if x in assigned_uw.email] else "false"

        payload = {
            "senderName": sender_name,
            "senderEmail": sender_email,
            "emailBody": email.email_body,
            "receivedOn": (email.email_sent_at or email.created_at or datetime.now()).strftime("%b %d, %Y %H:%M"),
            "effectiveDate": (
                submission.proposed_effective_date.strftime("%b %d, %Y") if submission.proposed_effective_date else "-"
            ),
            "primaryNaics": naics or "-",
            "brokerName": submission.broker.name if submission.broker else "-",
            "brokerageName": submission.brokerage.name if submission.brokerage else "-",
            "recommendation": (
                submission.recommendation_v2_action.capitalize() if submission.recommendation_v2_action else "-"
            ),
            "sales": f"${submission.sales:,}" if submission.sales else "-",
            "sendEmail": sendEmail,
            "reportName": report.name,
            "reportUrl": (
                build_report_url(report.url, assigned_uw.idp, "new+correspondence+message+email+notification")
                if report.url
                else None
            ),
            "reportId": str(report.id),
        }

        self._publish(current_user.id, assigned_uw, self.NEW_MESSAGE_IN_SUBMISSION_CORRESPONDENCE, payload)


def _extract_paragon_control_numbers_for_notification(policy_id: str | None) -> str:
    try:
        control_numbers = extract_paragon_control_numbers(policy_id)
        return ", ".join(map(str, control_numbers)) if control_numbers else ""
    except Exception:
        logger.exception("Could not extract paragon control numbers for notification")
        return ""
