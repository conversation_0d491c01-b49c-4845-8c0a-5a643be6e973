from collections import defaultdict
from dataclasses import dataclass
from datetime import date, datetime
from typing import Any
from uuid import UUID, uuid4
import json
import random
import re

from common.utils.address import cleanup_zip
from common.utils.collections import filter_none
from facts_client.model.fact_subtype import FactSubtype
from flask import current_app
from infrastructure_common.logging import get_logger
from marshmallow import ValidationError
from static_common.enums.coverage_names import CoverageName
from static_common.enums.entity import EntityFieldID, EntityInformation
from static_common.enums.fact_subtype import FactSubtypeID
from static_common.enums.fields import FieldType
from static_common.enums.interval import TimeSeriesInterval
from static_common.enums.submission_business import SubmissionBusinessEntityNamedInsured
from static_common.enums.submission_entity import SubmissionEntityType
from static_common.mappings.naics import validate_and_optionally_map_naics_to_2022
from static_common.models.coverages import CoverageDetails, ExtendedCoverageField
from static_common.models.file_onboarding import (
    AcordLocationInformation,
    BoundingBox,
    OnboardedFile,
    PDFEvidence,
    ResolvedDataField,
    ResolvedDataValue,
    SubmissionEntity,
)
from static_common.models.sensible import SensiblePolygon
from static_common.schemas.coverages import CoverageDetailsSchema

from copilot.constants import ACORD_ADDRESS_FILTERS, ACORD_GENERAL_FILTERS
from copilot.logic.onboarded_files_transformation import (
    calculate_entity_id,
    get_or_create_entity_field_by_name,
    get_or_create_field_by_fact_subtype,
)
from copilot.models.mappers import (
    CoverageAssignment,
    DataSection,
    EntitySection,
    ExtractionMethod,
    ExtractionValue,
    MappingConfiguration,
    MappingFieldConfiguration,
    MappingResponse,
    RawProperty,
    SubmissionProperty,
    TransientDataElement,
)
from copilot.models.reports import Coverage
from copilot.models.sensible_claim import (
    Acord126Coverages,
    AcordLinesOfBusinessType,
    PageMetadata,
    SensibleStringValue,
)
from copilot.models.types import CoverageType, SubmissionCoverageType
from copilot.schemas.mappers import MappingConfigurationSchema

logger = get_logger()


@dataclass
class FirstNameInsured:
    requested_name: str | None = None
    requested_address: str | None = None


def _normalize_sensible_address(address: str | None) -> str | None:
    if not address:
        return None
    normalized_address = " ".join(address.replace("\n", ", ").split())
    return cleanup_zip(normalized_address)


REQUESTED_NAME_MAX_LENGTH = 250

ADDRESS_STARTS_WITH_FILTERS = ["see", "any"]
# reasoning is 5 chars for postal code, 2 for state and the rest are spaces
ADDRESS_MIN_LENGTH = 10
EXPLANATION_PREFIX = "Additional information from the ACORD file:"


def _with_units(value: Any, units: str, as_dict: bool = False) -> str | dict | None:
    if value is None:
        return None
    if as_dict:
        return {"value": value, "units": units}
    return f"{value} {units}"


def calculate_confidence(line: BoundingBox, page_metadata: PageMetadata) -> float:
    if not page_metadata.bbox_page_data:
        return 1.0
    confidences = []
    for item in page_metadata.bbox_page_data:
        _, bbox, confidence = item
        if bbox.xmin >= line.xmin and bbox.xmax <= line.xmax and bbox.ymin >= line.ymin and bbox.ymax <= line.ymax:
            confidences.append(confidence)
    if not confidences:
        sample = random.sample(page_metadata.bbox_page_data, min(len(page_metadata.bbox_page_data), 5))
        confidences = [elem[2] for elem in sample]
    return sum(confidences) / len(confidences)


def get_pdf_evidence(lines: list[SensiblePolygon], page_metadata: dict[int, PageMetadata]) -> list[PDFEvidence]:
    if not page_metadata or not lines:
        logger.warning("Page metadata is missing")
        return []
    merged_bbox = None
    pages = set()
    field_confidences = []
    for line in lines:
        page = page_metadata[line.page]
        pages.add(line.page)
        x_min = min([point.x / page.width for point in line.boundingPolygon])
        x_max = max([point.x / page.width for point in line.boundingPolygon])
        y_min = min([point.y / page.height for point in line.boundingPolygon])
        y_max = max([point.y / page.height for point in line.boundingPolygon])
        line_box = BoundingBox(xmin=x_min, ymin=y_min, xmax=x_max, ymax=y_max)
        if line.confidence is not None:
            field_confidences.append(line.confidence)
        else:
            field_confidences.append(calculate_confidence(line_box, page_metadata[line.page]))
        if not merged_bbox:
            merged_bbox = line_box
        else:
            merged_bbox = BoundingBox(
                xmin=min(merged_bbox.xmin, x_min),
                ymin=min(merged_bbox.ymin, y_min),
                xmax=max(merged_bbox.xmax, x_max),
                ymax=max(merged_bbox.ymax, y_max),
            )
    if len(pages) > 1:
        logger.warning("Evidence found on multiple pages")
    return [
        PDFEvidence(
            page=pages.pop() + 1,
            observed_value_bbox=merged_bbox,
            confidence=sum(field_confidences) / len(field_confidences),
        )
    ]  # Sensible pages are 0-indexed


def _create_entity(
    data: OnboardedFile,
    existing_entity_unique_ids_to_idx: dict[str, int],
    name: str | None,
    address: str | None,
    should_generate_id: bool,
    entity_type: SubmissionEntityType | None,
    remote_id: str | None,
    entity_named_insured: SubmissionBusinessEntityNamedInsured | None,
    create_parent_business: bool = False,
    location_number: int | None = None,
    building_number: int | None = None,
) -> int:
    parent_idx = None
    if create_parent_business:
        parent_id = calculate_entity_id(name, address)
        parent_id = f"{parent_id}, loc:{location_number}" if location_number else parent_id
        parent_business = SubmissionEntity(
            type=SubmissionEntityType.BUSINESS,
            id=parent_id,
            acord_location_information=(
                AcordLocationInformation(location_number=location_number) if location_number else None
            ),
            resolved=False,
        )
        parent_idx = existing_entity_unique_ids_to_idx.get(parent_business.id)
        if parent_idx is None:
            parent_idx = len(data.entities)
            data.entities.append(parent_business)
            existing_entity_unique_ids_to_idx[parent_business.id] = parent_idx
            if name:
                name_field = get_or_create_entity_field_by_name(
                    file=data, field_name=EntityFieldID.NAME, value_type=FieldType.TEXT
                )
                name_field.values.append(ResolvedDataValue(value=name, entity_idx=parent_idx))
            if address:
                address_field = get_or_create_entity_field_by_name(
                    file=data, field_name=EntityFieldID.ADDRESS, value_type=FieldType.TEXT
                )
                address_field.values.append(ResolvedDataValue(value=address, entity_idx=parent_idx))
        else:
            existing_business = data.entities[parent_idx]
            if existing_business.acord_location_information is None:
                existing_business.acord_location_information = parent_business.acord_location_information
    location_information = (
        AcordLocationInformation(location_number=location_number, building_number=building_number)
        if location_number
        else None
    )
    entity = SubmissionEntity(
        remote_id=remote_id,
        parent_idx=parent_idx,
        parent_id=parent_business.id if parent_idx is not None else None,
        id=uuid4() if should_generate_id else calculate_entity_id(name, address),
        resolved=should_generate_id is True,
        type=entity_type,
        acord_location_information=location_information,
        entity_named_insured=entity_named_insured,
    )
    entity_idx = existing_entity_unique_ids_to_idx.get(entity.unique_identifier)
    if entity_idx is None:
        entity_idx = len(data.entities)
        existing_entity_unique_ids_to_idx[entity.unique_identifier] = entity_idx
        data.entities.append(entity)
        if name:
            name_field = get_or_create_entity_field_by_name(
                file=data, field_name=EntityFieldID.NAME, value_type=FieldType.TEXT
            )
            name_field.values.append(ResolvedDataValue(value=name, entity_idx=entity_idx))
        if address:
            address_field = get_or_create_entity_field_by_name(
                file=data, field_name=EntityFieldID.ADDRESS, value_type=FieldType.TEXT
            )
            address_field.values.append(ResolvedDataValue(value=address, entity_idx=entity_idx))
    else:
        existing_entity = data.entities[entity_idx]
        if existing_entity.acord_location_information is None:
            existing_entity.acord_location_information = location_information

    return entity_idx


def _get_naics_code(value: str) -> str | None:
    fact_subtypes = current_app.facts_client_v2.get_fact_subtypes_ids()
    naics = f"NAICS_{value}"
    return naics if naics in fact_subtypes else None


def _get_years_in_business(start_date: datetime) -> int | None:
    if not start_date:
        return None
    return (datetime.now() - start_date.replace(tzinfo=None)).days // 365


def _calculate_one_of(extraction_values: list[ExtractionValue], response_data: Any) -> Any | None:
    for one_of_value in extraction_values:
        sens_val = getattr(response_data, one_of_value.name, None)
        if sens_val and sens_val.value:
            if one_of_value.value:
                return one_of_value.value
            else:
                # If not specified in ExtractionValue we will use the one from Sensible
                return sens_val.value

    return None


def _wrap_in_timeseries(value: Any) -> str | None:
    """
    Wraps a value in a timeseries format with annual intervals for use in _process_data_section in AcordMapper.
    Since ACORD values do not include explicit intervals, we default to using the previous calendar year.
    """
    if value:
        beginning_of_last_year = datetime(year=date.today().year - 1, month=1, day=1)
        end_of_last_year = datetime(year=date.today().year - 1, month=12, day=31)
        timeseries_dict = {
            "values": [value],
            "units": "USD",
            "interval": TimeSeriesInterval.ANNUAL.value,
            "times": [str(end_of_last_year)],
            "start_dates": [str(beginning_of_last_year)],
        }
        return json.dumps(timeseries_dict)


def _get_computed_value(mapping_config: MappingFieldConfiguration, response_data: Any) -> Any | None:
    if mapping_config.value_config.extraction_method == ExtractionMethod.ONE_OF:
        return _calculate_one_of(mapping_config.value_config.values, response_data)
    if mapping_config.value_config.extraction_method == ExtractionMethod.DIFFERENCE_IN_YEARS:
        sensible_value = getattr(response_data, mapping_config.name, None)
        value = sensible_value.value if sensible_value else None
        return _get_years_in_business(value)
    if mapping_config.value_config.extraction_method == ExtractionMethod.TIMESERIES:
        sensible_value = getattr(response_data, mapping_config.name, None)
        if sensible_value:
            return _wrap_in_timeseries(sensible_value.value)


def _is_address_valid(address: str | None) -> bool:
    if not address:
        return False
    # remove non alphanum, with space
    address_l = address.lower()
    address_l = re.sub(r"\W", " ", address_l)

    # replace more than one space with only one space
    address_l = re.sub(r"\s{2,}", " ", address_l)
    address_l = f" {address_l} "
    if (
        len(address_l) < ADDRESS_MIN_LENGTH
        or address_l.startswith(tuple(ADDRESS_STARTS_WITH_FILTERS))
        or any(f" {val} " in address_l for val in ACORD_GENERAL_FILTERS)
        or any(f" {val} " in address_l for val in ACORD_ADDRESS_FILTERS)
    ):
        return False
    return True


def _is_value_valid(value: str | None, max_length: int | None = None) -> bool:
    if not value:
        return False
    value_l = value.lower()

    if any(val in value_l for val in ACORD_GENERAL_FILTERS):
        for val in ACORD_GENERAL_FILTERS:
            value_l = value_l.replace(val, "")
        if len(value_l) < 5:
            return False
    if max_length and len(value_l) > max_length:
        return False
    return True


def _is_object_empty(obj: Any) -> bool:
    empty = True
    for val in obj.__dict__.values():
        if isinstance(val, list):
            for item in val:
                empty = empty and _is_object_empty(item)
        else:
            empty = empty and (val is None or getattr(val, "value", None) is False)
    return empty


def _resolve_value_same_using_fallback(value: str, fallback: str, log: logger) -> str | None:
    if value and "same" in value:
        log.warning("Requested value is same", requested_value=value, fallback=fallback)
    if value and (value.strip().lower() in {"same", "same same"}):
        return fallback
    return value


class AcordMapper:
    REQUESTED_NAME = "requested_name"
    REQUESTED_ADDRESS = "requested_address"
    LOCATION_NUMBER = "location_number"
    BUILDING_NUMBER = "building_number"

    def __init__(self) -> None:
        self._mapping_configuration_schema = MappingConfigurationSchema()
        self.available_coverages = []
        self._fact_subtypes = None

    @property
    def fact_subtypes(self) -> dict[str, FactSubtype]:
        if not self._fact_subtypes:
            self._fact_subtypes = current_app.facts_client_v2.get_fact_subtypes_as_dict()
        return self._fact_subtypes

    def process_response(
        self,
        mapping_configuration: dict,
        response_data: Any,
        organization_id: int,
        submission_id: UUID,
        page_metadata: dict[int, PageMetadata] | None = None,
    ) -> MappingResponse:
        self.available_coverages = self._get_available_coverages(organization_id)
        result = MappingResponse(raw_processed_data=OnboardedFile())
        try:
            configuration: MappingConfiguration = self._mapping_configuration_schema.load(mapping_configuration)
        except ValidationError:
            logger.exception("Can't load mapping configuration")
            return result

        named_insured = self._extract_named_insured(response_data, "named_insured")

        first_name_insured = self._extract_first_name_insured_from_applicant_information(response_data)

        if named_insured:
            result.transient_data = dict()
            result.transient_data["named_insured"] = named_insured

        # both methods process_entity_sections and _process_data_sections are extracting first party data from
        # the sensible response (mapped into objects).
        # The only difference being is that:
        #   - _process_entity_sections => creates first party entities (Businesses, Structures, Products, etc...)
        #     and first party data related to those entities
        #   - _process_data_sections => just creates first party data that is related to the FNI if it exists
        #     otherwise, the data does not have a parent
        # How the response is processed (this applies to both functions):
        # The response that we get from sensible has the following format:
        # {
        #     "applicant_information": [
        #         {
        #             "applicant_name_and_address": {
        #                 "type": "string",
        #                 "value": "Landspider Tire Inc. 611 North Brand Boulevard, 13-176 Glendale, CA 91203"
        #             }...
        #         }
        #     ],
        #     "premises_info": [
        #         {
        #             "street": {
        #                 "type": "string",
        #                 "value": "611 North Brand Boulevard, 13-176"
        #             }
        #         }...
        #     ],
        #     "general_information": [
        #         {
        #             "is_applicant_subsidiary": {
        #                 "value": "N",
        #                 "type": "string"
        #             }...
        #         }
        #     ]
        # }
        # So in this case, we have three sections:
        # - applicant_information (has entities => businesses, first party data)
        # - premises_info (has entities => businesses and structures, first party data)
        # - general_information (first party data)
        # and every object in each section has the same attributes. This means that every config contains
        # what is essentially a mapping configuration on how to map each object into first party data
        fallback_requested_name = named_insured if configuration.use_named_insured_as_fallback else None
        self._process_entity_sections(
            result,
            configuration.entity_sections,
            response_data,
            fallback_requested_name,
            first_name_insured,
            submission_id,
            page_metadata=page_metadata,
        )
        self._process_data_sections(result, configuration.data_sections, response_data, page_metadata=page_metadata)
        self._process_submission_fields(
            mapping_response=result,
            response_data=response_data,
            props=configuration.submission_properties,
            coverage_assignment=configuration.coverages,
            organization_id=organization_id,
            submission_id=submission_id,
        )

        self._process_raw_fields(result, response_data, configuration.raw_properties, page_metadata)

        if result.transient_data:
            result.transient_data["file_classification"] = configuration.file_classification
            result.raw_processed_data.transient_data = result.transient_data_object

        return result

    def _get_available_coverages(self, organization_id: int) -> list[Coverage]:
        return Coverage.query.filter_by(organization_id=organization_id, is_disabled=False).all()

    def _process_entity_sections(
        self,
        mapping_response: MappingResponse,
        entity_sections: list[EntitySection],
        response_data: Any,
        fallback_name: str | None = None,
        first_name_insured: FirstNameInsured | None = None,
        submission_id: UUID | None = None,
        page_metadata: dict[int, PageMetadata] | None = None,
    ) -> None:
        data = mapping_response.raw_processed_data
        existing_entity_unique_ids_to_idx = {e.unique_identifier: idx for idx, e in enumerate(data.entities)}
        fallback_request_name = fallback_name
        for entity_section in entity_sections:
            section_data = getattr(response_data, entity_section.name, None)
            if not section_data:
                continue
            self._process_entity_section(
                mapping_response,
                entity_section,
                section_data,
                existing_entity_unique_ids_to_idx,
                fallback_request_name,
                first_name_insured,
                submission_id,
                page_metadata=page_metadata,
            )
            if entity_section.use_as_fallback_requested_name and not fallback_request_name:
                if data.entities:
                    e = data.entities[0]
                    fallback_request_name = None
                    if e.is_fni and (
                        name_field := next((f for f in data.entity_information if f.name == EntityFieldID.NAME), None)
                    ):
                        fallback_request_name = next((v.value for v in name_field.values if v.entity_idx == 0), None)
                elif section_data and isinstance(section_data, list) and section_data[0].requested_name:
                    fallback_request_name = section_data[0].requested_name.value

    def _process_entity_section(
        self,
        mapping_response: MappingResponse,
        entity_section: EntitySection,
        section_data: list[Any],
        existing_entity_unique_ids_to_idx: dict[str, int],
        fallback_request_name: str | None = None,
        first_name_insured: FirstNameInsured | None = None,
        submission_id: UUID | None = None,
        page_metadata: dict[int, PageMetadata] | None = None,
    ) -> None:
        of = mapping_response.raw_processed_data
        section_data = [sd for sd in section_data if not _is_object_empty(sd)]
        section_data_count = len(section_data)
        for idx, sd in enumerate(section_data):
            requested_address = getattr(sd, self.REQUESTED_ADDRESS, None)
            requested_name = getattr(sd, self.REQUESTED_NAME, None)
            location_number = getattr(sd, self.LOCATION_NUMBER, None)
            building_number = getattr(sd, self.BUILDING_NUMBER, None)
            requested_address = _normalize_sensible_address(requested_address.value) if requested_address else None
            requested_name = requested_name.value if requested_name else fallback_request_name
            requested_name = (
                requested_name if _is_value_valid(requested_name, REQUESTED_NAME_MAX_LENGTH) else fallback_request_name
            )
            acord_location_number = location_number.value if location_number else None
            acord_building_number = building_number.value if building_number else None
            remote_id = None

            log = logger.bind(
                requested_name=requested_name,
                requested_address=requested_address,
                submission_id=submission_id,
                response_data_idx=idx,
                response_data=sd,
            )

            if first_name_insured:
                requested_name = _resolve_value_same_using_fallback(
                    requested_name, first_name_insured.requested_name, log
                )
                requested_address = _resolve_value_same_using_fallback(
                    requested_address, first_name_insured.requested_address, log
                )

            no_requested_name = requested_name is None or not requested_name.strip()

            if no_requested_name and not _is_address_valid(requested_address):
                continue

            if entity_section.address_is_required and not _is_address_valid(requested_address):
                log.warning("Address is required but not valid, skipping process transient data")
                if entity_section.fallback_as_data_section and section_data_count == 1:
                    self._process_data_section(
                        response_data=sd,
                        mapping_response=mapping_response,
                        field_configs=entity_section.fields_mapping,
                        # We will assign the extracted data to the FNI as it comes from the applicant information
                        entity_idx=None,
                        remote_id_prop_name=entity_section.remote_id_property_name,
                        page_metadata=page_metadata,
                    )
                continue

            # it can happen in acord 140 that we get information with:
            # - location_number = 0
            # - building number = 0
            # This is invalid location, and it means it applies to all premises.
            if (
                entity_section.entity_type == SubmissionEntityType.STRUCTURE
                and acord_location_number == 0
                and acord_building_number == 0
            ):
                continue

            if (
                entity_section.entity_type == SubmissionEntityType.STRUCTURE
                and acord_location_number is None
                and acord_building_number is None
                and requested_name
                and section_data_count != 1
                and not _is_address_valid(requested_address)
            ):
                continue

            if entity_section.remote_id_property_name:
                remote_id = getattr(sd, entity_section.remote_id_property_name, None)
                remote_id = str(remote_id.value) if remote_id else None

            entity_named_insured = (
                (
                    SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED
                    if idx == entity_section.fni_index
                    else SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED
                )
                if entity_section.entity_type == SubmissionEntityType.BUSINESS
                else None
            )
            entity_idx = _create_entity(
                data=of,
                existing_entity_unique_ids_to_idx=existing_entity_unique_ids_to_idx,
                name=requested_name,
                address=requested_address,
                should_generate_id=entity_section.generate_id,
                entity_type=entity_section.entity_type,
                remote_id=remote_id,
                entity_named_insured=entity_named_insured,
                create_parent_business=entity_section.create_parent_business,
                location_number=acord_location_number,
                building_number=acord_building_number,
            )
            self._process_transient_data(
                sd,
                mapping_response,
                entity_section.transient_data,
                requested_name,
                requested_address,
                acord_location_number,
                acord_building_number,
            )
            self._process_data_section(
                response_data=sd,
                mapping_response=mapping_response,
                field_configs=entity_section.fields_mapping,
                entity_idx=entity_idx,
                remote_id_prop_name=entity_section.remote_id_property_name,
                page_metadata=page_metadata,
            )

            self._process_data_sections(
                response_data=sd,
                mapping_response=mapping_response,
                data_sections=entity_section.data_sections,
                entity_idx=entity_idx,
                page_metadata=page_metadata,
            )

    def _process_data_sections(
        self,
        mapping_response: MappingResponse,
        data_sections: list[DataSection],
        response_data: Any,
        page_metadata: dict[int, PageMetadata] | None = None,
        entity_idx: int | None = None,
    ) -> None:
        data = mapping_response.raw_processed_data
        fni_idx = data.fni_idx
        name_field = next((f for f in data.entity_information if f.name == EntityFieldID.NAME), None)
        req_name = next((v.value for v in name_field.values if v.entity_idx == fni_idx), None) if name_field else None
        address_field = next((f for f in data.entity_information if f.name == EntityFieldID.ADDRESS), None)
        req_address = (
            next((v.value for v in address_field.values if v.entity_idx == fni_idx), None) if address_field else None
        )
        for data_section in data_sections or []:
            entity_idx = entity_idx or fni_idx
            section_data: list[Any] = getattr(response_data, data_section.name, None)
            if not section_data:
                continue
            for sd in section_data:
                if _is_object_empty(sd):
                    continue
                if data_section.entity_match:
                    sensible_value = getattr(sd, data_section.entity_match, None)
                    value = sensible_value.value if sensible_value else None
                    business_entities = [
                        (idx, e)
                        for idx, e in enumerate(data.entities)
                        if e.type == SubmissionEntityType.BUSINESS and e.acord_location_information
                    ]
                    entity_idx = next(
                        (idx for idx, e in business_entities if e.acord_location_information.location_number == value),
                        entity_idx or fni_idx,
                    )
                self._process_data_section(
                    response_data=sd,
                    mapping_response=mapping_response,
                    field_configs=data_section.fields_mapping,
                    entity_idx=entity_idx,
                    page_metadata=page_metadata,
                )
                self._process_transient_data(sd, mapping_response, data_section.transient_data, req_name, req_address)
                if data_section.data_sections:
                    self._process_data_sections(
                        mapping_response=mapping_response,
                        data_sections=data_section.data_sections,
                        response_data=sd,
                        page_metadata=page_metadata,
                        entity_idx=entity_idx,
                    )

    def _process_data_section(
        self,
        response_data: Any,
        mapping_response: MappingResponse,
        field_configs: list[MappingFieldConfiguration],
        entity_idx: int | None,
        remote_id_prop_name: str | None = None,
        page_metadata: dict[int, PageMetadata] = None,
    ) -> None:
        for fc in field_configs or []:
            subtype = fc.subtype or fc.naics_code
            subtype = subtype.value if subtype else None
            sensible_value = getattr(response_data, fc.name, None)
            value = sensible_value.value if sensible_value else None
            if value is None and not fc.derived_prop_name:
                continue

            should_convert_sic_to_naics = fc.subtype == FactSubtypeID.SIC.value and len(value) == 6
            if fc.infer_naics_subtype_from_value or should_convert_sic_to_naics:
                subtype = validate_and_optionally_map_naics_to_2022(value)
                if not subtype:
                    logger.warning("Cannot get NAICS code for value", code=value)
                    continue
                value = True

            if fc.type == FieldType.TEXT.value and isinstance(value, str) and len(value) > 256:
                value = value[:256]

            if fc.value_config:
                value = _get_computed_value(fc, response_data)

            if value is None or (value is False and fc.naics_code):
                continue

            if isinstance(value, str) and not _is_value_valid(value):
                continue

            if fc.units:
                value = _with_units(value, fc.units)

            explanations = []
            if fc.explanation and (expl := getattr(response_data, fc.explanation, None)):
                explanations.append(f"{EXPLANATION_PREFIX} {expl.value}")

            for explanation in fc.list_explanations or []:
                list_of_explanation_values = getattr(response_data, explanation.property, None)
                if not list_of_explanation_values:
                    continue
                values_as_str = ";".join(filter_none([ev.explanation_value for ev in list_of_explanation_values]))
                if values_as_str:
                    explanations.append(f"{EXPLANATION_PREFIX} {fc.name}{values_as_str}")

            field = get_or_create_field_by_fact_subtype(
                file=mapping_response.raw_processed_data,
                field_name=fc.name,
                fact_subtype_id=subtype,
                value_type=fc.type,
                fact_subtypes=self.fact_subtypes,
            )
            field.values.append(
                ResolvedDataValue(
                    value=self._serialize_resolved_value(value),
                    entity_idx=entity_idx,
                    explanations=explanations,
                    evidences=get_pdf_evidence(getattr(sensible_value, "lines", []), page_metadata),
                )
            )

            if fc.dependent_fields and value == fc.dependent_fields.value_equal_to:
                self._process_data_section(
                    response_data=response_data,
                    mapping_response=mapping_response,
                    field_configs=fc.dependent_fields.fields,
                    entity_idx=entity_idx,
                    remote_id_prop_name=remote_id_prop_name,
                    page_metadata=page_metadata,
                )

    def _serialize_resolved_value(self, value: Any) -> str:
        if isinstance(value, datetime):
            return str(value)
        elif hasattr(value, "value"):
            return self._serialize_resolved_value(value.value)
        else:
            return value

    def _process_submission_fields(
        self,
        mapping_response: MappingResponse,
        response_data: Any,
        props: list[SubmissionProperty],
        coverage_assignment: list[CoverageAssignment],
        organization_id: int,
        submission_id: UUID,
    ) -> None:
        sub_entity_idx = mapping_response.raw_processed_data.get_or_create_submission_entity_idx(submission_id)
        coverages = self._process_static_coverages(coverage_assignment, organization_id)
        for p in props:
            if (
                p.apply_for_organizations
                and organization_id not in p.apply_for_organizations
                and ((current_app and current_app.is_prod) or current_app is None)
            ):  # env is used to enable testing on stage, but keep everything right on production
                continue

            sensible_value = getattr(response_data, p.path, None)
            if p.path == "lines_of_business":
                coverages += self._extract_coverages(lines_of_business=sensible_value, additional_coverage_data=None)
                continue
            elif p.path == "coverages":
                coverages += self._extract_coverages(lines_of_business=None, additional_coverage_data=sensible_value)
                continue
            elif p.path.startswith("agency_information") or p.path.startswith("application_information"):
                if p.entity_information == EntityInformation.CARRIER_TRANSACTION_TYPE:
                    value = self._process_transaction_type(response_data)
                    if value is not None:
                        field = get_or_create_entity_field_by_name(
                            mapping_response.raw_processed_data, p.entity_information, FieldType.TEXT
                        )
                        field.values.append(
                            ResolvedDataValue(value=self._serialize_resolved_value(value), entity_idx=sub_entity_idx)
                        )
                elif p.entity_information == EntityInformation.CARRIER_STATUS_OF_TRANSACTION:
                    value = self._process_transaction_status(response_data)
                    if value is not None:
                        field = get_or_create_entity_field_by_name(
                            mapping_response.raw_processed_data, p.entity_information, FieldType.TEXT
                        )
                        field.values.append(
                            ResolvedDataValue(value=self._serialize_resolved_value(value), entity_idx=sub_entity_idx)
                        )
                else:
                    main_field, subfield = self._extract_main_field_and_subfield(p.path)
                    value = self._get_nested_data_from_response_data(response_data, main_field, subfield)

            else:
                value = sensible_value.value if sensible_value else None

            if value is not None:
                field = get_or_create_entity_field_by_name(
                    mapping_response.raw_processed_data, p.entity_information, FieldType.TEXT
                )
                field.values.append(
                    ResolvedDataValue(
                        value=self._serialize_resolved_value(f"NAICS_{value}" if p.is_naics else value),
                        entity_idx=sub_entity_idx,
                    )
                )

        if coverages:
            coverages = self._deduplicate_coverages(coverages)
            field = get_or_create_entity_field_by_name(
                mapping_response.raw_processed_data, EntityInformation.COVERAGES_DETAILS, FieldType.TEXT
            )
            field.values.append(
                ResolvedDataValue(value=CoverageDetailsSchema().dumps(coverages, many=True), entity_idx=sub_entity_idx)
            )

    def _process_additional_coverages_data(self, sensible_value: list[Acord126Coverages]) -> list[CoverageDetails]:
        if sensible_value is None:
            return None
        result = []
        for value in sensible_value:
            if value is None:
                continue
            coverages = CoverageDetails(
                coverage_name=CoverageName.Liability, coverage_type=SubmissionCoverageType.PRIMARY
            )
            if value.limits_general_aggregate is not None:
                coverages.limit = value.limits_general_aggregate.value
            if value.limits_each_occurrence is not None:
                coverages.each_occurrence = value.limits_each_occurrence.value

            coverages.gl_limits = self._process_gl_limits(value)
            coverages.gl_deductibles = self._process_gl_deductibles(value)
            coverages.limit_applies = self._process_limit_applies(value)
            result.append(coverages)

        return result

    def _process_limit_applies(self, sensible_value: Acord126Coverages) -> list[ExtendedCoverageField]:
        result = []
        fields = [
            (sensible_value.limits_general_aggregate_limit_applies_per_policy, "Limit Applies Per Policy"),
            (sensible_value.limits_general_aggregate_limit_applies_per_location, "Limit Applies Per Location"),
            (sensible_value.limits_general_aggregate_limit_applies_per_other, "Limit Applies Per Other"),
            (sensible_value.limits_general_aggregate_limit_applies_per_project, "Limit Applies Per Project"),
        ]
        for f in fields:
            if (field := self._extract_single_value_from_coverages(f[0], f[1])) is not None:
                result.append(field)

        return result

    def _extract_single_value_from_coverages(self, field: Any, name: str) -> ExtendedCoverageField | None:
        if field is None:
            return None
        return ExtendedCoverageField(value=field.value, name=name)

    def _process_gl_limits(self, sensible_value: Acord126Coverages) -> list[ExtendedCoverageField] | None:
        result = []
        fields = [
            (sensible_value.commercial_general_liability, "Commercial General Liability"),
            (sensible_value.claims_made, "Claims Made"),
            (sensible_value.occurrence, "Occurrence"),
            (sensible_value.owners_and_contractors_protective, "Owners and Contractors Protective"),
            (sensible_value.coverage_custom_name, "Custom Coverage Name"),
            (sensible_value.coverage_custom_value, "Custom Coverage Value"),
            (sensible_value.limits_general_aggregate, "Limits: General Aggregate"),
            (
                sensible_value.limits_products_and_completed_operations_aggregate,
                "Limits: Products and Completed Operations Aggregate",
            ),
            (sensible_value.limits_personal_and_advertising_injury, "Limits: Personal and Advertising Injury"),
            (sensible_value.limits_each_occurrence, "Limits: Each Occurrence"),
            (sensible_value.limits_damage_to_rented_premises, "Limits: Damage to Rented Premises"),
            (sensible_value.limits_medical_expense, "Limits: Medical Expense"),
            (sensible_value.limits_employee_benefits, "Limits: Employee Benefits"),
            (sensible_value.limits_custom_limit_name, "Limits: Custom Limit Name"),
            (sensible_value.limits_custom_limit_amount, "Limits: Custom Limit Name Amount"),
            (sensible_value.premiums_premises_operations, "Premiums: Premises Operations"),
            (sensible_value.premiums_products, "Premiums: Products"),
            (sensible_value.premiums_other, "Premiums: Other"),
            (sensible_value.premiums_total, "Premiums: Total"),
        ]
        for f in fields:
            if (field := self._extract_single_value_from_coverages(f[0], f[1])) is not None:
                result.append(field)

        return result

    def _process_gl_deductibles(self, sensible_value: Acord126Coverages) -> list[ExtendedCoverageField] | None:
        result = []
        fields = [
            (sensible_value.deductibles_property_damage, "Deductibles: Property Damage"),
            (sensible_value.deductibles_property_damage_amount, "Deductibles: Property Damage Amount"),
            (sensible_value.deductibles_bodily_injury, "Deductibles: Bodily Injury"),
            (sensible_value.deductibles_bodily_injury_amount, "Deductibles: Bodily Injury Amount"),
            (sensible_value.deductibles_custom, "Deductibles: Custom"),
            (sensible_value.deductibles_custom_name, "Deductibles: Custom Name"),
            (sensible_value.deductibles_custom_value, "Deductibles: Custom Value"),
            (sensible_value.deductibles_per_occurrence, "Deductibles: Per Occurrence"),
            (sensible_value.deductibles_per_claim, "Deductibles: Per Claim"),
        ]
        for f in fields:
            if (field := self._extract_single_value_from_coverages(f[0], f[1])) is not None:
                result.append(field)

        return result

    def _extract_main_field_and_subfield(self, name: str) -> tuple[str, str] | None:
        names = name.split(".")
        if len(names) == 2:
            return names[0].strip(), names[1].strip()
        else:
            return None

    def _get_nested_data_from_response_data(
        self, response_data: Any, main_field: str | None, field: str | None = None
    ) -> str | None:
        if response_data is None or field is None or main_field is None:
            return None
        main_attribute = getattr(response_data, main_field, None)
        if isinstance(main_attribute, list) and bool(main_attribute):
            main_attribute_list = main_attribute[0]
            return getattr(main_attribute_list, field, None)
        return None

    def _process_transaction_type(self, response_data: Any) -> SensibleStringValue | None:
        issue_policy = self._get_nested_data_from_response_data(response_data, "agency_information", "issue_policy")
        renew = self._get_nested_data_from_response_data(response_data, "agency_information", "renew")
        if issue_policy and issue_policy.value:
            return SensibleStringValue(value="issue_policy", type="string")
        elif issue_policy and renew.value:
            return SensibleStringValue(value="renew", type="string")
        else:
            return None

    def _process_transaction_status(self, response_data: Any) -> SensibleStringValue | None:
        quote = self._get_nested_data_from_response_data(response_data, "agency_information", "quote")
        checked = self._get_nested_data_from_response_data(response_data, "agency_information", "checked")
        bound = self._get_nested_data_from_response_data(response_data, "agency_information", "bound")
        cancel = self._get_nested_data_from_response_data(response_data, "agency_information", "cancel")
        if quote and quote.value:
            return SensibleStringValue(value="quote", type="string")
        elif checked and checked.value:
            return SensibleStringValue(value="checked", type="string")
        elif bound and bound.value:
            return SensibleStringValue(value="bound", type="string")
        elif cancel and cancel.value:
            return SensibleStringValue(value="cancel", type="string")
        else:
            return None

    def _process_raw_fields(
        self,
        mapping_response: MappingResponse,
        response_data: Any,
        props: list[RawProperty],
        page_metadata: dict[int, PageMetadata] = None,
    ) -> None:
        data = mapping_response.raw_processed_data
        entity_idx = data.fni_idx
        for p in props:
            section_values = response_data
            if p.path:
                section_values = getattr(section_values, p.path, None)
            if isinstance(section_values, list):
                self._process_raw_list(
                    section_values=section_values,
                    mapping_response=mapping_response,
                    field_configs=p,
                    page_metadata=page_metadata,
                )
            else:
                for f in p.fields_mapping:
                    field = self._process_raw_field(
                        response_data=section_values,
                        field=f,
                        entity_idx=entity_idx,
                        page_metadata=page_metadata,
                    )
                    if field.values:
                        data.fields.append(field)

    def _process_raw_list(
        self,
        section_values: list[Any],
        mapping_response: MappingResponse,
        field_configs: RawProperty,
        entity_type: SubmissionEntityType | None = None,
        page_metadata: dict[int, PageMetadata] = None,
    ) -> None:
        data = mapping_response.raw_processed_data
        existing_entity_unique_ids_to_idx = {e.unique_identifier: idx for idx, e in enumerate(data.entities)}
        for idx, section_value in enumerate(section_values):
            requested_name = f"{field_configs.name} {idx + 1}"
            entity_idx = _create_entity(
                data=data,
                existing_entity_unique_ids_to_idx=existing_entity_unique_ids_to_idx,
                name=requested_name,
                address=None,
                should_generate_id=False,
                entity_type=entity_type or field_configs.entity_type,
                remote_id=None,
                create_parent_business=False,
                entity_named_insured=None,
            )
            for field in field_configs.fields_mapping:
                processed_field = self._process_raw_field(
                    response_data=section_value,
                    field=field,
                    entity_idx=entity_idx,
                    page_metadata=page_metadata,
                )
                if processed_field.values:
                    data.fields.append(processed_field)

    def _process_raw_field(
        self,
        response_data: Any,
        field: MappingFieldConfiguration,
        entity_idx: int | None,
        page_metadata: dict[int, PageMetadata] = None,
    ) -> ResolvedDataField:
        result = ResolvedDataField(
            name=field.explanation or field.name,
            value_type=field.type,
            display_as_fact=False,
            values=[],
        )

        sensible_value = getattr(response_data, field.name, None)
        value = sensible_value.value if sensible_value else None
        if value is not None:
            field_value = ResolvedDataValue(
                value=self._serialize_resolved_value(value),
                entity_idx=entity_idx,
                evidences=get_pdf_evidence(getattr(sensible_value, "lines", []), page_metadata),
            )
            result.values.append(field_value)

        return result

    def _deduplicate_coverages(self, coverages: list[CoverageDetails]) -> list[CoverageDetails]:
        deduplicated_coverages = []
        coverages_used = set()

        for coverage in coverages:
            if (coverage.coverage_name, coverage.coverage_type) in coverages_used:
                continue

            coverages_used.add((coverage.coverage_name, coverage.coverage_type))
            deduplicated_coverages.append(coverage)

        return deduplicated_coverages

    def _process_static_coverages(
        self, coverages_assignment: list[CoverageAssignment], organization_id: int
    ) -> list[CoverageDetails]:
        coverage_details = []
        for ca in coverages_assignment:
            if ca.apply_for_organizations and organization_id not in ca.apply_for_organizations:
                continue

            if cd := self._get_coverages_details(ca.coverage_name, ca.coverage_type):
                coverage_details.append(cd)

        return coverage_details

    def _extract_coverages(
        self,
        lines_of_business: list[AcordLinesOfBusinessType] | None,
        additional_coverage_data: list[Acord126Coverages] | None,
    ) -> list[CoverageDetails]:
        if additional_coverage_data is not None:
            return self._process_additional_coverages_data(additional_coverage_data)
        lines_of_business = lines_of_business or []
        coverages = []
        if lines_of_business:
            for lob in lines_of_business:
                for acord_coverage_name, acord_coverage_type in lob.get_coverages():
                    if coverage_details := self._get_coverages_details(acord_coverage_name, acord_coverage_type):
                        coverages.append(coverage_details)
        return coverages

    def _get_coverages_details(
        self, coverage_name: CoverageName, coverage_type: CoverageType | None
    ) -> CoverageDetails | None:
        coverage_name_to_types = {c.name: c.coverage_types or [] for c in self.available_coverages}

        if coverage_name not in coverage_name_to_types:
            return

        copilot_coverage_types = coverage_name_to_types[coverage_name]

        if not coverage_type and copilot_coverage_types:
            if CoverageType.PRIMARY in copilot_coverage_types:
                coverage_type = CoverageType.PRIMARY
            else:
                coverage_type = CoverageType.EXCESS

        if coverage_type and coverage_type not in copilot_coverage_types:
            return

        return CoverageDetails(coverage_name=coverage_name, coverage_type=coverage_type)

    @staticmethod
    def _extract_named_insured(response_data: Any, attribute_name: str | None) -> str | None:
        if not attribute_name:
            return None

        sensible_value = getattr(response_data, attribute_name, None)

        if not sensible_value or not sensible_value.value:
            return None

        return sensible_value.value if _is_value_valid(sensible_value.value, REQUESTED_NAME_MAX_LENGTH) else None

    @staticmethod
    def _extract_first_name_insured_from_applicant_information(response_data: Any) -> FirstNameInsured | None:
        sensible_value = getattr(response_data, "applicant_information", None)

        if not sensible_value:
            return None

        sensible_value = sensible_value[0]
        fni_requested_name = (
            sensible_value.requested_name.value
            if sensible_value.requested_name
            and _is_value_valid(sensible_value.requested_name.value, REQUESTED_NAME_MAX_LENGTH)
            else None
        )
        fni_requested_address = (
            sensible_value.requested_address.value
            if sensible_value.requested_address
            and _is_value_valid(sensible_value.requested_address.value, REQUESTED_NAME_MAX_LENGTH)
            else None
        )

        return FirstNameInsured(requested_name=fni_requested_name, requested_address=fni_requested_address)

    @staticmethod
    def _extract_transient_data_from_entity(data_entry: Any, fields: list[str]) -> dict:
        entry_dict = {}
        for data_field in fields:
            sensible_value = getattr(data_entry, data_field, None)
            value = sensible_value.value if sensible_value else None
            if value is not None and value != "":  # noqa
                entry_dict[data_field] = value
        return entry_dict

    def _process_transient_data(
        self,
        response_data: Any,
        mapping_response: MappingResponse,
        configs: list[TransientDataElement],
        requested_name: str | None = None,
        requested_address: str | None = None,
        location_number: int | None = None,
        building_number: int | None = None,
    ) -> None:
        identification_dict = {
            self.REQUESTED_NAME: requested_name,
            self.REQUESTED_ADDRESS: requested_address,
            self.LOCATION_NUMBER: location_number,
            self.BUILDING_NUMBER: building_number,
        }
        extracted_data_per_config = defaultdict(list)

        for config in configs:
            result: dict[str, Any] = {}
            data = getattr(response_data, config.sensible_name, None) if config.sensible_name else response_data
            if not data:
                continue

            if isinstance(data, list):
                key_name = config.sensible_name if config.sensible_name else "list"
                for entry in data:
                    entry_result = self._extract_transient_data_from_entity(entry, config.data_fields)
                    if not entry_result:
                        continue
                    entry_dicts_list = result.get(key_name, [])
                    entry_dicts_list.append(entry_result)
                    result[key_name] = entry_dicts_list
            else:
                entry_result = self._extract_transient_data_from_entity(data, config.data_fields)
                if entry_result:
                    if config.sensible_name:
                        result[config.sensible_name] = entry_result
                    else:
                        result.update(entry_result)

            if not result:
                continue

            for key, val in identification_dict.items():
                if key not in result or not result[key]:
                    result[key] = val

            extracted_data_per_config[config.serialized_name].append(result)

        if not extracted_data_per_config:
            return

        mapping_response.transient_data = mapping_response.transient_data if mapping_response.transient_data else {}

        for key, val in extracted_data_per_config.items():
            transient_value = mapping_response.transient_data.get(key, [])
            transient_value.extend(val)
            mapping_response.transient_data[key] = transient_value
