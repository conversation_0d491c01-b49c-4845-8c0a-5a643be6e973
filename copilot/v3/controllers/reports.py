from collections.abc import Iterable, Sequence
from dataclasses import asdict
from datetime import datetime, timedelta
from http import HTT<PERSON>tatus
from typing import Any
from uuid import UUID
import json
import re

from common.logic.queries import quicker_paginate
from dateutil.parser import parse
from dateutil.relativedelta import relativedelta
from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy import (
    and_,
    asc,
    case,
    desc,
    func,
    not_,
    null,
    nullslast,
    or_,
    select,
    true,
)
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import (
    Query,
    aliased,
    contains_eager,
    joinedload,
    lazyload,
    load_only,
    noload,
    selectinload,
    subqueryload,
)
from sqlalchemy.orm.attributes import flag_modified
from static_common.enums.additional_identifier import AdditionalIdentifierType
from static_common.enums.emails import EmailType
from static_common.enums.entity import EntityInformation
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.organization import (
    ORG_TO_EMAIL_RECIPIENT_TO_ORG_GROUP,
    ExistingOrganizations,
    OrganizationGroups,
)
from static_common.enums.origin import Origin
from static_common.enums.parent import ParentType
from static_common.enums.recommendation import RecommendationActionEnum
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.enums.submission_client_id import SubmissionClientIdSource
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.enums.underwriters import SubmissionUserSource
from static_common.mappings.client_stage_fields import CUSTOM_STATUS_FIELDS_PER_CLIENT
from static_common.mappings.naics import validate_and_optionally_map_naics_to_2022
from static_common.models.submission_level_data import SourceDetails
from static_common.taxonomies.industry_classification import (
    ISOGLCode,
    NaicsCode,
    SIC5Code,
    SICCode,
)
from werkzeug.exceptions import Conflict, NotFound, abort
import connexion
import flask
import psycopg2
import pytz
import redis_lock

from copilot.clients.ers_v3 import ERSClientV3
from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.constants import (
    NATIONWIDE_SLACK_CHANNEL_ID,
    ORG_GROUP_TO_USER_GROUP_MAPPINGS,
    SHADOW_CREATION_CUT_OFF_TIME,
    USER_MONITORING_SLACK_CHANNEL_ID,
)
from copilot.kalepa_domain_events.kalepa_events_handler import KalepaEventsHandler
from copilot.logic.agencies.agent_assigner import (
    AgentInfo,
    assign_corr_contact,
    auto_assign_agent,
)
from copilot.logic.coverages.coverages import (
    assign_coverages,
    check_and_adjust_coverages,
)
from copilot.logic.dao.emails_dao import EmailsDAO
from copilot.logic.dao.processed_file_dao import ProcessedFileDAO
from copilot.logic.dao.report_dao import ReportDAO
from copilot.logic.dao.submission_client_id_dao import SubmissionClientIdDAO
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.org_sharing.org_sharing import share_report_with_org
from copilot.logic.pds.email_handler import EmailHandler
from copilot.logic.pds.file_handler import FileHandler
from copilot.logic.pds.submission_handler import SubmissionHandler
from copilot.logic.reports import (
    can_restart_report_processing,
    cancel_report_processing,
    create_shadow_submission_for_revert,
    get_create_shadow_lock_name,
    get_metrics_v2_for_report,
    handle_delete_report,
    restart_report_processing,
    retry_report_processing,
    should_expose_permissions,
    start_report_processing,
)
from copilot.logic.submission_identifiers import patch_identifiers
from copilot.logic.taxonomy import (
    update_submission_gl_code_if_needed,
    update_submission_naics_if_needed,
    update_submission_sic_if_needed,
)
from copilot.logic.utils import get_matching_excerpt
from copilot.login import has_scope
from copilot.metric_utils import (
    CUSTOM_FILTERING_MODE,
    NO_FILTERING_MODE,
    STRUCTURES_BASED_FILTERING_MODE,
    dependent_metrics_are_enabled,
)
from copilot.models import (
    Brokerage,
    BrokerageEmployee,
    ExecutionEvent,
    MetricPreference,
    MetricV2,
    Organization,
    ReportPermission,
    Settings,
    User,
    WorkersCompExperience,
    WorkersCompStateRatingInfo,
    db,
)
from copilot.models.emails import Email, ReportEmailCorrespondence
from copilot.models.external.report import (
    EXTERNAL_SUBMISSION_STAGE_MAPPINGS,
    BrokerageExternal,
    BrokerExternal,
    GetReportExternalResponse,
    UserExternal,
)
from copilot.models.files import File, ProcessedFile
from copilot.models.metrics import BulkMetricV2Request, GetMetricsV2Request
from copilot.models.reports import (
    ClientSubmissionStageConfig,
    Coverage,
    CreateReportRequest,
    ExternalCustomStatus,
    ExternalReportsResponse,
    PermissionsEnvelope,
    ReadSubmission,
    ReportBundle,
    ReportLink,
    ReportsEnvelope,
    ReportV2,
    RushSource,
    Submission,
    SubmissionBookmark,
    SubmissionBusiness,
    SubmissionClearingIssue,
    SubmissionClientId,
    SubmissionCoverage,
    SubmissionDeductible,
    SubmissionUser,
    adjust_submission_fields,
)
from copilot.models.routing_rule import RoutingRule
from copilot.models.submission_level_extracted_data import SubmissionLevelExtractedData
from copilot.models.types import (
    PermissionType,
    ReportDependencyType,
    ReportShadowType,
    Scopes,
    Sorting,
    SubmissionCoverageSource,
    SubmissionEvent,
    SubmissionStage,
)
from copilot.models.user import DEFAULT_NATIONWIDE_REPORT_OWNER, get_user_by_email
from copilot.schemas.emails import EmailSchema
from copilot.schemas.execution_events import ExecutionEventSchema
from copilot.schemas.external.report import (
    GetReportExternalResponseSchema,
    GetReportExternalResponseSchemaV2,
)
from copilot.schemas.files import PdsDebuggerFileDataSchema
from copilot.schemas.metrics import (
    BulkMetricV2RequestSchema,
    GetMetricsV2RequestSchema,
    MetricV2Schema,
)
from copilot.schemas.report import (
    DEFAULT_ONLY_USER_REPORTS,
    AffectedReportsEnvelopeSchema,
    CreateReportRequestSchema,
    ExternalCustomStatusSchema,
    ExternalReportsResponseSchema,
    PermissionsEnvelopeSchema,
    ReportExtractResponse,
    ReportPatchRequest,
    ReportPatchSchema,
    ReportRedirectSchema,
    ReportSummaryPreferenceSchema,
    ReportV2Schema,
)
from copilot.v3.utils.db_session import no_expire_on_commit
from copilot.v3.utils.enrichment import (
    enrich_businesses_with_ers_data,
    enrich_businesses_with_ers_snapshot_data,
)
from copilot.v3.utils.reports import (
    add_processing_dependency,
    create_report_from_requested_properties,
    map_coverage_type_to_name,
    map_report_status_to_submission_stage,
    normalize_email_subject,
    report_exists_or_404,
    update_submission_priority_if_needed,
)
from copilot.v3.utils.routing_tags import (
    RoutingResult,
    get_routing_result_from_correspondence,
    get_routing_result_from_email,
    get_routing_result_from_report,
    get_routing_result_from_user,
)
from copilot.v3.utils.submission import (
    create_external_resource_snapshots_if_needed,
    handle_updating_naics_code,
)
from copilot.v3.utils.support_users import check_if_cs_manager_or_tier_2

logger = get_logger()

report_schema = ReportV2Schema()
report_patch_schema = ReportPatchSchema()
report_summary_preference_schema = ReportSummaryPreferenceSchema()
external_report_schema = ExternalReportsResponseSchema()
get_report_external_response_schema = GetReportExternalResponseSchema()
get_report_external_response_schema_v2 = GetReportExternalResponseSchemaV2()
metric_v2_schema = MetricV2Schema()
bulk_metric_v2_request_schema = BulkMetricV2RequestSchema()
execution_event_schema = ExecutionEventSchema()
get_metrics_v2_request = GetMetricsV2RequestSchema()
external_custom_status_schema = ExternalCustomStatusSchema()

DEFAULT_PAGINATION_FIELDS = ["page", "total_pages", "total_reports", "has_next"]
FAKE_REPORT_ID = UUID("********-1111-1111-1111-********1111")
FAKE_REPORT_ID_2 = UUID("********-1111-1111-1111-********1112")
FAKE_REPORT_ID_3 = UUID("********-1111-1111-1111-********1113")
FAKE_REPORT_IDS = [FAKE_REPORT_ID, FAKE_REPORT_ID_2, FAKE_REPORT_ID_3]
FAKE_REPORT_NAME_PREFIX = "test high risk score"
# technically its 24 hours, but we should be ok moving it half an hour# earlier
NW_SLA_EXPIRE_DELTA = timedelta(hours=23, minutes=30)


def get_report_or_404(report_id, with_permissions: bool = False) -> ReportV2:
    query = ReportV2.query.options(
        joinedload(ReportV2.submission).lazyload(Submission.assigned_underwriters),
        lazyload(ReportV2.links).selectinload(ReportLink.report_2),
    )
    if with_permissions:
        query = ReportV2.query.options(selectinload(ReportV2.report_permissions))
    report = query.get_or_404(report_id, description="The Report with specified ID wasn't found")
    if report.is_deleted:
        abort(404, "The Report with specified ID has been deleted")
    return report


def get_light_report(report_id):
    report = ReportV2.query.options(lazyload(ReportV2.report_bundle)).get_or_404(
        report_id, description="The Report with specified ID wasn't found"
    )
    if report.is_deleted:
        abort(404, "The Report with specified ID has been deleted")
    return report


def _create_file(file_object, file_type: FileType, is_internal: bool, origin: Origin | None) -> File:
    s3_key = flask.current_app.submission_s3_client.upload_as_xlsx(current_user.id, file_object)

    organization_id = None
    if user_id := current_user.id:
        if user := User.query.filter_by(id=user_id).first():
            organization_id = user.organization_id

    file = File(
        name=file_object.filename,
        s3_key=s3_key,
        file_type=file_type,
        user_id=current_user.id,
        processing_state=FileProcessingState.NOT_CLASSIFIED,
        origin=origin,
        organization_id=organization_id,
        is_internal=is_internal,
    )
    db.session.add(file)
    db.session.commit()

    return file


def get_reports_query(base_query: Query | None = None, with_wc_info: bool = False) -> Query:
    submission_columns = {str(c) for c in Submission.__table__.columns}
    # Fields that are dumped by schema and are columns in the table.
    explicit_fields = [f.replace("submissions.", "") for f in DEFAULT_ONLY_USER_REPORTS if f in submission_columns]
    # Needed for some properties.
    implicit_fields = [
        "owner_id",
        "is_metrics_set_manually",
        "fni_state",
    ]
    submission_fields = explicit_fields + implicit_fields
    if not base_query:
        base_query = ReportV2.query.distinct()
    wc_loads = []
    if with_wc_info:
        wc_loads.append(selectinload(Submission.workers_comp_experience))
        wc_loads.append(selectinload(Submission.workers_comp_rating_info))
    # selectinload relationships that can have multiple records, use joinedload for those that are not that frequent
    return (
        (
            base_query.options(
                selectinload(ReportV2.submission)
                .load_only(*adjust_submission_fields(submission_fields))
                .options(
                    lazyload(Submission.businesses),
                    lazyload(Submission.user),
                    lazyload(Submission.files),
                    lazyload(Submission.deductibles),
                    lazyload(Submission.clearing_issues),
                    joinedload(Submission.assigned_underwriters)
                    .joinedload(SubmissionUser.user)
                    .options(lazyload(User.settings), lazyload(User.organization), joinedload(User.groups)),
                    joinedload(Submission.coverages).joinedload(SubmissionCoverage.coverage),
                    joinedload(Submission.broker),
                    joinedload(Submission.brokerage),
                    joinedload(Submission.client_stage),
                    selectinload(Submission.bookmarks),
                    selectinload(Submission.identifiers),
                    selectinload(Submission.read_by_users).load_only(ReadSubmission.user_id),
                    *wc_loads,
                )
            )
        )
        .filter(ReportV2.is_deleted.isnot(True))
        .filter(ReportV2.is_archived.isnot(True))
    )


def ordered(
    query: Query, sortings: Sequence[str], descendings: Sequence[bool], user_id: int | None = None
) -> tuple[Query, list]:
    sortings_used = []
    for idx in range(len(sortings)):
        sorting = Sorting(sortings[idx])
        descending = descendings[idx]

        sort_fn = desc
        if not descending:
            sort_fn = asc

        if sorting == Sorting.EFFECTIVE_AT:
            query = query.order_by(nullslast(sort_fn(func.DATE(Submission.proposed_effective_date))))
            sortings_used.append(func.DATE(Submission.proposed_effective_date))
        elif sorting == Sorting.DECLINED_AT:
            query = query.order_by(nullslast(sort_fn(Submission.declined_date)))
            sortings_used.append(Submission.declined_date)
        elif sorting == Sorting.REPORT_NAME:
            query = query.order_by(nullslast(sort_fn(ReportV2.name)))
            sortings_used.append(ReportV2.name)
        elif sorting == Sorting.BROKER_NAME:
            query = query.outerjoin(Submission.broker).order_by(nullslast(sort_fn(BrokerageEmployee.name)))
            sortings_used.append(BrokerageEmployee.name)
        elif sorting == Sorting.BROKERAGE_NAME:
            query = query.outerjoin(Submission.brokerage).order_by(nullslast(sort_fn(Brokerage.name)))
            sortings_used.append(Brokerage.name)
        elif sorting == Sorting.DUE_DATE:
            query = query.order_by(nullslast(sort_fn(Submission.due_date)))
            sortings_used.append(Submission.due_date)
        elif sorting == Sorting.STAGE:
            sort_case = case(
                (Submission.stage == SubmissionStage.ON_MY_PLATE, 1),
                (Submission.stage == SubmissionStage.INDICATED, 2),
                (Submission.stage == SubmissionStage.WAITING_FOR_OTHERS, 3),
                (Submission.stage == SubmissionStage.QUOTED, 4),
                (Submission.stage == SubmissionStage.COMPLETED, 5),
                (Submission.stage == SubmissionStage.DECLINED, 6),
                (Submission.stage == SubmissionStage.QUOTED_LOST, 7),
                (Submission.stage == SubmissionStage.QUOTED_BOUND, 8),
                (Submission.stage == SubmissionStage.CLEARING_ISSUE, 9),
                (Submission.stage == SubmissionStage.BLOCKED, 10),
                (Submission.stage == SubmissionStage.EXPIRED, 11),
                else_=12,
            )

            query = query.order_by(nullslast(sort_fn(sort_case)))
            sortings_used.append(sort_case)
        elif sorting == Sorting.ADJUSTED_TIV:
            query = query.order_by(nullslast(sort_fn(Submission.adjusted_tiv)))
            sortings_used.append(Submission.adjusted_tiv)
        elif sorting == Sorting.TIV:
            query = query.order_by(nullslast(sort_fn(Submission.tiv)))
            sortings_used.append(Submission.tiv)
        elif sorting == Sorting.RECOMMENDATION_SCORE:
            # This sorting includes an old RECOMMENDATION_V2 Score as a backup in case score is not calculated
            priority_value_sort = desc if sort_fn == asc else asc
            sort_case = case(
                (Submission.recommendation_v2_action == RecommendationActionEnum.PREFERRED, 10),
                (Submission.recommendation_v2_action == RecommendationActionEnum.REFER, 20),
                (Submission.recommendation_v2_action == RecommendationActionEnum.NOTICE, 25),
                (Submission.recommendation_v2_action == RecommendationActionEnum.NO_ACTION, 30),
                (Submission.recommendation_v2_action.is_(null()), 30),
                (Submission.recommendation_v2_action == RecommendationActionEnum.FLAG_FOR_REVIEW, 40),
                (Submission.recommendation_v2_action == RecommendationActionEnum.RED_FLAG, 50),
                (Submission.recommendation_v2_action == RecommendationActionEnum.DECLINE, 60),
            )
            query = query.order_by(
                nullslast(priority_value_sort(Submission.recommendation_v2_score)),
                sort_fn(sort_case),
                nullslast(priority_value_sort(Submission.recommendation_v2_priority)),
            )
            sortings_used.append(Submission.recommendation_v2_score)
            sortings_used.append(sort_case)
            sortings_used.append(Submission.recommendation_v2_priority)
        elif sorting == Sorting.RECOMMENDATION_V2:
            priority_value_sort = desc if sort_fn == asc else asc
            sort_case = case(
                (Submission.recommendation_v2_action == RecommendationActionEnum.PREFERRED, 10),
                (Submission.recommendation_v2_action == RecommendationActionEnum.REFER, 20),
                (Submission.recommendation_v2_action == RecommendationActionEnum.NOTICE, 25),
                (Submission.recommendation_v2_action == RecommendationActionEnum.NO_ACTION, 30),
                (Submission.recommendation_v2_action.is_(null()), 30),
                (Submission.recommendation_v2_action == RecommendationActionEnum.FLAG_FOR_REVIEW, 40),
                (Submission.recommendation_v2_action == RecommendationActionEnum.RED_FLAG, 50),
                (Submission.recommendation_v2_action == RecommendationActionEnum.DECLINE, 60),
            )
            query = query.order_by(
                sort_fn(sort_case), nullslast(priority_value_sort(Submission.recommendation_v2_priority))
            )
            sortings_used.append(sort_case)
            sortings_used.append(Submission.recommendation_v2_priority)
        elif sorting == Sorting.BOOKMARKED:
            sort_case = db.case((SubmissionBookmark.user_id == user_id, 1), else_=0)
            query = query.outerjoin(Submission.bookmarks).order_by(nullslast(sort_fn(sort_case)))
            sortings_used.append(sort_case)
        elif sorting == Sorting.READ:
            sort_case = db.case((ReadSubmission.user_id == user_id, 1), else_=0)
            query = query.outerjoin(Submission.read_by_users).order_by(nullslast(sort_fn(sort_case)))
            sortings_used.append(sort_case)
        elif sorting == Sorting.NAICS:
            query = query.order_by(nullslast(sort_fn(Submission.primary_naics_code)))
            sortings_used.append(Submission.primary_naics_code)
        elif sorting == Sorting.EXPIRED_PREMIUM:
            query = query.order_by(nullslast(sort_fn(Submission.expired_premium)))
            sortings_used.append(Submission.expired_premium)
        elif sorting == Sorting.TARGET_PREMIUM:
            query = query.order_by(nullslast(sort_fn(Submission.target_premium)))
            sortings_used.append(Submission.target_premium)
        elif sorting == Sorting.SALES:
            query = query.order_by(nullslast(sort_fn(Submission.sales)))
            sortings_used.append(Submission.sales)
        elif sorting == Sorting.AMOUNT_QUOTED:
            subq = select(func.sum(SubmissionCoverage.quoted_premium).label("quoted_sum"))
            subq = subq.filter(SubmissionCoverage.submission_id == Submission.id).correlate(Submission)
            query = query.order_by(nullslast(sort_fn(subq)))
            sortings_used.append(subq)
        elif sorting == Sorting.AMOUNT_BOUND:
            subq = select(func.sum(SubmissionCoverage.bound_premium).label("bound_sum"))
            subq = subq.filter(SubmissionCoverage.submission_id == Submission.id).correlate(Submission)
            query = query.order_by(nullslast(sort_fn(subq)))
            sortings_used.append(subq)
        elif sorting == Sorting.ESTIMATED_PREMIUM:
            subq = select(func.sum(SubmissionCoverage.estimated_premium).label("estimated_premium_sum"))
            subq = subq.filter(SubmissionCoverage.submission_id == Submission.id).correlate(Submission)
            query = query.order_by(nullslast(sort_fn(subq)))
            sortings_used.append(subq)
        elif sorting == Sorting.TOTAL_PREMIUM_OR_BOUND_PREMIUM:
            subq = select(func.sum(SubmissionCoverage.total_premium_or_bound_premium).label("tp_or_bp_premium_sum"))
            subq = subq.filter(SubmissionCoverage.submission_id == Submission.id).correlate(Submission)
            query = query.order_by(nullslast(sort_fn(subq)))
            sortings_used.append(subq)
        elif sorting == Sorting.BOUND_PREMIUM:
            subq = select(func.sum(SubmissionCoverage.bound_premium).label("bp_premium_sum"))
            subq = subq.filter(SubmissionCoverage.submission_id == Submission.id).correlate(Submission)
            query = query.order_by(nullslast(sort_fn(subq)))
            sortings_used.append(subq)
        elif sorting == Sorting.QUOTED_PREMIUM:
            subq = select(func.sum(SubmissionCoverage.quoted_premium).label("bp_quoted_premium_sum"))
            subq = subq.filter(SubmissionCoverage.submission_id == Submission.id).correlate(Submission)
            query = query.order_by(nullslast(sort_fn(subq)))
            sortings_used.append(subq)
        elif sorting == Sorting.PAYROLL:
            subq = select(func.sum(WorkersCompStateRatingInfo.payroll).label("payroll_sum"))
            subq = subq.filter(WorkersCompStateRatingInfo.submission_id == Submission.id).correlate(Submission)
            query = query.order_by(nullslast(sort_fn(subq)))
            sortings_used.append(subq)
        elif sorting == Sorting.X_MOD_SCORES:
            subq = select(func.sum(WorkersCompExperience.experience_modification).label("experience_modification_sum"))
            subq = subq.filter(WorkersCompExperience.submission_id == Submission.id).correlate(Submission)
            query = query.order_by(nullslast(sort_fn(subq)))
            sortings_used.append(subq)
        elif sorting == Sorting.FNI_STATE:
            query = query.order_by(nullslast(sort_fn(Submission.fni_state)))
            sortings_used.append(Submission.fni_state)
        elif sorting == Sorting.LAST_EMAIL:
            latest_email_subquery = (
                db.session.query(
                    Email.correspondence_id.label("correspondence_id"),
                    func.max(Email.created_at).label("latest_email_at"),
                )
                .group_by(Email.correspondence_id)
                .subquery()
            )
            query = query.outerjoin(
                latest_email_subquery, ReportV2.correspondence_id == latest_email_subquery.c.correspondence_id
            ).order_by(nullslast(sort_fn(latest_email_subquery.c.latest_email_at)))
            sortings_used.append(latest_email_subquery.c.latest_email_at)
        elif sorting == Sorting.ASSIGNEE:
            aliased_submission_user = aliased(SubmissionUser)
            aliased_user = aliased(User)
            query = (
                query.outerjoin(aliased_submission_user, aliased_submission_user.submission_id == Submission.id)
                .outerjoin(aliased_user, aliased_user.id == aliased_submission_user.user_id)
                .order_by(nullslast(sort_fn(aliased_user.name)))
            )
            sortings_used.append(aliased_user.name)
        else:
            query = query.order_by(nullslast(sort_fn(ReportV2.created_at)))
            sortings_used.append(ReportV2.created_at)
    return query, sortings_used


def renewal_candidates(query: Query) -> Query:
    return (
        query.join(ReportV2.submission)
        .filter(
            Submission.stage.in_(
                [
                    SubmissionStage.QUOTED,
                    SubmissionStage.QUOTED_BOUND,
                ]
            )
        )
        .filter(Submission.renewal_creation_date < datetime.utcnow())
    )


def upcoming_renewals(query: Query, n_days: int) -> Query:
    date_from = datetime.utcnow() + timedelta(days=n_days - 1)
    date_to = datetime.utcnow() + timedelta(days=n_days)
    return (
        query.join(ReportV2.submission)
        .filter(
            Submission.stage.in_(
                [
                    SubmissionStage.QUOTED,
                    SubmissionStage.QUOTED_BOUND,
                ]
            )
        )
        .filter(Submission.renewal_creation_date >= date_from)
        .filter(Submission.renewal_creation_date < date_to)
    )


def get_reports(
    page: int = None,
    per_page: int = None,
    order_by: str = None,
    descending: bool = True,
    organization_id: int = None,
    submission_ids: list[str] | None = None,
    search: str = None,
    stage: str = None,
    date_from: str = None,
    date_to: str = None,
    assignee: int = None,
    include_client_ids: bool = False,
    with_client_ids_only: bool = False,
    effective_date_from: str | None = None,
):
    report_fields = [
        "id",
        "name",
        "created_at",
    ]
    submission_fields = [
        "id",
        "stage",
    ]
    query = ReportV2.query.join(ReportV2.submission)

    submission_options = [
        selectinload(Submission.assigned_underwriters)
        .joinedload(SubmissionUser.user)
        .options(lazyload(User.settings), lazyload(User.organization), selectinload(User.groups)),
        selectinload(Submission.coverages).joinedload(SubmissionCoverage.coverage),
        joinedload(Submission.broker),
        joinedload(Submission.brokerage),
        joinedload(Submission.client_stage),
    ]

    if include_client_ids or with_client_ids_only:
        submission_options.append(joinedload(Submission.client_submission_ids))
    else:
        submission_options.append(noload(Submission.client_submission_ids))

    query = (
        (
            query.options(
                load_only(*report_fields)
                .joinedload(ReportV2.submission)
                .load_only(*adjust_submission_fields(submission_fields))
                .options(
                    *submission_options,
                )
            )
        )
        .filter(ReportV2.is_deleted.isnot(True))
        .filter(ReportV2.is_archived.isnot(True))
    )

    if organization_id:
        query = query.join(User).filter(ReportV2.owner_id == User.id).filter(User.organization_id == organization_id)
    if submission_ids:
        submission_ids = [UUID(submission_id) for submission_id in submission_ids]
        query = query.filter(Submission.id.in_(submission_ids))

    if include_client_ids or with_client_ids_only:
        query = query.join(
            SubmissionClientId,
            SubmissionClientId.submission_id == Submission.id,
            isouter=not with_client_ids_only,
        )

    if search:
        alpha_numeric = "[^a-zA-Z0-9]"
        name_pattern = f'%{re.sub(alpha_numeric, "", search)}%'
        query = query.filter(func.regexp_replace(ReportV2.name, alpha_numeric, "", "g").ilike(name_pattern))

    if stage:
        query = query.filter(Submission.stage == stage)

    if date_from:
        date_from = parse(date_from)
        query = query.filter(ReportV2.created_at >= date_from)
    if date_to:
        date_to = parse(date_to) + timedelta(days=1)
        query = query.filter(ReportV2.created_at < date_to)

    if effective_date_from:
        effective_date_from = parse(effective_date_from)
        query = query.filter(Submission.proposed_effective_date >= effective_date_from)

    if assignee:
        query = query.join(SubmissionUser, SubmissionUser.submission_id == Submission.id).filter(
            SubmissionUser.user_id == assignee
        )

    sort_fn = desc
    if not descending:
        sort_fn = asc
    if order_by:
        if order_by == "created_at":
            query = query.order_by(sort_fn(ReportV2.created_at))
        if order_by == "status":
            query = query.order_by(sort_fn(Submission.stage))
    else:
        query = query.order_by(sort_fn(ReportV2.name))

    if page and per_page:
        reports_page = quicker_paginate(query, page, per_page, max_per_page=100)
        reports_envelope = ReportsEnvelope(
            reports=reports_page.items,
            page=page,
            total_pages=reports_page.pages,
            total_reports=reports_page.total,
            has_next=reports_page.has_next,
        )
    else:
        reports = query.all()
        reports_envelope = ReportsEnvelope(reports=reports)

    return AffectedReportsEnvelopeSchema().dump(reports_envelope)


def get_reports_post(
    page: int = None,
    per_page: int = None,
    order_by: str = None,
    descending: bool = True,
    organization_id: int = None,
    search: str = None,
    stage: str = None,
    date_from: str = None,
    date_to: str = None,
    assignee: int = None,
    include_client_ids: bool = False,
    body: dict = None,
):
    return get_reports(
        page=page,
        per_page=per_page,
        order_by=order_by,
        descending=descending,
        organization_id=organization_id,
        search=search,
        stage=stage,
        date_from=date_from,
        date_to=date_to,
        assignee=assignee,
        include_client_ids=include_client_ids,
        submission_ids=body.get("submission_ids") if body else None,
    )


def create_report_external(body: str):
    # Sometimes it happens that NW sends us a json with \u0009 characters, which are not valid in json
    if isinstance(body, str):
        body = body.replace("\u0009", " ")
    if isinstance(body, bytes):
        body = body.decode("utf-8").replace("\u0009", " ")
    return create_report(body, True)


def _get_existing_report(create_report_request: CreateReportRequest, report_owner: User) -> ReportV2 | None:
    if create_report_request.email_message_id and create_report_request.origin == Origin.EMAIL:
        return ReportV2.query.filter(
            ReportV2.email_message_id == create_report_request.email_message_id, ReportV2.owner_id == report_owner.id
        ).first()
    return None


def create_report(body: str, external: bool = False, from_sync: bool = False, adjust_coverages: bool = True):
    file_object = connexion.request.files.get("file")
    file = None
    create_report_request = _create_report_request(body)
    if (
        not create_report_request.original_report_id
        and not current_user.is_internal_machine_user
        and not has_scope(Scopes.CREATE_REPORTS)
        and not (create_report_request.origin == Origin.COPILOT and create_report_request.pds)
    ):
        flask.abort(403)

    if external and _should_return_fake_report_response(create_report_request):
        return _create_fake_report_response(create_report_request), 201

    if report_owner := _get_report_owner(create_report_request):
        organization_id = report_owner.organization_id

        if not external and (existing_report := _get_existing_report(create_report_request, report_owner)):
            return report_schema.dump(existing_report), 201

        if external and (
            existing_report := ReportDAO.get_existing_report_for_client_submission_id(
                create_report_request.external_id, organization_id
            )
        ):
            return external_report_schema.dump(existing_report), 201

        if organization_id in ORG_TO_EMAIL_RECIPIENT_TO_ORG_GROUP and create_report_request.correspondence_id:
            recipients_and_senders = EmailsDAO.get_recipients_and_senders_for_correspondence(
                create_report_request.correspondence_id
            )
            org_groups = OrganizationGroups.get_from_email_recipients(organization_id, recipients_and_senders)
            if org_groups:
                create_report_request.org_group = org_groups[0].value
            else:
                logger.warning(
                    "Missing org group during report creation",
                    recipients_and_senders=recipients_and_senders,
                    organization_id=organization_id,
                )
        if OrganizationGroups.is_organization_group_based(organization_id) and create_report_request.correspondence_id:
            corr_id = create_report_request.correspondence_id
            correspondence: ReportEmailCorrespondence = ReportEmailCorrespondence.query.get(corr_id)
            if correspondence and correspondence.email_account:
                org_group = OrganizationGroups.get_from_forward_account(correspondence.email_account)
                create_report_request.org_group = org_group.value if org_group else None

    is_internal = bool(report_owner and report_owner.is_support)
    file_type = FileType.SOV

    if file_object:
        file = _create_file(file_object, file_type, is_internal, create_report_request.origin)
        create_report_request.s3_key = file.s3_key
        create_report_request.s3_bucket = flask.current_app.submission_s3_client.default_bucket
        create_report_request.name = create_report_request.name or file.name

    report = (
        _create_report_as_copy(create_report_request, report_owner)
        if create_report_request.original_report_id
        else _create_new_report(create_report_request, report_owner, file)
    )

    if not from_sync and adjust_coverages:
        check_and_adjust_coverages(report.submission)

    if from_sync:
        return report.id

    if not external:
        return ReportV2Schema().dump(report), 201
    else:
        return external_report_schema.dump(report), 201


def _create_report_as_copy(create_report_request: CreateReportRequest, report_owner: User | None):
    original_report = ReportV2.query.get_or_404(create_report_request.original_report_id)
    report = original_report.copy()
    logger.info(
        "Report duplicated",
        original_report_id=str(create_report_request.original_report_id),
        copy_report_id=str(report.id),
    )

    report.submission.origin = create_report_request.origin
    client_ids_source = (
        SubmissionClientIdSource.SYNC if create_report_request.origin == Origin.SYNC else SubmissionClientIdSource.COPY
    )
    for f in report.submission.files or []:
        f.origin = create_report_request.origin
    if create_report_request.external_id and not report.submission.client_submission_ids:
        report.submission.client_submission_ids.append(
            SubmissionClientId(
                client_submission_id=create_report_request.external_id,
                submission_id=report.submission.id,
                source=client_ids_source,
            )
        )
    if original_submission := original_report.submission:
        if original_submission.is_auto_processed and original_submission.is_verified:
            report.submission.is_auto_processed = True
            report.submission.is_waiting_for_auto_verify = True

    if report_owner:
        report.owner_id = report_owner.id

    db.session.add(report)
    db.session.commit()
    _activate_report(report, create_report_request, original_report)
    return report


def _maybe_set_rush_report(report: ReportV2, email: Email | None = None) -> None:
    try:
        if (
            report.is_rush
            or not report.correspondence_id
            or report.organization_id != ExistingOrganizations.Paragon.value
        ):
            return

        if not email:
            email = Email.query.filter(
                Email.correspondence_id == report.correspondence_id, Email.type == EmailType.ROOT
            ).first()

        if (
            email.email_from
            and email.email_to
            and "<EMAIL>" in email.email_from
            and "@kalepa.com" in email.email_to
        ):
            report.is_rush = True
            report.rush_source = RushSource.USER
    except Exception as e:
        logger.error("Error while setting rush report", error=str(e))


def _create_new_report(create_report_request: CreateReportRequest, report_owner: User | None, file: File | None):
    if not report_owner:
        flask.abort(400, "Report can't be created by anonymous user without specifying user_email")

    routing_result = None
    report = create_report_from_requested_properties(
        create_report_request.businesses or [], report_owner, create_report_request, file
    )
    submission_level_extracted_data = []

    def add_submission_level_data(field: EntityInformation, value: Any):
        submission_level_extracted_data.append(
            SubmissionLevelExtractedData(
                submission_id=report.submission.id,
                field=field,
                value=json.dumps(value, default=str),
                source_details=SourceDetails.API,
                is_selected=True,
            )
        )

    if create_report_request.is_renewal is not None:
        add_submission_level_data(EntityInformation.IS_RENEWAL, create_report_request.is_renewal)
    if create_report_request.proposed_effective_date:
        add_submission_level_data(
            EntityInformation.POLICY_EFFECTIVE_START_DATE, create_report_request.proposed_effective_date
        )
    if create_report_request.policy_expiration_date:
        add_submission_level_data(EntityInformation.POLICY_END_DATE, create_report_request.policy_expiration_date)

    if create_report_request.broker:
        add_submission_level_data(EntityInformation.BROKER_NAME, create_report_request.broker)

    if create_report_request.broker_email:
        add_submission_level_data(EntityInformation.BROKER_EMAIL, create_report_request.broker_email)

    if create_report_request.brokerage:
        add_submission_level_data(EntityInformation.BROKERAGE_NAME, create_report_request.brokerage)

    if create_report_request.coverage_type:
        add_submission_level_data(EntityInformation.COVERAGES, [["liability", create_report_request.coverage_type]])

    if report.correspondence_id:
        routing_result = get_routing_result_from_correspondence(report.correspondence_id, report_owner.organization)
        report.routing_tags = routing_result.tags
        report.tier = routing_result.tier
        report.is_rush = routing_result.rush
        report.rush_source = RushSource.ROUTING_RULE

    _maybe_set_rush_report(report)

    db.session.add(report)
    db.session.flush()
    for sled in submission_level_extracted_data:
        db.session.add(sled)
    db.session.commit()

    submission_level_extracted_data = []

    agent_info = AgentInfo(
        agent_name=create_report_request.broker,
        agent_email=create_report_request.broker_email,
        agency_name=create_report_request.brokerage,
    )
    corr_contact = AgentInfo(
        agent_email=create_report_request.correspondence_contact_email,
        agent_name=create_report_request.correspondence_contact_name,
        agency_name=create_report_request.brokerage,
    )
    assigned_corr_contact, assigned_broker = auto_assign_agent(report.submission, agent_info)
    db.session.commit()
    assigned_corr_contact = assign_corr_contact(report.submission, corr_contact) or assigned_corr_contact
    db.session.commit()

    if assigned_corr_contact:
        add_submission_level_data(EntityInformation.CORRESPONDENCE_CONTACT_ID, assigned_corr_contact.id)

    if assigned_broker:
        add_submission_level_data(EntityInformation.BROKER_ID, assigned_broker.id)
        add_submission_level_data(EntityInformation.BROKERAGE_ID, assigned_broker.brokerage_id)

    for sled in submission_level_extracted_data:
        db.session.add(sled)
    db.session.commit()

    if report.submission.is_verified_shell:
        from copilot.logic.org_sharing.org_sharing import share_report_with_org

        share_report_with_org(report)

    append_sov_file_if_needed(file, report)
    force = (
        create_report_request.origin == Origin.COPILOT
        and create_report_request.pds
        and not create_report_request.businesses
    )
    _enable_auto_processing_if_needed(report, force=force)

    if routing_result:
        _assign_underwriters(report, routing_result.user_ids)

    if create_report_request.user_email and report_owner and not current_user.cross_organization_access:
        user = _assign_underwriter_and_update_tags(report, create_report_request.user_email, SubmissionUserSource.API)
        add_submission_level_data(EntityInformation.UNDERWRITER_EMAILS, [create_report_request.user_email])
        if user:
            add_submission_level_data(EntityInformation.UNDERWRITER_USER_IDS, [user.id])

    if Organization.is_nationwide_for_id(report_owner.organization_id):
        _handle_submission_coverage(report.submission)

        if create_report_request.external_id:
            try:
                _bundle_reports_with_related_client_ids(report, create_report_request.external_id)
            except:
                logger.exception(
                    "Error while bundling reports", report_id=report.id, external_id=create_report_request.external_id
                )

    _activate_report(report, create_report_request)
    return report


def _create_fake_report_response(create_report_request: CreateReportRequest) -> dict:
    from events_common.lambdas.constants import (
        IS_SUBMISSION_VERIFIED_KEY,
        NATIONWIDE_TEST_DATA_KEY,
        SUBMISSION_ORIGIN_KEY,
    )

    from copilot.kalepa_domain_events.kalepa_events_handler import KalepaEventsHandler

    test_data = {
        "submission_number": create_report_request.external_id,
        "quote_number": create_report_request.quote_number,
    }

    response = ExternalReportsResponse(name="Fake report", id=FAKE_REPORT_ID)
    if create_report_request.external_id == "********A" or (
        create_report_request.name and create_report_request.name.lower().startswith(FAKE_REPORT_NAME_PREFIX)
    ):
        response = ExternalReportsResponse(name="Fake report", id=FAKE_REPORT_ID_2)
    if create_report_request.name and create_report_request.name.lower().startswith("test"):
        response = ExternalReportsResponse(name="Fake report", id=FAKE_REPORT_ID_3)
    raw_data = {
        "submission_id": str(response.id),
        "report_id": str(response.id),
        "organization_id": ExistingOrganizations.Nationwide.value,
        "additional_data": {
            IS_SUBMISSION_VERIFIED_KEY: True,
            SUBMISSION_ORIGIN_KEY: Origin.API.value,
            NATIONWIDE_TEST_DATA_KEY: json.dumps(test_data),
        },
    }
    KalepaEventsHandler.send_nw_boss_test_call_received_event(raw_data)
    return external_report_schema.dump(response)


def _activate_report(
    report: ReportV2, create_report_request: CreateReportRequest, original_report: ReportV2 | None = None
):
    if not report.submission.businesses:
        current_app.event_service.handle_submission_event(
            SubmissionEvent.SHELL_SUBMISSION_CREATED, report.submission, business=None
        )
    else:
        current_app.event_service.handle_submission_event(
            SubmissionEvent.SUBMISSION_CREATED, report.submission, business=None
        )
    for file in report.submission.files:
        FileHandler().handle_new_file_added(report.submission, file)

    if report.is_copy:
        target_stage = create_report_request.target_stage or report.submission.stage
        declined_date = (
            create_report_request.additional_data.get("declined_date", None)
            if create_report_request.additional_data
            else None
        )
        stage_details = (
            create_report_request.additional_data.get("stage_details", None)
            if create_report_request.additional_data
            else None
        )
        additional_data = {"stage_details": stage_details} if stage_details else None
        current_app.event_service.handle_submission_event(
            SubmissionEvent.SUBMISSION_COPIED,
            report.submission,
            business=None,
            original_report=original_report,
            target_stage=target_stage,
            declined_date=declined_date,
            additional_data=additional_data,
        )


def _should_return_fake_report_response(create_report_request: CreateReportRequest) -> bool:
    is_nw = current_user.is_nationwide
    if not is_nw:
        return False

    dummy_name = create_report_request.name and (
        create_report_request.name.lower().startswith(FAKE_REPORT_NAME_PREFIX)
        or (not current_app.is_prod and create_report_request.name.lower().startswith("test"))
    )
    if dummy_name:
        return True

    external_id = create_report_request.external_id
    if external_id:
        dummy_external_id = external_id.startswith("1") or "000000000C" in external_id or "000000000A" in external_id
        if dummy_external_id:
            return True

    dummy_brokerage = create_report_request.brokerage and create_report_request.brokerage == "99999"
    has_nw_in_requested_name = any(
        "nationwide" in business["requested_name"].lower()
        for business in create_report_request.businesses
        if "requested_name" in business
    )
    if dummy_brokerage and not has_nw_in_requested_name:
        return True

    return False


def _get_report_owner(create_report_request: CreateReportRequest) -> User | None:
    report_owner: User | None = None
    if current_user.cross_organization_access:
        owner_id = Organization.find_organization_owner_id(current_user.organization_id)
        report_owner = User.query.get(owner_id)
    elif current_user.is_nationwide and current_user.is_machine_user:
        report_owner = get_user_by_email(DEFAULT_NATIONWIDE_REPORT_OWNER)
    elif create_report_request.user_email:
        report_owner = get_user_by_email(create_report_request.user_email)
        if not report_owner:
            abort(404, "User with specified email does not exist")
    return report_owner


def _handle_submission_coverage(submission: Submission):
    if submission.coverage_type:
        coverage_name = map_coverage_type_to_name(submission.coverage_type)
        from copilot.logic.coverages.coverages import assign_coverages

        assign_coverages(
            submission.id,
            coverage_name,
            submission.coverage_type,
            raise_exceptions=True,
            source=SubmissionCoverageSource.MANUAL,
            source_details="Created from Report creation request",
        )


def _bundle_reports_with_related_client_ids(report: ReportV2, external_id: str):
    """
    Given the newly created report, we are looking for the related existing report to bundle them.
    The related reports are ones that:
     - external_ids are corresponding based on defined logic
     - names matches
     - effective dates matches
    """
    submission = report.submission if report.submission else None

    if not submission or not submission.name or not submission.proposed_effective_date:
        return

    # get all submissions within the same organization
    # that have the same name, effective date and client_clearing_status
    # then of the ones found, filter the ones that have same type external_id
    # then of the ones found, filter the ones with different sub_producer_name, sub_producer_email, brokerage_office
    # in the end if we are left with more than one
    # - log an error
    # - pick the one with the closest external id

    query = Submission.query.join(Submission.report).filter(
        Submission.id != submission.id,
        Submission.report_id != report.id,
        Submission.name == submission.name,
        Submission.proposed_effective_date == submission.proposed_effective_date,
        Submission.client_clearing_status == submission.client_clearing_status,
        Submission.sub_producer_name == submission.sub_producer_name,
        Submission.sub_producer_email == submission.sub_producer_email,
        Submission.brokerage_office == submission.brokerage_office,
        Submission.brokerage_id == submission.brokerage_id,
        Submission.broker_id == submission.broker_id,
        Submission.is_deleted.isnot(True),
        ReportV2.is_deleted.isnot(True),
    )

    query = SubmissionDAO.add_organization_id_filter(query, report.organization_id)
    query = query.options(joinedload(Submission.client_submission_ids))
    candidates: list[Submission] = query.all()
    logger.info("Found candidates for bundling", report_id=report.id, report_ids=[c.report_id for c in candidates])
    # filter out submissions that have more than one client submission id
    candidates = [c for c in candidates if c.client_submission_ids and len(c.client_submission_ids) == 1]
    # filter out submissions that have the same external id
    candidates = [c for c in candidates if c.first_client_submission_id != submission.first_client_submission_id]
    # filter out submissions with same external id type (e.g. "A" or "C")
    candidates = [c for c in candidates if c.first_client_submission_id[-1] != external_id[-1]]
    logger.info("Filtered candidates for bundling", report_id=report.id, report_ids=[c.report_id for c in candidates])

    if not candidates:
        return

    if len(candidates) > 1:
        logger.warning(
            "Multiple candidates found for bundling. Choosing the one with closest client id",
            report_id=report.id,
            external_id=external_id,
            submission_ids=[c.id for c in candidates],
        )
        # sort by the difference between the external id and the candidate's external id
        candidates = sorted(
            candidates, key=lambda c: abs(int(c.first_client_submission_id[:-1]) - int(external_id[:-1]))
        )

    logger.info("Bundling reports", report_id=report.id, report_ids=[c.report_id for c in candidates])
    bundle_reports_by_ids([report.id, candidates[0].report_id])


def _create_report_request(body: str) -> CreateReportRequest:
    no_file = connexion.request.files.get("file") is None
    if no_file and body:
        request = CreateReportRequestSchema().loads(body)
        request.origin = request.origin or (Origin.COPY if request.original_report_id else Origin.API)
        return request

    # owner_email - the fallback for previous version of the endpoint
    create_report_dict = {
        "user_email": connexion.request.form.get("user_email") or connexion.request.form.get("owner_email"),
        "name": connexion.request.form.get("name"),
        "is_renewal": connexion.request.form.get("is_renewal"),
        "proposed_effective_date": connexion.request.form.get("proposed_effective_date"),
        "policy_expiration_date": (
            connexion.request.form.get("policy_expiration_date")
            if connexion.request.form.get("policy_expiration_date")
            else None
        ),
        "broker": connexion.request.form.get("broker"),
        "broker_email": (
            connexion.request.form.get("broker_email") if connexion.request.form.get("broker_email") else None
        ),
        "brokerage": connexion.request.form.get("brokerage"),
        "brokerage_office": (
            connexion.request.form.get("brokerage_office") if connexion.request.form.get("brokerage_office") else None
        ),
        "correspondence_contact_name": (
            connexion.request.form.get("correspondence_contact_name")
            if connexion.request.form.get("correspondence_contact_name")
            else None
        ),
        "correspondence_contact_email": (
            connexion.request.form.get("correspondence_contact_email")
            if connexion.request.form.get("correspondence_contact_email")
            else None
        ),
        "sub_producer_name": (
            connexion.request.form.get("sub_producer_name") if connexion.request.form.get("sub_producer_name") else None
        ),
        "sub_producer_email": (
            connexion.request.form.get("sub_producer_email")
            if connexion.request.form.get("sub_producer_email")
            else None
        ),
        "status": connexion.request.form.get("status").lower() if connexion.request.form.get("status") else "cleared",
        "external_id": connexion.request.form.get("external_id"),
        "coverage_type": (
            connexion.request.form.get("coverage_type").lower() if connexion.request.form.get("coverage_type") else None
        ),
        "pds": bool(connexion.request.form.get("pds")) if connexion.request.form.get("pds") else False,
        "origin": (
            Origin(connexion.request.form.get("origin")) if connexion.request.form.get("origin") else Origin.API.value
        ),
        "email_body": str(email_body) if (email_body := connexion.request.form.get("email_body")) else None,
        "email_subject": str(email_subject) if (email_subject := connexion.request.form.get("email_subject")) else None,
        "email_message_id": (
            email_message_id if (email_message_id := connexion.request.form.get("email_message_id")) else None
        ),
        "email_references": (
            email_references if (email_references := connexion.request.form.get("email_references")) else None
        ),
        "correspondence_id": (
            correspondence_id if (correspondence_id := connexion.request.form.get("correspondence_id")) else None
        ),
        "client_clearing_status": (
            connexion.request.form.get("client_clearing_status")
            if connexion.request.form.get("client_clearing_status")
            else None
        ),
    }
    return CreateReportRequestSchema().load(create_report_dict)


def append_sov_file_if_needed(file: File, report: ReportV2):
    if file and report.submission:
        if not [f for f in report.submission.files if f.id == file.id]:
            report.submission.files.append(file)


def get_reports_by_dossier(
    id: str,
    expand: list[str] = None,
    max_age_days: int = None,
    include_frozen: bool = True,
    lightweight: bool = False,
    include_snapshots: bool = False,
    include_in_pds: bool = False,
    organization_id: int | None = None,
) -> dict:
    expand = expand or []

    reports_query = ReportV2.query.join(Submission).join(SubmissionBusiness).filter(ReportV2.is_deleted.isnot(True))

    if include_snapshots:
        ers_client: ERSClientV3 = flask.current_app.ers_client_v3
        snapshots = ers_client.get_snapshots_for_entity(id, lightweight=True)
        snapshot_ids = [UUID(s.id) for s in snapshots]
        reports_query = reports_query.filter(
            or_(SubmissionBusiness.business_id == UUID(id), SubmissionBusiness.ers_snapshot_id.in_(snapshot_ids))
        )
    else:
        reports_query = reports_query.filter(SubmissionBusiness.business_id == UUID(id))

    if lightweight:
        submission_businesses_load_fields = [SubmissionBusiness.id, SubmissionBusiness.business_id]
        if "submissions.businesses.entity_role" in expand:
            submission_businesses_load_fields.append(SubmissionBusiness.entity_role)
        if "submissions.businesses.named_insured" in expand:
            submission_businesses_load_fields.append(SubmissionBusiness.named_insured)

        submission_loading_options = [
            lazyload(Submission.assigned_underwriters),
            load_only(Submission.id),
            selectinload(Submission.businesses).load_only(*submission_businesses_load_fields),
        ]

        if any("submissions.coverages" in expand_param for expand_param in expand):
            submission_loading_options.append(selectinload(Submission.coverages))

        report_fields_to_load = [ReportV2.id, ReportV2.name, ReportV2.owner_id]
        for e in expand:
            if "." not in e and hasattr(ReportV2, e):
                report_fields_to_load.append(getattr(ReportV2, e))

        reports_query = reports_query.options(
            load_only(*report_fields_to_load),
            contains_eager(ReportV2.submission).options(*submission_loading_options),
        )

    if not include_frozen:
        # The param name here did not age well - we care about excluding terminal stages,
        # not frozen stages. For example "DECLINED" should not be included, "CLEARING_ISSUE" should.
        reports_query = reports_query.filter(not_(Submission.stage.in_(SubmissionStage.terminal_stages())))

    if max_age_days:
        threshold_date = datetime.utcnow() - relativedelta(days=max_age_days)
        reports_query = reports_query.filter(ReportV2.created_at >= threshold_date)

    if organization_id:
        reports_query = reports_query.filter(ReportV2.organization_id == organization_id)

    reports = reports_query.all()

    if include_in_pds:
        threshold_date = datetime.utcnow() - relativedelta(days=30)
        br_path = "$.resolution_data[*].entity_id"

        no_submission_businesses_cond = SubmissionBusiness.id.is_(None)
        recent_cond = ReportV2.created_at >= threshold_date
        has_br_data_cond = ProcessedFile.business_resolution_data.isnot(None)
        contains_id_cond = func.jsonb_path_query_array(ProcessedFile.business_resolution_data, br_path).op("?")(id)

        query = ReportV2.query.join(Submission).join(SubmissionBusiness, isouter=True)
        query = query.join(Submission.files).join(File.processed_file)
        query = query.filter(and_(no_submission_businesses_cond, recent_cond, has_br_data_cond, contains_id_cond))
        reports_pending_resolution = query.all()
        reports.extend(reports_pending_resolution)

    if not lightweight:
        only = ["id", "name", "owner_id", "submissions"]
    else:
        only = [
            "id",
            "name",
            "owner_id",
            "submissions.id",
            "submissions.businesses.id",
            "submissions.businesses.business_id",
        ]

    only = only + expand
    reports = ReportV2Schema(only=only).dump(reports, many=True)
    return {"reports": reports}


def get_reports_by_submission(id: str, stages: list[str] | None = None):
    submission_id = UUID(id)

    if stages is None:
        stages = []

    query = (
        ReportV2.query.join(Submission)
        .options(
            load_only("id", "name", "owner_id", "organization_id"),
            subqueryload("submission").load_only("id"),
        )
        .filter(Submission.id == submission_id)
        .filter(ReportV2.is_deleted.isnot(True))
    )

    if "ALL" not in stages:
        query = query.filter(Submission.stage.in_(stages))

    reports = query.limit(100).all()

    reports = ReportV2Schema(only=["id", "name", "owner_id", "submissions.id", "organization_id"]).dump(
        reports, many=True
    )
    return {"reports": reports}


def _map_submission_user_to_external_response(submission_user: SubmissionUser) -> UserExternal:
    return UserExternal(
        name=submission_user.user.name,
        email=submission_user.user.email,
    )


def _map_broker_to_external_response(broker: BrokerageEmployee | None) -> BrokerExternal | None:
    if not broker:
        return None

    return BrokerExternal(
        name=broker.name,
        email=broker.email,
    )


def _map_brokerage_to_external_response(brokerage: Brokerage | None) -> BrokerageExternal | None:
    if not brokerage:
        return None

    return BrokerageExternal(
        name=brokerage.name,
    )


def _map_report_to_external_response(report: ReportV2) -> GetReportExternalResponse:
    submission = report.submission

    if report.submission.stage not in EXTERNAL_SUBMISSION_STAGE_MAPPINGS:
        logger.error("Submission stage not mapped to external status, using default", stage=report.submission.stage)
        status = SubmissionStage.get_label(report.submission.stage)
    else:
        status = EXTERNAL_SUBMISSION_STAGE_MAPPINGS[report.submission.stage]

    assigned_uws = [
        _map_submission_user_to_external_response(submission_user)
        for submission_user in submission.assigned_underwriters
    ]
    return GetReportExternalResponse(
        id=report.id,
        name=report.name,
        status=status,
        created_at=report.created_at,
        assigned_underwriters=assigned_uws,
        broker=_map_broker_to_external_response(submission.broker),
        brokerage=_map_brokerage_to_external_response(submission.brokerage),
        fni_state=submission.fni_state,
        policy_expiration_date=submission.policy_expiration_date,
        primary_naics_code=submission.primary_naics_code,
        proposed_effective_date=submission.proposed_effective_date,
        received_date=submission.received_date,
        recommendation_action=submission.recommendation_v2_action,
        recommendation_score=submission.recommendation_v2_score,
        sic_code=submission.sic_code,
        stage=submission.stage,
    )


def _dump_external_report(report_repr: GetReportExternalResponse) -> dict:
    extended_schema_enabled = False
    try:
        extended_schema_enabled = FeatureFlagsClient.is_feature_enabled_for_request_user(
            FeatureType.EXTEND_EXTERNAL_REPORT_API_RESPONSE
        )
    except Exception:
        logger.exception("Error while checking feature flag, assuming false")

    if extended_schema_enabled:
        logger.info("Using extended schema for external report response", report_id=report_repr.id)
        return get_report_external_response_schema_v2.dump(report_repr)
    else:
        return get_report_external_response_schema.dump(report_repr)


def get_report_external(id: str, expand: str = None):
    return get_report_lite(id, expand, external=True)


# This version of endpoint is tailored towards BE usage, when usually fewer fields are needed.
# Clients interested in submission data should pass submissions.id in expand and call GET submissions.
def get_report_lite(id: str, expand: str = None, external: bool = False):
    id = UUID(id)
    if not current_user.has_report_permission(PermissionType.VIEWER, id):
        abort(403)

    if external:
        expand = f"{expand or ''},submissions"

    query = ReportV2.query.options(lazyload(ReportV2.report_bundle)).filter(ReportV2.id == id)
    if expand and "submissions" in expand:
        query = query.options(joinedload(ReportV2.submission).lazyload(Submission.assigned_underwriters))

    if expand and "bundled_reports" in expand:
        query = query.options(
            joinedload(ReportV2.report_bundle)
            .selectinload(ReportBundle.reports)
            .options(
                joinedload(ReportV2.submission).options(
                    joinedload(Submission.coverages).joinedload(SubmissionCoverage.coverage)
                ),
                lazyload(ReportV2.report_bundle),
            )
        )

    report = query.first()
    if not report:
        raise NotFound("The Report with specified ID wasn't found")

    if report.is_deleted:
        abort(404, "The Report with specified ID has been deleted")

    fields_to_expand = list(map(str.strip, expand.split(","))) if expand else []
    default_only = ["id", "name", "owner_id", "created_at", "is_archived", "organization_id"]
    only = default_only + fields_to_expand
    if not external:
        return ReportV2Schema(only=only).dump(report)
    else:
        external_report_response = _map_report_to_external_response(report)
        return _dump_external_report(external_report_response)


def _notify_if_report_unavailable(report: ReportV2):
    submission = report.submission

    is_unavailable = (
        submission.is_verification_required
        and not submission.is_verified
        and not submission.is_verified_shell
        and not current_user.is_kalepa
    )
    if not is_unavailable:
        return

    if current_user and not current_user.is_machine_user:
        user = User.query.get(current_user.id)
        if user.applicable_settings.can_adjust_email_classification:
            # can_adjust_email_classification setting is used to show inbox in CWA
            # while user is clicking in the inbox, FE is fetching report get some basic details
            # and correspondence details - for such user we don't want to send slack message
            return

    flask.current_app.slack_client.send_slack_message(
        USER_MONITORING_SLACK_CHANNEL_ID,
        (
            f":warning: Underwriter tried to open an unverified submission. ReportId: {report.id}, missing data status:"
            f" {report.submission.missing_data_status}, created_at: {report.created_at}, user:"
            f" {current_user.email} :warning:"
        ),
    )


def get_report(id: str, expand: str = None, enrich_with_ers_data: bool = False):
    id = UUID(id)

    if not current_user.has_report_permission(PermissionType.VIEWER, id) and not current_user.cross_organization_access:
        abort(403)

    query = ReportV2.query.options(
        joinedload(ReportV2.submission).options(
            joinedload(Submission.bookmarks).load_only(SubmissionBookmark.user_id),
            selectinload(Submission.read_by_users).load_only(ReadSubmission.user_id),
            joinedload(Submission.client_submission_ids).load_only(SubmissionClientId.client_submission_id),
            selectinload(Submission.assigned_underwriters)
            .joinedload(SubmissionUser.user)
            .load_only(User.name, User.email)
            .options(lazyload(User.organization), lazyload(User.settings)),
            selectinload(Submission.coverages).joinedload(SubmissionCoverage.coverage),
            selectinload(Submission.deductibles).joinedload(SubmissionDeductible.coverage),
            selectinload(Submission.clearing_issues)
            .joinedload(SubmissionClearingIssue.suspected_report)
            .lazyload(ReportV2.report_bundle),
            joinedload(Submission.broker),
            joinedload(Submission.brokerage),
            joinedload(Submission.files).joinedload(File.processed_file).load_only(ProcessedFile.id),
        ),
        selectinload(ReportV2.links)
        .selectinload(ReportLink.report_2)
        .lazyload(ReportV2.report_bundle)
        .joinedload(ReportBundle.reports)
        .joinedload(ReportV2.submission),
        selectinload(ReportV2.report_bundle).joinedload(ReportBundle.reports).joinedload(ReportV2.submission),
    )

    if should_expose_permissions(id):
        query = query.options(
            selectinload(ReportV2.report_permissions)
            .joinedload(ReportPermission.grantee)
            .options(lazyload(User.organization), lazyload(User.settings))
        )

    report = query.get_or_404(id, description="The Report with specified ID wasn't found")
    if current_user.cross_organization_access and not current_user.has_report_permission(PermissionType.VIEWER, id):
        return {"organization_id": report.organization_id}, 404

    if report.is_deleted:
        depends_on_report = report.processing_depends_on_report
        if depends_on_report and depends_on_report.report_id and depends_on_report.is_same_org:
            new_url = report.processing_depends_on_report.report.get_url()
            logger.info("Redirecting deleted report to", new_url=new_url)
            return ReportRedirectSchema().dump({"redirect_url": new_url})
        same_org_dependencies = [dep.dependent_report for dep in report.processing_dependencies if dep.is_same_org]
        if same_org_dependencies:
            new_url = same_org_dependencies[0].get_url()
            logger.info("Redirecting deleted report to", new_url=new_url)
            return ReportRedirectSchema().dump({"redirect_url": new_url})
        abort(404, "The Report with specified ID has been deleted")

    if enrich_with_ers_data:
        logger.warning("Enrich with ERS data is deprecated and has no effect")

    fields_to_expand = set(list(map(str.strip, expand.split(","))) if expand else [])
    exclude = [
        "submissions.expected_premium",
        "submissions.target_premium",
        "submissions.expired_premium",
        "submissions.sales",
        "submissions.renewal_creation_date",
        "submissions.report_ids",
        "submissions.email_description",
        "submissions.decline_email_delivered",
        "submissions.decline_email_tracking_id",
        "submissions.businesses.expand_address_range",
        "submissions.businesses.submission_id",
        "submissions.has_unique_submission_client_id",
        "submissions.has_been_synced",
        "submissions.workers_comp_experience",
        "submissions.workers_comp_rating_info",
        "submissions.is_brokerage_set_by_machine_user",
        "submissions.is_broker_agent_set_by_machine_user",
        "submissions.is_broker_correspondence_contact_set_by_machine_user",
    ]
    exclude = [e for e in exclude if e not in fields_to_expand]

    # we do not want to exclude submissions fields that are not part of expand param
    fields_to_expand = [f for f in fields_to_expand if "submissions" not in f]
    expand = [
        "id",
        "name",
        "is_archived",
        "is_rush",
        "owner_id",
        "submissions",
        "permissions",
        "current_user_permission_type",
        "created_at",
        "organization_permission_level",
        "url",
        "full_pds",
    ]
    only = expand + fields_to_expand

    if current_user.applicable_settings and current_user.applicable_settings.show_internal_account_id:
        submission = report.submission
        submission.use_internal_account_id = True

    _notify_if_report_unavailable(report)

    return ReportV2Schema(only=only, exclude=exclude, context={"dump_processed_file_id": True}).dump(report)


def update_report_external(id: str, body: dict):
    if id in FAKE_REPORT_IDS:
        return {"id": id, "name": "Fake report"}, 200
    return update_report(id, body, True)


def update_report(id: str, body: dict, external: bool = False, return_response: bool = True):
    id = UUID(id)
    report = get_report_or_404(id)
    submission = report.submission
    report_patch = None
    is_empty_report = report.submission is None
    was_frozen_before_update = report.submission.is_frozen if report.submission else False
    start_processing_again = False
    submission_level_extracted_data = []
    org_group_changed = False

    def add_submission_level_data(field: EntityInformation, value: Any):
        submission_level_extracted_data.append(
            SubmissionLevelExtractedData(
                submission_id=submission.id,
                field=field,
                value=json.dumps(value, default=str),
                source_details=SourceDetails.API,
                is_selected=True,
            )
        )

    if not current_user.has_report_permission(PermissionType.EDITOR, report.id):
        abort(403)

    log = logger.bind(report_id=id, user_id=current_user.id)

    if "is_rush" in body and not current_user.is_internal_machine_user:
        report.rush_source = RushSource.USER

    if "submissions" in body or "organization_permission_level" in body:
        report = report_schema.load(body, instance=report)
        report.id = id
        if "submissions" in body:
            create_external_resource_snapshots_if_needed(
                submission, refresh_existing_snapshots=not was_frozen_before_update
            )
            if is_empty_report:
                current_app.event_service.handle_submission_event(
                    SubmissionEvent.SUBMISSION_CREATED,
                    submission,
                    business=None,
                )
    else:
        report_patch: ReportPatchRequest = report_patch_schema.load(body)
        if report_patch.name:
            report.name = report_patch.name
            submission.name = report_patch.name

        if report_patch.external_id:
            # check if the external_id is already assigned to another verified submission
            should_add_client_id = all(
                [p.client_submission_id != report_patch.external_id for p in submission.client_submission_ids]
            )
            client_ids = SubmissionClientIdDAO.get_submission_client_ids(
                [report_patch.external_id],
                report.organization_id,
                exclude_deleted_reports=True,
            )

            if should_add_client_id:
                if bool(client_ids):
                    abort(409, "External ID is already assigned to another submission")
                submission.client_submission_ids = [
                    SubmissionClientId(
                        client_submission_id=report_patch.external_id,
                        submission_id=submission.id,
                        source=SubmissionClientIdSource.API,
                    )
                ]

        if report_patch.assigned_user_email:
            user = _assign_underwriter_and_update_tags(
                report, report_patch.assigned_user_email, SubmissionUserSource.API
            )
            add_submission_level_data(EntityInformation.UNDERWRITER_EMAILS, [report_patch.assigned_user_email])
            if user:
                add_submission_level_data(EntityInformation.UNDERWRITER_USER_IDS, [user.id])

        if report_patch.is_renewal is not None:
            submission.is_renewal = report_patch.is_renewal
            add_submission_level_data(EntityInformation.IS_RENEWAL, report_patch.is_renewal)

        if report_patch.is_rush is not None:
            report.is_rush = report_patch.is_rush

        if report_patch.status:
            submission_stage = map_report_status_to_submission_stage(report_patch.status)
            if start_processing_again := (
                Organization.is_nationwide_for_id(report.organization_id)
                and submission.is_in_terminal_stage
                and not SubmissionStage.is_terminal(submission_stage)
                and submission.processing_state == SubmissionProcessingState.CANCELLED
            ):
                submission.processing_state = None
                submission.is_auto_processed = False

            was_frozen_before_update = submission.is_frozen
            submission.stage = submission_stage
            create_external_resource_snapshots_if_needed(
                submission, refresh_existing_snapshots=not was_frozen_before_update, commit=False
            )

        if report_patch.status_details:
            stage_details = {}
            if report_patch.status_details.update_date:
                stage_details["updateDate"] = report_patch.status_details.update_date.isoformat()
            if report_patch.status_details.reason:
                stage_details["reason"] = report_patch.status_details.reason

            if stage_details:
                submission.stage_details = stage_details

        if report_patch.proposed_effective_date:
            submission.proposed_effective_date = report_patch.proposed_effective_date
            add_submission_level_data(
                EntityInformation.POLICY_EFFECTIVE_START_DATE, report_patch.proposed_effective_date
            )

        if report_patch.policy_expiration_date:
            submission.policy_expiration_date = report_patch.policy_expiration_date
            add_submission_level_data(EntityInformation.POLICY_END_DATE, report_patch.policy_expiration_date)

        if report_patch.brokerage_office:
            submission.brokerage_office = report_patch.brokerage_office

        if report_patch.sub_producer_name:
            submission.sub_producer_name = report_patch.sub_producer_name

        if report_patch.sub_producer_email:
            submission.sub_producer_email = report_patch.sub_producer_email

        if report_patch.client_clearing_status:
            org_settings: Settings = Settings.query.filter(
                Settings.organization_id == report.organization_id
            ).one_or_none()
            if org_settings:
                clearing_settings = org_settings.client_clearing_status_config or {}
                is_enabled = clearing_settings.get("enabled", False)
                allowed_statuses = clearing_settings.get("statuses") or []

                if is_enabled:
                    if report_patch.client_clearing_status in allowed_statuses:
                        submission.client_clearing_status = report_patch.client_clearing_status
                    else:
                        abort(
                            400,
                            (
                                f"Client clearing status {report_patch.client_clearing_status} is not allowed. Allowed"
                                f" statuses are: {allowed_statuses}"
                            ),
                        )

        if report_patch.primary_naics_code:
            # validate the naics code
            naics_code: NaicsCode = validate_and_optionally_map_naics_to_2022(report_patch.primary_naics_code)
            if not naics_code or not naics_code.is_six_digit_code():
                logger.warning("Received invalid NAICS code", naics_code=report_patch.primary_naics_code)
                abort(400, f"The received NAICS code: {report_patch.primary_naics_code} is invalid")
            if naics_code:
                is_naics_verified = report.submission.is_naics_verified
                naics_before_update = report.submission.primary_naics_code
                sic_code_before_update = report.submission.sic_code
                update_submission_naics_if_needed(submission, naics_code.value)
                add_submission_level_data(EntityInformation.NAICS_CODES, {naics_code.value: 1.0})
                handle_updating_naics_code(
                    report.submission,
                    is_naics_verified,
                    naics_before_update,
                    sic_code_before_update,
                )

        if report_patch.sic_code:
            # validate the sic_code
            if Organization.is_cna_for_id(report.organization_id):
                enum = SIC5Code
            else:
                enum = SICCode
            if sic_code := enum.try_parse_str(report_patch.sic_code):
                update_submission_sic_if_needed(submission, sic_code.value)
            else:
                logger.warning("Received invalid SIC code", naics_code=report_patch.sic_code)
                abort(400, f"The received SIC code: {report_patch.sic_code} is invalid")

        if report_patch.iso_gl_code:
            # validate the iso gl code
            if iso_gl_code := ISOGLCode.try_parse_str(report_patch.iso_gl_code):
                update_submission_gl_code_if_needed(submission, iso_gl_code.value)
            else:
                logger.warning("Received invalid ISO GL code", naics_code=report_patch.iso_gl_code)
                abort(400, f"The received ISO GL code: {report_patch.iso_gl_code} is invalid")

        if report_patch.received_date:
            submission.received_date = report_patch.received_date

        if report_patch.businesses and not external:
            if submission.businesses:
                log.info(
                    "UPDATE_REPORT: Received businesses for a submission that already has them",
                    businesses=report_patch.businesses,
                    submission_id=submission.id,
                )
            elif submission.is_frozen:
                log.info(
                    "UPDATE_REPORT: Received businesses for a frozen submission",
                    businesses=report_patch.businesses,
                    submission_id=submission.id,
                )
            else:
                from copilot.v3.controllers.submissions import add_submission_business

                for business in report_patch.businesses:
                    add_submission_business(str(submission.id), business)
                log.info(
                    "UPDATE_REPORT: Added businesses to submission",
                    businesses=report_patch.businesses,
                    submission_id=submission.id,
                )

        if report_patch.coverages:
            SubmissionCoverage.query.filter(SubmissionCoverage.submission_id == submission.id).delete(
                synchronize_session=False
            )
            added_coverage_types = []
            for coverage in report_patch.coverages:
                if coverage.coverage_type in added_coverage_types:
                    abort(
                        400,
                        (
                            "Only single coverage record can be added for specific coverage type"
                            f" ({coverage.coverage_type})."
                        ),
                    )
                added_coverage_types.append(coverage.coverage_type)
                if coverage_id := (
                    db.session.query(Coverage.id)
                    .filter(
                        Coverage.name == map_coverage_type_to_name(coverage.coverage_type),
                        Coverage.organization_id == report.organization_id,
                        Coverage.is_disabled.is_(False),
                    )
                    .first()
                ):
                    quoted_premium = coverage.quoted_premium or 0.0
                    bound_premium = coverage.bound_premium
                    total_premium = coverage.total_premium
                    is_quoted = quoted_premium > 0.0

                    coverage_type = (
                        coverage.coverage_type.upper()
                        if coverage.coverage_type and (coverage.coverage_type.upper() in ["PRIMARY", "EXCESS"])
                        else None
                    )

                    submission_coverage = SubmissionCoverage(
                        submission_id=submission.id,
                        coverage_id=coverage_id[0],
                        coverage_type=coverage_type,
                        quoted_premium=quoted_premium,
                        bound_premium=bound_premium,
                        total_premium=total_premium,
                        is_quoted=is_quoted,
                        source=SubmissionCoverageSource.API,
                        source_details=f"Created by an update_report API call by {current_user.email}",
                    )
                    if coverage.estimated_premium:
                        submission_coverage.estimated_premium = coverage.estimated_premium
                    db.session.add(submission_coverage)
                else:
                    abort(400, "Invalid coverage name")
            db.session.commit()

        if report_patch.email_body:
            report.email_body = report_patch.email_body

        if report_patch.org_group:
            org_group_changed = report.org_group != report_patch.org_group
            report.org_group = report_patch.org_group

        if report_patch.email_subject:
            report.email_subject = normalize_email_subject(report_patch.email_subject)

        if report_patch.email_message_id:
            report.email_message_id = report_patch.email_message_id

        if report_patch.email_references:
            report.email_references = report_patch.references

        if report_patch.additional_identifiers:
            patch_identifiers(report.submission, report_patch.additional_identifiers)

        if report_patch.additional_data:
            report.additional_data = report_patch.additional_data

        logger.info(
            "Auto assigning agent",
            submission_id=submission.id,
            broker=report_patch.broker,
            broker_email=report_patch.broker_email,
            brokerage=report_patch.brokerage,
            corr_contact_name=report_patch.correspondence_contact_name,
            corr_contact_email=report_patch.correspondence_contact_email,
            existing_brokerage=submission.brokerage_id,
            existing_broker=submission.broker_id,
        )
        agent_info = AgentInfo(
            agent_email=report_patch.broker_email,
            agent_name=report_patch.broker,
            agency_name=report_patch.brokerage,
        )
        corr_contact = AgentInfo(
            agent_email=report_patch.correspondence_contact_email,
            agent_name=report_patch.correspondence_contact_name,
            agency_name=report_patch.brokerage,
        )
        c_contact, broker = auto_assign_agent(submission, agent_info)
        c_contact = assign_corr_contact(submission, corr_contact) or c_contact

        if report_patch.broker:
            add_submission_level_data(EntityInformation.BROKER_NAME, report_patch.broker)
        if report_patch.broker_email:
            add_submission_level_data(EntityInformation.BROKER_EMAIL, report_patch.broker_email)
        if report_patch.brokerage:
            add_submission_level_data(EntityInformation.BROKERAGE_NAME, report_patch.brokerage)

        if c_contact:
            add_submission_level_data(EntityInformation.CORRESPONDENCE_CONTACT_ID, c_contact.id)

        if broker:
            add_submission_level_data(EntityInformation.BROKER_ID, broker.id)
            add_submission_level_data(EntityInformation.BROKERAGE_ID, broker.brokerage_id)

    if report_patch and report_patch.assigned_user_email is None and "assigned_user_email" in body:
        # we received a null or empty value for assigned uw. We should remove the assigned uw

        assigned_uws = submission.assigned_underwriters

        if len(assigned_uws) > 1:
            logger.error(
                "Cannot remove assigned underwriter because there are multiple assigned underwriters",
                report_id=report.id,
                submission_id=submission.id,
            )
        elif len(assigned_uws) == 1:
            from copilot.v3.controllers.submissions import delete_submission_user

            delete_submission_user(str(submission.id), assigned_uws[0].user_id)

    _handle_submission_level_data_update(submission.id, submission_level_extracted_data)

    db.session.commit()
    if report_patch and (report_patch.email_body or start_processing_again):
        if start_processing_again:
            _enable_auto_processing_if_needed(report, force=True)
        else:
            routing_result = get_routing_result_from_report(report)
            _update_routing_tags_and_tiers(
                report, routing_result, skip_processing_for_boss=False, start_processing=False
            )
            if not report.submission.is_boss:
                _assign_underwriters(report, routing_result.user_ids, SubmissionUserSource.AUTO)

    db.session.add(report)
    db.session.commit()
    db.session.refresh(report)

    if (
        org_group_changed
        and (report.submission.is_verified or report.submission.is_verified_shell)
        and report.organization_id in ORG_GROUP_TO_USER_GROUP_MAPPINGS
    ):
        share_report_with_org(report, delete_other_group_permissions=True)

    if not is_empty_report:
        current_app.event_service.handle_submission_event(
            SubmissionEvent.SUBMISSION_UPDATED,
            submission,
            business=None,
            raw_submission=next(iter(body.get("submissions", [])), None),
        )
    if not return_response:
        return
    if not external:
        return ReportV2Schema(exclude=["submissions.expected_premium"]).dump(report)
    else:
        return external_report_schema.dump(report)


def _handle_submission_level_data_update(
    submission_id: UUID, submission_level_extracted_data: list[SubmissionLevelExtractedData]
):
    existing_submission_level_data_dict = {}
    if submission_level_extracted_data:
        existing_submission_level_data = SubmissionLevelExtractedData.query.filter(
            SubmissionLevelExtractedData.submission_id == submission_id,
            SubmissionLevelExtractedData.source_details == SourceDetails.API,
            SubmissionLevelExtractedData.field.in_([sled.field for sled in submission_level_extracted_data]),
        ).all()
        existing_submission_level_data_dict = {sled.field: sled for sled in existing_submission_level_data}
    for sled in submission_level_extracted_data:
        if existing_submission_level_data_dict.get(sled.field):
            existing_submission_level_data_dict[sled.field].value = sled.value
        else:
            db.session.add(sled)


def handle_new_email_in_report(report: ReportV2, email: Email) -> None:
    routing_result = get_routing_result_from_email(report, email)
    EmailHandler().maybe_handle_follow_up(email, report.submission.id, report.organization_id)
    _update_routing_tags_and_tiers(report, routing_result, start_processing=False)
    should_assign_uw = True
    if report.submission.is_boss:
        should_assign_uw = False
    if should_assign_uw:
        _assign_underwriters(report, routing_result.user_ids)
    _maybe_set_rush_report(report, email)


def start_processing(id: str, ignore_terminal_stages: bool = False):
    report = get_report_or_404(UUID(id))
    submission = report.submission
    if submission.is_auto_processed:
        abort(400, "The report is already being processed")
    if submission.businesses:
        abort(400, "The report already have businesses")
    if not submission.files:
        abort(400, "The report does not have any files")
    if ignore_terminal_stages and not current_user.is_internal_machine_user:
        abort(403, "Only internal machine users can ignore terminal stages")
    start_report_processing(report, check_terminal_stages=not ignore_terminal_stages)


def cancel_processing(id: str):
    report = get_report_or_404(UUID(id))
    submission = report.submission
    if not submission.is_auto_processed:
        abort(400, "The report is not processed")
    cancel_report_processing(report)


def restart_processing(id: str, force: bool = False, skip_file_cache: bool = False) -> (None, int):
    report = get_report_or_404(UUID(id))
    submission = report.submission
    if not force:
        if not submission.is_auto_processed:
            abort(
                400,
                (
                    "The report is not a PDS report. Use force parameter to remove all report data and start PDS"
                    " processing"
                ),
            )
        if submission.businesses:
            abort(
                400,
                (
                    "The report already have businesses. Use force parameter to remove all report data and start PDS"
                    " processing"
                ),
            )
    shadow_creation_cut_off_time = datetime.now(submission.created_at.tzinfo) - SHADOW_CREATION_CUT_OFF_TIME
    if submission.created_at < shadow_creation_cut_off_time:
        abort(400, "The report is too old to restart processing.")
    if not submission.files:
        abort(400, "The report does not have any files")
    is_restart_possible, reason_for_not_restarting = can_restart_report_processing(report)
    if not is_restart_possible:
        abort(400, reason_for_not_restarting)
    restart_report_processing(report, skip_file_cache)
    return None, 204


# POST /reports/{id}/revert/{state}
def revert_report_to_state(id: str, state: SubmissionProcessingState, force: bool = False) -> (None, int):
    if not FeatureFlagsClient.is_feature_enabled(FeatureType.ALLOW_SUB_REVERTING, current_user.email):
        check_if_cs_manager_or_tier_2()

    report = get_report_or_404(UUID(id))
    submission = report.submission

    shadow_creation_cut_off_time = datetime.now(submission.created_at.tzinfo) - SHADOW_CREATION_CUT_OFF_TIME
    if submission.created_at < shadow_creation_cut_off_time and not force:
        abort(
            400,
            (
                "The report is too old to restart processing. Use force parameter to remove all report data and"
                " start PDS processing"
            ),
        )

    valid_revert_states = {
        SubmissionProcessingState.DATA_ONBOARDING: [SubmissionProcessingState.COMPLETED],
        SubmissionProcessingState.BUSINESS_CONFIRMATION: [
            SubmissionProcessingState.COMPLETED,
            SubmissionProcessingState.DATA_ONBOARDING,
        ],
        SubmissionProcessingState.ENTITY_MAPPING: [
            SubmissionProcessingState.COMPLETED,
            SubmissionProcessingState.DATA_ONBOARDING,
            SubmissionProcessingState.BUSINESS_CONFIRMATION,
        ],
    }
    if (
        state not in valid_revert_states
        or report.submission.processing_state not in valid_revert_states[state]
        or submission.is_processing
    ):
        abort(400, "Invalid state to revert submission")

    submission_to_revert = submission
    if submission.processing_state == SubmissionProcessingState.COMPLETED:
        lock_name = get_create_shadow_lock_name(report.id)
        try:
            with redis_lock.Lock(current_app.locks_client, name=lock_name, expire=60):
                if any(sd.is_active for sd in report.shadow_dependencies):
                    abort(409, "Cannot revert to DO for report with active shadow report")
                submission_to_revert = create_shadow_submission_for_revert(report)
        except redis_lock.NotAcquired:
            logger.warning("Lock already expired", lock=lock_name)

    submission_handler = SubmissionHandler()
    revert_methods = {
        SubmissionProcessingState.DATA_ONBOARDING: submission_handler.revert_to_data_onboarding,
        SubmissionProcessingState.BUSINESS_CONFIRMATION: submission_handler.revert_to_business_confirmation,
        SubmissionProcessingState.ENTITY_MAPPING: submission_handler.revert_to_entity_mapping,
    }
    revert_methods[state](submission_to_revert)
    return None, 204


# POST /reports/{id}/revert_to_do
def revert_to_data_onboarding(id: str) -> (None, int):
    return revert_report_to_state(id, SubmissionProcessingState.DATA_ONBOARDING)


def revert_to_business_confirmation(id: str):
    return revert_report_to_state(id, SubmissionProcessingState.BUSINESS_CONFIRMATION)


def retry_processing(id: str) -> (None, int):
    report = get_report_or_404(UUID(id))
    submission = report.submission
    if not submission.is_auto_processed:
        abort(400, "The report is not PDS report")
    if submission.processing_state in [
        SubmissionProcessingState.CANCELLED,
        SubmissionProcessingState.PROCESSING_FAILED,
        SubmissionProcessingState.NO_FILES_WITH_ENTITY_DATA,
        SubmissionProcessingState.COMPLETED,
    ]:
        abort(400, "The report is already processed or processing was cancelled")
    retry_report_processing(report)
    return None, 204


def _enable_auto_processing_if_needed(report, force: bool = False, start_processing: bool = True):
    if (
        report.submission.is_processing_enabled
        or (report.submission.is_verified_shell and not report.submission.is_boss)
        or (not report.email_body and not force)
        or report.submission.origin in [Origin.COPY, Origin.SYNC]
    ):
        return
    organization = report.owner.organization
    if report.is_email_classification_enabled:
        email = EmailHandler.get_email_for_classification(report=report, pick_root=start_processing)
        if not EmailHandler().maybe_handle_follow_up(email, report.submission.id, organization.id):
            KalepaEventsHandler.send_email_classification_requested_event(email_id=email.id, report=report)
    elif organization.is_auto_processing_enabled(report.tier) or force:
        start_report_processing(report)
    elif organization.is_triage_processing_enabled():
        SubmissionDAO.create_or_update_submission_processing(
            report.submission, SubmissionProcessingState.WAITING_FOR_TRIAGE, is_triage_processing=True
        )


def _update_routing_tags_and_tiers(
    report: ReportV2,
    routing_result: RoutingResult,
    skip_processing_for_boss: bool = True,
    start_processing: bool = True,
) -> None:
    prev_routing_tags = set(report.routing_tags) if report.routing_tags else set()
    if routing_result.tags != prev_routing_tags:
        for tag in routing_result.tags:
            if not report.routing_tags:
                report.routing_tags = [tag]
            elif tag not in (report.routing_tags or []):
                report.routing_tags.append(tag)
                flag_modified(report, "routing_tags")
    if routing_result.tier is None:
        return

    prev_is_rush = report.is_rush or False
    report.is_rush = routing_result.rush or prev_is_rush

    if not prev_is_rush and report.is_rush:
        report.rush_source = RushSource.ROUTING_RULE

    prev_tier = report.tier
    report.tier = min(routing_result.tier, report.tier) if report.tier is not None else routing_result.tier
    if report.tier != prev_tier and report.processing_depends_on_report:
        if report.processing_depends_on_report.dependency_type == ReportDependencyType.SAME_ORG and (
            report.processing_depends_on_report.report.tier is None
            or report.tier < report.processing_depends_on_report.report.tier
        ):
            report.processing_depends_on_report.report.tier = report.tier
        elif report.processing_depends_on_report.dependency_type == ReportDependencyType.CROSS_ORG:
            update_submission_priority_if_needed(
                report.processing_depends_on_report.report.submission, report.submission
            )

    # We don't want to start processing submissions for BOSS from this place to avoid race conditions
    if skip_processing_for_boss and report.submission.is_boss:
        return
    _enable_auto_processing_if_needed(report, start_processing=start_processing)


def _assign_underwriters(
    report: ReportV2, user_ids: set[int], source: SubmissionUserSource = SubmissionUserSource.AUTO
) -> bool:
    from copilot.v3.controllers.submissions import assign_submission_users

    submission = report.submission
    if submission.is_frozen:
        return False
    user_ids_for_assigning = user_ids - {report.owner_id} - {su.user_id for su in submission.assigned_underwriters}
    users = (
        User.query.filter(User.id.in_(user_ids_for_assigning))
        .filter(User.is_enabled.is_(True), User.name.is_not(None), User.organization_id == report.organization_id)
        .all()
    )
    viewer_user_ids = [user.id for user in users if user.is_read_only_account]
    owner_user_ids = [user.id for user in users if not user.is_read_only_account]
    if viewer_user_ids:
        assign_submission_users(submission, viewer_user_ids, PermissionType.VIEWER, source=source)
    if owner_user_ids:
        assign_submission_users(submission, owner_user_ids, PermissionType.OWNER, source=source)
    return any(users)


def _assign_underwriter_and_update_tags(
    report: ReportV2, user_email: str, source: SubmissionUserSource = SubmissionUserSource.AUTO
) -> User | None:
    user = get_user_by_email(user_email)
    if not user or not user.name or user.organization_id != report.organization_id:
        _notify_about_missing_underwriter(str(report.id), report.organization_id, user_email)
        return None

    if _assign_underwriters(report, {user.id}, source):
        routing_result = get_routing_result_from_user(user)
        _update_routing_tags_and_tiers(report, routing_result)
    else:
        logger.info(
            "User is already assigned or submission is frozen",
            report_id=report.id,
            user_email=user_email,
        )
    return user


def _notify_about_missing_underwriter(report_identifier: str, organization_id: int, user_email: str) -> None:
    try:
        current_app.notifications_handler_v2.send_missing_underwriter_notification(
            report_identifier, organization_id, user_email
        )
    except Exception:
        logger.exception(
            "Problem with notifying support about missing underwriter",
            user_email=user_email,
            report_identifier=report_identifier,
            organization_id=organization_id,
        )


def delete_report(id: str, from_sync: bool = False, deletion_reason: str | None = None):
    id = UUID(id)
    report = get_light_report(id)
    if not current_user.has_report_permission(PermissionType.OWNER, report.id):
        abort(403)
    handle_delete_report(report, from_sync, deletion_reason)

    return None, 204


def get_report_summary_preferences(id: str):
    id = UUID(id)
    report = get_light_report(id)
    if not current_user.has_report_permission(PermissionType.VIEWER, report.id):
        abort(403)
    return report_summary_preference_schema.dump(report.summary_preferences, many=True)


def create_or_replace_report_summary_preferences(id: str, body: dict):
    id = UUID(id)
    report = get_light_report(id)
    if not current_user.has_report_permission(PermissionType.VIEWER, report.id):
        abort(403)

    report_summary_preferences = report_summary_preference_schema.load(body, many=True)
    for report_summary_preference in report_summary_preferences:
        report_summary_preference.report_v2_id = id

    report.summary_preferences = report_summary_preferences
    db.session.add(report)
    db.session.commit()

    status_code = 200 if report.summary_preferences else 201

    return None, status_code


def create_metric_v2_in_db(metric: MetricV2, commit_session: bool):
    try:
        if metric.metric_preference and not metric.metric_preference.is_applicable:
            metric.metric_preference.is_applicable = True
            db.session.add(metric.metric_preference)
        db.session.add(metric)

        if commit_session:
            db.session.commit()
    except IntegrityError as e:
        db.session.rollback()
        logger.warning("Integrity error while creating metric", error=e)
        if isinstance(e.orig, psycopg2.errors.UniqueViolation) or isinstance(  # type: ignore
            e.orig,
            psycopg2.errors.ForeignKeyViolation,
        ):
            raise Conflict
        raise e
    return None, 204


def create_metric_v2(id: str, body: dict) -> tuple[None, int]:
    with no_expire_on_commit():
        id_as_uuid: UUID = UUID(id)
        report_exists_or_404(id_as_uuid)
        body["report_v2_id"] = id
        metric: MetricV2 = metric_v2_schema.load(body)

        create_metric_v2_in_db(metric, True)
        return None, 204


def bulk_create_metric_v2(id: str, body: dict) -> tuple[None, int]:
    with no_expire_on_commit():
        report_id: UUID = UUID(id)
        report_exists_or_404(report_id)

        bulk_request: BulkMetricV2Request = bulk_metric_v2_request_schema.load(body)
        for metric_v2 in bulk_request.metric_requests:
            metric_v2.report_v2_id = report_id
            create_metric_v2_in_db(metric_v2, False)

        try:
            db.session.commit()
        except IntegrityError as e:
            db.session.rollback()
            logger.warning("Integrity error while creating metric", error=e)
            if isinstance(e.orig, psycopg2.errors.UniqueViolation) or isinstance(  # type: ignore
                e.orig,
                psycopg2.errors.ForeignKeyViolation,
            ):
                raise Conflict
            raise e

        return None, 204


def __get_summary_config_ids_for_report(report_id: UUID) -> list[str]:
    from copilot.logic.reports import get_summary_config_ids_to_hide

    submission = Submission.query.filter(Submission.report_id == report_id).first()
    config_ids_to_hide = get_summary_config_ids_to_hide(submission.primary_naics_code)

    summary_config_rows = (
        db.session.query(MetricPreference.summary_config_id)
        .filter(MetricPreference.report_id == report_id)
        .filter(MetricPreference.is_applicable == true())
        .filter(MetricPreference.summary_config_id.not_in(config_ids_to_hide))
        .filter(MetricPreference.is_enabled == true())
        .all()
    )
    summary_config_ids: list[str] = [row["summary_config_id"] for row in summary_config_rows]  # type: ignore

    return summary_config_ids


def __get_metrics_v2_json(metrics: list[MetricV2], request: GetMetricsV2Request, report: ReportV2):
    """
    We dump metrics individually to prevent the endpoint from returning a 500
    in the case of a single metric being problematic, with the remainder of the metrics
    being usable. This also allows us to group dependent metrics together.

    This logic used to exist implicitly when metrics were calculated entirely in summarization.
    """
    schema = MetricV2Schema(exclude=request.exclude) if request.exclude is not None else metric_v2_schema
    metrics_json: list[list[dict]] = []
    for metric in metrics:
        try:
            enable_dependent_metrics: bool = dependent_metrics_are_enabled(report, metric)
            if not enable_dependent_metrics:
                metric.filtering_mode = NO_FILTERING_MODE

            if request.restricted_parent_ids:
                metric.restricted_parent_ids = set(request.restricted_parent_ids)

            metric_data: list[dict] = [schema.dump(metric)]
            if enable_dependent_metrics and metric.is_multi_metric:
                metric.filtering_mode = STRUCTURES_BASED_FILTERING_MODE
                metric_data.append(schema.dump(metric))
            metrics_json.append(metric_data)
        except Exception as ex:
            logger.error("Failed to dump metric", metric=metric, ex=ex)

    return metrics_json


def __get_structures_metrics_v2_json(metrics: list[MetricV2], request: GetMetricsV2Request):
    """
    We dump metrics individually to prevent the endpoint from returning a 500
    in the case of a single metric being problematic, with the remainder of the metrics
    being usable. This also allows us to filter down to only a set of parent_ids.

    This logic used to exist implicitly when metrics were calculated entirely in summarization.
    """
    schema = MetricV2Schema(exclude=request.exclude) if request.exclude is not None else metric_v2_schema
    metrics_json: list[list[dict]] = []
    for metric in metrics:
        try:
            if request.restricted_parent_ids:
                metric.restricted_parent_ids = set(request.restricted_parent_ids)
                metric.filtering_mode = CUSTOM_FILTERING_MODE
            metric_data: list[dict] = [schema.dump(metric)]
            metrics_json.append(metric_data)
        except Exception as ex:
            logger.error("Failed to dump metric", metric=metric, ex=ex)

    return metrics_json


def get_metrics_v2(
    id: str,
    body: dict | None,
) -> dict:
    report_id: UUID = UUID(id)
    report = get_report_or_404(report_id)
    if not current_user.has_report_permission(PermissionType.VIEWER, report_id):
        abort(403)

    request: GetMetricsV2Request = get_metrics_v2_request.load(body or {})

    # FE is using metrics as "Bulk get facts" as well
    # so we need a way to download a particular metric even when it's disabled in metric preferences
    # (because e.g. metric should be hidden in exposure breakdown, but still we want to have all fact
    # values in a different section of FE)
    # To achieve that FE will send a set of summary_config_ids that need to be returned
    if request.summary_config_ids:
        summary_config_ids = request.summary_config_ids
    else:
        summary_config_ids: list[str] = __get_summary_config_ids_for_report(report_id)

    metrics = get_metrics_v2_for_report(
        report_id, summary_config_ids, request.submission_business_id, request.submission_business_ids
    )

    if request.only_business_metrics:
        metrics = [metric for metric in metrics if metric.children_type in [ParentType.BUSINESS, ParentType.PREMISES]]

    metric_preferences = (
        db.session.query(MetricV2.id, MetricPreference)
        .join(
            MetricV2,
            and_(
                MetricPreference.parent_id == MetricV2.parent_id,
                MetricPreference.parent_type == MetricV2.parent_type,
                MetricPreference.report_id == MetricV2.report_v2_id,
                MetricPreference.summary_config_id == MetricV2.summary_config_id,
            ),
        )
        .filter(MetricV2.id.in_([m.id for m in metrics]))
        .filter(
            or_(
                MetricPreference.children_type != ParentType.STRUCTURE,
                MetricPreference.parent_type == ParentType.REPORT,
            )
        )
        .all()
    )
    metric_id_to_preference = {row[0]: row[1] for row in metric_preferences}
    for m in metrics:
        m.metric_preference = metric_id_to_preference.get(m.id)

    metrics_json: list[str] = (
        __get_metrics_v2_json(metrics, request, report)
        if not request.submission_business_id
        else __get_structures_metrics_v2_json(metrics, request)
    )
    return {
        "metrics": metrics_json,
    }


def get_execution_events_by_report(id: str) -> dict:
    execution_events = db.session.query(ExecutionEvent).filter_by(report_id=id).all()
    return {"execution_events": execution_event_schema.dump(execution_events, many=True)}


def create_execution_event(id: str, body: dict):
    id = UUID(id)
    report_exists_or_404(id)
    body["report_id"] = str(id)
    execution_event = execution_event_schema.load(body)
    db.session.add(execution_event)
    db.session.commit()
    return execution_event_schema.dump(execution_event), 201


def get_report_permissions(id: str, type: list[str] | None = None):
    id = UUID(id)
    report = get_report_or_404(id, with_permissions=True)
    if not type:
        permissions = report.report_permissions
    else:
        permissions = []
        for permission in report.report_permissions:
            for t in type:
                if permission.permission_type == PermissionType(t):
                    permissions.append(permission)
    permissions_envelope = PermissionsEnvelope(permissions=permissions)

    return PermissionsEnvelopeSchema().dump(permissions_envelope)


def bundle_reports_by_ids(report_ids: list[UUID | str]) -> None:
    report_ids = [str(id_) for id_ in report_ids]
    bundle_reports({"report_ids": report_ids})


def bundle_reports(body: dict):
    report_ids = [UUID(id) for id in body["report_ids"]]

    reports = ReportV2.query.filter(ReportV2.id.in_(report_ids), ReportV2.is_deleted.isnot(True)).all()
    if len(reports) != len(report_ids):
        abort(404)

    if len(reports) < 2:
        abort(400, "At least two reports must be provided")

    for report in reports:
        if not current_user.has_report_permission(PermissionType.EDITOR, report.id):
            abort(403)

    bundles = []
    for report in reports:
        if report.report_bundle:
            bundles.append(report.report_bundle)

    existing_bundle_reports = set()
    for bundle in bundles:
        for report in bundle.reports:
            existing_bundle_reports.add(report)

    bundle = ReportBundle()
    db.session.add(bundle)

    for report in existing_bundle_reports.union(set(reports)):
        bundle.reports.append(report)

    for bundle in bundles:
        db.session.delete(bundle)

    db.session.commit()

    return {}, 201


def unbundle_reports(body: dict):
    report_ids = [UUID(id) for id in body["report_ids"]]

    reports = ReportV2.query.filter(ReportV2.id.in_(report_ids)).all()
    if len(reports) != len(report_ids):
        abort(404)

    for report in reports:
        if not current_user.has_report_permission(PermissionType.EDITOR, report.id):
            abort(403)

    bundles = []
    for report in reports:
        if report.report_bundle:
            bundles.append(report.report_bundle)

            report.report_bundle.reports.remove(report)
            report.report_bundle = None

    for bundle in bundles:
        if len(bundle.reports) <= 1:
            db.session.delete(bundle)

    db.session.commit()

    return {}, 200


def link_reports(id1: str, id2: str):
    id1 = UUID(id1)
    id2 = UUID(id2)

    reports = ReportV2.query.filter(ReportV2.id.in_([id1, id2])).all()

    if len(reports) != 2:
        abort(404)

    report1, report2 = reports

    if not (
        current_user.has_report_permission(PermissionType.EDITOR, report1.id)
        or current_user.has_report_permission(PermissionType.EDITOR, report2.id)
    ):
        abort(403)

    db.session.add(ReportLink(report_1_id=id1, report_2_id=id2))
    db.session.add(ReportLink(report_2_id=id1, report_1_id=id2))
    db.session.commit()

    return None, 204


def remove_reports_link(id1: str, id2: str):
    id1 = UUID(id1)
    id2 = UUID(id2)

    links = (
        ReportLink.query.filter(ReportLink.report_1_id.in_([id1, id2]))
        .filter(ReportLink.report_2_id.in_([id1, id2]))
        .all()
    )

    if len(links) != 2:
        abort(404)

    if not (
        current_user.has_report_permission(PermissionType.EDITOR, id1)
        or current_user.has_report_permission(PermissionType.EDITOR, id2)
    ):
        abort(403)

    for link in links:
        db.session.delete(link)
    db.session.commit()

    return None, 204


def get_report_id_by_external_id(external_id: str) -> dict:
    organization_id = current_user.organization_id
    # For now let's allow only machine users to use this endpoint (the condition can be removed if needed)
    if not current_user.is_machine_user:
        abort(403)
    if report := ReportDAO.get_existing_report_for_client_submission_id(external_id, organization_id, True):
        return {"report_id": str(report.id)}
    abort(404, "Report not found for given external id")


def generate_report_extract(
    report_id: str,
    naics_sla: bool = False,
    report_share_sla: bool = False,
    send_slack_notifications: bool = True,
    skip_permissions_check: bool = False,
):
    quote_number = None
    submission_number = None
    if not skip_permissions_check and not current_user.is_machine_user:
        abort(403)

    if report_id in [str(FAKE_REPORT_ID), str(FAKE_REPORT_ID_2), str(FAKE_REPORT_ID_3)]:
        response = _get_fake_report_extract_response(report_id)
    else:
        report = get_report_or_404(UUID(report_id))
        submission = report.submission
        submission_number = submission.first_client_submission_id
        response = _get_report_extract_response(
            report, submission, naics_sla, report_share_sla, send_slack_notifications
        )
        quote_number_identifier = next(
            (
                iden
                for iden in submission.identifiers
                if iden.identifier_type == AdditionalIdentifierType.QUOTE_NUMBER.value
            ),
            None,
        )
        quote_number = quote_number_identifier.identifier if quote_number_identifier else None

    result = asdict(response)
    result["quote_number"] = quote_number
    result["submission_number"] = submission_number

    return result


def get_report_extract(id: str) -> dict:
    if id in [str(FAKE_REPORT_ID), str(FAKE_REPORT_ID_2), str(FAKE_REPORT_ID_3)]:
        response = _get_fake_report_extract_response(id)
    else:
        report = get_report_or_404(UUID(id))
        submission = report.submission
        if not current_user.has_report_permission(PermissionType.VIEWER, report.id):
            abort(403)
        response = _get_report_extract_response(report, submission)

    return asdict(response)


def get_report_extracts(report_ids: list[str] | None = None):
    if len(report_ids) > 100:
        flask.abort(400, "Report extracts can be requested for a maximum of 100 reports at the time")
    fake_report_ids = set()
    non_fake_ids = set()
    for report_id in report_ids:
        if report_id in [str(FAKE_REPORT_ID), str(FAKE_REPORT_ID_2), str(FAKE_REPORT_ID_3)]:
            fake_report_ids.add(report_id)
        else:
            non_fake_ids.add(report_id)

    if not current_user.has_permission_for_reports(PermissionType.VIEWER, non_fake_ids):
        flask.abort(403)

    reports = _get_reports_for_extract_query(report_ids=non_fake_ids, with_report_dependency=True).all()

    additional_reports_ids = set()
    for report in reports:
        if not report.submission.is_verified and report.processing_depends_on_report:
            additional_reports_ids.add(report.processing_depends_on_report.report_id)
    if additional_reports_ids:
        _get_reports_for_extract_query(report_ids=additional_reports_ids, with_report_dependency=False).all()

    response = []

    for report in reports:
        try:
            submission = report.submission
        except IndexError:
            response.append(
                asdict(
                    ReportExtractResponse(
                        id=str(report.id),
                        name=report.name,
                        stage=SubmissionStage.ON_MY_PLATE,
                    )
                )
            )
            continue

        report_response = _get_report_extract_response(report, submission)
        response.append(asdict(report_response))

    for fake_report_id in fake_report_ids:
        fake_response = _get_fake_report_extract_response(fake_report_id)
        response.append(asdict(fake_response))

    return response


def _get_reports_for_extract_query(report_ids: set[str], with_report_dependency: bool) -> Query:
    query = (
        ReportV2.query.options(
            joinedload(ReportV2.submission).lazyload(Submission.assigned_underwriters),
            lazyload(ReportV2.report_bundle),
        )
        .filter(ReportV2.id.in_(report_ids))
        .filter(ReportV2.is_deleted.isnot(True))
    )
    if with_report_dependency:
        query = query.options(joinedload(ReportV2.processing_depends_on_report))
    return query


def _get_report_extract_response(
    report: ReportV2,
    submission: Submission,
    naics_sla: bool = False,
    report_share_sla: bool = False,
    send_slack_notifications: bool = True,
) -> ReportExtractResponse:
    report_response = ReportExtractResponse(
        id=str(report.id),
        name=report.name,
        stage=submission.stage.value,
    )
    submission_verification_cutoff_date = pytz.UTC.localize(datetime.utcnow() - NW_SLA_EXPIRE_DELTA)
    is_sla_about_to_expire = submission.created_at < submission_verification_cutoff_date or report_share_sla
    fallback_submission = None
    if not submission.is_verified and report.processing_depends_on_report:
        fallback_submission = report.processing_depends_on_report.report.submission

    naics_verification_cutoff_date = pytz.UTC.localize(datetime.utcnow() - timedelta(minutes=105))
    is_verified = (
        not submission.is_verification_required
        or submission.is_naics_verified
        or (fallback_submission and fallback_submission.is_naics_verified)
    )
    is_past_naics_cutoff = (
        submission.primary_naics_code or (fallback_submission and fallback_submission.primary_naics_code)
    ) and submission.created_at < naics_verification_cutoff_date

    if is_verified or is_past_naics_cutoff:
        report_response.naics_code = submission.primary_naics_code or (
            fallback_submission.primary_naics_code if fallback_submission else None
        )

    email_file_cutoff_date = pytz.UTC.localize(datetime.utcnow() - timedelta(minutes=100))
    if not report.email_body and (submission.created_at < email_file_cutoff_date or naics_sla):
        report_response.naics_code = "NAICS_999999"
    default_risk_score = 40
    if (submission.recommendation_v2_score is not None and submission.is_verified) or is_sla_about_to_expire:
        report_response.risk_score = (
            max(min(submission.recommendation_v2_score, 100), 0)
            if submission.recommendation_v2_score is not None
            else default_risk_score
        )
        report_response.url = report.get_url()
        if (
            send_slack_notifications
            and report.email_body
            and not (submission.is_verified or submission.is_in_terminal_stage)
        ):
            message = (
                f"<!here> We shared unverified report with BOSS {report_response.url}. Email body found. Processing"
                f" state: {submission.processing_state}."
            )
            flask.current_app.slack_client.send_slack_message(channel=NATIONWIDE_SLACK_CHANNEL_ID, text=message)

    return report_response


def _get_fake_report_extract_response(id: str) -> ReportExtractResponse:
    risk_score = 50 if id in [str(FAKE_REPORT_ID), str(FAKE_REPORT_ID_3)] else 9999
    return ReportExtractResponse(
        id=id,
        name="Fake Report",
        stage=SubmissionStage.ON_MY_PLATE.value,
        naics_code="NAICS_111110",
        risk_score=risk_score,
    )


def get_emails_for_report(report_id: str):
    if not current_user.has_report_permission(PermissionType.VIEWER, report_id):
        abort(403)
    report = get_light_report(UUID(report_id))
    query = (
        db.session.query(Email)
        .options(joinedload(Email.status))
        .filter(Email.correspondence_id == report.correspondence_id, Email.is_deleted.isnot(True))
        .order_by(Email.created_at.desc())
    )
    return [EmailSchema().dump(item) for item in query.all()], 200


def get_shadow_or_shadowed_report(report_id: str):
    if not current_user.has_report_permission(PermissionType.VIEWER, report_id):
        abort(403)
    shadow_report_id = ReportDAO.get_shadow_report_id(report_id)
    shadowed_report_id = ReportDAO.get_shadowed_report_id(report_id)
    return {
        "shadow_report_id": shadow_report_id,
        "shadowed_report_id": shadowed_report_id,
    }


# noinspection PyUnusedLocal
def auto_set_name(
    id: str,
    body: dict | None = None,
    data_onboarding: bool = False,
    submission_businesses: Iterable[SubmissionBusiness] | None = None,
) -> tuple[None, int]:
    rid = UUID(id)
    report = get_report_or_404(rid)

    if report.submission.is_boss and not FeatureFlagsClient.is_feature_enabled(
        FeatureType.AUTO_SET_NAME_FOR_BOSS_SUBMISSIONS
    ):
        return None, 204

    if report.shadow_type == ReportShadowType.IS_ACTIVE_SHADOW:
        shadowed_report = report.shadow_origin_dependency.report if report.shadow_origin_dependency else None
        if shadowed_report and shadowed_report.submission.is_verified:
            return None, 204

    if (
        not current_user.is_internal_machine_user
        and not current_user.applicable_settings.is_cs_manager
        and not data_onboarding
    ):
        flask.abort(403)
    sbs = submission_businesses if submission_businesses else report.submission.businesses
    businesses: list[SubmissionBusiness] = [sb for sb in sbs if (sb.is_project or sb.is_fni or sb.is_gc)]
    report_name = _generate_name(businesses, report.submission.is_frozen)

    logger.info("Generated name for report", report_id=rid, name=report_name, len_businesses=len(businesses))
    if report_name:
        resp = update_report(id, {"name": report_name.upper()}, return_response=False)
        current_app.event_service.handle_submission_event(
            SubmissionEvent.SUBMISSION_UPDATED, report.submission, business=None, raw_submission=None
        )
        return resp
    return None, 204


def _get_project_gci_fni(
    businesses: list[SubmissionBusiness],
) -> tuple[SubmissionBusiness | None, SubmissionBusiness | None, SubmissionBusiness | None]:
    project: SubmissionBusiness | None = None
    gc: SubmissionBusiness | None = None
    fni: SubmissionBusiness | None = None
    # sort by id and put requested_name = None in the front (False < True)
    # order of requested/resolved attributes priority starting from least to most priority:
    # 1. requested_name
    # 2. resolved_name
    # 4. requested_address
    # 4. resolved_address
    # this means that the records that have requested_name are last then comes resolved_name
    # then requested_address and last resolved_address
    for sb in sorted(
        businesses,
        key=lambda x: (
            x.requested_name is not None,
            x.resolved_name is not None,
            x.requested_address is not None,
            x.resolved_address is not None,
            x.id,
        ),
    ):
        project = sb if sb.entity_role == SubmissionBusinessEntityRole.PROJECT else project
        gc = sb if sb.entity_role == SubmissionBusinessEntityRole.GENERAL_CONTRACTOR else gc
        fni = sb if sb.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED else fni
    return project, gc, fni


def _generate_name(businesses: list[SubmissionBusiness], is_submission_frozen: bool) -> str | None:
    project, gc, fni = _get_project_gci_fni(businesses)
    if not project and fni:
        result = fni.get_name_for_report()
        if not result:
            enriched_businesses = (
                enrich_businesses_with_ers_snapshot_data(businesses)
                if is_submission_frozen
                else enrich_businesses_with_ers_data(businesses)
            )
            _, _, fni = _get_project_gci_fni(enriched_businesses)
            result = fni.get_name_for_report(use_resolved_attributes=True) if fni else None
        return result

    if project and project.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED and gc:
        gc_name = gc.get_name_for_report()
        project_name = project.get_name_for_report(use_address=True)
        return f"GC: {gc_name}, PROJECT: {project_name}" if gc_name and project_name else None
    if project and project.named_insured != SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED and fni:
        owner_name = fni.get_name_for_report()
        project_name = project.get_name_for_report(use_address=True)
        return f"OWNER: {owner_name}, PROJECT: {project_name}" if owner_name and project_name else None
    return None


def get_rush_report_evidence(id: str) -> dict | None:
    report: ReportV2 = get_report_or_404(UUID(id))
    if not current_user.has_report_permission(PermissionType.VIEWER, id):
        abort(403)

    if not report.is_rush:
        return None

    if report.rush_source == RushSource.USER:
        return {"excerpt": "The flag was set by a user"}

    if report.rush_source == RushSource.EFFECTIVE_DATE:
        effective_date = report.submission.proposed_effective_date
        created_at = report.created_at.replace(tzinfo=None)

        if not effective_date:
            return None

        created_at = datetime(created_at.year, created_at.month, created_at.day)
        effective_date = datetime(effective_date.year, effective_date.month, effective_date.day)

        days = (effective_date - created_at).days
        day_variant = "day" if days == 1 else "days"

        return {
            "excerpt": (
                f'{days} {day_variant} from submission creation ({created_at.strftime("%x")}) to effective date'
                f' ({effective_date.strftime("%x")})'
            )
        }

    email = Email.query.filter(Email.correspondence_id == report.correspondence_id, Email.type == "ROOT").first()
    if not email:
        return None

    rules = (
        RoutingRule.query.filter(
            or_(RoutingRule.organization_id == report.organization_id, RoutingRule.organization_id.is_(None)),
            RoutingRule.is_rush == True,
        )
        .order_by(RoutingRule.order)
        .all()
    )

    rule: RoutingRule
    for rule in rules:
        if not rule.contained_phrases:
            continue

        for phrase in rule.contained_phrases:
            found = get_matching_excerpt(email.email_subject, phrase[0], 5) or get_matching_excerpt(
                email.email_body, phrase[0], 5
            )
            if found:
                return found

    return {"excerpt": "Submission was manually sent to Copilot as urgent"}


def update_report_status_external(report_id: str, body: dict):
    log = logger.bind(report_id=report_id, user_id=current_user.id)
    report_id = UUID(report_id)
    report = get_report_or_404(report_id)
    if not current_user.has_report_permission(PermissionType.EDITOR, report.id):
        abort(403)

    config = CUSTOM_STATUS_FIELDS_PER_CLIENT.get(report.organization_id)

    if not config:
        abort(400, "Custom status is not supported for this organization")

    custom_status: ExternalCustomStatus = external_custom_status_schema.load(body)
    status_array = []
    for field in config.status_fields:
        value = custom_status.key_value_pairs.get(field)
        if value:
            status_array.append(value)

    if not status_array:
        abort(400, "Missing status fields")

    # get all client statuses for the organization
    client_statuses: list[ClientSubmissionStageConfig] = ClientSubmissionStageConfig.query.filter(
        ClientSubmissionStageConfig.organization_id == report.organization_id
    ).all()

    # get the client status that matches the status array
    matched_status: ClientSubmissionStageConfig | None = next(
        (status for status in client_statuses if status.status_array == status_array), None
    )

    if not matched_status:
        abort(400, "Cannot find status mappings for the given status fields")

    try:
        from copilot.v3.controllers.submissions import update_submission_internal

        update_body = {
            "client_stage_id": matched_status.id,
            "client_stage_comment": custom_status.key_value_pairs.get(config.client_stage_comment),
        }

        if report.submission.is_verified or report.submission.is_verified_shell:
            update_body["stage"] = matched_status.copilot_stage

        update_submission_internal(
            str(report.submission.id),
            update_body,
            is_external=True,
        )
    except:
        db.session.rollback()
        log.exception("Failed to update submission")
        flask.abort(422, "Failed to update submission")

    return {"status": "Success"}, 200


def handle_org_group_split(submission: Submission) -> None:
    if (
        submission.organization_id != ExistingOrganizations.BowheadSpecialty.value
        or not FeatureFlagsClient.is_feature_enabled(FeatureType.SPLIT_BOWHEAD_GROUPS)
    ):
        return
    recipients_and_senders = EmailsDAO.get_recipients_and_senders_for_correspondence(
        str(submission.report.correspondence_id)
    )
    org_groups = OrganizationGroups.get_from_email_recipients(submission.organization_id, recipients_and_senders)
    existing_reports = (
        ReportV2.query.filter(
            ReportV2.correspondence_id == submission.report.correspondence_id, ReportV2.is_deleted.isnot(True)
        )
        .options(load_only(ReportV2.org_group))
        .all()
    )
    existing_org_groups = {r.org_group for r in existing_reports}
    new_org_groups = [g for g in org_groups if g.value not in existing_org_groups]
    for org_group in new_org_groups:
        new_create_report_request = CreateReportRequest(
            name=submission.report.name,
            origin=submission.origin,
            email_subject=submission.report.email_subject,
            email_message_id=submission.report.email_message_id,
            email_references=submission.report.email_references,
            correspondence_id=submission.report.correspondence_id,
            org_group=org_group.value,
        )
        new_report = _create_new_report(new_create_report_request, submission.report.owner, None)
        new_report.email_body = submission.report.email_body
        new_submission = new_report.submission
        submission.copy_files(
            new_submission,
            old_to_new_ids={submission.id: new_submission.id},
            full_copy=True,
            only_parent_files=True,
            for_shadow=False,
            emit_events=False,
            should_copy_processed_data=False,
        )
        for file in new_submission.files:
            file.processing_state = (
                FileProcessingState.NOT_CLASSIFIED if file.classification is None else FileProcessingState.CLASSIFIED
            )
        add_processing_dependency(submission.report, new_report, ReportDependencyType.SAME_ORG)
        db.session.commit()
        for file in new_submission.files:
            FileHandler().handle_new_file_added(new_submission, file)
        assign_coverages(new_submission.id, allow_no_coverages=True, source=SubmissionCoverageSource.AUTO)


def mark_report_emails_as_not_submission(report_id: str, body: dict) -> tuple[None, int]:
    from copilot.logic.pds.email_handler import EmailHandler

    report = ReportV2.query.options(joinedload(ReportV2.submission)).get_or_404(UUID(report_id))

    if not current_user.has_report_permission(PermissionType.EDITOR, report.id):
        flask.abort(403)

    explanation = (
        f"Manually set by {current_user.name} on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')},"
        f" {body.get('reason', '')}"
    )

    emails = Email.query.filter(Email.correspondence_id == report.correspondence_id).all()
    for email in emails:
        EmailHandler.maybe_mark_as_not_submission(
            email=email,
            organization_id=report.organization_id,
            explanation=explanation,
            submission=report.submission,
            is_manual=True,
        )

    db.session.commit()
    return None, HTTPStatus.NO_CONTENT


def get_pds_debugger_files_data(report_id: str | UUID) -> list[dict[str, Any]]:
    uuid_report_id = UUID(report_id) if not isinstance(report_id, UUID) else report_id
    if not current_user.has_report_permission(PermissionType.VIEWER, uuid_report_id):
        flask.abort(403)
    submission_id = db.session.query(Submission.id).filter(Submission.report_id == uuid_report_id)
    processed_files = ProcessedFileDAO.get_processed_files_data_for_pds_debugger(submission_id)

    return PdsDebuggerFileDataSchema().dump(processed_files, many=True)
