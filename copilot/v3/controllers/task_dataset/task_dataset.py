import uuid

from flask import abort, current_app
from sqlalchemy.orm import load_only

from copilot.logic.task_metrics.task_metrics_calculator import (
    MetricsCalculatorContext,
    TaskDefinitionMetricsCalculator,
)
from copilot.models import db
from copilot.models.task_dataset import (
    TaskDataset,
    TaskDatasetExecution,
    TaskDatasetGroundTruth,
    TaskDatasetInput,
)
from copilot.models.tasks import TaskDefinition
from copilot.models.types import TaskDatasetExecutionStatus


def init_task_dataset_execution(body: dict) -> tuple[dict, int]:
    task_dataset_id = body["task_dataset_id"]
    task_definition_id = body["task_definition_id"]
    execution_arn = body["execution_arn"]
    task_definition = TaskDefinition.query.options(load_only(TaskDefinition.code)).get_or_404(task_definition_id)
    existing_dataset_execution = TaskDatasetExecution.query.filter(
        TaskDatasetExecution.task_dataset_id == task_dataset_id,
        TaskDatasetExecution.task_definition_id == task_definition_id,
        TaskDatasetExecution.status == TaskDatasetExecutionStatus.PENDING,
    ).first()
    if existing_dataset_execution:
        abort(409, "Task dataset execution already exists")

    task_dataset_inputs = (
        TaskDatasetInput.query.join(TaskDataset, TaskDataset.dataset_inputs_id == TaskDatasetInput.dataset_inputs_id)
        .filter(TaskDataset.id == task_dataset_id)
        .options(load_only(TaskDatasetInput.id))
        .all()
    )

    if not task_dataset_inputs:
        abort(404, f"Task dataset inputs not found for task dataset ID: {task_dataset_id}")

    task_dataset_execution = TaskDatasetExecution(
        task_dataset_id=task_dataset_id,
        task_definition_id=task_definition_id,
        execution_arn=execution_arn,
        status=TaskDatasetExecutionStatus.PENDING,
    )
    db.session.add(task_dataset_execution)
    db.session.commit()

    return {
        "task_definition_code": task_definition.code,
        "task_dataset_execution_id": str(task_dataset_execution.id),
        "task_dataset_input_ids": [str(task_dataset_input.id) for task_dataset_input in task_dataset_inputs],
    }, 201


def cancel_task_dataset_execution(task_dataset_execution_id: str) -> tuple[None, int]:
    task_dataset_execution = TaskDatasetExecution.query.get(task_dataset_execution_id)

    if not task_dataset_execution:
        abort(404, f"Task dataset execution not found for ID: {task_dataset_execution_id}")
    if task_dataset_execution.status != TaskDatasetExecutionStatus.PENDING:
        abort(400, "Task dataset execution is not in a pending state")

    current_app.workflows_client.stop_execution(
        task_dataset_execution.execution_arn, "TaskDatasetExecutionCanceled", "Task dataset execution canceled"
    )
    task_dataset_execution.status = TaskDatasetExecutionStatus.CANCELLED
    db.session.commit()
    return None, 204


def complete_task_dataset_execution(task_dataset_execution_id: str) -> tuple[None, int]:
    task_dataset_execution = TaskDatasetExecution.query.get(task_dataset_execution_id)
    if not task_dataset_execution:
        abort(404, f"Task dataset execution not found for ID: {task_dataset_execution_id}")
    if task_dataset_execution.status != TaskDatasetExecutionStatus.PENDING:
        abort(400, "Task dataset execution is not in a pending state")
    metrics_context = MetricsCalculatorContext.from_task_dataset_execution(task_dataset_execution_id)
    metrics_calculator = TaskDefinitionMetricsCalculator(metrics_context)
    task_definition_metrics = metrics_calculator.calculate_metrics()
    db.session.add(task_definition_metrics)
    task_dataset_execution.status = TaskDatasetExecutionStatus.COMPLETED
    db.session.commit()
    return None, 204


def create_dataset_with_inputs_and_ground_truths(body: dict) -> tuple[dict, int]:
    """
    Create a new TaskDataset with associated TaskDatasetInputs and TaskDatasetGroundTruths.

    Args:
        body: A dictionary containing:
            - description: String description of the dataset
            - task_dataset_id: string ID of the task dataset
            - task_dataset_inputs_id: string ID of the task dataset inputs
            - processing_type: String indicating processing type
            - output_type: String indicating output type
            - inputs: List of dictionaries, each containing:
                - input: JSON object representing the input
                - submission_id: UUID of the submission
                - file_id: UUID of the file
                - ground_truth: JSON object representing the ground truth

    Returns:
        A tuple containing:
            - JSON response with created dataset ID
            - HTTP status code
    """
    # Extract parameters from request body
    description = body.get("description")
    task_dataset_id = body.get("task_dataset_id")
    task_dataset_inputs_id = body.get("task_dataset_inputs_id")
    processing_type = body.get("processing_type")
    output_type = body.get("output_type")
    inputs_with_ground_truths = body.get("inputs", [])

    # Validate required fields
    if not description or not processing_type or not output_type:
        abort(400, "Missing required fields: description, processing_type, or output_type")

    if not inputs_with_ground_truths:
        abort(400, "At least one input with ground truth is required")

    if not task_dataset_id:
        # Generate a UUID for dataset_inputs_id to link inputs
        if not task_dataset_inputs_id:
            task_dataset_inputs_id = uuid.uuid4()
        # Create the TaskDataset
        task_dataset = TaskDataset(
            description=description,
            processing_type=processing_type,
            output_type=output_type,
            dataset_inputs_id=task_dataset_inputs_id,
        )
        db.session.add(task_dataset)
        db.session.flush()  # Flush to get the task_dataset.id
        task_dataset_id = task_dataset.id

    # Create TaskDatasetInputs and TaskDatasetGroundTruths
    for item in inputs_with_ground_truths:
        input = item.get("input")
        submission_id = item.get("submission_id")
        file_id = item.get("file_id")
        business_id = item.get("business_id")
        ground_truth_data = item.get("ground_truth")
        context = item.get("context")

        task_dataset_input_id = None
        if body.get("task_dataset_inputs_id"):
            existing_task_dataset_input = TaskDatasetInput.query.filter_by(
                dataset_inputs_id=task_dataset_inputs_id, submission_id=submission_id, file_id=file_id
            ).one_or_none()
            task_dataset_input_id = existing_task_dataset_input.id if existing_task_dataset_input else None
        if not task_dataset_input_id:
            # Create TaskDatasetInput
            task_dataset_input = TaskDatasetInput(
                dataset_inputs_id=task_dataset_inputs_id,
                input=input,
                submission_id=submission_id,
                file_id=file_id,
                business_id=business_id,
                context=context,
            )
            db.session.add(task_dataset_input)
            db.session.flush()  # Flush to get task_dataset_input.id
            task_dataset_input_id = task_dataset_input.id

        # Create TaskDatasetGroundTruth
        has_value = ground_truth_data is not None
        task_dataset_ground_truth = TaskDatasetGroundTruth(
            task_dataset_id=task_dataset_id,
            task_dataset_input_id=task_dataset_input_id,
            value=ground_truth_data,
            has_value=has_value,
        )
        db.session.add(task_dataset_ground_truth)

    # Commit all changes
    db.session.commit()

    return {"id": str(task_dataset_id)}, 201
