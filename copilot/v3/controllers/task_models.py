from http import HTTPStatus

from flask import abort
from infrastructure_common.logging import get_logger

from copilot.models import db
from copilot.models.tasks import TaskModel
from copilot.schemas.tasks import TaskModelSchema

logger = get_logger()


def create_task_model(body: dict) -> tuple[dict, int]:
    try:
        task_definition_model = TaskModelSchema().load(body)
    except Exception:
        logger.exception("Failed to create task model")
        abort(HTTPStatus.BAD_REQUEST, "Could not create task model")

    try:
        db.session.add(task_definition_model)
        db.session.commit()
    except Exception:
        db.session.rollback()
        logger.exception("Failed to create task model")
        raise
    return TaskModelSchema().dump(task_definition_model), HTTPStatus.CREATED


def get_task_model(task_model_id: str) -> tuple[dict, int]:
    task_model = TaskModel.query.get_or_404(task_model_id)
    return TaskModelSchema().dump(task_model), HTTPStatus.OK


def update_task_model(task_model_id: str, body: dict) -> tuple[dict, int]:
    task_model = TaskModel.query.get_or_404(task_model_id)

    try:
        TaskModelSchema().load(body, instance=task_model)
    except Exception:
        db.session.rollback()
        logger.exception("Failed to update task model")
        abort(HTTPStatus.BAD_REQUEST, "Could not update task model")
    db.session.commit()
    return TaskModelSchema().dump(task_model), HTTPStatus.OK


def delete_task_model(task_model_id: str) -> HTTPStatus:
    task_model = TaskModel.query.get_or_404(task_model_id)

    try:
        db.session.delete(task_model)
        db.session.commit()
    except Exception:
        db.session.rollback()
        logger.exception("Failed to delete task model")

    return HTTPStatus.NO_CONTENT
