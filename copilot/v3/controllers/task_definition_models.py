from http import HTT<PERSON>tatus
from uuid import UUID

from flask import abort
from infrastructure_common.logging import get_logger

from copilot.models import db
from copilot.models.tasks import TaskDefinitionModel
from copilot.schemas.tasks import TaskDefinitionModelSchema

logger = get_logger()


def create_task_definition_model(body: dict, task_definition_id: str) -> tuple[dict, int]:
    try:
        task_definition_model = TaskDefinitionModelSchema().load(body)
    except Exception:
        logger.exception("Failed to create task definition model")
        abort(HTTPStatus.BAD_REQUEST, "Could not create task definition model")

    task_definition_id = UUID(task_definition_id)
    task_definition_model.task_definition_id = task_definition_id
    try:
        db.session.add(task_definition_model)
        db.session.commit()
    except Exception:
        db.session.rollback()
        logger.exception("Failed to create task definition model")
        raise
    return TaskDefinitionModelSchema().dump(task_definition_model), HTTPStatus.CREATED


def get_task_definition_model(task_definition_model_id: str) -> tuple[dict, int]:
    task_definition_model = TaskDefinitionModel.query.get_or_404(task_definition_model_id)

    return TaskDefinitionModelSchema().dump(task_definition_model), HTTPStatus.OK


def update_task_definition_model(task_definition_model_id: str, body: dict) -> tuple[dict, int]:
    task_definition_model = TaskDefinitionModel.query.get_or_404(task_definition_model_id)

    try:
        TaskDefinitionModelSchema().load(body, instance=task_definition_model)
    except Exception:
        db.session.rollback()
        logger.exception("Failed to update task definition model")
        abort(HTTPStatus.BAD_REQUEST, "Could not update task definition model")

    db.session.commit()
    return TaskDefinitionModelSchema().dump(task_definition_model), HTTPStatus.OK


def delete_task_definition_model(task_definition_model_id: str) -> HTTPStatus:
    task_definition_model = TaskDefinitionModel.query.get_or_404(task_definition_model_id)

    try:
        db.session.delete(task_definition_model)
        db.session.commit()
    except Exception:
        db.session.rollback()
        logger.exception("Failed to delete task definition model")
        abort(HTTPStatus.BAD_REQUEST, "Could not delete task definition model")
    return HTTPStatus.NO_CONTENT
