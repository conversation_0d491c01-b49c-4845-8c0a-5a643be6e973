from static_common.enums.async_ops import AsyncOperation

from copilot.schemas.async_ops import (
    AsyncOpCreateResponseSchema,
    AsyncOpFullDetailsSchema,
    AsyncOpLowDetailsSchema,
)
from copilot.services.async_ops import AsyncOpsService

async_op_create_response_schema = AsyncOpCreateResponseSchema()
async_op_full_details_schema = AsyncOpFullDetailsSchema()
async_op_low_details_schema = AsyncOpLowDetailsSchema()


def get_async_op_by_id(async_op_id: str) -> tuple[dict, int]:
    async_op = AsyncOpsService.get_async_op_by_id(async_op_id)
    if not async_op:
        return {}, 404
    return async_op_full_details_schema.dump(async_op), 200


def create_new_async_op(body: dict) -> tuple[dict, int]:
    async_op = AsyncOpsService.create_new_async_op(body, commit=True)
    return async_op_create_response_schema.dump(async_op), 201


def get_latest_async_op_by_report_and_operation(report_id: str, operation: str) -> tuple[dict, int]:
    async_op = AsyncOpsService.get_latest_async_op_by_report_and_operation(report_id, AsyncOperation(operation))
    if not async_op:
        return {}, 404
    return async_op_low_details_schema.dump(async_op), 200


def update_async_op_by_id(async_op_id: str, body: dict) -> tuple[dict, int]:
    async_op = AsyncOpsService.update_async_op_by_id(async_op_id, body, commit=True)
    if not async_op:
        return {}, 404
    return async_op_create_response_schema.dump(async_op), 200
