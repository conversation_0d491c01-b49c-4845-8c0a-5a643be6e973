from http import HTTPStatus
import json

from flask import Response
from flask_login import current_user
from infrastructure_common.logging import get_logger
from pydantic import ValidationError
from sqlalchemy import and_, func, select
from sqlalchemy.orm import aliased
from static_common.enums.insights_document_type import InsightsDocumentType
from static_common.enums.organization import ExistingOrganizations
import flask

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.models import Submission, db
from copilot.models.custom_file_types import CustomizableClassifiersV2InputTypes
from copilot.models.files import CustomFileType, File
from copilot.schemas.files import AggregatedCustomFileTypeSchema, CustomFileTypeSchema

logger = get_logger()

custom_file_type_schema = CustomFileTypeSchema()


def create_or_update_custom_file_type(body: dict) -> Response:
    try:
        request: CustomFileType = custom_file_type_schema.load(body)
    except ValidationError as e:
        flask.abort(HTTPStatus.BAD_REQUEST, f"The payload is not in the desired format - {e!s}")

    if existing_type := CustomFileType.query.filter_by(
        file_type_name=request.file_type_name, organization_id=request.organization_id
    ).first():
        if existing_type.is_enabled and request.is_enabled:
            flask.abort(HTTPStatus.CONFLICT, f"Custom file type {request.file_type_name} already exists")
        else:
            existing_type.is_enabled = request.is_enabled
            db.session.commit()
            return Response(custom_file_type_schema.dumps(existing_type), status=HTTPStatus.CREATED)
    else:
        logger.info("Creating new custom file type", file_type_name=request.file_type_name)
        custom_file_type = CustomFileType(
            file_type_name=request.file_type_name,
            organization_id=request.organization_id,
            is_enabled=request.is_enabled,
        )
        db.session.add(custom_file_type)
        db.session.commit()
        return Response(custom_file_type_schema.dumps(custom_file_type), status=HTTPStatus.CREATED)


def get_aggregated_custom_file_types(organization_id: int, enabled_only: bool = True):
    base_filters = [
        CustomFileType.organization_id == int(organization_id),
    ]
    if enabled_only:
        base_filters.append(CustomFileType.is_enabled == True)

    SubFile = aliased(File)
    subquery = (
        select(
            [
                func.json_build_object(
                    "id",
                    SubFile.id,
                    "name",
                    SubFile.name,
                    "submission_id",
                    SubFile.submission_id,
                )
            ]
        )
        .join(Submission, and_(Submission.id == SubFile.submission_id, Submission.is_deleted == False))
        .where(
            SubFile.organization_id == int(organization_id),
            SubFile.is_deleted == False,
            SubFile.custom_file_type_id == CustomFileType.id,
        )
        .order_by(SubFile.id)
        .limit(5)
        .scalar_subquery()
    )

    query = (
        db.session.query(
            CustomFileType.id.label("file_type_id"),
            CustomFileType.file_type_name.label("file_type"),
            func.count(File.id)
            .filter(
                and_(
                    File.is_deleted == False,
                    Submission.is_deleted == False,
                )
            )
            .label("file_count"),
            func.min(CustomFileType.created_at).label("created_at"),
            func.array(subquery).label("sampled_files"),
        )
        .outerjoin(
            File,
            and_(
                File.custom_file_type_id == CustomFileType.id,
                File.organization_id == int(organization_id),
                File.is_deleted == False,
            ),
        )
        .outerjoin(Submission, and_(Submission.id == File.submission_id, Submission.is_deleted == False))
        .where(*base_filters)
        .group_by(CustomFileType.id, CustomFileType.file_type_name)
    )

    result = query.all()
    return AggregatedCustomFileTypeSchema().dump(result, many=True), HTTPStatus.OK


def get_custom_file_types_for_org(organization_id: int, enabled_only: bool = True) -> Response:
    query = CustomFileType.query.filter_by(organization_id=organization_id)

    if enabled_only:
        query = query.filter_by(is_enabled=True)

    custom_file_types = query.all()
    return Response(custom_file_type_schema.dumps(custom_file_types, many=True), status=HTTPStatus.OK)


def get_insights_document_types(organization_id: int, enabled_only: bool = True) -> Response:
    query = CustomFileType.query.filter_by(organization_id=organization_id)

    if enabled_only:
        query = query.filter_by(is_enabled=True)

    custom_file_types = query.all()
    if FeatureFlagsClient.is_feature_enabled(FeatureType.CUSTOMIZABLE_CLASSIFIERS_V2, current_user.email):
        insights_document_types = [
            item.value for item in InsightsDocumentType.file_types() + InsightsDocumentType.third_party_documents()
        ]
    elif organization_id == ExistingOrganizations.MarkelDemo.value:
        insights_document_types = [elem.value for elem in InsightsDocumentType.file_types()]
    else:
        insights_document_types = [
            item.value
            for item in InsightsDocumentType.first_party_documents() + InsightsDocumentType.third_party_documents()
        ]
    # allowing only _PDF classification through to the classifiers
    custom_types = [f"Custom - {item.file_type_name}" for item in custom_file_types]
    return Response(
        json.dumps(insights_document_types + custom_types), status=HTTPStatus.OK, mimetype="application/json"
    )


def get_customizable_classifiers_v2_input_types(organization_id: int, enabled_only: bool = True) -> Response:
    query = CustomFileType.query.filter_by(organization_id=organization_id)

    if enabled_only:
        query = query.filter_by(is_enabled=True)

    custom_file_types = query.all()
    custom_types = [f"Custom - {item.file_type_name}" for item in custom_file_types]
    file_types = [ft.value for ft in InsightsDocumentType.file_types()] + custom_types
    document_types = [dt.value for dt in InsightsDocumentType.third_party_documents()]

    response = CustomizableClassifiersV2InputTypes(
        file_types=file_types,
        document_types=document_types,
    )

    return Response(response.model_dump_json(), status=HTTPStatus.OK, mimetype="application/json")
