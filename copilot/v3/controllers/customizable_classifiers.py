from http import HTTPStatus

from infrastructure_common.logging import get_logger
from marshmallow import ValidationError
from more_itertools import flatten
from sqlalchemy import Integer, String, cast, or_
from sqlalchemy.dialects.postgresql import ARRAY, array
from sqlalchemy.orm import selectinload
from static_common.enums.classification import ExtractionType, FilterRuleType
import flask

from copilot.logic.classifiers.classifiers import create_task_definitions
from copilot.logic.classifiers.configuration import configure_classifier
from copilot.models import (
    ClassifierConfig,
    ClassifierToConfigVersion,
    ClassifierVersion,
    CustomizableClassifierV2,
    db,
)
from copilot.models.customizable_classifiers import ClassifierVersionTaskDefinition
from copilot.models.get_submissions_for_dataset import (
    GetSubmissionsForDatasetRequest,
    GetSubmissionsForDatasetRequestSchema,
)
from copilot.models.reports import (
    Coverage,
    File,
    ReportV2,
    Submission,
    SubmissionCoverage,
)
from copilot.schemas.customizable_classifiers import CustomizableClassifierV2Schema
from copilot.schemas.report import SubmissionSchema
from copilot.v3.controllers.customization import (
    check_classifier_permissions_at_least_manager,
)

customizable_classifier_schema = CustomizableClassifierV2Schema()

logger = get_logger()


def get_customizable_classifiers_v2(
    org_id: int | None = None,
    input_types: list[str] | None = None,
    pds_only: bool = False,
    active_only: bool = False,
    fact_subtype_id: str | None = None,
):
    query = db.session.query(CustomizableClassifierV2)
    if input_types:
        query = query.filter(CustomizableClassifierV2.input_types.op("&&")(cast(array(input_types), ARRAY(String))))
    if org_id:
        check_classifier_permissions_at_least_manager(org_id)
        query = query.filter(
            or_(
                CustomizableClassifierV2.organization_ids.op("&&")(cast(array([org_id]), ARRAY(Integer))),
                CustomizableClassifierV2.is_internal == True,
            )
        )
    else:
        query = query.filter(CustomizableClassifierV2.is_internal == True)

    if pds_only:
        query = query.filter(CustomizableClassifierV2.run_in_pds == True)

    if fact_subtype_id:
        query = query.filter(CustomizableClassifierV2.fact_subtype_id == fact_subtype_id)

    db_classifiers: list[CustomizableClassifierV2] = query.all()

    serialized_data: list[dict] = customizable_classifier_schema.dump(db_classifiers, many=True)

    result: list[dict] = []
    if active_only:
        for classifier in serialized_data:
            active_version = classifier.get("active_version")
            if not active_version:
                continue

            relevant_configs = []
            for config in active_version.get("configs", []):
                if input_types and any(input_type in config["input_types"] for input_type in input_types):
                    config_active_versions = config.get("active_versions", [])
                    config["versions"] = config_active_versions
                    relevant_configs.append(config)

            active_version["configs"] = relevant_configs

            if active_version["configs"]:
                result.append(classifier)
    else:
        result = serialized_data

    return result, HTTPStatus.OK


# DELETE /api/v3.0/customizable-classifiers-v2/:id
def delete_customizable_classifier_v2(id: str):
    classifier = CustomizableClassifierV2.query.get_or_404(id)

    configs = flatten([v.configs for v in classifier.versions])
    config_ids = {str(config.id) for config in configs}

    org_ids = classifier.organization_ids
    org_id = org_ids[0] if len(org_ids) == 1 else None
    check_classifier_permissions_at_least_manager(org_id)
    db.session.delete(classifier)
    ClassifierConfig.query.filter(ClassifierConfig.id.in_(config_ids)).delete()
    db.session.commit()
    return None, HTTPStatus.NO_CONTENT


# DELETE /api/v3.0/customizable-classifiers-v2/:id/versions/:version_id
def delete_classifier_version(classifier_id: str, version_id: str):
    classifier = CustomizableClassifierV2.query.get_or_404(classifier_id)
    version_to_delete = ClassifierVersion.query.get_or_404(version_id)

    try:
        for config in version_to_delete.configs:
            linked_classifier_version_ids = {
                str(mapping.classifier_version_id) for mapping in config.classifier_to_config_versions
            }
            if len(linked_classifier_version_ids) == 1:
                logger.info("Existing classifier version is the only one linked to config, deleting config")
                for config_version in config.versions:
                    # cascades seem to have some issue here, so we need to delete the phrases manually
                    if config_version.extraction_type in [ExtractionType.PHRASES, ExtractionType.PHRASES_WITH_LLM]:
                        for phrase in config_version.phrases:
                            db.session.delete(phrase)
                            db.session.flush()
                    db.session.delete(config_version)
                    db.session.flush()
                db.session.delete(config)
                db.session.flush()
            else:
                logger.info("Existing classifier version is not the only one linked to config, not deleting config")
                continue

        ClassifierVersionTaskDefinition.query.filter_by(classifier_version_id=version_id).delete()
        db.session.flush()

        db.session.refresh(version_to_delete)
        db.session.delete(version_to_delete)
        if len(classifier.versions) == 1:
            db.session.flush()
            db.session.delete(classifier)

        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger.exception("Failed to delete classifier version", classifier_id=classifier_id, version_id=version_id)
        return flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, f"Failed to delete classifier version: {e}")
    return None, HTTPStatus.NO_CONTENT


# POST /api/v3.0/customizable-classifiers-v2
def create_customizable_classifier_v2(body: dict):
    if org_ids := body.get("organization_ids"):
        org_id = org_ids[0] if len(org_ids) == 1 else None
        check_classifier_permissions_at_least_manager(org_id)
    try:
        classifier_db_model = customizable_classifier_schema.load(body)
        configs = list(flatten([v.configs for v in classifier_db_model.versions]))
    except ValidationError as e:
        flask.abort(HTTPStatus.BAD_REQUEST, f"Invalid request body: {e!s}.")

    if classifier_db_model.fact_subtype_id is not None:
        existing_classifier_to_fact = CustomizableClassifierV2.query.filter(
            CustomizableClassifierV2.fact_subtype_id == classifier_db_model.fact_subtype_id,
            CustomizableClassifierV2.organization_ids.op("&&")(classifier_db_model.organization_ids),
        ).first()
    else:
        existing_classifier_to_fact = CustomizableClassifierV2.query.filter(
            CustomizableClassifierV2.extracted_value_name == classifier_db_model.extracted_value_name,
            CustomizableClassifierV2.organization_ids.op("&&")(classifier_db_model.organization_ids),
        ).first()
    if existing_classifier_to_fact is not None:
        return flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, "Classifier with the same fact subtype id already exists")

    try:
        db.session.add(classifier_db_model)
        db.session.flush()
        _link_existing_configs_to_classifier(classifier_db_model, classifier_db_model.versions, configs)
        configure_classifier(classifier_db_model)

        for version in classifier_db_model.versions:
            create_task_definitions(version)

        db.session.commit()
    except Exception:
        db.session.rollback()
        return flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, "Failed to create classifier")

    return customizable_classifier_schema.dump(classifier_db_model), HTTPStatus.CREATED


def update_customizable_classifier_v2(id: str, body: dict):
    if org_ids := body.get("organization_ids"):
        org_id = org_ids[0] if len(org_ids) == 1 else None
        check_classifier_permissions_at_least_manager(org_id)

    if body.get("id") != id:
        flask.abort(422, "ID in URL and body do not match")

    body.pop("id")

    db_classifier = CustomizableClassifierV2.query.get_or_404(id)

    try:
        request_classifier = customizable_classifier_schema.load(body)
        configs = list(flatten([v.configs for v in request_classifier.versions]))
    except ValidationError as e:
        flask.abort(HTTPStatus.BAD_REQUEST, f"Invalid request body: {e!s}.")

    try:
        db_classifier.versions.extend(request_classifier.versions)
        db.session.flush()

        _link_existing_configs_to_classifier(db_classifier, request_classifier.versions, configs)
        configure_classifier(db_classifier)

        for version in request_classifier.versions:
            create_task_definitions(version)

        db.session.commit()
    except Exception:
        db.session.rollback()
        return flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, "Failed to update classifier")

    return customizable_classifier_schema.dump(db_classifier), HTTPStatus.OK


def _link_existing_configs_to_classifier(
    classifier: CustomizableClassifierV2, classifier_versions: list[ClassifierVersion], configs: list[ClassifierConfig]
):
    db.session.refresh(classifier)
    # this is ugly but I'm not able to find a better way to create all the links
    mappings = {}
    for config in configs:
        db.session.refresh(config)
        for version in config.versions:
            for classifier_version in classifier_versions:
                mappings[(str(classifier.versions[0].id), str(config.id), str(version.id))] = ClassifierToConfigVersion(
                    classifier_version_id=classifier_version.id,
                    classifier_config_id=config.id,
                    classifier_config_version_id=version.id,
                )
    db.session.add_all(mappings.values())
    db.session.flush()


def get_customizable_classifier_v2_by_id(id: str):
    db_classifier = CustomizableClassifierV2.query.get_or_404(id)
    org_ids = db_classifier.organization_ids
    org_id = org_ids[0] if len(org_ids) == 1 else None
    check_classifier_permissions_at_least_manager(org_id)
    return customizable_classifier_schema.dump(db_classifier), HTTPStatus.OK


# POST /api/v3.0/customizable-classifiers-v2/dataset
def get_submissions_for_dataset(
    body: dict,
) -> dict:
    try:
        request: GetSubmissionsForDatasetRequest = GetSubmissionsForDatasetRequestSchema().load(body)
    except Exception as e:
        return flask.abort(HTTPStatus.BAD_REQUEST, f"Invalid request body: {e!s}.")

    filters = [
        Submission.is_verified.is_(True),
    ]

    if request.created_at_from is not None:
        filters.append(Submission.created_at >= request.created_at_from)

    if request.created_at_to is not None:
        filters.append(Submission.created_at <= request.created_at_to)

    submissions = (
        Submission.query.join(ReportV2, Submission.report_id == ReportV2.id)
        .filter(ReportV2.organization_id == request.organization_id, *filters)
        .options(
            selectinload(Submission.coverages).joinedload(SubmissionCoverage.coverage),
            selectinload(Submission.businesses),
            selectinload(Submission.files),
        )
    )

    for filter_rule in request.filter_rules:
        if filter_rule.filter_type == FilterRuleType.NAICS:
            if filter_rule.negated:
                submissions = submissions.filter(~Submission.primary_naics_code.in_(filter_rule.values))
            else:
                submissions = submissions.filter(Submission.primary_naics_code.in_(filter_rule.values))
        elif filter_rule.filter_type == FilterRuleType.COVERAGE:
            if filter_rule.negated:
                submissions = submissions.filter(
                    ~Submission.coverages.any(SubmissionCoverage.coverage.has(Coverage.name.in_(filter_rule.values)))
                )
            else:
                submissions = submissions.filter(
                    Submission.coverages.any(SubmissionCoverage.coverage.has(Coverage.name.in_(filter_rule.values)))
                )
        else:
            return flask.abort(HTTPStatus.BAD_REQUEST, f"Unsupported filter type: {filter_rule.filter_type}")

    if request.required_file_types:
        submissions = submissions.filter(Submission.files.any(File.file_type.in_(request.required_file_types)))

    submissions = submissions.order_by(Submission.created_at.desc())

    if request.limit is not None:
        submissions = submissions.limit(request.limit)

    return {
        "submissions": SubmissionSchema(
            only=["id", "name", "created_at", "primary_naics_code", "coverages", "businesses", "files"],
            context={"include_s3_key": True},
            many=True,
        ).dump(submissions)
    }


# POST api/v3/customizable-classifiers-v2/:id/activate-version/:version_id
def change_classifier_version_active(classifier_id: str, version_id: str, is_active=True):
    classifier = CustomizableClassifierV2.query.get_or_404(classifier_id)
    version_to_change = ClassifierVersion.query.get_or_404(version_id)

    try:
        if is_active is False:
            version_to_change.is_active = False
        else:
            for version in classifier.versions:
                version.is_active = False
            db.session.flush()
            version_to_change.is_active = True
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, f"Failed to activate classifier version: {e!s}.")

    return None, HTTPStatus.NO_CONTENT


# POST /api/v3.0/customizable-classifiers-v2/:id/create-task-definitions
def create_classifier_task_definitions(id: str):
    classifier = CustomizableClassifierV2.query.get_or_404(id)

    if not classifier.active_version:
        return flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, "No active version found for this classifier")

    try:
        create_task_definitions(classifier.active_version)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        return flask.abort(HTTPStatus.UNPROCESSABLE_ENTITY, f"Failed to create task definitions: {e!s}.")

    return None, HTTPStatus.NO_CONTENT
