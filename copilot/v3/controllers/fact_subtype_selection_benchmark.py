from http import HTTPStatus

from flask import abort
from flask_login import current_user
from sqlalchemy import desc, func, or_
from sqlalchemy.exc import IntegrityError

from copilot.models import db
from copilot.models.fact_subtype_selection_benchmark import (
    FactSubtypeSelectionBenchmark,
)
from copilot.schemas.fact_subtype_selection_benchmark import (
    FactSubtypeSelectionBenchmarkSchema,
)


# POST /fact-subtype-selection-benchmark
def create_fact_subtype_selection_benchmark_bulk(body: list[dict]) -> HTTPStatus:
    if not current_user.is_kalepa:
        abort(403)

    rows_to_load: list[FactSubtypeSelectionBenchmark] = FactSubtypeSelectionBenchmarkSchema(many=True).load(
        body, session=db.session
    )
    if len(rows_to_load) == 0:
        return HTTPStatus.BAD_REQUEST

    try:
        db.session.add_all(rows_to_load)
        db.session.commit()
        return HTTPStatus.CREATED
    except IntegrityError:
        db.session.rollback()
        return HTTPStatus.CONFLICT
    except Exception:
        db.session.rollback()
        return HTTPStatus.INTERNAL_SERVER_ERROR


# GET /fact-subtype-selection-benchmark
def get_fact_subtype_selection_benchmark(
    benchmark_id: str | None = None, file_type: str | None = None, organization_id: int | None = None
) -> list[dict]:
    if not current_user.is_kalepa:
        abort(403)

    subtypes_benchmark_vars = [
        FactSubtypeSelectionBenchmark.benchmark_id,
        FactSubtypeSelectionBenchmark.organization_id,
        FactSubtypeSelectionBenchmark.file_type,
    ]
    endpoint_params = [benchmark_id, organization_id, file_type]
    nullable_or_fields = {
        FactSubtypeSelectionBenchmark.organization_id,
        FactSubtypeSelectionBenchmark.file_type,
    }
    # If a param passed into the endpoint is not None, filter by it
    conditions = [
        or_(sb_type == param, sb_type.is_(None)) if sb_type in nullable_or_fields else sb_type == param
        for sb_type, param in zip(subtypes_benchmark_vars, endpoint_params)
        if param is not None
    ]

    if benchmark_id:
        rows = FactSubtypeSelectionBenchmark.query.filter(*conditions).all()
    else:
        latest_record = (
            FactSubtypeSelectionBenchmark.query.filter(*conditions)
            .order_by(FactSubtypeSelectionBenchmark.created_at.desc())
            .execution_options(bypass_filter_required=True)
            .first()
        )
        if not latest_record:
            return []
        rows = FactSubtypeSelectionBenchmark.query.filter(
            FactSubtypeSelectionBenchmark.benchmark_id == latest_record.benchmark_id
        ).all()
    return FactSubtypeSelectionBenchmarkSchema(many=True).dump(rows)


def get_latest_fact_subtype_selection_benchmarks(amount: int = 5):
    if not current_user.is_kalepa:
        abort(403)

    subq = db.session.query(
        FactSubtypeSelectionBenchmark,
        func.row_number()
        .over(
            partition_by=FactSubtypeSelectionBenchmark.benchmark_id,
            order_by=desc(FactSubtypeSelectionBenchmark.created_at),
        )
        .label("rank"),
    ).subquery()

    latest_benchmarks = (
        db.session.query(subq).filter(subq.c.rank == 1).order_by(desc(subq.c.created_at)).limit(amount).all()
    )
    return FactSubtypeSelectionBenchmarkSchema(many=True, only=["benchmark_id", "file_type"]).dump(latest_benchmarks)
