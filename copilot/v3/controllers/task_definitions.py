from http import HTTPStatus

from flask import abort
from infrastructure_common.logging import get_logger

from copilot.models import db
from copilot.models.task_dataset import TaskDatasetModelOutcome
from copilot.models.tasks import TaskDefinition
from copilot.schemas.tasks import TaskDefinitionSchema

logger = get_logger()


def create_task_definition(body: dict) -> tuple[dict, int]:
    try:
        task_definition = TaskDefinitionSchema().load(body)
    except Exception:
        logger.exception("Failed to create task definition")
        abort(HTTPStatus.BAD_REQUEST, "Could not create task definition")

    try:
        db.session.add(task_definition)
        db.session.commit()
    except Exception:
        db.session.rollback()
        logger.exception("Failed to create task definition")
        raise
    return TaskDefinitionSchema().dump(task_definition), HTTPStatus.CREATED


def update_task_definition(task_definition_id: str, body: dict) -> tuple[dict, int]:
    task_definition = TaskDefinition.query.get_or_404(task_definition_id)

    try:
        TaskDefinitionSchema().load(body, instance=task_definition)
    except Exception:
        db.session.rollback()
        logger.exception("Failed to update task definition")
        abort(HTTPStatus.BAD_REQUEST, "Could not update task definition")

    db.session.commit()
    return TaskDefinitionSchema().dump(task_definition), HTTPStatus.OK


def delete_task_definition(task_definition_id: str) -> HTTPStatus:
    task_definition = TaskDefinition.query.get_or_404(task_definition_id)

    try:
        db.session.delete(task_definition)
        db.session.commit()
    except Exception:
        db.session.rollback()
        logger.exception("Failed to delete task definition")
        abort(HTTPStatus.BAD_REQUEST, "Could not delete task definition")

    return HTTPStatus.NO_CONTENT


def get_task_definition_by_id(
    task_definition_id: str,
    organization_id: int | None = None,
    task_dataset_input_id: str | None = None,
    use_cost_fallback: bool = False,
) -> tuple[dict, int]:
    task_definition = TaskDefinition.query.get_or_404(task_definition_id)
    return _get_task_definition_response(task_definition, organization_id, task_dataset_input_id, use_cost_fallback)


def get_task_definition(
    task_code: str,
    organization_id: int | None = None,
    task_dataset_input_id: str | None = None,
    use_cost_fallback: bool = False,
) -> tuple[dict, int]:
    task_definition = TaskDefinition.query.filter(TaskDefinition.code == task_code).one_or_none()
    if not task_definition:
        abort(404, f"Task definition not found for task code: {task_code}")
    return _get_task_definition_response(task_definition, organization_id, task_dataset_input_id, use_cost_fallback)


def _get_task_definition_response(
    task_definition: TaskDefinition, organization_id: int | None, task_dataset_input_id, use_cost_fallback: bool
):
    # Filter out disabled models
    task_definition.task_definition_models = [
        tdm for tdm in task_definition.task_definition_models if not tdm.is_disabled
    ]
    # Filter by organization if provided
    if organization_id is not None:
        task_definition.task_definition_models = [
            tdm for tdm in task_definition.task_definition_models if tdm.is_applicable_for_organization(organization_id)
        ]

    # Filter out models based on use_cost_fallback flag
    task_definition.task_definition_models = [
        tdm for tdm in task_definition.task_definition_models if tdm.is_cost_fallback == use_cost_fallback
    ]

    # If task_dataset_input_id is provided and model has already been run for it, fetch the output
    if task_dataset_input_id is not None:
        task_model_ids = [tdm.task_model_id for tdm in task_definition.task_definition_models]
        dataset_model_outcomes = TaskDatasetModelOutcome.query.filter(
            TaskDatasetModelOutcome.task_dataset_input_id == task_dataset_input_id,
            TaskDatasetModelOutcome.task_model_id.in_(task_model_ids),
        ).all()
        task_model_id_to_outcome = {
            outcome.task_model_id: outcome
            for outcome in dataset_model_outcomes
            if task_definition.execution_model_ids_to_validation_model_ids.get(outcome.task_model_id)
            == outcome.validation_task_model_id
        }
        for tdm in task_definition.task_definition_models:
            if outcome := task_model_id_to_outcome.get(tdm.task_model_id):
                tdm.output = outcome.output
                tdm.task_dataset_model_outcome_id = outcome.id
    result = TaskDefinitionSchema().dump(task_definition)
    db.session.rollback()
    return result, 200
