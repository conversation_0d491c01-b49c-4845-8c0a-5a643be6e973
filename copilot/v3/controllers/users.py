from collections.abc import Sequence
from datetime import datetime, timedelta
from math import ceil
from uuid import UUID
import json
import re

from dateutil.parser import parse
from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from marshmallow import ValidationError
from sqlalchemy import (
    and_,
    case,
    delete,
    distinct,
    exists,
    func,
    literal,
    not_,
    or_,
    tuple_,
    update,
)
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import (
    Query,
    aliased,
    contains_eager,
    joinedload,
    lazyload,
    load_only,
    selectinload,
)
from static_common.enums.organization import ExistingOrganizations
from static_common.enums.recommendation import RecommendationActionEnum
from static_common.enums.submission import SubmissionStage
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.timezones import TIMEZONES
import flask

from copilot.logic.dao.report_dao import ReportDAO
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.models import (
    Brokerage,
    BrokerageEmployee,
    ClientApplication,
    HubTemplate,
    MetricTemplate,
    Organization,
    ReportPermission,
    Settings,
    Submission,
    SubmissionClearingIssue,
    User,
    db,
)
from copilot.models.broker_groups import BrokerGroupMapping
from copilot.models.emails import Email, ReportEmailCorrespondence
from copilot.models.permissions import report_permission_type_db_sort_order
from copilot.models.reports import (
    ClearingSubStatus,
    ReadSubmission,
    ReportBundle,
    ReportsEnvelope,
    ReportV2,
    SubmissionBookmark,
    SubmissionClearingSubStatus,
    SubmissionCoverage,
    SubmissionRecommendationResult,
    SubmissionUser,
)
from copilot.models.types import (
    ClearingStatus,
    HubTemplateType,
    LogicalOperator,
    PermissionType,
    Sorting,
)
from copilot.models.user import UserGroup, user_open_reports
from copilot.schemas.bookmark import BookmarkSchema
from copilot.schemas.client_applications import ClientApplicationSchema
from copilot.schemas.hub_template import HubTemplateSchema
from copilot.schemas.metric_template import MetricTemplateSchema
from copilot.schemas.report import (
    InboxReportsEnvelopeSchema,
    ReportsEnvelopeSchema,
    SubmissionSchema,
    WCReportsEnvelopeSchema,
)
from copilot.schemas.user import UserOpenReport, UserSignupData, UserUpdateSchema
from copilot.utils import extract_domain
from copilot.v1.schemas import OrganizationSchema, UserSchema
from copilot.v3.controllers.reports import get_light_report, get_reports_query, ordered
from copilot.v3.utils.reports import submission_query_filter_non_verified
from copilot.v3.utils.support_users import add_or_remove_support_user
from copilot.v3.utils.users_common import (
    create_or_update_settings_for_user_in_relation_with_cross_org_access,
    create_organization_for_user,
)

logger = get_logger()

submission_schema = SubmissionSchema()
hub_template_schema = HubTemplateSchema()
user_update_schema = UserUpdateSchema()
organization_schema = OrganizationSchema()
user_open_report_schema = UserOpenReport()
bookmark_schema = BookmarkSchema()
user_signup_data_schema = UserSignupData()
MAX_NUM_BUSINESSES = 500
DEFAULT_USERS_EXPAND = [
    "id",
    "name",
    "email",
    "photo_url",
    "is_read_only_account",
    "applicable_settings.is_support",
    "redirect_user_id",
    "applicable_settings.can_resolve_clearing_issues",
]


def create_user_if_not_present(body: dict) -> tuple[dict, int]:
    logger.info("Entering create_user_if_not_present", body=body)
    user_email = body["email"]
    external_id = body["external_id"]
    organization_id = body.get("organization_id")
    is_read_only = body.get("is_read_only", False)
    user_full_name = body.get("full_name")
    create_organization_if_not_assigned = body.get("create_organization_if_not_assigned", False)
    cross_organization_access = body.get("cross_organization_access", False)
    if user_full_name:
        user_full_name = user_full_name[:100] if len(user_full_name) > 100 else user_full_name
    user_created = False

    # Get the User by email, if it exists.
    user_profile = db.session.execute(
        (
            "select id, external_id, organization_id, name, cross_organization_access from users "
            "where lower(email) = :email"
        ),
        dict(email=user_email.lower()),
    ).fetchone()

    log = logger.bind(user_email=user_email, external_id=external_id)
    if not user_profile:
        log.info("User was not found")
        # Get the User's assigned Organization, or derive it from their email address's domain.
        if not organization_id and not create_organization_if_not_assigned:
            log.info(
                "The app_metadata does not specify an organization_id; infer it from the email domain",
            )
            organization_id = Organization.get_id_by_email(user_email)
        # Try to find organization based on the email domain, searching for allowed domains in the settings table
        if not organization_id:
            log.info(
                (
                    "The app_metadata does not specify an organization_id; searching for allowed domains in the"
                    " settings table"
                ),
            )
            settings_row = (
                db.session.query(Settings)
                .filter(Settings.email_domains.contains([extract_domain(user_email).lower().strip()]))
                .first()
            )
            if settings_row:
                organization_id = settings_row.organization_id
                log.info("Settings lookup succeeded, found organization for user", organization_id=organization_id)
            else:
                log.info("Settings lookup failed")
        if not organization_id and create_organization_if_not_assigned:
            organization = create_organization_for_user(user_full_name, user_email, db.session, with_commit=True)
            organization_id = organization.id

        # Create the User.
        user_profile = db.session.execute(
            (
                "INSERT INTO users "
                "(email, external_id, organization_id, name, is_read_only_account,"
                " cross_organization_access, is_enabled, registered_in_knock) "
                "VALUES "
                "(:email, :external_id, :organization_id, :user_full_name, :is_read_only, :cross_organization_access, "
                ":is_enabled, :registered_in_knock) RETURNING id, external_id,"
                " organization_id, name, cross_organization_access;"
            ),
            dict(
                email=user_email,
                external_id=external_id,
                organization_id=organization_id,
                user_full_name=user_full_name,
                is_read_only=is_read_only,
                cross_organization_access=cross_organization_access,
                is_enabled=True,
                registered_in_knock=False,
            ),
        ).fetchone()
        if cross_organization_access:
            db.session.execute(
                f"""
            insert into support_users
            (id, created_at, updated_at, user_id,
            last_action_at, current_report_id, hourly_rate,
            can_load_construction, can_load_submissions, can_assign_naics, is_tier_2)
            values (uuid_generate_v4(), now(), null, {user_profile[0]}, now(), null, null, false, false, false, false);
            """
            )
        user_created = True

    user_id = user_profile[0]
    existing_external_id = user_profile[1]
    existing_organization_id = user_profile[2]
    existing_user_name = user_profile[3]
    existing_cross_organization_access = user_profile[4]

    log = logger.bind(
        user_id=user_id,
        user_email=user_email,
        external_id=external_id,
        cross_organization_access=cross_organization_access,
        xorg_in_body="cross_organization_access" in body,
    )

    # Update the User's organization_id if they were not assigned on creation.
    if not existing_organization_id:
        if create_organization_if_not_assigned:
            organization = create_organization_for_user(user_full_name, user_email, db.session, with_commit=True)
            organization_id = organization.id
        else:
            organization_id = Organization.get_id_by_email(user_email)
        db.session.execute(
            "UPDATE users SET organization_id = :organization_id where id = :user_id;",
            dict(organization_id=organization_id, user_id=user_id),
        )

    # Synchronize the User's external_id to support changing Auth0 connections.
    if not existing_external_id or (external_id and existing_external_id != external_id):
        db.session.execute(
            "UPDATE users SET external_id = :external_id where id = :user_id;",
            dict(external_id=external_id, user_id=user_id),
        )

    # Synchronize the User's name to OAuth provider existing name
    if not existing_user_name or (user_full_name and existing_user_name != user_full_name):
        db.session.execute(
            "UPDATE users SET name = :user_full_name where id = :user_id;",
            dict(user_full_name=user_full_name, user_id=user_id),
        )

    # If a new user was created, or the user already existed but cross_org_access value changed,
    # synchronize User's cross_organization_access and settings
    if user_created or cross_organization_access != existing_cross_organization_access:
        create_or_update_settings_for_user_in_relation_with_cross_org_access(
            user_id, cross_organization_access, db.session
        )

    if not user_created and cross_organization_access != existing_cross_organization_access:
        log.info(
            "cross_organization_access has been changed",
            existing_cross_organization_access=existing_cross_organization_access,
            new_cross_organization_access=cross_organization_access,
        )
        db.session.execute(
            "UPDATE users SET cross_organization_access = :cross_organization_access where id = :user_id;",
            dict(cross_organization_access=cross_organization_access, user_id=user_id),
        )

        if not cross_organization_access:
            log.info("cross_organization_access has been set to False, setting organization_id to Kalepa")
            db.session.execute(
                "UPDATE users SET organization_id = :organization_id where id = :user_id;",
                dict(organization_id=ExistingOrganizations.KalepaTest.value, user_id=user_id),
            )

    db.session.commit()

    user = User.query.get(user_id)
    if "cross_organization_access" in body:
        db_session = db.session
        log.info("Handling support user logic after login")
        add_or_remove_support_user(user_id, cross_organization_access, db_session)
        db_session.commit()

    if user_created:
        log.info("Creating [permissions+hub_template+knock_id] for new user with id")
        current_app.knock_client.identify_user(user)
    elif not existing_organization_id:
        log.info(
            "User was already present but organization has been assigned to this user. "
            "Creating [permissions+hub_template]"
        )

    return {
        "user_id": user.id,
        "organization_id": user.organization_id,
        "external_user_id": user.external_id,
        "email": user.email,
        "is_read_only": user.is_read_only_account,
        "is_trusted": user.is_trusted,
        "cross_organization_access": user.cross_organization_access,
        "is_enabled": user.is_enabled,
    }, (201 if user_created else 200)


def get_reports_query_by_user(
    user: User, base_query: Query | None = None, with_wc_info: bool = False, include_unverified: bool = False
) -> Query:
    # todo: use `expand` to control which columns are selected. For now, always select the default columns.
    query = get_reports_query(base_query, with_wc_info=with_wc_info)
    owner_id_to_use = current_app.report_owners_cache.get_owner_id_for_organization_id(user.organization_id)
    if owner_id_to_use is None:
        logger.warning("Owner id not found for organization", organization_id=user.organization_id)
        query = query.filter(ReportV2.organization_id == user.organization_id)
    else:
        query = query.filter(Submission.owner_id == owner_id_to_use)
    if user.cross_organization_access or include_unverified:
        return query

    return query.join(ReportPermission).filter(
        or_(
            ReportPermission.grantee_user_id == user.id,
            ReportPermission.grantee_group_id.in_(User.get_group_ids_subquery(user.id)),
            ReportV2.organization_permission_level != None,
        )
    )


def get_reports_by_user(
    user_id: int,
    broker_ids: list[str] = None,
    broker_group_ids: list[str] = None,
    brokerage_ids: list[str] = None,
    stage: list[str] = None,
    name: str = None,
    declination_reason: str = None,
    only_owned_by_me: bool = False,
    only_shared_with_me: bool = False,
    owned_by_user: str = None,
    only_recommended: bool = False,
    only_renewals: bool = False,
    only_rush: bool = False,
    only_non_rush: bool = False,
    fni_state: str = None,
    show_renewals: str = None,
    only_bookmarked: bool = False,
    only_not_bookmarked: bool = False,
    read: bool = None,
    page: int = None,
    per_page: int = None,
    sorting: list[str] | None = None,
    descending: list[bool] | None = None,
    recommended_v2_action: str = None,
    score_ml_min: float | None = None,
    score_ml_max: float | None = None,
    pm_rules_modifier_min: float | None = None,
    pm_rules_modifier_max: float | None = None,
    only_bind_likely: bool | None = None,
    before: str = None,
    after: str = None,
    date_type: str = None,
    before_second: str = None,
    after_second: str = None,
    date_type_second: str = None,
    coverages: list[str] = None,
    coverage_operator: LogicalOperator | None = LogicalOperator.AND,
    assignees: list[int] = None,
    assignee_groups: list[str] = None,
    only_unassigned: bool = False,
    only_referred: bool = False,
    only_not_referred: bool = False,
    naics_2: list[str] = None,
    naics_6: list[str] = None,
    report_ids: list[str] = None,
    account_id: str = None,
    clearing_stage: str = None,
    expired_premium_min: int | None = None,
    expired_premium_max: int | None = None,
    target_premium_min: int | None = None,
    target_premium_max: int | None = None,
    sales_min: int | None = None,
    sales_max: int | None = None,
    referred_to: Sequence[int] | None = None,
    verified: bool | None = None,
    verified_shell: bool | None = None,
    exclude_bundled: bool = False,
    with_wc_info: bool = False,
    client_stage_ids: list[int] = None,
    org_groups: list[str] = None,
    client_clearing_statuses: Sequence[str] = None,
    only_with_client_id: bool = False,
    include_email: bool | None = False,
    include_unverified: bool = False,
    only_assigned: bool = False,
    not_assignees: list[int] = None,
    not_assignee_groups: list[str] = None,
    not_referred_to: Sequence[int] | None = None,
    not_broker_ids: list[str] = None,
    not_broker_group_ids: list[str] = None,
    not_brokerage_ids: list[str] = None,
    not_coverages: list[str] = None,
    not_coverage_operator: LogicalOperator | None = LogicalOperator.AND,
    not_before: str = None,
    not_after: str = None,
    not_before_second: str = None,
    not_after_second: str = None,
    not_naics_2: list[str] = None,
    not_naics_6: list[str] = None,
    not_expired_premium_min: int | None = None,
    not_expired_premium_max: int | None = None,
    not_target_premium_min: int | None = None,
    not_target_premium_max: int | None = None,
    not_org_groups: list[str] = None,
    not_recommended_v2_action: str = None,
    not_sales_min: int | None = None,
    not_sales_max: int | None = None,
    not_stage: list[str] = None,
    not_client_stage_ids: list[int] = None,
    not_client_clearing_statuses: Sequence[str] = None,
    brokerage_offices: str = None,
    not_brokerage_offices: str = None,
    insurance_accounting_date_from: str = None,
    insurance_accounting_date_to: str = None,
    only_bundled: bool = False,
    sic_2: list[str] = None,
    sic_code: list[str] = None,
    not_sic_2: list[str] = None,
    not_sic_code: list[str] = None,
    clearing_assignees: list[int] = None,
    not_clearing_assignees: list[int] = None,
    include_clearing_user_unassigned: bool = False,
    exclude_clearing_user_unassigned: bool = False,
) -> tuple[dict, int]:
    if current_user.id != user_id:
        flask.abort(401)

    brokerage_offices = json.loads(brokerage_offices) if brokerage_offices else None
    not_brokerage_offices = json.loads(not_brokerage_offices) if not_brokerage_offices else None

    if descending and not sorting:
        sorting = [Sorting.CREATED_AT]

    if not sorting:
        sorting = [
            Sorting.RECOMMENDATION_SCORE,
            Sorting.STAGE,
            Sorting.EFFECTIVE_AT,
            Sorting.CREATED_AT,
        ]

    if not descending:
        descending = [False for x in sorting]
        descending[-1] = True

    if include_email:
        sorting = [Sorting.LAST_EMAIL]
        descending = [True]

    if not clearing_stage:
        clearing_stage = "ALL"

    if not page:
        page = 1

    if not per_page:
        per_page = 100

    if only_owned_by_me and only_shared_with_me:
        raise ValidationError("only_owned_by_me and only_shared_with_me cannot both be true!")

    if owned_by_user and (only_owned_by_me or only_shared_with_me):
        raise ValidationError("only_owned_by_me and only_shared_with_me cannot be used if owned_by_user is specified!")

    params = locals()

    if owned_by_user:
        owner = User.query.filter(User.email == owned_by_user).first()
        if not owner:
            raise ValidationError("owned_by_user is not a valid user!")
        if current_user.organization_id != owner.organization_id:
            flask.abort(403)
    else:
        owner = current_user

    report_fields = [
        "id",
        "name",
        "routing_tags",
        "is_rush",
        "created_at",
        "owner_id",
        "organization_permission_level",
        "organization_id",
        "report_bundle_id",
        "additional_data",
        "org_group",
        "correspondence_id",
    ]
    count_query = db.session.query(distinct(ReportV2.id)).options(load_only(ReportV2.id))
    select_query = db.session.query(ReportV2).options(load_only(*report_fields))
    count_query = get_reports_query_by_user(
        owner, count_query, with_wc_info=False, include_unverified=include_unverified
    )
    select_query = get_reports_query_by_user(
        owner, select_query, with_wc_info=with_wc_info, include_unverified=include_unverified
    )
    count_query = _get_reports_query_by_user(**params, query=count_query)
    select_query = _get_reports_query_by_user(**params, query=select_query)

    if not only_recommended:
        select_query, sortings = ordered(select_query, sorting, descending, user_id)
        sortings = [ReportV2.id, *sortings]
        select_query = select_query.distinct(*sortings)
    else:
        select_query = select_query.distinct(ReportV2.id)

    if include_email:
        select_query = select_query.options(
            selectinload(ReportV2.correspondence)
            .selectinload(ReportEmailCorrespondence.emails)
            .selectinload(Email.classifications)
        )

    submission_options = [
        joinedload(Submission.coverages),
        selectinload(Submission.identifiers),
        joinedload(Submission.assigned_underwriters)
        .joinedload(SubmissionUser.user)
        .options(lazyload(User.settings), lazyload(User.organization)),
    ]
    if with_wc_info:
        submission_options.append(selectinload(Submission.workers_comp_experience))
        submission_options.append(selectinload(Submission.workers_comp_rating_info))
    select_query = select_query.options(
        selectinload(ReportV2.report_bundle)
        .joinedload(ReportBundle.reports)
        .joinedload(ReportV2.submission)
        .options(*submission_options),
        selectinload(ReportV2.report_permissions),
    )

    total_for_account_id = None
    if account_id:
        all_with_the_account_id_query = get_reports_query().filter(
            ReportV2.organization_id == current_user.organization_id
        )
        all_with_the_account_id_query = submission_query_filter_non_verified(
            all_with_the_account_id_query, clearing_stage
        )
        if "business-intersection-with:" in account_id:
            report_id = account_id.split(":")[1]
            total_for_account_id = ReportDAO.wrap_with_shared_fni(all_with_the_account_id_query, report_id).count()
        else:
            total_for_account_id = all_with_the_account_id_query.filter(Submission.account_id == account_id).count()

    reports = select_query.limit(per_page).offset((page - 1) * per_page).all()

    if len(reports) < per_page:
        total_reports = ((page - 1) * per_page) + len(reports)
    else:
        total_reports = count_query.count()
    pages = int(ceil(total_reports / float(per_page)))
    reports_envelope = ReportsEnvelope(
        reports=reports,
        page=page,
        total_pages=pages,
        total_reports=total_reports,
        has_next=page < pages,
        total_with_the_account_id=total_for_account_id,
    )

    schema = ReportsEnvelopeSchema() if not include_email else InboxReportsEnvelopeSchema()
    if with_wc_info:
        schema = WCReportsEnvelopeSchema()
    _fill_reports_with_current_user_permissions(
        reports_envelope.reports, user_id, include_unverified=include_unverified
    )
    return schema.dump(reports_envelope)


def _contains_unknown(filter: list[str]) -> bool:
    return any(item.lower() == "unknown" for item in filter)


def _filter_out_unknown(filter: list[str]) -> list[str]:
    return [item for item in filter if item.lower() != "unknown"]


def _get_reports_query_by_user(
    query: Query,
    user_id: int,
    broker_ids: list[str] = None,
    broker_group_ids: list[str] = None,
    brokerage_ids: list[str] = None,
    stage: list[str] = None,
    name: str = None,
    declination_reason: str = None,
    only_owned_by_me: bool = False,
    only_shared_with_me: bool = False,
    owned_by_user: str = None,
    only_recommended: bool = False,
    only_renewals: bool = False,
    only_rush: bool = False,
    only_non_rush: bool = False,
    fni_state: str = None,
    show_renewals: str = None,
    only_bookmarked: bool = False,
    only_not_bookmarked: bool = False,
    read: bool = None,
    page: int = None,
    per_page: int = None,
    sorting: list[str] | None = None,
    descending: list[bool] | None = None,
    recommended_v2_action: str = None,
    score_ml_min: float | None = None,
    score_ml_max: float | None = None,
    pm_rules_modifier_min: float | None = None,
    pm_rules_modifier_max: float | None = None,
    only_bind_likely: bool | None = None,
    before: str = None,
    after: str = None,
    date_type: str = None,
    before_second: str = None,
    after_second: str = None,
    date_type_second: str = None,
    coverages: list[str] = None,
    coverage_operator: LogicalOperator | None = LogicalOperator.AND,
    assignees: list[int] = None,
    assignee_groups: list[str] = None,
    only_unassigned: bool = False,
    only_referred: bool = False,
    only_not_referred: bool = False,
    naics_2: list[str] = None,
    naics_6: list[str] = None,
    report_ids: list[str] = None,
    account_id: str = None,
    clearing_stage: str = None,
    expired_premium_min: int | None = None,
    expired_premium_max: int | None = None,
    target_premium_min: int | None = None,
    target_premium_max: int | None = None,
    sales_min: int | None = None,
    sales_max: int | None = None,
    referred_to: Sequence[int] | None = None,
    verified: bool | None = None,
    verified_shell: bool | None = None,
    exclude_bundled: bool = False,
    with_wc_info: bool = False,
    client_stage_ids: list[int] = None,
    org_groups: list[str] = None,
    client_clearing_statuses: Sequence[str] = None,
    only_with_client_id: bool = False,
    include_email: bool | None = False,
    include_unverified: bool = False,
    only_assigned: bool = False,
    not_assignees: list[int] = None,
    not_assignee_groups: list[str] = None,
    not_referred_to: Sequence[int] | None = None,
    not_broker_ids: list[str] = None,
    not_broker_group_ids: list[str] = None,
    not_brokerage_ids: list[str] = None,
    not_coverages: list[str] = None,
    not_coverage_operator: LogicalOperator | None = LogicalOperator.AND,
    not_before: str = None,
    not_after: str = None,
    not_before_second: str = None,
    not_after_second: str = None,
    not_naics_2: list[str] = None,
    not_naics_6: list[str] = None,
    not_expired_premium_min: int | None = None,
    not_expired_premium_max: int | None = None,
    not_target_premium_min: int | None = None,
    not_target_premium_max: int | None = None,
    not_org_groups: list[str] = None,
    not_recommended_v2_action: str = None,
    not_sales_min: int | None = None,
    not_sales_max: int | None = None,
    not_stage: list[str] = None,
    not_client_stage_ids: list[int] = None,
    not_client_clearing_statuses: Sequence[str] = None,
    brokerage_offices: list[str] = None,
    not_brokerage_offices: list[str] = None,
    insurance_accounting_date_from: str = None,
    insurance_accounting_date_to: str = None,
    only_bundled: bool = False,
    sic_2: list[str] = None,
    sic_code: list[str] = None,
    not_sic_2: list[str] = None,
    not_sic_code: list[str] = None,
    clearing_assignees: list[int] = None,
    not_clearing_assignees: list[int] = None,
    include_clearing_user_unassigned: bool = False,
    exclude_clearing_user_unassigned: bool = False,
) -> Query:
    if current_user.cross_organization_access:
        user_id = Organization.find_organization_owner_id(current_user.organization_id)

    if assignees and len(assignees) > 0 and only_unassigned:
        raise ValidationError("Cannot use assignee_groups and only_unassigned at the same time")

    query = (
        submission_query_filter_non_verified(query, clearing_stage)
        if not include_unverified
        else query.join(ReportV2.submission)
    )
    joined_with_submission_users = False

    if broker_group_ids:
        if not joined_with_submission_users:
            joined_with_submission_users = True
            query = query.join(SubmissionUser)

        mappings_with_user_id = aliased(BrokerGroupMapping)
        mappings_without_user_id = aliased(BrokerGroupMapping)
        query = query.outerjoin(
            mappings_with_user_id,
            and_(
                SubmissionUser.user_id == mappings_with_user_id.user_id,
                Submission.broker_id == mappings_with_user_id.broker_id,
            ),
        )
        query = query.outerjoin(
            mappings_without_user_id,
            and_(
                mappings_without_user_id.organization_id == ReportV2.organization_id,
                Submission.broker_id == mappings_without_user_id.broker_id,
                # Joining to mapping without user_id only makes sense if we did not have a mapping with user_id
                mappings_with_user_id.id.is_(None),
            ),
        )

        query = query.filter(
            or_(
                mappings_with_user_id.broker_group_id.in_(broker_group_ids),
                mappings_without_user_id.broker_group_id.in_(broker_group_ids),
            )
        )

    if not_broker_group_ids:
        if not joined_with_submission_users:
            joined_with_submission_users = True
            query = query.join(SubmissionUser)

        mappings_with_user_id = aliased(BrokerGroupMapping)
        mappings_without_user_id = aliased(BrokerGroupMapping)
        query = query.outerjoin(
            mappings_with_user_id,
            and_(
                SubmissionUser.user_id == mappings_with_user_id.user_id,
                Submission.broker_id == mappings_with_user_id.broker_id,
            ),
        )
        query = query.outerjoin(
            mappings_without_user_id,
            and_(
                mappings_without_user_id.organization_id == ReportV2.organization_id,
                Submission.broker_id == mappings_without_user_id.broker_id,
                # Joining to mapping without user_id only makes sense if we did not have a mapping with user_id
                mappings_with_user_id.id.is_(None),
            ),
        )

        query = query.filter(
            and_(
                or_(
                    mappings_with_user_id.broker_group_id.notin_(not_broker_group_ids),
                    mappings_with_user_id.broker_group_id.is_(None),
                ),
                or_(
                    mappings_without_user_id.broker_group_id.notin_(not_broker_group_ids),
                    mappings_without_user_id.broker_group_id.is_(None),
                ),
            )
        )

    if broker_ids:
        if _contains_unknown(broker_ids):
            query = (
                query.options(contains_eager(ReportV2.submission, Submission.broker))
                .outerjoin(Submission.broker)
                .filter(or_(BrokerageEmployee.id.in_(_filter_out_unknown(broker_ids)), BrokerageEmployee.id.is_(None)))
            )
        else:
            query = (
                query.options(contains_eager(ReportV2.submission, Submission.broker))
                .join(Submission.broker)
                .filter(BrokerageEmployee.id.in_(broker_ids))
            )

    if not_broker_ids:
        if _contains_unknown(not_broker_ids):
            query = (
                query.options(contains_eager(ReportV2.submission, Submission.broker))
                .join(Submission.broker)
                .filter(BrokerageEmployee.id.notin_(_filter_out_unknown(not_broker_ids)))
            )
        else:
            query = (
                query.options(contains_eager(ReportV2.submission, Submission.broker))
                .outerjoin(Submission.broker)
                .filter(or_(BrokerageEmployee.id == None, BrokerageEmployee.id.notin_(not_broker_ids)))
            )

    if brokerage_ids:
        if _contains_unknown(brokerage_ids):
            query = (
                query.options(contains_eager(ReportV2.submission, Submission.brokerage))
                .outerjoin(Submission.brokerage)
                .filter(or_(Brokerage.id.in_(_filter_out_unknown(brokerage_ids)), Brokerage.id.is_(None)))
            )
        else:
            query = (
                query.options(contains_eager(ReportV2.submission, Submission.brokerage))
                .join(Submission.brokerage)
                .filter(Brokerage.id.in_(brokerage_ids))
            )

    if not_brokerage_ids:
        if _contains_unknown(not_brokerage_ids):
            query = (
                query.options(contains_eager(ReportV2.submission, Submission.brokerage))
                .join(Submission.brokerage)
                .filter(Brokerage.id.notin_(_filter_out_unknown(not_brokerage_ids)))
            )
        else:
            query = (
                query.options(contains_eager(ReportV2.submission, Submission.brokerage))
                .outerjoin(Submission.brokerage)
                .filter(or_(Brokerage.id == None, Brokerage.id.notin_(not_brokerage_ids)))
            )

    if brokerage_offices:
        tuples = [tuple(x.split(":")) for x in brokerage_offices]
        query = query.filter(
            or_(*[and_(Submission.brokerage_id == t[0], Submission.brokerage_office == t[1]) for t in tuples])
        )
    if not_brokerage_offices:
        tuples = [tuple(x.split(":")) for x in not_brokerage_offices]
        query = query.filter(
            or_(
                and_(
                    *[not_(and_(Submission.brokerage_id == t[0], Submission.brokerage_office == t[1])) for t in tuples],
                ),
                Submission.brokerage_id == None,
                Submission.brokerage_office == None,
            )
        )

    if stage and "ALL" not in stage:
        query = query.filter(Submission.stage.in_(stage))

    if not_stage:
        query = query.filter(Submission.stage.notin_(not_stage))

    if client_stage_ids:
        query = query.filter(Submission.client_stage_id.in_(client_stage_ids))

    if not_client_stage_ids:
        query = query.filter(Submission.client_stage_id.notin_(not_client_stage_ids))

    if client_clearing_statuses:
        query = query.filter(Submission.client_clearing_status.in_(client_clearing_statuses))

    if not_client_clearing_statuses:
        query = query.filter(Submission.client_clearing_status.notin_(not_client_clearing_statuses))

    if org_groups:
        query = query.filter(ReportV2.org_group.in_(org_groups))

    if not_org_groups:
        query = query.filter(or_(ReportV2.org_group == None, ReportV2.org_group.notin_(not_org_groups)))

    if insurance_accounting_date_to or insurance_accounting_date_from:
        insurance_accounting_date = case(
            [
                (
                    Submission.stage == SubmissionStage.QUOTED_BOUND,
                    func.greatest(Submission.proposed_effective_date, Submission.bound_date),
                ),
                (
                    Submission.stage.in_([SubmissionStage.QUOTED_LOST, SubmissionStage.QUOTED]),
                    func.greatest(Submission.proposed_effective_date, Submission.quoted_date),
                ),
            ],
            else_=Submission.proposed_effective_date,
        ).label("insurance_accounting_date")

        if insurance_accounting_date_from:
            query = query.filter(insurance_accounting_date > parse(insurance_accounting_date_from))
        if insurance_accounting_date_to:
            query = query.filter(insurance_accounting_date < parse(insurance_accounting_date_to))

    def append_coverage_query(query, coverages, coverage_operator, exclude=False):
        filters = []
        tuples = [c.split(":") if ":" in c else [c, None] for c in coverages]
        for t in tuples:
            if not t[1]:
                subq = (
                    db.session.query(SubmissionCoverage.coverage_id)
                    .filter(SubmissionCoverage.submission_id == Submission.id)
                    .correlate(Submission)
                )
                filters.append(literal(t[0]).in_(subq))
            else:
                subq = (
                    db.session.query(SubmissionCoverage.coverage_id, SubmissionCoverage.coverage_type)
                    .filter(SubmissionCoverage.submission_id == Submission.id)
                    .correlate(Submission)
                )
                filters.append(tuple_(literal(t[0]), literal(t[1])).in_(subq))
        fn = and_ if coverage_operator == LogicalOperator.AND else or_

        if exclude:
            return query.filter(not_(fn(*filters)))

        return query.filter(fn(*filters))

    if coverages:
        query = append_coverage_query(query, coverages, coverage_operator)

    if not_coverages:
        query = append_coverage_query(query, not_coverages, not_coverage_operator, exclude=True)

    if only_bundled:
        query = query.filter(ReportV2.report_bundle_id != None)
    if exclude_bundled:
        query = query.filter(ReportV2.report_bundle_id == None)

    if assignees:
        if not joined_with_submission_users:
            joined_with_submission_users = True
            query = query.join(SubmissionUser)
        query = query.filter(SubmissionUser.user_id.in_(assignees))

    if not_assignees:
        subquery = (
            db.session.query(SubmissionUser)
            .filter(SubmissionUser.submission_id == Submission.id, SubmissionUser.user_id.in_(not_assignees))
            .correlate(Submission)
            .exists()
        )

        query = query.filter(~subquery)

    if assignee_groups:
        user_ids = db.session.query(User.id).join(User.groups).filter(UserGroup.id.in_(assignee_groups))

        group_submission_users = aliased(SubmissionUser)
        query = query.join(group_submission_users, group_submission_users.submission_id == Submission.id).filter(
            group_submission_users.user_id.in_(user_ids)
        )

    if not_assignee_groups:
        user_ids = db.session.query(User.id).join(User.groups).filter(UserGroup.id.in_(not_assignee_groups))

        subquery = (
            db.session.query(SubmissionUser)
            .filter(SubmissionUser.submission_id == Submission.id, SubmissionUser.user_id.in_(user_ids))
            .correlate(Submission)
            .exists()
        )

        query = query.filter(not_(subquery))

    if clearing_assignees and include_clearing_user_unassigned:
        query = query.filter(
            or_(Submission.clearing_assignee_id == None, Submission.clearing_assignee_id.in_(clearing_assignees))
        )
    elif clearing_assignees:
        query = query.filter(Submission.clearing_assignee_id.in_(clearing_assignees))
    elif include_clearing_user_unassigned:
        query = query.filter(Submission.clearing_assignee_id == None)

    if not_clearing_assignees and exclude_clearing_user_unassigned:
        query = query.filter(Submission.clearing_assignee_id.notin_(not_clearing_assignees))
    elif not_clearing_assignees:
        query = query.filter(
            or_(
                Submission.clearing_assignee_id.notin_(not_clearing_assignees),
                Submission.clearing_assignee_id == None,
            )
        )
    elif exclude_clearing_user_unassigned:
        query = query.filter(Submission.clearing_assignee_id != None)

    if not_referred_to or referred_to or only_referred or only_not_referred:
        referred_report_ids = (
            db.session.query(ReportV2.id)
            .join(ReportPermission)
            .filter(ReportV2.organization_id == current_user.organization_id)
            .filter(ReportPermission.is_referral == True)
        )

        if only_referred and only_not_referred:
            raise ValidationError("only_referred and only_not_referred cannot both be true!")

        if not_referred_to:
            not_referred_report_ids = referred_report_ids.filter(ReportPermission.grantee_user_id.in_(not_referred_to))
            query = query.filter(ReportV2.id.notin_(not_referred_report_ids))

        if referred_to or only_referred or only_not_referred:
            if referred_to:
                referred_report_ids = referred_report_ids.filter(ReportPermission.grantee_user_id.in_(referred_to))
            if only_not_referred:
                query = query.filter(ReportV2.id.notin_(referred_report_ids))
            else:
                query = query.filter(ReportV2.id.in_(referred_report_ids))

    if only_unassigned:
        if not joined_with_submission_users:
            joined_with_submission_users = True
            query = query.outerjoin(SubmissionUser)
        query = query.filter(SubmissionUser.user_id == None)

    if only_assigned:
        if not joined_with_submission_users:
            query = query.join(SubmissionUser)

    if verified is not None:
        query = query.filter(Submission.is_verified == verified)

    if verified_shell is not None:
        query = query.filter(Submission.is_verified_shell == verified_shell)

    if clearing_stage != "ALL":
        query = query.filter(Submission.clearing_status != ClearingStatus.CLEARING_IN_PROGRESS)

        if clearing_stage == "PRE_CLEARING":
            # In this context: missing client ID.
            query = query.outerjoin(Submission.clearing_sub_statuses).filter(
                Submission.clearing_status == ClearingStatus.PRE_CLEARING, SubmissionClearingSubStatus.id == None
            )
        elif clearing_stage == "IN_CLEARING_CONFLICT":
            with_conflicting_substatus = (
                db.session.query(SubmissionClearingSubStatus.submission_id)
                .join(SubmissionClearingIssue)
                .filter(
                    SubmissionClearingSubStatus.status == ClearingSubStatus.CONFLICT,
                    SubmissionClearingIssue.is_resolved != True,
                )
                .all()
            )
            with_conflicting_substatus = [x[0] for x in with_conflicting_substatus]
            # In this context: with clearing conflicts and/or with clearing conflict sub_statuses.
            query = query.filter(
                or_(
                    Submission.clearing_status == ClearingStatus.IN_CLEARING_CONFLICT,
                    Submission.id.in_(with_conflicting_substatus),
                )
            )
        elif clearing_stage == "MISSING_DETAILS":
            # In this context: has missing details sub_statuses and is not cleared
            subquery = (
                db.session.query(SubmissionClearingSubStatus)
                .filter(
                    SubmissionClearingSubStatus.status == ClearingSubStatus.MISSING_DETAILS,
                    SubmissionClearingSubStatus.submission_id == Submission.id,
                )
                .correlate(Submission)
                .exists()
            )
            query = query.filter(
                and_(
                    Submission.clearing_status != ClearingStatus.CLEARED,
                    Submission.clearing_status != ClearingStatus.BLOCKED,
                ),
                subquery,
            )
        elif clearing_stage == "PENDING_CLEARING":
            # In this context: pending full clearing.
            query = query.filter(Submission.clearing_status.in_(ClearingStatus.pending_clearing_statuses()))
        elif clearing_stage == "POST_CLEARING":
            # In this context: fully cleared. Light clearing is performed earlier, pre-verification.
            query = query.filter(Submission.clearing_status == ClearingStatus.CLEARED)
        elif clearing_stage == "BLOCKED_ON_CLEARING":
            # In this context: blocked during full clearing.
            query = query.filter(Submission.clearing_status == ClearingStatus.BLOCKED)
        elif clearing_stage == "PENDING_LIGHT_CLEARING":
            query = query.filter(Submission.processing_state == SubmissionProcessingState.NEEDS_CLEARING)

    if name:
        alpha_numeric = "[^a-zA-Z0-9]"
        name_pattern = f'%{re.sub(alpha_numeric, "", name)}%'

        query = query.filter(func.regexp_replace(ReportV2.name, alpha_numeric, "", "g").ilike(name_pattern))

    if declination_reason:
        query = query.filter(func.lower(Submission.reason_for_declining).contains(declination_reason.lower()))

    if only_owned_by_me or owned_by_user:
        query = query.filter(ReportPermission.permission_type == PermissionType.OWNER)

    if only_shared_with_me:
        query = query.filter(ReportPermission.permission_type != PermissionType.OWNER)

    if only_not_bookmarked and only_bookmarked:
        pass
    elif only_bookmarked:
        query = query.join(Submission.bookmarks).filter(SubmissionBookmark.user_id == user_id)
    elif only_not_bookmarked:
        query = query.outerjoin(
            SubmissionBookmark, and_(Submission.bookmarks, SubmissionBookmark.user_id == user_id)
        ).filter(SubmissionBookmark.user_id == None)

    if only_with_client_id:
        query = query.join(Submission.client_submission_ids)

    if read is not None:
        if read:
            query = query.join(Submission.read_by_users).filter(ReadSubmission.user_id == user_id)
        else:
            read_submission = db.session.query(ReadSubmission.submission_id).filter(ReadSubmission.user_id == user_id)
            query = query.filter(Submission.id.notin_(read_submission))

    if recommended_v2_action:
        if recommended_v2_action == RecommendationActionEnum.NO_ACTION:
            query = query.filter(
                or_(
                    Submission.recommendation_v2_action == RecommendationActionEnum.NO_ACTION,
                    Submission.recommendation_v2_action == None,
                )
            )
        else:
            query = query.filter(Submission.recommendation_v2_action == recommended_v2_action)

    if not_recommended_v2_action:
        if not_recommended_v2_action == RecommendationActionEnum.NO_ACTION:
            query = query.filter(
                and_(
                    Submission.recommendation_v2_action != RecommendationActionEnum.NO_ACTION,
                    Submission.recommendation_v2_action != None,
                )
            )
        else:
            query = query.filter(
                or_(
                    Submission.recommendation_v2_action == None,
                    Submission.recommendation_v2_action != not_recommended_v2_action,
                )
            )

    def get_query_from_date_interval(
        date_type,
        before,
        after,
        query,
    ):
        date_type = date_type.lower()
        if before:
            before = parse(before) + timedelta(days=1)
            if date_type == "created_at":
                query = query.filter(getattr(ReportV2, date_type) < before)
            else:
                query = query.filter(getattr(Submission, date_type) < before)
        if after:
            after = parse(after)
            if date_type == "created_at":
                query = query.filter(getattr(ReportV2, date_type) >= after)
            else:
                query = query.filter(getattr(Submission, date_type) >= after)

        return query

    def get_query_from_date_interval_exclude(
        date_type,
        before,
        after,
        query,
    ):
        date_type = date_type.lower()
        before = parse(before) + timedelta(days=1) if before else None
        after = parse(after) if after else None
        if before and after:
            if date_type == "created_at":
                query = query.filter(
                    or_(
                        ~and_(getattr(ReportV2, date_type) > before, getattr(ReportV2, date_type) < after),
                        getattr(ReportV2, date_type) == None,
                    )
                )
            else:
                query = query.filter(
                    or_(
                        ~and_(getattr(Submission, date_type) > before, getattr(Submission, date_type) < after),
                        getattr(Submission, date_type) == None,
                    )
                )
        elif before:
            if date_type == "created_at":
                query = query.filter(or_(getattr(ReportV2, date_type) >= before, getattr(ReportV2, date_type) == None))
            else:
                query = query.filter(
                    or_(getattr(Submission, date_type) >= before, getattr(Submission, date_type) == None)
                )
        elif after:
            if date_type == "created_at":
                query = query.filter(or_(getattr(ReportV2, date_type) < after, getattr(ReportV2, date_type) == None))
            else:
                query = query.filter(
                    or_(getattr(Submission, date_type) < after, getattr(Submission, date_type) == None)
                )

        return query

    if date_type and (before or after):
        query = get_query_from_date_interval(date_type, before, after, query)

    if not_before or not_after:
        query = get_query_from_date_interval_exclude("CREATED_AT", not_before, not_after, query)

    if date_type_second and (before_second or after_second):
        query = get_query_from_date_interval(date_type_second, before_second, after_second, query)

    if not_before_second or not_after_second:
        query = get_query_from_date_interval_exclude(
            "PROPOSED_EFFECTIVE_DATE", not_before_second, not_after_second, query
        )

    if only_renewals:
        query = query.filter(Submission.is_renewal.is_(True))

    if only_rush:
        query = query.filter(ReportV2.is_rush == True)
    elif only_non_rush:
        query = query.filter(ReportV2.is_rush.is_distinct_from(True))

    if fni_state:
        query = query.filter(Submission.fni_state == fni_state)

    if show_renewals == "Y":
        query = query.filter(Submission.is_renewal.is_(True))
    elif show_renewals == "N":
        query = query.filter(not_(Submission.is_renewal.is_(True)))

    if naics_6:
        filters = [Submission.primary_naics_code.in_(naics_6)]
        if _contains_unknown(naics_6):
            filters.append(Submission.primary_naics_code.is_(None))
        query = query.filter(or_(*filters))

    if not_naics_6:
        query = query.filter(
            or_(Submission.primary_naics_code == None, Submission.primary_naics_code.notin_(not_naics_6))
        )

    if naics_2:
        formatted_naics = _format_naics(naics_2)
        filters = [func.substring(Submission.primary_naics_code, 1, 8).in_(formatted_naics)]
        if _contains_unknown(formatted_naics):
            filters.append(func.substring(Submission.primary_naics_code, 1, 8).is_(None))
        query = query.filter(or_(*filters))

    if not_naics_2:
        formatted_naics = _format_naics(not_naics_2)
        query = query.filter(
            or_(
                Submission.primary_naics_code == None,
                func.substring(Submission.primary_naics_code, 1, 8).notin_(formatted_naics),
            )
        )

    if sic_2:
        filters = [func.substring(Submission.sic_code, 1, 6).in_(sic_2)]
        if _contains_unknown(sic_2):
            filters.append(func.substring(Submission.sic_code, 1, 6).is_(None))
        query = query.filter(or_(*filters))

    if sic_code:
        filters = [Submission.sic_code.in_(sic_code)]
        if _contains_unknown(sic_code):
            filters.append(Submission.sic_code.is_(None))
        query = query.filter(or_(*filters))

    if not_sic_2:
        query = query.filter(
            or_(Submission.sic_code == None, func.substring(Submission.sic_code, 1, 6).notin_(not_sic_2))
        )

    if not_sic_code:
        query = query.filter(or_(Submission.sic_code == None, Submission.sic_code.notin_(not_sic_code)))

    if report_ids:
        query = query.filter(ReportV2.id.in_(report_ids))

    if expired_premium_min is not None:
        query = query.filter(Submission.expired_premium >= expired_premium_min)
    if expired_premium_max is not None:
        query = query.filter(Submission.expired_premium <= expired_premium_max)

    if not_expired_premium_min is not None and not_expired_premium_max is not None:
        query = query.filter(
            ~and_(
                Submission.expired_premium >= not_expired_premium_min,
                Submission.expired_premium <= not_expired_premium_max,
            )
        )
    elif not_expired_premium_min is not None:
        query = query.filter(Submission.expired_premium < not_expired_premium_min)
    elif not_expired_premium_max is not None:
        query = query.filter(Submission.expired_premium > not_expired_premium_max)

    if target_premium_min is not None:
        query = query.filter(Submission.target_premium >= target_premium_min)
    if target_premium_max is not None:
        query = query.filter(Submission.target_premium <= target_premium_max)

    if not_target_premium_min is not None and not_target_premium_max is not None:
        query = query.filter(
            ~and_(
                Submission.target_premium >= not_target_premium_min, Submission.target_premium <= not_target_premium_max
            )
        )
    elif not_target_premium_min is not None:
        query = query.filter(Submission.target_premium < not_target_premium_min)
    elif not_target_premium_max is not None:
        query = query.filter(Submission.target_premium > not_target_premium_max)

    if sales_min is not None:
        query = query.filter(Submission.sales >= sales_min)
    if sales_max is not None:
        query = query.filter(Submission.sales <= sales_max)

    if not_sales_min is not None and not_sales_max is not None:
        query = query.filter(~and_(Submission.sales >= not_sales_min, Submission.sales <= not_sales_max))
    elif not_sales_min is not None:
        query = query.filter(Submission.sales < not_sales_min)
    elif not_sales_max is not None:
        query = query.filter(Submission.sales > not_sales_max)

    if score_ml_min or score_ml_max or pm_rules_modifier_min or pm_rules_modifier_max or only_bind_likely:
        query = query.outerjoin(Submission.recommendation_result)

    if score_ml_min is not None:
        query = query.filter(SubmissionRecommendationResult.score_ml >= score_ml_min)
    if score_ml_max is not None:
        query = query.filter(SubmissionRecommendationResult.score_ml <= score_ml_max)

    if pm_rules_modifier_min is not None:
        query = query.filter(SubmissionRecommendationResult.pm_rules_modifier >= pm_rules_modifier_min)
    if pm_rules_modifier_max is not None:
        query = query.filter(SubmissionRecommendationResult.pm_rules_modifier <= pm_rules_modifier_max)

    if only_bind_likely:
        query = query.filter(Submission.is_bind_likely == True)

    if account_id:
        if "business-intersection-with:" in account_id:
            report_id = account_id.split(":")[1]
            query = ReportDAO.wrap_with_shared_fni(query, report_id)
        else:
            query = query.filter(Submission.account_id == account_id)

    if not account_id and current_user.organization_id == 10:
        query = query.filter(Submission.is_verified_shell.isnot(True))

    return query


def _format_naics(naics_codes: list[str]) -> list[str]:
    try:
        result = []
        for n in naics_codes:
            if "-" in n:
                naics = n.split("-")
                start = int(naics[0][-2:])
                end = int(naics[1])
                for i in range(start, end + 1):
                    result.append(f"NAICS_{i}")
            else:
                result.append(n)
    except Exception as e:
        logger.error("Error while formatting NAICS codes", error=str(e), naics_codes=naics_codes)
        return naics_codes
    return result


def _fill_reports_with_current_user_permissions(
    reports: Sequence[ReportV2], user_id: int, include_unverified: bool = False
) -> None:
    # This method optimizes setting "current_user_permission_type" on a collection of reports and their bundles, by
    # fetching all permissions in one query and then setting the property of the Report.
    # Gather all report ids.
    report_ids: set[UUID] = set()
    bundled_reports: Sequence[ReportV2] = []
    for report in reports:
        report_ids.add(report.id)
        if not report.report_bundle or not report.report_bundle.reports:
            continue
        for r in report.report_bundle.reports:
            report_ids.add(r.id)
            bundled_reports.append(r)

    if current_user.cross_organization_access or include_unverified:
        # Watch out: this logic is only correct if "reports" were previously correctly filtered on org id.
        permission_type = PermissionType.OWNER if current_user.cross_organization_access else PermissionType.VIEWER
        report_id_to_permission = {rid: permission_type for rid in report_ids}
    else:
        # Fetch all permissions for the reports, in asc order of permission type.
        # If we get multiple permissions, the latest (highest) one wins in the
        # report_id_to_permission dict.
        permissions = (
            ReportPermission.query.options(lazyload(ReportPermission.grantee))
            .filter(ReportPermission.report_id.in_(report_ids))
            .filter(
                or_(
                    ReportPermission.grantee_user_id == user_id,
                    ReportPermission.grantee_group_id.in_(User.get_group_ids_subquery(user_id)),
                )
            )
            .order_by(report_permission_type_db_sort_order)
            .all()
        )
        report_id_to_permission = {p.report_id: p.permission_type for p in permissions}

        all_reports = reports + bundled_reports

        for r in all_reports:
            if r.organization_permission_level:
                current_perm = report_id_to_permission.get(r.id, r.organization_permission_level)
                report_id_to_permission[r.id] = max(r.organization_permission_level, current_perm)

    # Populate permissions to avoid lazy loading.
    for report in reports:
        report.current_user_permission_type = report_id_to_permission.get(report.id).name
        if not report.report_bundle or not report.report_bundle.reports:
            continue
        for r in report.report_bundle.reports:
            r.current_user_permission_type = (
                report_id_to_permission.get(r.id).name if r.id in report_id_to_permission else None
            )


def get_all_users(expand: list[str] | None = None) -> tuple[dict]:
    if not current_user.is_internal_machine_user:
        flask.abort(403)

    users = (
        User.skip_disabled_query.options(joinedload(User.settings))
        .filter(User.cross_organization_access == False)
        .filter(User.organization_id != None)
        .all()
    )
    expand = expand or []
    expand = list(set(expand + DEFAULT_USERS_EXPAND))
    return UserSchema(only=expand).dump(users, many=True)


def get_users_by_organization(
    organization_id: str, get_all_assigned: bool | None = None, expand: list[str] | None = None
) -> tuple[dict]:
    if not current_user.is_internal_machine_user and current_user.organization_id != organization_id:
        """
        Should return 403.
        But snoops could use this endpoint to determine how many organizations and which IDs are used.
        So 404 is returned.

        Anonymous users can access all users data.
        """
        flask.abort(404, f"No users found for organization with ID {organization_id}.")

    if get_all_assigned:
        users = (
            User.query.options(joinedload(User.settings))
            .filter(
                and_(
                    User.organization_id == organization_id,
                    User.cross_organization_access == False,
                    or_(User.is_enabled == True, exists().where(SubmissionUser.user_id == User.id)),
                )
            )
            .all()
        )
    else:
        # Will skip disabled users!
        users = (
            User.skip_disabled_query.options(joinedload(User.settings))
            .filter(and_(User.organization_id == organization_id, User.cross_organization_access == False))
            .all()
        )
    expand = expand or []
    expand = list(set(expand + DEFAULT_USERS_EXPAND))
    return UserSchema(only=expand).dump(users, many=True)


def get_client_applications_by_user(user_id: int) -> dict:
    if user_id != current_user.id:
        flask.abort(403)
    user = User.query.get_or_404(user_id, description="The User with specified ID wasn't found")
    client_applications = ClientApplication.query.filter(ClientApplication.organization_id == user.organization_id)
    return ClientApplicationSchema().dump(client_applications, many=True)


def get_metric_templates(user_id: int) -> list[dict]:
    if user_id != current_user.id:
        flask.abort(403)
    organization_id = current_user.organization_id
    metric_templates = (
        MetricTemplate.query.join(User)
        .filter(
            or_(
                MetricTemplate.user_id == user_id,
                and_(MetricTemplate.is_shared, User.organization_id == organization_id),
            )
        )
        .all()
    )
    return MetricTemplateSchema().dump(metric_templates, many=True)


def create_metric_template(user_id: int, body: dict) -> tuple[dict, int]:
    if user_id != current_user.id:
        flask.abort(403)
    metric_template = MetricTemplateSchema().load(body)
    if not metric_template.user_id:
        metric_template.user_id = user_id
    db.session.add(metric_template)
    db.session.commit()
    return MetricTemplateSchema().dump(metric_template), 201


def update_user(user_id: str, body: dict) -> tuple[dict, int]:
    if user_id != current_user.id or current_user.is_internal_machine_user or current_user.is_machine_user:
        flask.abort(403)
    user = db.session.query(User).get_or_404(user_id)
    if body.get("organization_id") is not None and user.organization_id != body.get("organization_id"):
        if not user.cross_organization_access:
            flask.abort(403, "Cannot change organization_id.")

        new_organization_id = body.get("organization_id")
        new_organization = Organization.query.get(new_organization_id)
        if not new_organization:
            flask.abort(422, f"Invalid organization_id ({new_organization_id}).")

    if body.get("tac_file_id") is not None and user.tac_file_id is None:
        user.tac_signed_on = datetime.utcnow()

    user = user_update_schema.load(body, instance=user, session=db.session)

    if user.timezone and user.timezone not in TIMEZONES.keys():
        flask.abort(422, "Invalid timezone.")
    if user.organization_id:
        organization = db.session.query(Organization).get(user.organization_id)
        if not organization:
            flask.abort(422, f"Invalid organization_id ({user.organization_id}).")

    user.id = user_id
    db.session.merge(user)
    db.session.commit()
    current_app.user_cache.invalidate_user(user_id)
    return user_update_schema.dump(user), 200


def get_hub_templates(user_id: int, template_type: HubTemplateType = HubTemplateType.HUB) -> dict:
    if user_id != current_user.id:
        flask.abort(403)
    hub_templates = HubTemplate.query.filter(
        HubTemplate.user_id == user_id, HubTemplate.template_type == template_type
    ).all()
    return {"hub_templates": hub_template_schema.dump(hub_templates, many=True)}


def create_hub_template(user_id: int, body: dict) -> tuple[dict, int]:
    if user_id != current_user.id:
        flask.abort(403)
    hub_template = HubTemplateSchema().load(body)
    hub_template.user_id = user_id
    db.session.add(hub_template)
    db.session.commit()
    return hub_template_schema.dump(hub_template), 201


def get_user_open_reports(user_id: str) -> tuple[dict, int]:
    if current_user.id != user_id:
        flask.abort(401)

    reports_query = db.session.query(ReportV2).options(load_only(ReportV2.id, ReportV2.name))
    reports_query = reports_query.filter(ReportV2.is_deleted.isnot(True))
    reports_query = reports_query.join(user_open_reports, user_open_reports.c.report_id == ReportV2.id)
    reports_query = reports_query.filter(user_open_reports.c.user_id == user_id)
    if current_user.cross_organization_access:
        reports_query = reports_query.filter(ReportV2.organization_id == current_user.organization_id)

    reports: list[ReportV2] = reports_query.all()

    return user_open_report_schema.dump(reports, many=True)


def add_user_open_report(user_id: str, report_id: str) -> tuple[None, int]:
    if current_user.id != user_id:
        flask.abort(401)

    report = get_light_report(report_id)
    if not current_user.has_report_permission(PermissionType.VIEWER, report.id):
        flask.abort(403)

    try:
        db.session.execute(update(User).where(User.id == user_id).values(last_opened_report=report_id))
        insert_query = (
            insert(user_open_reports).values({"user_id": user_id, "report_id": report_id}).on_conflict_do_nothing()
        )
        db.session.execute(insert_query)
        db.session.commit()
    except IntegrityError:
        db.session.rollback()
        flask.abort(409, "Open report already added for this user!")

    try:
        delete_query = delete(user_open_reports).where(
            and_(
                user_open_reports.c.created_at < datetime.now() - timedelta(days=30),
                user_open_reports.c.report_id != report_id,
                user_open_reports.c.user_id == user_id,
            )
        )
        db.session.execute(delete_query)
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger.error("Error deleting old open reports", exc_info=e)

    return None, 204


# TODO: Remove this function after switching CWA to delete_user_open_reports
def delete_user_open_report(user_id: str, report_id: str) -> tuple[dict, int]:
    if current_user.id != user_id:
        flask.abort(401)

    report = ReportV2.query.filter(ReportV2.id == report_id).first()
    if not report:
        flask.abort(404)

    try:
        db.session.execute(
            delete(user_open_reports).where(
                and_(user_open_reports.c.user_id == user_id, user_open_reports.c.report_id == report_id)
            )
        )
        db.session.commit()
    except ValueError as e:
        # If an account is used by multiple users at once there can be a race condition
        # when 2 users close the same tab. For internal users CWA is not using CAPI for
        # storing open reports information anymore, so this shouldn't happen at all.
        logger.warning("Error removing open report", exc_info=e)

    return {}, 204


def delete_user_open_reports(user_id: int, body: dict) -> tuple[dict, int]:
    if current_user.id != user_id:
        flask.abort(401)

    report_ids = [UUID(id) for id in body["report_ids"]]
    reports = ReportV2.query.filter(ReportV2.id.in_(report_ids)).all()
    if not len(reports):
        flask.abort(404)

    try:
        db.session.execute(
            delete(user_open_reports).where(
                and_(user_open_reports.c.user_id == user_id, user_open_reports.c.report_id.in_(report_ids))
            )
        )
        db.session.commit()
    except ValueError as e:
        # If an account is used by multiple users at once there can be a race condition
        # when 2 users close the same tab. For internal users CWA is not using CAPI for
        # storing open reports information anymore, so this shouldn't happen at all.
        db.session.rollback()
        logger.warning("Error removing open report", exc_info=e)

    return {}, 204


def bookmark(id: str, body: dict) -> tuple[dict, int]:
    if current_user.id != id:
        flask.abort(401)
    new_bookmark = bookmark_schema.load(body)

    SubmissionDAO.get_minimal_submission_or_404(new_bookmark.submission_id)

    new_bookmark.user_id = id
    db.session.add(new_bookmark)
    try:
        db.session.commit()
    except IntegrityError:
        db.session.rollback()

    return {}, 201


def delete_bookmark(id: str, submission_id: str) -> tuple[dict, int]:
    if current_user.id != id:
        flask.abort(401)

    SubmissionDAO.get_minimal_submission_or_404(submission_id)

    SubmissionBookmark.query.filter(SubmissionBookmark.submission_id == submission_id).filter(
        SubmissionBookmark.user_id == id
    ).delete()
    db.session.commit()
    return {}, 204


def get_underwriter(name: str | None, email: str | None, submission: Submission) -> User | None:
    if not name and not email:
        return None
    uw = None
    owner = User.query.get(submission.owner_id)
    uw = User.query.filter(
        User.organization_id == owner.organization_id, func.lower(User.email) == email.lower()
    ).first()
    if uw and uw.name and uw.name.lower() != name.lower():
        logger.warning(
            "The name of the found by email underwriter doesn't match the requested name",
            found_name=uw.name,
            requested_name=name,
        )
    if not uw:
        uw = User.query.filter(
            User.organization_id == owner.organization_id, func.lower(User.name) == name.lower()
        ).first()
    return uw


def get_user_signup_data() -> tuple[dict, int]:
    user = User.query.get(current_user.id)
    return user_signup_data_schema.dump(user)


def get_latest_user_email(user_id: int, is_internal: bool = False) -> tuple[dict, int]:
    if not is_internal and not current_user.is_internal_machine_user:
        flask.abort(403)

    # this is a hack for NW to get the latest user email. Because they can change their email
    # we create a new user with redirect. So to get the latest email we need to find the user and
    # iterate until we find the most recent redirect

    user = User.query.get(user_id)

    while user.redirect_user_id:
        user = User.query.get(user.redirect_user_id)

    email = user.email
    redirect_user = None
    if not user:
        flask.abort(404)

    current_user_id = user.id
    number_of_redirects = 0

    while number_of_redirects < 10:
        # get first user that redirects to the current user ordered by created_at desc
        tmp_redirect_user = (
            User.query.filter(User.redirect_user_id == current_user_id).order_by(User.created_at.desc()).first()
        )
        if not tmp_redirect_user:
            break

        current_user_id = tmp_redirect_user.id
        number_of_redirects += 1
        redirect_user = tmp_redirect_user

    if redirect_user and redirect_user.created_at > user.created_at:
        email = redirect_user.email

    if is_internal:
        return email

    return {"email": email}, 200
