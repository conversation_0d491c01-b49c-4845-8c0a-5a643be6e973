from datetime import datetime, timedelta, timezone

from flask import abort
from infrastructure_common.logging import get_logger
from llm_common.models.llm_model import LLMModel
from sqlalchemy import func
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import joinedload, load_only
import psycopg2
import pytz

from copilot.constants import MILL<PERSON>
from copilot.logic.task_metrics.task_definition_models_calibration_service import (
    DEFAULT_DISABLE_THRESHOLD,
    DEFAULT_QUALITY_TO_COST_THRESHOLD,
    TaskDefinitionModelsCalibrationService,
)
from copilot.logic.task_metrics.task_metrics_calculator import (
    DEFAULT_LOOKBACK_HOURS,
    MetricsCalculatorContext,
    TaskDefinitionMetricsCalculator,
)
from copilot.logic.tasks.input_providers.input_provider_factory import (
    get_input_provider,
)
from copilot.logic.tasks.output_processors.output_processor_factory import (
    get_output_processor,
)
from copilot.logic.tasks.task_dataset import (
    complete_task_test_run,
    init_task_for_dataset_input,
)
from copilot.models import db
from copilot.models.task_dataset import TaskDatasetModelOutcome
from copilot.models.task_metrics import TaskDefinitionMetrics
from copilot.models.tasks import (
    Task,
    TaskDefinition,
    TaskDefinitionModel,
    TaskExecution,
    TaskModel,
    TaskStatus,
)
from copilot.schemas.tasks import TaskExecutionSchema, TaskSchema
from copilot.v3.utils.db_session import no_expire_on_commit

logger = get_logger()


def _create_task_definition_metrics(task_definition_id: str, lookback_hours: int) -> TaskDefinitionMetrics:
    metrics_context = MetricsCalculatorContext.from_task_definition(
        task_definition_id=task_definition_id, lookback_hours=lookback_hours
    )
    metrics_calculator = TaskDefinitionMetricsCalculator(metrics_context)
    task_definition_metrics = metrics_calculator.calculate_metrics()
    db.session.add(task_definition_metrics)
    db.session.commit()
    return task_definition_metrics


def create_task_definition_metrics(task_definition_id: str, body: dict | None) -> tuple[None, int]:
    body = body or {}
    lookback_hours = body.get("lookback_hours", DEFAULT_LOOKBACK_HOURS)
    _create_task_definition_metrics(task_definition_id, lookback_hours)
    return None, 204


def calibrate_task_definition_models(task_definition_id: str, body: dict | None) -> tuple[None, int]:
    body = body or {}
    lookback_hours = body.get("lookback_hours", DEFAULT_LOOKBACK_HOURS)
    save_changes = body.get("save_changes", False)
    quality_to_cost_threshold = body.get("quality_to_cost_threshold", DEFAULT_QUALITY_TO_COST_THRESHOLD)
    disable_threshold_for_valid_ratio = body.get("disable_threshold_for_valid_ratio", DEFAULT_DISABLE_THRESHOLD)

    task_definition_metrics = _create_task_definition_metrics(task_definition_id, lookback_hours)
    calibration_service = TaskDefinitionModelsCalibrationService(
        task_definition_metrics, quality_to_cost_threshold, disable_threshold_for_valid_ratio
    )
    calibration_service.auto_calibrate(save_changes=save_changes)
    return None, 204


def init_task(body: dict) -> tuple[dict, int]:
    task = TaskSchema().load(body)
    if task.task_definition_id is None:
        abort(400, "Task definition ID must be provided")
    task.status = TaskStatus.PENDING

    if task.context and "task_dataset_input_id" in task.context:
        init_task_for_dataset_input(task)
    else:
        task_definition = (
            TaskDefinition.query.filter(TaskDefinition.id == task.task_definition_id)
            .options(load_only(TaskDefinition.id, TaskDefinition.code))
            .one_or_none()
        )
        if task_definition is None:
            abort(404, "Task definition not found")

        task_input, is_valid_input = get_input_provider(task_definition).provide_task_input(
            task.submission_id, task.file_id, str(task.task_definition_id), task.context
        )

        task.input = task_input or {}  # Ensure input is never None
        task.is_valid_input = is_valid_input

    try:
        db.session.add(task)
        db.session.commit()
    except IntegrityError as e:
        if isinstance(e.orig, psycopg2.errors.ForeignKeyViolation):
            logger.warning("Task creation failed due to foreign key violation", error=str(e), body=body)
            abort(404, "Task related data not found")
        raise
    return TaskSchema().dump(task), 201


def store_task_execution(task_id: str, body: dict) -> tuple[dict, int]:
    task_execution = TaskExecutionSchema().load(body)
    if str(task_execution.task_id) != task_id:
        abort(400, "Task ID in request body does not match task ID in URL")

    task = Task.query.get_or_404(task_id)

    if "task_dataset_model_outcome_id" in body:
        model_outcome = TaskDatasetModelOutcome.query.get_or_404(body["task_dataset_model_outcome_id"])
        task_execution.processed_output = model_outcome.output
        task_execution.output_evaluation = {"is_valid_output": model_outcome.is_valid_output}
        task_model = TaskModel.query.get(model_outcome.task_model_id)
        model_processing_cost = _get_processing_cost(
            task_model, model_outcome.input_tokens, model_outcome.output_tokens
        )
        validation_task_model = (
            TaskModel.query.get(model_outcome.validation_task_model_id)
            if model_outcome.validation_task_model_id
            else None
        )
        validation_processing_cost = (
            _get_processing_cost(
                validation_task_model, model_outcome.validation_input_tokens, model_outcome.validation_output_tokens
            )
            if validation_task_model
            else 0.0
        )
        task_execution.processing_cost = model_processing_cost + validation_processing_cost
        task_execution.processing_time = model_outcome.processing_time
    else:
        task_model = TaskModel.query.get(task_execution.task_model_id)
        task_execution.processing_cost = _get_processing_cost(
            task_model, task_execution.used_input_tokens, task_execution.used_output_tokens
        )

        if task_execution.validated_task_execution_id is not None:
            validated_task_execution = (
                TaskExecution.query.filter(
                    TaskExecution.id == task_execution.validated_task_execution_id, TaskExecution.task_id == task_id
                )
                .options(joinedload(TaskExecution.task_model))
                .one()
            )
            if not task_execution.is_valid_output:
                validated_task_execution.output_evaluation = task_execution.output_evaluation
            else:
                _process_output_for_task_execution(
                    task.task_definition, validated_task_execution, validation_output=task_execution.output
                )
                task_execution.processed_output = validated_task_execution.processed_output
        else:
            task_definition_model = TaskDefinitionModel.query.filter(
                TaskDefinitionModel.task_definition_id == task.task_definition_id,
                TaskDefinitionModel.task_model_id == task_execution.task_model_id,
            ).one_or_none()
            if (
                task_definition_model
                and task_definition_model.validation_task_model_id is None
                and task_execution.is_valid_output
            ):
                _process_output_for_task_execution(task.task_definition, task_execution)

    db.session.add(task_execution)
    db.session.commit()
    return TaskExecutionSchema().dump(task_execution), 201


def _get_processing_cost(task_model: TaskModel, input_tokens: int, output_tokens: int) -> float:
    if task_model.llm_model:
        llm_model = LLMModel(task_model.llm_model)
        if llm_model.cost_per_1M_input_tokens is not None and llm_model.cost_per_1M_output_tokens is not None:
            return (
                llm_model.cost_per_1M_input_tokens * input_tokens / MILLION
                + llm_model.cost_per_1M_output_tokens * output_tokens / MILLION
            )
        else:
            logger.error("Cost per 1M input/output tokens not found for LLM model", llm_model=llm_model.value)
    return 0.0


def _process_output_for_task_execution(
    task_definition: TaskDefinition,
    task_execution: TaskExecution,
    validation_output: dict | None = None,
) -> None:
    validation_explanation = None
    if not validation_output or "validation_result" not in validation_output:
        use_default_processor = task_execution.is_consolidation_run is True
        output_processor = get_output_processor(task_definition, use_default_processor)
        log = get_logger().bind(task_execution_id=task_execution.id)
        is_valid_output, processed_output = output_processor.validate_and_get_processed_output(
            validation_output or task_execution.output, log
        )
        if is_valid_output:
            task_execution.processed_output = processed_output
    else:
        is_valid_output = validation_output["validation_result"].get("is_valid", False)
        validation_explanation = validation_output["validation_result"].get("explanation", None)
    if not is_valid_output:
        task_execution.output_evaluation = {
            "is_valid_output": False,
            "validation_explanation": validation_explanation or "Unable to process output",
            "is_error": False,
            "error_message": None,
        }


def update_task(task_id: str, body: dict) -> tuple[dict, int]:
    task = Task.query.get(task_id)
    if task is None:
        abort(404, f"Task not found for task_id: {task_id}")
    is_completed = task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]
    original_status = task.status
    TaskSchema().load(body, instance=task)
    if is_completed and task.status != original_status:
        abort(400, "Status of a completed task cannot be updated")
    if not is_completed and task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
        output_processor = get_output_processor(task.task_definition)
        output_processor.process_task_output(task)
        task.processing_cost = sum([te.processing_cost for te in task.task_executions if te.processing_cost])
        task.processing_time = (datetime.now(timezone.utc) - task.created_at).total_seconds()
        if task.is_test_run:
            complete_task_test_run(task)

    db.session.commit()
    return TaskSchema().dump(task), 200


def handle_pending_tasks():
    with no_expire_on_commit():
        default_timeout = 300

        pending_tasks = (
            Task.query.join(Task.task_definition)
            .filter(
                Task.status == TaskStatus.PENDING,
                Task.created_at
                < (
                    func.now()
                    - func.make_interval(0, 0, 0, 0, 0, 0, func.coalesce(TaskDefinition.timeout, default_timeout))
                ),
            )
            .all()
        )
        for task in pending_tasks:
            logger.error(
                "Task was in PENDING status for more than 5 minutes. Marking it as FAILED",
                submission_id=task.submission_id,
                file_id=task.file_id,
                task_id=task.id,
                task_definition=task.task_definition.code,
            )
            task.is_valid_output = False
            task.status = TaskStatus.FAILED
            db.session.commit()
            try:
                output_processor = get_output_processor(task.task_definition)
                output_processor.process_task_output(task)
                db.session.commit()
            except Exception:
                db.session.rollback()
                logger.exception(
                    "Failed to process task output for failed task",
                    submission_id=task.submission_id,
                    file_id=task.file_id,
                    task_id=task.id,
                    task_definition=task.task_definition.code,
                )
    return None, 204


def get_tasks_cost_from_the_last_day(
    organization_id: int | None = None, task_definition_group: str | None = None, for_test_run: bool = False
) -> tuple[dict, int]:
    # Build a query to calculate the sum directly
    query = db.session.query(db.func.sum(Task.processing_cost))

    # Add the time filter
    query = query.filter(
        Task.created_at >= datetime.now(timezone.utc) - timedelta(hours=24), Task.is_test_run == for_test_run
    )

    # Handle joins and additional filters
    if organization_id is not None:
        query = query.filter(Task.organization_id == organization_id)

    if task_definition_group is not None:
        query = query.join(TaskDefinition).filter(TaskDefinition.group == task_definition_group)

    # Execute the query
    total_cost = query.scalar() or 0.0

    return {"total_cost": total_cost}, 200


def get_tasks_avg_daily_cost_for_previous_week(
    organization_id: int | None = None, task_definition_group: str | None = None
) -> tuple[dict, int]:
    # Get the EST timezone
    est = pytz.timezone("US/Eastern")

    # Calculate the start and end dates for the previous week
    # Ensure the date range is 7 days exactly, from 7 days ago to yesterday
    end_date = (datetime.now(est) - timedelta(hours=24)).replace(hour=0, minute=0, second=0, microsecond=0)
    start_date = end_date - timedelta(days=7)

    # Start building the query
    query = db.session.query(
        db.func.date_trunc("day", db.func.timezone("US/Eastern", Task.created_at)).label("day"),
        db.func.sum(Task.processing_cost).label("daily_cost"),
    )

    # Add the time filter
    query = query.filter(Task.created_at >= start_date, Task.created_at < end_date)

    # Apply task definition group filter if specified
    # Important: Join must come before filter
    if task_definition_group is not None:
        query = query.join(TaskDefinition).filter(TaskDefinition.group == task_definition_group)

    # Apply organization filter if specified
    if organization_id is not None:
        query = query.filter(Task.organization_id == organization_id)

    # Group by date
    query = query.group_by(db.func.date_trunc("day", db.func.timezone("US/Eastern", Task.created_at)))

    # Execute the query and get list of daily costs
    result = query.all()
    daily_costs = [row.daily_cost for row in result]

    # Take five max values to skip weekends and get their average value
    daily_costs.sort(reverse=True)
    daily_costs = daily_costs[:5]

    avg_daily_cost = sum(daily_costs) / len(daily_costs) if daily_costs else 0.0

    return {"avg_daily_cost": avg_daily_cost}, 200
