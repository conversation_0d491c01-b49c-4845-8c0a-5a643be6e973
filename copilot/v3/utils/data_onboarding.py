from uuid import UUI<PERSON>

from flask import abort
from static_common.enums.file_processing_state import FileProcessingState
from static_common.models.file_onboarding import OnboardedFile
from static_common.schemas.file_onboarding import OnboardedFileSchema

from copilot.exceptions import (
    NotReady,
    PermissionDenied,
    UnsupportedProcessedDataFormat,
)
from copilot.logic.dao.file_dao import <PERSON><PERSON><PERSON>
from copilot.v3.utils.files import FE_SCHEMA_FIELDS, get_merged_files

ALLOWED_FILE_STATES = [
    FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
    FileProcessingState.DATA_ONBOARDED,
    FileProcessingState.PROCESSING_FAILED,
    FileProcessingState.PROCESSED,
    FileProcessingState.CACHE_PROCESSED,
]


def get_onboarding_data_file(
    submission_id: UUID, clean: bool = False, dict_output: bool = False, lean_fe_schema: bool = False
) -> OnboardedFile | dict:
    try:
        data = get_merged_files(
            submission_id=submission_id,
            files_to_merge=FileDAO.get_submission_files_for_state(
                submission_id, FileProcessingState.WAITING_FOR_DATA_ONBOARDING
            ),
            data_field="onboarded_data",
            allowed_file_states=ALLOWED_FILE_STATES,
            requested_columns=[
                "file_id",
                "processed_data",
                "entity_mapped_data",
                "onboarded_data",
                "business_resolution_data",
            ],
            if_submission_data=True,
            clean=clean,
            drop_empty_values=True,
            enhance_with_resolution_data=True,
            flatten_single_structures=False,
        )
        if dict_output is False:
            return data
        else:
            # temporarily drop evidences, to make the data ligher
            schema = [f for f in FE_SCHEMA_FIELDS if f != "fields.values.evidences"]
            return OnboardedFileSchema(only=schema).dump(data) if lean_fe_schema else OnboardedFileSchema().dump(data)
    except PermissionDenied:
        abort(403)
    except NotReady as e:
        abort(409, str(e))
    except UnsupportedProcessedDataFormat as e:
        abort(400, str(e))
