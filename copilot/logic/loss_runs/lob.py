import re

from infrastructure_common.logging import get_logger

from copilot.models.policy import <PERSON><PERSON><PERSON><PERSON><PERSON>, Lob<PERSON>ields, LossPolicy
from copilot.models.sensible_claim import sanitize_string_value
from copilot.models.types import LineOfBusinessType

logger = get_logger()


def create_lob_carrier_mapping() -> dict[tuple[str, str], str]:
    lob_carrier_mappings = LOBCarrier.query.execution_options(bypass_filter_required=True).all()
    mapping_dict = {}
    for mapping in lob_carrier_mappings:
        # using tuple (lob_raw, carrier) as key
        key = (mapping.lob_raw, mapping.carrier)
        mapping_dict[key] = mapping.line_of_business
    return mapping_dict


def normalize_lob_raw(lob_raw: str | int | float | None) -> str | None:
    if isinstance(lob_raw, (int, float)):
        lob_raw = str(int(lob_raw))

    if not lob_raw:
        return lob_raw

    space_after_special_char_pattern = re.compile(r"\s*([^\w\s]+)\s*")
    multiple_spaces_pattern = re.compile(r"\s+")

    lower_without_spaces_after_special_char = space_after_special_char_pattern.sub(r"\1", lob_raw.lower())
    without_multiple_spaces = multiple_spaces_pattern.sub(" ", lower_without_spaces_after_special_char)

    return without_multiple_spaces.strip()


def parse_lob_fields(sanitized_lob_value: str | None, carrier_value: str | None) -> LobFields:
    if sanitized_lob_value is None:
        return LobFields(line_of_business=None, original_line_of_business=None, raw_line_of_business=None)

    return LobFields(
        line_of_business=check_loss_lob_value(sanitized_lob_value, carrier=carrier_value),
        original_line_of_business=normalize_lob_raw(sanitized_lob_value),
        raw_line_of_business=sanitized_lob_value,
    )


def check_loss_lob_value(
    lob_raw: str, carrier: str | None, lob_carrier_mapping_dict: dict[tuple[str, str], str] | None = None
) -> LineOfBusinessType | str | None:
    normalized_lob_raw = normalize_lob_raw(lob_raw)

    if lob_carrier_mapping_dict:
        if carrier:
            if mapping := lob_carrier_mapping_dict.get((normalized_lob_raw, carrier)):
                return mapping
        return lob_carrier_mapping_dict.get((normalized_lob_raw, None))

    if carrier:
        if mapping := LOBCarrier.query.filter_by(lob_raw=normalized_lob_raw, carrier=carrier).first():
            return mapping.line_of_business

    if mapping := LOBCarrier.query.filter_by(lob_raw=normalized_lob_raw).filter(LOBCarrier.carrier == None).first():
        return mapping.line_of_business
    else:
        return None


def get_normalized_lob_for_cross_matching(
    line_of_business: str | None, original_line_of_business: str | None
) -> str | None:
    lob: str | None = (
        line_of_business
        if line_of_business is not None and line_of_business.lower() != "unknown"
        else original_line_of_business
    )
    return lob.lower() if lob else None


def normalize_loss_policy_lob_fields(loss_policy: LossPolicy) -> None:
    sanitized_raw_lob = (
        sanitize_string_value(loss_policy.raw_line_of_business)
        if loss_policy.raw_line_of_business is not None
        else "Unknown"
    )
    lob_fields = parse_lob_fields(sanitized_raw_lob, carrier_value=None)
    loss_policy.original_line_of_business = lob_fields.original_line_of_business
    loss_policy.line_of_business = lob_fields.line_of_business
