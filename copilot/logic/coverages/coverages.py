from uuid import UUID

from infrastructure_common.logging import get_logger
from static_common.enums.organization import ExistingOrganizations, OrganizationGroups

from copilot.logic.coverages.combined_coverage_assigner import CombinedCoverageAssigner
from copilot.logic.coverages.constant_coverage_assigner import (
    ConstantAssignerConfig,
    ConstantCoverageAssigner,
)
from copilot.logic.coverages.coverage_assigner import (
    AssignerConfig,
    BaseCoverageAssigner,
)
from copilot.logic.coverages.email_based_coverage_assigner import (
    EmailBasedAssignerConfig,
    EmailBasedCoverageAssigner,
)
from copilot.logic.coverages.file_based_coverage_assigner import (
    FileBasedAssignerConfig,
    FileBasedCoverageAssigner,
)
from copilot.logic.coverages.group_based_coverage_assigner import (
    GroupBasedAssignerConfig,
    GroupBasedCoverageAssigner,
)
from copilot.logic.coverages.types import CoverageAssignment, CoverageMapping
from copilot.logic.coverages.uw_based_assigner import (
    SupportedCoverageConfig,
    UWBasedAssignerConfig,
    UWBasedCoverageAssigner,
)
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.logic.emails import (
    get_submission_earliest_email,
    is_email_address_in_email_parties,
)
from copilot.models import Organization, Submission, db
from copilot.models.reports import Coverage, SubmissionCoverage
from copilot.models.types import CoverageType, SubmissionCoverageSource
from copilot.v3.utils.reports import map_submission_coverage_type_to_coverage_type

logger = get_logger()

default_assigner_config = AssignerConfig()
default_file_assigner_config = FileBasedAssignerConfig()

PARAGON_UNXS_EMAIL = "<EMAIL>"
PARAGON_ES_EMAIL = "<EMAIL>"

###############################
#           Munich Re         #
###############################
munich_re_assigner_config = UWBasedAssignerConfig(
    uw_coverage_mappings={
        "<EMAIL>": SupportedCoverageConfig(
            "<EMAIL>",
            str(Coverage.ExistingNames.Liability.value),
            {CoverageType.PRIMARY},
            CoverageType.PRIMARY,
        ),
        "<EMAIL>": SupportedCoverageConfig(
            "<EMAIL>",
            str(Coverage.ExistingNames.Liability.value),
            {CoverageType.PRIMARY},
            CoverageType.PRIMARY,
        ),
        "<EMAIL>": SupportedCoverageConfig(
            "<EMAIL>",
            str(Coverage.ExistingNames.Liability.value),
            {CoverageType.EXCESS},
            CoverageType.EXCESS,
        ),
        "<EMAIL>": SupportedCoverageConfig(
            "<EMAIL>",
            str(Coverage.ExistingNames.Liability.value),
            {CoverageType.EXCESS},
            CoverageType.EXCESS,
        ),
        "<EMAIL>": SupportedCoverageConfig(
            "<EMAIL>",
            str(Coverage.ExistingNames.Property.value),
            {CoverageType.PRIMARY, CoverageType.EXCESS},
            CoverageType.PRIMARY,
        ),
        "<EMAIL>": SupportedCoverageConfig(
            "<EMAIL>",
            str(Coverage.ExistingNames.Property.value),
            {CoverageType.PRIMARY, CoverageType.EXCESS},
            CoverageType.PRIMARY,
        ),
    }
)


###############################
#            Paragon          #
###############################
paragon_email_assigner_config = EmailBasedAssignerConfig(
    email_coverage_assignments={
        "<EMAIL>": CoverageAssignment(
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.PRIMARY),
            [CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.EXCESS)],
        ),
        "<EMAIL>": CoverageAssignment(
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.EXCESS)
        ),
        "<EMAIL>": CoverageAssignment(
            CoverageMapping(Coverage.ExistingNames.WorkersComp, CoverageType.PRIMARY)
        ),
        "<EMAIL>": CoverageAssignment(
            CoverageMapping(Coverage.ExistingNames.Property, CoverageType.EXCESS)
        ),
        "<EMAIL>": CoverageAssignment(
            expected_coverage=None,
            allowed_coverages=[
                CoverageMapping(Coverage.ExistingNames.Package, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.Package, CoverageType.EXCESS),
                CoverageMapping(Coverage.ExistingNames.Property, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.equipmentBreakdown, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.WorkersComp, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.BusinessAuto, CoverageType.PRIMARY),
            ],
        ),
    },
    check_email_cc=True,
)
paragon_group_assigner_config = GroupBasedAssignerConfig(
    group_coverages_mappings={
        "Ally Auto": {
            CoverageMapping(Coverage.ExistingNames.Property, CoverageType.PRIMARY),
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.PRIMARY),
            CoverageMapping(Coverage.ExistingNames.BusinessAuto, CoverageType.PRIMARY),
        }
    }
)
paragon_es_unxs_constant_assigner = ConstantAssignerConfig(
    mapping=CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.EXCESS)
)
paragon_es_email_assigner_config = EmailBasedAssignerConfig(
    email_coverage_assignments={
        "<EMAIL>": CoverageAssignment(
            expected_coverage=None,
            allowed_coverages=[
                CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.EXCESS),
            ],
        )
    },
    email_to_skip_expected_fallback={"<EMAIL>"},
    check_email_cc=True,
)

###############################
#        Omaha National       #
###############################
omaha_national_assigner_config = ConstantAssignerConfig(
    mapping=CoverageMapping(Coverage.ExistingNames.WorkersComp, CoverageType.PRIMARY)
)


###############################
#              NSM            #
###############################
nsm_assigner_config = GroupBasedAssignerConfig(
    group_coverages_mappings={
        "CPS": {
            CoverageMapping(Coverage.ExistingNames.Property, CoverageType.PRIMARY),
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.PRIMARY),
        },
        "KBK": {
            CoverageMapping(Coverage.ExistingNames.Property, CoverageType.PRIMARY),
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.PRIMARY),
            CoverageMapping(Coverage.ExistingNames.BusinessAuto, CoverageType.PRIMARY),
            CoverageMapping(Coverage.ExistingNames.GarageDealers, CoverageType.PRIMARY),
        },
    }
)


###############################
#           Bowhead           #
###############################
bowhead_assigner_config = EmailBasedAssignerConfig(
    email_coverage_assignments={
        "<EMAIL>": CoverageAssignment(
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.EXCESS),
        ),
        "<EMAIL>": CoverageAssignment(
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.EXCESS),
        ),
        "<EMAIL>": CoverageAssignment(
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.PRIMARY)
        ),
        "<EMAIL>": CoverageAssignment(
            CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.PRIMARY)
        ),
        "<EMAIL>": CoverageAssignment(
            None,
            [
                CoverageMapping(Coverage.ExistingNames.professionalLiability, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.professionalLiability, CoverageType.EXCESS),
                CoverageMapping(Coverage.ExistingNames.sitePollution, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.contractorsPollution, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.productLiability, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.productLiability, CoverageType.EXCESS),
            ],
        ),
    },
    check_email_cc=True,
    fallback_to_requested=True,
)
bowhead_xs_assigner_config = ConstantAssignerConfig(
    mapping=CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.EXCESS)
)
bowhead_primary_assigner_config = ConstantAssignerConfig(
    mapping=CoverageMapping(Coverage.ExistingNames.Liability, CoverageType.PRIMARY)
)
bowhead_environmental_assigner_config = EmailBasedAssignerConfig(
    email_coverage_assignments={
        "<EMAIL>": CoverageAssignment(
            None,
            [
                CoverageMapping(Coverage.ExistingNames.professionalLiability, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.professionalLiability, CoverageType.EXCESS),
                CoverageMapping(Coverage.ExistingNames.sitePollution, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.contractorsPollution, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.productLiability, CoverageType.PRIMARY),
                CoverageMapping(Coverage.ExistingNames.productLiability, CoverageType.EXCESS),
            ],
        ),
    },
    check_email_cc=True,
    fallback_to_requested=True,
    whitelisted_coverages=[
        CoverageMapping(Coverage.ExistingNames.professionalLiability, CoverageType.PRIMARY),
        CoverageMapping(Coverage.ExistingNames.professionalLiability, CoverageType.EXCESS),
        CoverageMapping(Coverage.ExistingNames.sitePollution, CoverageType.PRIMARY),
        CoverageMapping(Coverage.ExistingNames.contractorsPollution, CoverageType.PRIMARY),
        CoverageMapping(Coverage.ExistingNames.productLiability, CoverageType.PRIMARY),
        CoverageMapping(Coverage.ExistingNames.productLiability, CoverageType.EXCESS),
    ],
)


###############################
#           ARU           #
###############################
aru_assigner_config = ConstantAssignerConfig(
    mapping=CoverageMapping(Coverage.ExistingNames.Property, CoverageType.PRIMARY)
)


###############################
#           K2                #
###############################

k2_aegis_config = ConstantAssignerConfig(
    mapping=CoverageMapping(Coverage.ExistingNames.GarageDealers, CoverageType.PRIMARY)
)


def assign_coverages(
    submission_id: UUID,
    requested_coverage_name: str | None = None,
    requested_coverage_type: CoverageType | str | None = None,
    uw_email: str | None = None,
    raise_exceptions: bool = False,
    new_only: bool = False,
    allow_no_coverages: bool = False,
    fallback_to_file_assigner: bool = False,
    source: SubmissionCoverageSource = SubmissionCoverageSource.AUTO,
    source_details: str | None = None,
) -> list[SubmissionCoverage]:
    if not (
        submission := SubmissionDAO.get_minimal_submission(
            submission_id,
            report_additional_fields=["organization_id", "correspondence_id"],
        )
    ):
        logger.warning(
            "Failed to assign coverage for submission, submission does not exist", submission_id=submission_id
        )
        return []

    # if the submission is a backfill then proceed only if coverage source is SYNC or MANUAL
    if submission.report.is_backfill and source not in {SubmissionCoverageSource.SYNC, SubmissionCoverageSource.MANUAL}:
        logger.info(
            "Skipping auto assignment as submission is a backfill and source is not SYNC or MANUAL",
            submission_id=submission.id,
            source=source,
        )
        return submission.coverages or []

    return _assign_coverages_for_submission(
        submission,
        requested_coverage_name,
        requested_coverage_type,
        uw_email,
        raise_exceptions,
        new_only,
        allow_no_coverages,
        fallback_to_file_assigner,
        source,
        source_details,
    )


def _assign_coverages_for_submission(
    submission: Submission,
    requested_coverage_name: str | None = None,
    requested_coverage_type: CoverageType | str | None = None,
    uw_email: str | None = None,
    raise_exceptions: bool = False,
    new_only: bool = False,
    allow_no_coverages: bool = False,
    fallback_to_file_assigner: bool = False,
    source: SubmissionCoverageSource = SubmissionCoverageSource.AUTO,
    source_details: str | None = None,
) -> list[SubmissionCoverage]:
    if isinstance(requested_coverage_type, str):
        requested_coverage_type = map_submission_coverage_type_to_coverage_type(requested_coverage_type)

    assigner = _choose_assigner(submission)
    result = assigner.assign_coverages(
        submission,
        requested_coverage_name,
        requested_coverage_type,
        uw_email,
        raise_exceptions,
        new_only,
        allow_no_coverages,
        source,
        source_details,
    )
    if not result and fallback_to_file_assigner:
        file_assigner = FileBasedCoverageAssigner(default_file_assigner_config)
        return file_assigner.assign_coverages(
            submission,
            requested_coverage_name,
            requested_coverage_type,
            uw_email,
            raise_exceptions,
            new_only,
            allow_no_coverages,
            source,
            source_details,
        )
    else:
        return result


def _true_on_org_match(submission: Submission) -> bool:
    """A rule that always returns True if the org ID is in the dictionary."""
    return True


def _skip_on_split_submission(submission: Submission) -> bool:
    """
    A custom rule that returns False (disallows) coverage adjustment if submission is after the split.
    If split is not detected it allows coverage adjustment.
    """
    return not submission.is_split


# Gateway rules that decide whether to allow check and adjust coverages for a submission and organization
CHECK_AND_ADJUST_COVERAGES_GATEWAY = {
    ExistingOrganizations.MunichRe.value: [_true_on_org_match],
    ExistingOrganizations.NSM.value: [_true_on_org_match],
    ExistingOrganizations.OmahaNational.value: [_true_on_org_match],
    ExistingOrganizations.BowheadSpecialty.value: [_true_on_org_match],
    ExistingOrganizations.Paragon.value: [_true_on_org_match, _skip_on_split_submission],
}


def _submission_needs_checking(submission) -> bool:
    org_id = submission.organization_id
    # If org_id is not in the gateway, return False immediately = it should not be checked
    if org_id not in CHECK_AND_ADJUST_COVERAGES_GATEWAY:
        return False

    return all(rule_fn(submission) for rule_fn in CHECK_AND_ADJUST_COVERAGES_GATEWAY[org_id])


def check_and_adjust_coverages(submission: Submission) -> None:
    try:
        if _submission_needs_checking(submission):
            _assign_coverages_for_submission(submission, allow_no_coverages=True)
    except Exception as e:
        db.session.rollback()
        logger.error("Failed to adjust coverages for submissions", submission=submission.id, error=e)


def _get_paragon_es_coverage_assigner(submission: Submission) -> BaseCoverageAssigner:
    submission_earliest_email = get_submission_earliest_email(submission)

    unxs_present = submission_earliest_email and is_email_address_in_email_parties(
        PARAGON_UNXS_EMAIL, submission_earliest_email, check_cc=True
    )
    es_present = submission_earliest_email and is_email_address_in_email_parties(
        PARAGON_ES_EMAIL, submission_earliest_email, check_cc=True
    )

    should_choose_unxs = submission_earliest_email and (unxs_present and not es_present)

    if should_choose_unxs:
        # If submission is for unsupported excess, we return constant excess assigner
        return ConstantCoverageAssigner(paragon_es_unxs_constant_assigner)
    else:
        # Otherwise return email based assigner for ES forward
        return EmailBasedCoverageAssigner(paragon_es_email_assigner_config)


def _choose_assigner(submission: Submission) -> BaseCoverageAssigner:
    organization_id = submission.organization_id
    if Organization.is_munich_re_for_id(organization_id):
        return UWBasedCoverageAssigner(munich_re_assigner_config)
    elif submission.is_paragon_es:
        return _get_paragon_es_coverage_assigner(submission)
    elif Organization.is_paragon_for_id(organization_id):
        return CombinedCoverageAssigner.combine(
            [
                GroupBasedCoverageAssigner(paragon_group_assigner_config),
                EmailBasedCoverageAssigner(paragon_email_assigner_config),
            ],
            fallback_to_requested=True,
        )
    elif Organization.is_omaha_national_for_id(organization_id):
        return ConstantCoverageAssigner(omaha_national_assigner_config)
    elif Organization.is_nsm_for_id(organization_id):
        return GroupBasedCoverageAssigner(nsm_assigner_config)
    elif Organization.is_bowhead_or_bowhead_test_for_id(organization_id):
        if submission.organization_group == OrganizationGroups.BOWHEAD_XS.value:
            return ConstantCoverageAssigner(bowhead_xs_assigner_config)
        elif submission.organization_group == OrganizationGroups.BOWHEAD_PRIMARY.value:
            return ConstantCoverageAssigner(bowhead_primary_assigner_config)
        elif submission.organization_group == OrganizationGroups.BOWHEAD_ENVIRONMENTAL.value:
            return EmailBasedCoverageAssigner(bowhead_environmental_assigner_config)
        return EmailBasedCoverageAssigner(bowhead_assigner_config)
    elif Organization.is_aru_for_id(organization_id):
        return ConstantCoverageAssigner(aru_assigner_config)
    elif (
        Organization.is_k2_for_id(organization_id)
        and submission.organization_group == OrganizationGroups.K2_AEGIS.value
    ):
        return ConstantCoverageAssigner(k2_aegis_config)
    else:
        return BaseCoverageAssigner(default_assigner_config)
