from dataclasses import dataclass

from flask import abort
from infrastructure_common.logging import get_logger
from static_common.enums.task_definition_groups import TaskDefinitionGroups

from copilot.models import ClassifierConfig, ClassifierVersion, db
from copilot.models.customizable_classifiers import ClassifierVersionTaskDefinition
from copilot.models.tasks import TaskDefinition, TaskDefinitionModel

CLASSIFIERS_CONSOLIDATION_TASK_MODEL_ID = "148840f2-1bef-4bc5-8bbe-4dfb0fcbdf07"

logger = get_logger()


@dataclass
class CustomizableClassifierIds:
    classifier_id: str
    config_id: str
    version_id: str

    @classmethod
    def from_task_definition_code(cls, task_definition_code: str) -> "CustomizableClassifierIds":
        parts = task_definition_code.split("_")
        if len(parts) != 5 or not all(parts):
            raise ValueError(f"Invalid task definition code: {task_definition_code}")
        return cls(classifier_id=parts[2], config_id=parts[3], version_id=parts[4])

    @classmethod
    def is_task_definition_for_customizable_classifier(cls, task_definition_code: str) -> bool:
        return task_definition_code.startswith("CUSTOMIZABLE_CLASSIFIER_") and len(task_definition_code.split("_")) == 5

    def to_task_definition_code(self) -> str:
        return f"CUSTOMIZABLE_CLASSIFIER_{self.classifier_id}_{self.config_id}_{self.version_id}"


def get_task_definition_code(classifier_version: ClassifierVersion, classifier_config: ClassifierConfig) -> str:
    customizable_classifier_ids = CustomizableClassifierIds(
        classifier_id=classifier_version.classifier_id,
        config_id=classifier_config.id,
        version_id=classifier_version.id,
    )
    return customizable_classifier_ids.to_task_definition_code()


def create_task_definitions(classifier_version: ClassifierVersion) -> None:
    log = logger.bind(
        classifier_version_id=classifier_version.id,
    )
    log.info("Creating MDA structures for classifier version.")
    config_ids_with_task_definition = {td.classifier_config_id for td in classifier_version.classifier_task_definitions}
    for config in classifier_version.configs:
        if config.id in config_ids_with_task_definition:
            log.info(
                "Task definition already exists for classifier version and config, skipping creation.",
                classifier_config_id=config.id,
            )
            continue

        task_definition_code = get_task_definition_code(classifier_version, config)
        task_definition = TaskDefinition.query.filter(TaskDefinition.code == task_definition_code).first()
        if task_definition:
            abort(409, f"Task definition with code {task_definition_code} already exists.")

        task_definition = TaskDefinition(
            code=task_definition_code,
            name=(
                f"CC - {classifier_version.classifier.name} - {', '.join(config.input_types)} -"
                f" {classifier_version.name}"
            ),
            group=(
                TaskDefinitionGroups.INTERNAL_CUSTOM_CLASSIFIERS.value
                if classifier_version.classifier.is_internal
                else TaskDefinitionGroups.EXTERNAL_CUSTOM_CLASSIFIERS.value
            ),
        )
        db.session.add(task_definition)
        db.session.flush()
        db.session.add(
            ClassifierVersionTaskDefinition(
                classifier_version_id=classifier_version.id,
                classifier_config_id=config.id,
                task_definition_id=task_definition.id,
            )
        )
        for classifier_config_version in config.versions:
            log = log.bind(classifier_config_id=config.id, classifier_config_version_id=classifier_config_version.id)
            if not (task_model_id := classifier_config_version.task_model_id):
                log.info(
                    "No task model ID found, creating new task model for classifier config version.",
                )
                task_model = classifier_config_version.get_task_model()
                db.session.add(task_model)
                db.session.flush()
                task_model_id = task_model.id
                classifier_config_version.task_model_id = task_model_id

            if not TaskDefinitionModel.query.filter(
                TaskDefinitionModel.id == task_definition.id, TaskDefinitionModel.task_model_id == task_model_id
            ).first():
                log.info(
                    "Creating task definition model for classifier version and config.",
                    classifier_config_id=config.id,
                    classifier_config_version_id=classifier_config_version.id,
                    task_model_id=task_model_id,
                )
                db.session.add(
                    TaskDefinitionModel(
                        task_definition_id=task_definition.id,
                        task_model_id=task_model_id,
                        order=0,
                        is_always_run=False,
                        is_aware_of_previous_runs=False,
                        is_refinement_run=False,
                        is_preprocessing_input_run=False,
                        is_consolidation_run=False,
                        is_validation_run=False,
                        can_self_reflect=False,
                        is_benchmark_run=False,
                        is_disabled=False,
                    )
                )
            else:
                log.info(
                    "Task definition model already exists for classifier version and config.",
                    classifier_config_id=config.id,
                    classifier_config_version_id=classifier_config_version.id,
                    task_model_id=task_model_id,
                )

        if len(config.active_versions) > 1:
            log.info(
                "Creating consolidation task definition model for classifier version and config.",
                classifier_config_id=config.id,
            )
            db.session.add(
                TaskDefinitionModel(
                    task_definition_id=task_definition.id,
                    task_model_id=CLASSIFIERS_CONSOLIDATION_TASK_MODEL_ID,
                    order=1,
                    is_always_run=False,
                    is_aware_of_previous_runs=False,
                    is_refinement_run=False,
                    is_preprocessing_input_run=False,
                    is_consolidation_run=True,
                    is_validation_run=False,
                    can_self_reflect=False,
                    is_benchmark_run=False,
                    is_disabled=False,
                )
            )
