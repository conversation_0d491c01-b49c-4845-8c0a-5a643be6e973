from common.logic.paragon import ParagonLines
from infrastructure_common.logging import get_logger

from copilot.logic.users.paragon_underwriter_assigner import (
    BrokerMappingResult,
    ParagonUnderwriterAssigner,
)
from copilot.logic.users.underwriter_assigner import UnderwriterAssignerConfig
from copilot.models import Submission, db
from copilot.models.paragon_wc_uw_mapping import ParagonWCUWMapping
from copilot.models.reports import SubmissionUser

logger = get_logger()


class ParagonWCUnderwriterAssigner(ParagonUnderwriterAssigner):
    _default_config = UnderwriterAssignerConfig(delete_other_assigned=True, already_assigned_error=False)

    SHANNON_SULLIVAN_USER_ID = 3660
    DEFAULT_WC_USER = 3290  # Paragon-WorkersComp
    ROBERT_ETZLER_USER_ID = 3388

    BROKER_MAPPING_STATES_TO_ASSIGN_DEFAULT_UW = {
        BrokerMappingResult.AGENCY_BLOCKED,
        BrokerMappingResult.AGENCY_HAVE_NO_UW_ASSIGNED,
    }

    def auto_assign_underwriters(
        self, submission: Submission, config: UnderwriterAssignerConfig | None = None
    ) -> list[SubmissionUser]:
        self.log = logger.bind(submission_id=submission.id)

        config = UnderwriterAssignerConfig.determine_config(config, self._default_config)

        user_ids = []
        if submission.is_renewal:
            user_ids = [self.SHANNON_SULLIVAN_USER_ID]
            self.log = self.log.bind(uw_names=["Sullivan, Shannon"])
            self.log.info("Detected renewal Paragon WC sub, assigning accordingly")
        else:
            if submission.broker:
                user_ids = self._match_email(submission)
            else:
                self.log.warning("Broker not assigned for Paragon WC submission, cannot assign UW")

            if not user_ids:
                user_ids = [self.DEFAULT_WC_USER]
                self.log.warning(
                    "Failed to find UW for Paragon WC submission, using default user",
                    default_user_id=self.DEFAULT_WC_USER,
                )

        return self.assign_underwriters(user_ids, submission, config=config)

    def _match_email(self, submission: Submission) -> list[int]:
        broker_email = submission.broker_email
        self.log = self.log.bind(broker_email=broker_email, submission_id=submission.id)

        mapping_result, user_ids = self._match_broker_email_to_uw(
            submission, ParagonLines.WORKERS_COMPENSATION, self.log
        )

        if mapping_result == BrokerMappingResult.AGENCY_BLOCKED:
            self.log.info("Agency is blocked for Paragon WC submission, ignoring UW mapping")
            return []

        if mapping_result == BrokerMappingResult.NO_BROKER_AGENCY_CONFIG:
            return self._match_email_db_fallback(submission)

        return user_ids

    def _match_email_db_fallback(self, submission: Submission) -> list[int]:
        broker_email = submission.broker_email
        # Temporary fallback - remove all below this comment after ENG-22764 is tested on prod for a few days
        self.log.info("No Paragon agency config found for broker email - temp fallback to 'ParagonWCUWMapping' table")
        mapping = db.session.query(ParagonWCUWMapping).filter_by(sheet_broker_email=broker_email).first()

        if not mapping:
            self.log.warning("No Paragon WC UW mapping found for broker email")
            return []
        else:
            if not mapping.is_confirmed:
                self.log.warning("Paragon WC UW mapping found for broker email, but it's not confirmed")
                return []

            self.log.info(
                "Matched Paragon WC UW mapping by broker email",
                matched_name=mapping.sheet_broker_name,
            )
            self.log = self.log.bind(uw_name=mapping.sheet_uw_name)

            return [mapping.user_id]
