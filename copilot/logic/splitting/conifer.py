from dataclasses import dataclass

from dataclasses_json import dataclass_json
from events_common.lambdas.constants import (
    ASYNC_OP_ID_KEY,
    USER_EMAIL,
    USER_PROVIDED_QUOTE_NUMBER_KEY,
)
from flask import current_app
from flask_login import current_user
from infrastructure_common.logging import get_logger
from static_common.enums.async_ops import AsyncOperation
from static_common.enums.coverage_names import CoverageName
from static_common.enums.organization import ExistingOrganizations
from structlog import BoundLogger

from copilot.logic.coverages.user_forced_assigner import (
    UserForcedAssignerConfig,
    UserForcedCoverageAssigner,
    UserForcedCoverageConfig,
)
from copilot.logic.dao.submission_dao import SubmissionDAO
from copilot.models import Submission
from copilot.models.async_ops import AsyncOperationDB
from copilot.models.types import CoverageType, SubmissionEvent
from copilot.services.async_ops import AsyncOpsService

logger = get_logger()


@dataclass_json
@dataclass
class CoverageForConiferSplit:
    coverage_name: CoverageName
    coverage_type: CoverageType

    @staticmethod
    def from_string(str_repr: str) -> "CoverageForConiferSplit":
        # We expect format: "coverage_name - coverage_type", e.g. "Liability - PRIMARY"
        try:
            name_str, type_str = str_repr.split(" - ", 1)
        except ValueError:
            raise ValueError(f"Expected format 'coverage_name - coverage_type'. Got '{str_repr}' instead.")
        cov_name = CoverageName.try_parse_str(name_str)
        cov_type = CoverageType.try_parse_str(type_str)
        if cov_name is None:
            raise ValueError(f"Valid Coverage name is required in '{str_repr}' - '{name_str}' is not valid")
        if cov_type is None:
            raise ValueError(f"Valid Coverage type is required in '{str_repr}' - '{type_str}' is not valid")
        return CoverageForConiferSplit(coverage_name=cov_name, coverage_type=cov_type)


@dataclass_json
@dataclass
class ConiferSplitSpec:
    requested_coverages: list[CoverageForConiferSplit]
    quote_number: str | None = None  # This can be empty (UAT has vs PROD empty)


@dataclass_json
@dataclass
class ListOfConiferSplitSpec:
    split_specs: list[ConiferSplitSpec]


@dataclass
class ConiferSplitResult:
    split_submission_ids: list[str]


class ConiferSplitInput:
    def __init__(
        self,
        submission: Submission,
        splits: list[ConiferSplitSpec],
        send_external_clearing_finished_event_after_splitting: bool,
    ):
        if submission.organization_id != ExistingOrganizations.BishopConifer.value:
            raise RuntimeError(
                "ConiferSplitInput can only be used for BishopConifer submissions",
                sub_organization_id=submission.organization_id,
            )
        if not splits:
            raise RuntimeError("At least one split must be provided in Conifer split input")
        self.submission = submission
        self.splits = splits
        self.send_external_clearing_finished_event_after_splitting = (
            send_external_clearing_finished_event_after_splitting
        )


def split_conifer_submission(split_input: ConiferSplitInput, bnd_log: BoundLogger | None = None) -> ConiferSplitResult:
    # Conifer split is really simple.
    # 1. Take origin submission and duplicate it in full N times, where N is the number of splits
    #    (This function works also when only 1 split is requested - in this case no split will be created, only
    #    coverages will be forced.)
    # 2. Force assign coverages specified in each split to each duplicate and also to origin submission
    # That's it.
    log = bnd_log or logger
    submission = split_input.submission
    splits = split_input.splits

    if not splits:
        # We should not ever be here due to validations happening before but if we are, we should raise quickly
        raise RuntimeError("At least one split must be provided in Conifer split input")

    # Create final submissions: the original plus duplicates if needed
    if len(splits) > 1:
        log.info("Splitting Conifer submission into multiple submissions", splits=splits)
    else:
        log.info(
            "No need for Conifer submission splitting, forcing user specified coverages for Conifer submission",
            splits=splits,
        )
    final_submissions = [submission] + [SubmissionDAO.duplicate_submission(submission) for _ in range(len(splits) - 1)]
    log.info(
        "Created final Conifer submission(s)",
        final_submission_ids=[str(submission.id) for submission in final_submissions],
    )

    # Force coverage assignment & (optionally) send events for each split
    for idx, split_spec in enumerate(splits):
        target_submission = final_submissions[idx]
        user_requested_coverages = [
            UserForcedCoverageConfig(
                coverage_name=cov.coverage_name,
                coverage_type=cov.coverage_type,
            )
            for cov in split_spec.requested_coverages
        ]

        _force_assign_user_specified_coverages(user_requested_coverages, target_submission, log)

        if split_input.send_external_clearing_finished_event_after_splitting:
            pending_async_op = _create_pending_async_op(target_submission, split_spec.quote_number)
            _send_clearing_finished_event(split_spec, target_submission, pending_async_op)

    result = ConiferSplitResult(split_submission_ids=[str(submission.id) for submission in final_submissions])
    return result


def _send_clearing_finished_event(
    split_spec: ConiferSplitSpec,
    target_submission: Submission,
    async_op: AsyncOperationDB,
) -> None:
    # If quote_number is None, we just skip it in the additional_data (this is on PROD vs UAT)
    additional_data = {}
    if split_spec.quote_number:
        additional_data[USER_PROVIDED_QUOTE_NUMBER_KEY] = split_spec.quote_number
    if async_op:
        additional_data[ASYNC_OP_ID_KEY] = str(async_op.id)
        additional_data[USER_EMAIL] = async_op.executing_user_email
    current_app.event_service.handle_submission_event(
        event=SubmissionEvent.EXTERNAL_CLEARING_FINISHED,
        submission=target_submission,
        additional_data=additional_data,
    )


def _create_pending_async_op(submission: Submission, quote_number: str) -> AsyncOperationDB:
    organization_id = submission.organization_id
    submission_id = str(submission.id)
    report_id = str(submission.report_id)
    pending_async_op = AsyncOpsService.add_pending_async_op(
        organization_id=organization_id,
        report_id=report_id,
        submission_id=submission_id,
        operation=AsyncOperation.FINALIZE_CONIFER_EXTERNAL_CLEARING,
        logical_identifier=quote_number,
        executing_user_email=current_user.email,
        commit=True,
    )
    return pending_async_op


def _force_assign_user_specified_coverages(
    user_requested_coverages: list[UserForcedCoverageConfig], submission: Submission, log
) -> None:
    log.info("Forcing user specified coverages", user_requested_coverages=user_requested_coverages)
    assigner_config = UserForcedAssignerConfig(user_requested_coverages=user_requested_coverages)
    coverage_assigner = UserForcedCoverageAssigner(config=assigner_config)
    coverage_assigner.assign_coverages(submission)
