from collections import defaultdict
from datetime import date, datetime
from typing import Any
from uuid import UUID

from dateutil import parser
from fuzzywuzzy import fuzz

from copilot.logic.tasks.constants import FUZZY_SCORE_THRESHOLD
from copilot.logic.tasks.dataset_evaluators.dataset_evaluator_factory import (
    get_dataset_evaluator,
)
from copilot.logic.tasks.input_providers.customizable_classifier_input_provider import (
    CustomizableClassifierInputProvider,
)
from copilot.models import db
from copilot.models.task_dataset import (
    TaskDataset,
    TaskDatasetExecution,
    TaskDatasetGroundTruth,
    TaskDatasetInput,
    TaskDatasetModelOutcome,
)
from copilot.models.tasks import Task, TaskExecution
from copilot.models.types import PredictionResult


def init_task_for_dataset_input(task: Task) -> None:
    task_dataset_input_id = task.context["task_dataset_input_id"]
    task_dataset_execution_id = task.context["task_dataset_execution_id"]
    task_dataset_input = TaskDatasetInput.query.filter(TaskDatasetInput.id == task_dataset_input_id).first()
    task.submission_id = task_dataset_input.submission_id
    task.file_id = task_dataset_input.file_id
    task.business_id = task_dataset_input.business_id
    task.input, task.is_valid_input = _get_input_from_task_dataset_input(task, task_dataset_input)
    task.task_dataset_execution_id = task_dataset_execution_id
    task.is_test_run = True


def _get_input_from_task_dataset_input(task: Task, task_dataset_input: TaskDatasetInput) -> tuple[dict, bool]:
    is_customizable_classifier = (task_dataset_input.context or {}).get("is_customizable_classifier", False)
    if not is_customizable_classifier:
        is_valid_input = True if task_dataset_input.input else False
        return task_dataset_input.input, is_valid_input
    return CustomizableClassifierInputProvider().provide_task_input(
        submission_id=task.submission_id,
        file_id=task.file_id,
        task_definition_id=task.task_definition_id,
        context=task_dataset_input.context,
    )


def complete_task_test_run(task: Task) -> None:
    task_dataset_input_id = UUID(task.context.get("task_dataset_input_id"))
    task_dataset_execution_id = UUID(task.context.get("task_dataset_execution_id"))
    task_dataset_execution = TaskDatasetExecution.query.get_or_404(task_dataset_execution_id)

    ground_truth = _get_ground_truth(task_dataset_execution.task_dataset_id, task_dataset_input_id)
    task_execution_ids_to_validation = {
        te.validated_task_execution_id: te for te in task.task_executions if te.validated_task_execution_id
    }
    task_model_id_to_task_executions = defaultdict(list)
    for task_execution in task.task_executions:
        if task_execution.is_consolidation_run or task_execution.is_validation_run:
            continue
        task_model_id_to_task_executions[task_execution.task_model_id].append(task_execution)
        if task_execution.id in task_execution_ids_to_validation:
            task_model_id_to_task_executions[task_execution.task_model_id].append(
                task_execution_ids_to_validation[task_execution.id]
            )

    model_outcomes = []
    for task_model_id, task_executions in task_model_id_to_task_executions.items():
        validation_task_model_id = task.task_definition.execution_model_ids_to_validation_model_ids.get(task_model_id)
        if len(task_executions) == 1 and (
            model_outcome := _get_existing_model_outcome(task_dataset_input_id, task_model_id, validation_task_model_id)
        ):
            model_outcomes.append(model_outcome)
            continue
        input_tokens = sum(te.used_input_tokens for te in task_executions if not te.is_validation_run)
        output_tokens = sum(te.used_output_tokens for te in task_executions if not te.is_validation_run)
        validation_input_tokens = sum(te.used_input_tokens for te in task_executions if te.is_validation_run)
        validation_output_tokens = sum(te.used_output_tokens for te in task_executions if te.is_validation_run)
        processing_time = sum(te.processing_time for te in task_executions)
        execution_output = _get_output_task_executions(task_executions)

        model_outcomes.append(
            TaskDatasetModelOutcome(
                task_dataset_input_id=task_dataset_input_id,
                task_model_id=task_model_id,
                validation_task_model_id=validation_task_model_id,
                task_definition_id=task.task_definition.id,
                output=execution_output.processed_output if execution_output.is_valid_output else None,
                is_valid_output=execution_output.is_valid_output,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                validation_input_tokens=validation_input_tokens,
                validation_output_tokens=validation_output_tokens,
                processing_time=processing_time,
            )
        )
        _set_prediction_result(task, task_dataset_execution.task_dataset, ground_truth, model_outcomes[-1])
    model_outcomes.append(
        TaskDatasetModelOutcome(
            task_dataset_input_id=task_dataset_input_id,
            task_definition_id=task.task_definition.id,
            output=task.processed_output if task.is_valid_output else None,
            is_valid_output=task.is_valid_output,
            input_tokens=sum(mo.input_tokens for mo in model_outcomes),
            output_tokens=sum(mo.output_tokens for mo in model_outcomes),
            validation_input_tokens=sum(mo.validation_input_tokens for mo in model_outcomes),
            validation_output_tokens=sum(mo.validation_output_tokens for mo in model_outcomes),
            processing_time=sum(mo.processing_time for mo in model_outcomes),
        )
    )
    _set_prediction_result(task, task_dataset_execution.task_dataset, ground_truth, model_outcomes[-1])
    db.session.add_all(model_outcomes)


def _get_existing_model_outcome(
    task_dataset_input_id: UUID, task_model_id: UUID, validation_task_model_id: UUID | None
) -> TaskDatasetModelOutcome | None:
    return TaskDatasetModelOutcome.query.filter(
        TaskDatasetModelOutcome.task_model_id == task_model_id,
        TaskDatasetModelOutcome.validation_task_model_id == validation_task_model_id,
        TaskDatasetModelOutcome.task_dataset_input_id == task_dataset_input_id,
    ).one_or_none()


def _get_ground_truth(task_dataset_id: UUID, task_dataset_input_id: UUID) -> TaskDatasetGroundTruth:
    return TaskDatasetGroundTruth.query.filter(
        TaskDatasetGroundTruth.task_dataset_input_id == task_dataset_input_id,
        TaskDatasetGroundTruth.task_dataset_id == task_dataset_id,
    ).one()


def _get_output_task_executions(task_executions: list[TaskExecution]) -> TaskExecution:
    output_task_execution = next(
        (te for te in task_executions if te.is_self_reflection_run and not te.is_validation_run), None
    )
    if not output_task_execution:
        output_task_execution = next(te for te in task_executions if not te.is_validation_run)
    return output_task_execution


def _set_prediction_result(
    task: Task, task_dataset: TaskDataset, ground_truth: TaskDatasetGroundTruth, model_outcome: TaskDatasetModelOutcome
) -> None:
    if not model_outcome.is_valid_output:
        return

    score: float | None = None
    if task_dataset.output_type == "CUSTOM":
        dataset_evaluator = get_dataset_evaluator(task.task_definition)
        prediction_result = dataset_evaluator.evaluate_prediction_result(
            model_outcome.output, ground_truth.value, ground_truth.has_value
        )
    elif task_dataset.output_type == "BOOLEAN":
        prediction_result = _get_boolean_prediction_result(ground_truth, model_outcome)
    elif task_dataset.output_type == "TEXT":
        prediction_result, score = _get_fuzzy_match_prediction_result(ground_truth, model_outcome)
    else:
        value_types = {"NUMERIC": float, "DATE": date, "TAGS": set}
        value_type = value_types.get(task_dataset.output_type)
        if value_type is None:
            raise ValueError(f"Unsupported output type: {task_dataset.output_type}")
        prediction_result = _get_not_boolean_prediction_result(ground_truth, model_outcome, value_type)
    model_outcome.prediction_result = prediction_result
    if score:
        model_outcome.score = score


def _get_boolean_prediction_result(
    ground_truth: TaskDatasetGroundTruth, model_outcome: TaskDatasetModelOutcome
) -> PredictionResult:
    outcome_raw_value = _get_raw_value(model_outcome.output, bool)
    ground_truth_raw_value = _get_raw_value(ground_truth.value, bool)
    if prediction_result := _get_prediction_result_for_missing_values(outcome_raw_value, ground_truth_raw_value):
        return prediction_result
    if outcome_raw_value:
        return PredictionResult.TRUE_POSITIVE if ground_truth_raw_value else PredictionResult.FALSE_POSITIVE
    return PredictionResult.FALSE_NEGATIVE if ground_truth_raw_value else PredictionResult.TRUE_NEGATIVE


def _get_fuzzy_match_prediction_result(
    ground_truth: TaskDatasetGroundTruth, model_outcome: TaskDatasetModelOutcome
) -> tuple[PredictionResult, float]:
    outcome_raw_value = _get_raw_value(model_outcome.output, str)
    ground_truth_raw_value = _get_raw_value(ground_truth.value, str)
    if prediction_result := _get_prediction_result_for_missing_values(outcome_raw_value, ground_truth_raw_value):
        return prediction_result, 100.0

    fuzzy_score_threshold: int = FUZZY_SCORE_THRESHOLD
    fuzzy_score = fuzz.ratio(outcome_raw_value, ground_truth_raw_value)
    if fuzzy_score >= fuzzy_score_threshold:
        prediction_result = PredictionResult.TRUE_POSITIVE
    else:
        prediction_result = PredictionResult.FALSE_POSITIVE

    return prediction_result, fuzzy_score


def _get_not_boolean_prediction_result(
    ground_truth: TaskDatasetGroundTruth, model_outcome: TaskDatasetModelOutcome, value_type: type
) -> PredictionResult:
    outcome_raw_value = _get_raw_value(model_outcome.output, value_type)
    ground_truth_raw_value = _get_raw_value(ground_truth.value, value_type)
    prediction_result = _get_prediction_result_for_missing_values(outcome_raw_value, ground_truth_raw_value)
    if prediction_result:
        return prediction_result
    return (
        PredictionResult.TRUE_POSITIVE
        if ground_truth_raw_value == outcome_raw_value
        else PredictionResult.FALSE_POSITIVE
    )


def _get_raw_value(output: dict | None, value_type: type) -> Any | None:
    raw_value = output.get("value", None) if output else None
    if raw_value is None:
        return None
    try:
        if value_type is date:
            if isinstance(raw_value, str):
                return parser.parse(raw_value).date()
            elif isinstance(raw_value, datetime):
                return raw_value.date()
        return value_type(raw_value)
    except (ValueError, TypeError):
        return None


def _get_prediction_result_for_missing_values(
    outcome_raw_value: Any, ground_truth_raw_value: Any
) -> PredictionResult | None:
    if ground_truth_raw_value is None:
        return PredictionResult.TRUE_NEGATIVE if outcome_raw_value is None else PredictionResult.FALSE_POSITIVE
    elif outcome_raw_value is None:
        return PredictionResult.FALSE_NEGATIVE
    return None
