from datascience_common.models.customizable_classifiers.tasks import ClassifierTaskInput

from copilot.logic.tasks.input_providers.base_input_provider import BaseInput<PERSON>rovider
from copilot.models import db
from copilot.models.customizable_classifiers import (
    ClassifierVersion,
    ClassifierVersionTaskDefinition,
)


class CustomizableClassifierInputProvider(BaseInputProvider):
    def provide_task_input(
        self, submission_id: str, file_id: str, task_definition_id: str, context: dict | None = None
    ) -> tuple[dict | None, bool]:
        if not context or "datasets_key" not in context:
            return None, False

        classifier_version_task_definition = (
            db.session.query(ClassifierVersionTaskDefinition)
            .filter(ClassifierVersionTaskDefinition.task_definition_id == task_definition_id)
            .one_or_none()
        )
        if not classifier_version_task_definition:
            return None, False

        classifier_version = db.session.query(ClassifierVersion).get(
            classifier_version_task_definition.classifier_version_id
        )
        classifier_task_input = ClassifierTaskInput(
            classifier_name=classifier_version.classifier.name,
            classifier_description=classifier_version.classifier_description,
            fact_subtype_id=classifier_version.classifier.fact_subtype_id,
            output_unit=classifier_version.classifier.output_unit,
            output_type=classifier_version.classifier.output_type,
            datasets_key=context["datasets_key"],
            datasets_stored_in_s3=context.get("datasets_stored_in_s3", False),
        )
        return classifier_task_input.model_dump(), True
