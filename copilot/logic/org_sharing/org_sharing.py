from infrastructure_common.logging import get_logger
from sqlalchemy.orm import load_only
from sqlalchemy.sql.expression import nullslast

from copilot.constants import ORG_GROUP_TO_USER_GROUP_MAPPINGS
from copilot.logic.users.permissions import add_group_permission
from copilot.models import Organization, ReportPermission, ReportV2, db
from copilot.models.emails import Email, ReportEmailCorrespondence
from copilot.models.types import PermissionType
from copilot.models.user import UserGroup

logger = get_logger()


def share_report_with_org(report: ReportV2, delete_other_group_permissions: bool = False) -> None:
    try:
        _share_report_with_org(report, delete_other_group_permissions)
    except:
        logger.exception(
            "Failed to share the report with org",
            report_id=report.id,
            delete_other_group_permissions=delete_other_group_permissions,
        )


def _share_report_with_org(report: ReportV2, delete_other_group_permissions: bool = False) -> None:
    groups_ids_to_share = _get_group_ids_for_org_sharing(report)
    if not groups_ids_to_share:
        logger.info("Sharing with the org", report_id=report.id)
        report.organization_permission_level = PermissionType.EDITOR
        return
    for group_id in groups_ids_to_share:
        logger.info("Sharing with the master group", report_id=report.id, group_id=group_id)
        add_group_permission(
            ReportPermission(grantee_group_id=group_id, permission_type=PermissionType.EDITOR, report_id=report.id),
            report=report,
            is_admin=True,
            raise_conflict=False,
            send_event=False,
        )
    if delete_other_group_permissions:
        logger.info("Deleting other group permissions", report_id=report.id)
        report_permissions: list[ReportPermission] = report.report_permissions
        group_permissions = [p for p in report_permissions if p.grantee_group_id]
        to_delete = [p for p in group_permissions if str(p.grantee_group_id) not in groups_ids_to_share]
        for permission in to_delete:
            db.session.delete(permission)
        if to_delete:
            db.session.commit()


def _get_group_ids_for_org_sharing(report: ReportV2) -> list[str]:
    groups = []
    master_group = _get_master_group_id_for_org(report.organization_id)
    if master_group:
        groups.append(master_group)
    other_groups = _get_group_id_from_email(report)
    if other_groups:
        groups.extend(other_groups)
    groups_for_org_group = _get_group_ids_for_org_group(report)
    if groups_for_org_group:
        groups.extend(groups_for_org_group)
    return groups


def _get_group_ids_for_org_group(report: ReportV2) -> list[str]:
    org_mappings = ORG_GROUP_TO_USER_GROUP_MAPPINGS.get(report.organization_id)

    if not org_mappings:
        return []

    group_names = org_mappings.get(report.org_group)

    if not group_names:
        return []

    return _get_group_ids_by_names(group_names, report)


def _get_group_id_from_email(report: ReportV2) -> list[str]:
    result = set()
    from_account = _get_group_ids_from_email_account(report)
    if from_account:
        result.update(from_account)
    from_from = _get_group_ids_from_email_from(report)
    if from_from:
        result.update(from_from)
    return list(result)


def get_paragon_group_name_from_org_group(report: ReportV2) -> str | None:
    if not Organization.is_paragon_for_id(report.organization_id) or not report.org_group:
        return None

    mappings = Organization.PARAGON_ORG_GROUP_TO_USER_GROUP
    group_name = mappings.get(report.org_group)
    if not group_name:
        logger.error("Did not find group!", report_id=report.id, group_name=group_name)
        return None
    return group_name


def get_paragon_group_name_from_email_account(report: ReportV2) -> str | None:
    if not Organization.is_paragon_for_id(report.organization_id) or not report.correspondence_id:
        return None

    return _get_group_name_from_email_email_account(report, use_report_email_correspondence=True)


def get_k2_group_name_from_email_account(report: ReportV2) -> str | None:
    if not Organization.is_k2_for_id(report.organization_id) or not report.correspondence_id:
        return None

    return _get_group_name_from_email_email_account(report)


def _get_group_name_from_email_email_account(
    report: ReportV2, use_report_email_correspondence: bool = False
) -> str | None:
    emails = (
        db.session.query(Email)
        .options(load_only(Email.email_account))
        .filter(Email.correspondence_id == report.correspondence_id)
        .order_by(nullslast(Email.email_sent_at.desc()))
        .all()
    )
    group_name = None
    group_mapping = {}
    if Organization.is_paragon_for_id(report.organization_id):
        group_mapping = Organization.PARAGON_FORWARD_EMAIL_TO_GROUP
    if Organization.is_merchants_for_id(report.organization_id):
        group_mapping = Organization.MERCHANTS_FORWARD_EMAIL_TO_GROUP
    if Organization.is_k2_for_id(report.organization_id):
        group_mapping = Organization.K2_FORWARD_EMAIL_TO_GROUP

    if use_report_email_correspondence:
        correspondence: ReportEmailCorrespondence | None = report.correspondence
        correspondence_email_account = correspondence.email_account if correspondence else None
        correspondence_email_account = (
            correspondence_email_account.lower().strip() if correspondence_email_account else None
        )

        if correspondence_email_account and correspondence_email_account in group_mapping:
            group_name = group_mapping[correspondence_email_account]
            return group_name

    for email in emails:
        account = email.email_account.lower().strip() if email.email_account else None
        if not account:
            continue
        if account in group_mapping:
            group_name = group_mapping[account]
            break
    if not group_name:
        logger.error("Did not find group!", report_id=report.id, group_name=group_name)
        return None
    return group_name


def _get_group_ids_from_email_account(report: ReportV2) -> list[str] | None:
    group_name = None
    if Organization.is_paragon_for_id(report.organization_id):
        group_name = get_paragon_group_name_from_email_account(report)
    if Organization.is_merchants_for_id(report.organization_id):
        group_name = _get_group_name_from_email_email_account(report)
    if Organization.is_k2_for_id(report.organization_id):
        group_name = _get_group_name_from_email_email_account(report)
    if not group_name:
        return None
    return _get_group_ids_by_names([group_name], report)


def _get_group_ids_from_email_from(report: ReportV2) -> list[str] | None:
    if not Organization.is_nsm_for_id(report.organization_id) or not report.correspondence_id:
        return None

    emails = (
        db.session.query(Email)
        .options(load_only(Email.email_from))
        .filter(Email.correspondence_id == report.correspondence_id)
        .all()
    )
    group_names = []
    for email in emails:
        if not email or not email.email_from:
            continue
        for nsm_email, group_name in Organization.NSM_EMAIL_TO_GROUP.items():
            if nsm_email.lower() in email.email_from.lower():
                group_names.append(group_name)
    if not group_names:
        logger.warning("Did not find group based on email from!", report_id=report.id, group_names=group_names)
        return None
    logger.info("Found group from email", report_id=report.id, group_name=group_names)
    return _get_group_ids_by_names(group_names, report)


def _get_group_ids_by_names(names: list[str], report: ReportV2) -> list[str]:
    query = UserGroup.query.filter(UserGroup.organization_id == report.organization_id)
    query = query.filter(UserGroup.name.in_(names))
    groups = query.all()
    if not groups:
        logger.error("Did not find groups!", report_id=report.id, names=names)
        return []
    return [str(grp.id) for grp in groups]


def _get_master_group_id_for_org(organization_id: int) -> str | None:
    master_group_name = None
    if Organization.is_paragon_for_id(organization_id):
        master_group_name = Organization.PARAGON_MASTER_GROUP_NAME
    if Organization.is_nsm_for_id(organization_id):
        master_group_name = Organization.NSM_MASTER_GROUP_NAME
    if Organization.is_kalapa_new_demo_for_id(organization_id):
        master_group_name = Organization.KALEPA_NEW_DEMO_MASTER_GROUP_NAME
    if Organization.is_k2_for_id(organization_id):
        master_group_name = Organization.K2_MASTER_GROUP_NAME
    if Organization.is_kalepa_demo_for_id(organization_id):
        master_group_name = Organization.KALEPA_DEMO_MASTER_GROUP_NAME
    if not master_group_name:
        return None
    query = db.session.query(UserGroup).filter(UserGroup.organization_id == organization_id)
    query = query.filter(UserGroup.name == master_group_name)
    group = query.first()
    return str(group.id) if group else None
