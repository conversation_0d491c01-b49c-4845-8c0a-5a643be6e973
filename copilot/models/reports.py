from __future__ import annotations

from dataclasses import dataclass, field
from datetime import datetime
from functools import lru_cache
from typing import TYPE_CHECKING
from uuid import uuid4
import concurrent
import json
import os
import threading
import time
import uuid

from bs4 import BeautifulSoup
from common.utils.submission_note import extract_text_from_note
from entity_resolution_service_client_v3 import Entity, Premises
from flask_login import current_user
from infrastructure_common.logging import get_logger
from sqlalchemy import (
    DECIMAL,
    JSON,
    Boolean,
    Column,
    Computed,
    DateTime,
    Enum,
    Float,
    ForeignKey,
    Index,
    Integer,
    Sequence,
    String,
    UniqueConstraint,
    and_,
    case,
    desc,
    event,
    literal,
    or_,
)
from sqlalchemy.dialects.postgresql import ARRAY, JSONB, UUID
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import joinedload, load_only, relationship
from sqlalchemy.orm.attributes import flag_modified
from sqlalchemy.sql import func
from static_common.constants import NO_BROKER_GROUP, REAL_ESTATE_NAME
from static_common.enums.additional_identifier import AdditionalIdentifierType
from static_common.enums.brokerage_employee_roles import BrokerageEmployeeRoles
from static_common.enums.business_resolve import BusinessesResolvingState
from static_common.enums.classification_document_type import ClassificationDocumentType
from static_common.enums.contractor import ContractorSubmissionType
from static_common.enums.coverage import CoverageGroup
from static_common.enums.entity import EntityPremisesType
from static_common.enums.enum import StrEnum
from static_common.enums.file_processing_state import FileProcessingState
from static_common.enums.file_type import FileType
from static_common.enums.insurance import ProjectInsuranceType
from static_common.enums.organization import ExistingOrganizations, OrganizationGroups
from static_common.enums.origin import Origin
from static_common.enums.recommendation import RecommendationActionEnum
from static_common.enums.report import ReportStatus
from static_common.enums.submission import (
    SubmissionCoverageType,
    SubmissionMode,
    SubmissionRelationSource,
    SubmissionRelationType,
    SubmissionStage,
)
from static_common.enums.submission_business import (
    SubmissionBusinessEntityNamedInsured,
    SubmissionBusinessEntityRole,
)
from static_common.enums.submission_client_id import SubmissionClientIdSource
from static_common.enums.submission_processing_state import SubmissionProcessingState
from static_common.enums.taxonomy_change_type import TaxonomyChangeType
from static_common.enums.underwriters import SubmissionUserSource
from static_common.mappings.client_stage_fields import CUSTOM_STATUS_FIELDS_PER_CLIENT
from static_common.models.first_party import FirstPartyFields
from static_common.models.submission_level_data import SourceDetails
from werkzeug.exceptions import Conflict
import flask
import psycopg2

from copilot.constants import (
    ALLY_AUTO_EMAIL,
    CONSTRUCTION_NAICS_PREFIX,
    K2_AEGIS_EMAIL,
    K2_VIKCO_EMAIL,
    NATIONWIDE_DEFAULT_OWNER_ID,
    PARAGON_ES_INBOX,
    PARAGON_PSP_EMAIL,
    PARAGON_WC_EMAIL,
    TRANSPORTATION_NAICS_PREFIX,
    WAREHOUSING_NAICS_PREFIX,
)
from copilot.database_events.after_commit_events import (
    AfterCommitEventType,
    add_after_commit_event,
)
from copilot.models import Brokerage, BrokerageEmployee
from copilot.models._private import BaseModel, db, meta
from copilot.models.broker_groups import BrokerGroup, BrokerGroupMapping
from copilot.models.brokerages_v2 import (
    SubmissionBrokerage,
    SubmissionBrokerageEmployee,
)

# required for the relationship mapping
from copilot.models.emails import Email, ReportEmailCorrespondence  # noqa
from copilot.models.execution_events import ExecutionEvent
from copilot.models.files import File, ProcessedFile
from copilot.models.lob import Lob  # noqa
from copilot.models.organization import Organization
from copilot.models.permissions import ReportPermission
from copilot.models.resolution import ResolutionResult
from copilot.models.submission_consolidation_process import (
    SubmissionConsolidationProcess,
)
from copilot.models.submission_premises import SubmissionPremises
from copilot.models.submission_processing import SubmissionProcessing
from copilot.models.submission_relations import SubmissionRelation
from copilot.models.types import (
    AlertType,
    BrokerageEmployeeSource,
    ClearingStatus,
    CoverageType,
    MissingDataStatus,
    PermissionType,
    PolicyLevelDeductibleType,
    ReportDependencyType,
    ReportShadowType,
    ReportTriageResult,
    SubmissionActionType,
    SubmissionConsolidationStatus,
    SubmissionCoverageSource,
    SubmissionParentType,
    UserShadowFileState,
)

# do not remove, it's required although there is no explicit usage
from copilot.models.underlying_policy import UnderlyingPolicy  # noqa
from copilot.models.utils import (
    compare_extracted_data_from_email,
    filter_out_data_from_not_copied_files,
)
from copilot.models.verification_check import VerificationCheckResult  # noqa
from copilot.notifications.errors import SendNotificationError
from copilot.utils import (
    copy_grouped_first_party_fields_and_save_to_s3,
    escape_sql_like_pattern,
    flatten,
    generate_report_url,
)

logger = get_logger()

REAL_ESTATE_INDUSTRY = "Real Estate"
ZENDESK_TICKET_ID_KEY = "zendesk_ticket_id"
FULL_PDS_KEY = "full_pds"
GOING_BACK_KEY = "going_back"
CACHED_PDS_KEY = "cached_pds"
# If submission has more than 4 files, copy files in multithreading
COPY_FILES_IN_MULTITHREADING_COUNT_THRESHOLD = 4
COPY_FILES_MULTITHREADING_WORKERS = 8 if "IS_TEST_ENV" not in os.environ else 2
DEFAULT_TIER_FOR_PRIORITY = 4
LOWEST_PRIORITY = 1000
RUSH_PRIORITY = 0
SCORE_ML_BIND_LIKELIHOOD_THRESHOLD = 0.7
COMBINED_SCORE_BIND_LIKELIHOOD_THRESHOLD = 70

if TYPE_CHECKING:
    pass


@lru_cache(maxsize=None)
def _get_average_premium(owner_id: int) -> float:
    average_premium_query = f"""
        select avg(pr) from (
            select sum(coalesce(quoted_premium, estimated_premium)) pr from submission_coverages sc
            join submissions s on sc.submission_id = s.id
            where s.owner_id = '{owner_id}' and (quoted_premium > 0 or estimated_premium > 0)
                and s.created_at > now() - interval '24 months'
            group by s.id
        ) x;
    """
    average_premium = db.session.execute(average_premium_query).scalar()
    return average_premium or 0


def adjust_submission_fields(fields: Sequence[str]) -> Sequence[str]:
    def get_adjusted_field(submission_field: str) -> str:
        if submission_field in [
            "processing_state",
            "auto_verified_at",
            "manual_verified_at",
            "verified_at",
            "stuck_reason",
            "is_stuck_for_engineering",
        ]:
            return f"_{submission_field}"
        return submission_field

    return [get_adjusted_field(f) for f in fields]


class RushSource(StrEnum):
    USER = "USER"
    ROUTING_RULE = "ROUTING_RULE"
    EFFECTIVE_DATE = "EFFECTIVE_DATE"


class ReportV2(BaseModel):
    __tablename__ = "reports_v2"

    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(2048), index=True)
    submission = relationship("Submission", uselist=False, back_populates="report", cascade="all, delete-orphan")

    @property
    def submissions(self) -> list[Submission]:
        # This is for schema dumping as API change will be the second step of this bigger refactor
        return [self.submission] if self.submission else []

    @submissions.setter
    def submissions(self, value: list[Submission]) -> None:
        if not value:
            return
        if len(value) > 1:
            raise ValueError("Only one submission is allowed per report")
        self.submission = value[0]

    summary_preferences = relationship(
        "ReportSummaryPreference", uselist=True, cascade="all, delete-orphan", backref="report_v2"
    )
    is_deleted = Column(Boolean, default=False, nullable=True, index=True)
    deletion_reason = Column(String, nullable=True)
    is_archived = Column(Boolean, default=False, nullable=False, index=True)
    organization_permission_level = Column(Enum(PermissionType), nullable=True, index=True)
    report_permissions = relationship("ReportPermission", uselist=True, cascade="all, delete-orphan", backref="report")
    subscriptions = relationship("Subscription", uselist=True, cascade="all, delete-orphan", backref="report")
    metrics_v2 = relationship("MetricV2", uselist=True, cascade="all, delete-orphan", backref="report_v2")
    metric_preferences = relationship("MetricPreference", uselist=True, cascade="all, delete-orphan")
    additional_data: dict | None = Column(JSONB(none_as_null=True), nullable=True)
    email_body = Column(String, nullable=True)
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=False, index=True)
    email_subject = Column(String, nullable=True, index=True)
    email_message_id = Column(String, nullable=True)
    email_references = Column(String, nullable=True)
    owner = relationship("User", uselist=False)
    is_copy = Column(Boolean, nullable=True)
    links = relationship("ReportLink", back_populates="report_1", foreign_keys="ReportLink.report_1_id")
    report_bundle_id = Column(UUID(as_uuid=True), ForeignKey("report_bundle.id"), index=True)
    report_bundle = relationship("ReportBundle", uselist=False, back_populates="reports")
    routing_tags = Column(ARRAY(String), nullable=True)
    tier = Column(Integer, nullable=True)
    is_rush = Column(Boolean, nullable=True)
    rush_source = Column(Enum(RushSource), nullable=True)
    org_group = Column(String, nullable=True)
    is_user_waiting_for_shadow = Column(Boolean, nullable=True)
    is_email_classification_enabled = Column(Boolean, nullable=True)

    shadow_type = Column(Enum(ReportShadowType), nullable=True)
    correspondence_id = Column(
        UUID(as_uuid=True),
        ForeignKey("report_email_correspondence.id", ondelete="SET NULL"),
        nullable=True,
        index=True,
    )
    correspondence = relationship("ReportEmailCorrespondence", uselist=False, back_populates="reports")
    alerts = relationship("ReportAlert", uselist=True, cascade="all, delete-orphan", back_populates="report")
    processing_dependencies = relationship(
        "ReportProcessingDependency",
        uselist=True,
        cascade="all, delete-orphan",
        back_populates="report",
        foreign_keys="ReportProcessingDependency.report_id",
    )
    processing_depends_on_report = relationship(
        "ReportProcessingDependency",
        uselist=False,
        cascade="all, delete-orphan",
        back_populates="dependent_report",
        foreign_keys="ReportProcessingDependency.dependent_report_id",
    )
    shadow_dependencies = relationship(
        "ReportShadowDependency",
        uselist=True,
        cascade="all, delete-orphan",
        back_populates="report",
        foreign_keys="ReportShadowDependency.report_id",
    )
    shadow_origin_dependency = relationship(
        "ReportShadowDependency",
        uselist=False,
        cascade="all, delete-orphan",
        back_populates="shadow_report",
        foreign_keys="ReportShadowDependency.shadow_report_id",
    )

    support_user_reports = relationship(
        "SupportUserReport",
        uselist=True,
        back_populates="report",
        foreign_keys="SupportUserReport.report_id",
        lazy="noload",
    )

    def copy_processing_data_to(self, new: ReportV2, is_cross_org: bool = True) -> None:
        def copy_submission_data(
            new_submission: Submission, self_submission: Submission, is_cross_org: bool = True
        ) -> dict[UUID, UUID]:
            old_to_new_ids = {
                self.id: new.id,
                self.organization_id: new.organization_id,
                self_submission.id: new_submission.id,
            }
            self_submission.copy_processing_data_to(new_submission, old_to_new_ids, is_cross_org)
            db.session.commit()
            return old_to_new_ids

        # There is a rare race condition where we can add through API a file that was marked for copying already.
        # In that case we will raise  UniqueViolation error. As handling this is complicated as it might happen
        # on separate threads, we simply try second time. The second time we will see that the file exists and
        # won't try to copy it
        try:
            old_to_new_ids = copy_submission_data(new.submission, self.submission, is_cross_org)
        except IntegrityError as e:
            db.session.rollback()
            if isinstance(e.orig, psycopg2.errors.UniqueViolation):
                logger.info(
                    "Failed to copy submission data, as data already exists, retrying ...",
                    report_id=str(self.id),
                    new_report_id=str(new.id),
                )
                try:
                    time.sleep(5)
                    db.session.refresh(new.submission)
                    db.session.refresh(self.submission)
                    old_to_new_ids = copy_submission_data(new.submission, self.submission, is_cross_org)
                except Exception as inner_exc:
                    db.session.rollback()
                    logger.error(
                        "Retrying copying files failed.",
                        report_id=str(self.id),
                        new_report_id=str(new.id),
                        error=str(inner_exc),
                        exc=inner_exc,
                    )
                    raise inner_exc
            else:
                raise e

        # We only want to copy correspondence related data if it is the same owner and there's no correspondence
        # In practice it means that we haven't received email file from BOSS,
        # and we used other metadata to create the processing dependency
        if self.owner_id == new.owner_id and new.correspondence_id is None:
            new.email_message_id = self.email_message_id
            new.email_references = self.email_references
            new.correspondence = self.correspondence
            new.routing_tags = self.routing_tags
            new.tier = self.tier

        if new.submission.origin != Origin.API:
            new.name = self.name

        self.copy_execution_events(old_to_new_ids)

    def create_shadow_report(self, restart_shadow: bool = False, revert_shadow: bool = False) -> ReportV2:
        is_chained_shadow = self.shadow_type == ReportShadowType.IS_ACTIVE_SHADOW
        old_to_new_ids = {}
        new = ReportV2()
        new.id = uuid4()
        new.name = self.name
        new.owner_id = self.owner_id
        new.organization_id = self.organization_id
        old_to_new_ids[self.id] = new.id
        new.routing_tags = self.routing_tags
        new.org_group = self.org_group
        new.tier = self.tier
        new.full_pds = True
        new.shadow_type = ReportShadowType.IS_ACTIVE_SHADOW
        self.shadow_type = ReportShadowType.CHAINED_SHADOW if is_chained_shadow else ReportShadowType.HAS_ACTIVE_SHADOW
        db.session.add(new)
        new.submission = self.submission.copy(old_to_new_ids, for_shadow_report=True, restart_shadow=restart_shadow)
        new.submission.report = new
        if revert_shadow:
            self.submission.copy_data_for_files_without_entities(
                new.submission, old_to_new_ids, exclude_data_populated_during_completion=True, is_cross_org=False
            )
        new.submission.is_auto_processed = True
        new.submission.is_shadow_processed = True
        new.submission.is_processing = False
        new.submission.is_verification_required = True
        new.submission.light_cleared = True
        new.submission.client_submission_ids = [
            SubmissionClientId(client_submission_id=csi.client_submission_id, source=csi.source)
            for csi in self.submission.client_submission_ids
        ]
        new.correspondence_id = self.correspondence_id
        shadowed_report = self

        processing_state = (
            SubmissionProcessingState.COMPLETED
            if revert_shadow
            else SubmissionProcessingState.NOT_STARTED if restart_shadow else SubmissionProcessingState.FILES_CLEARING
        )

        from copilot.logic.dao.submission_dao import SubmissionDAO

        SubmissionDAO.create_or_update_submission_processing(
            new.submission, processing_state=processing_state, report_id=shadowed_report.id, is_shadow_processing=True
        )
        new.submission._processing_state = processing_state

        if is_chained_shadow:
            logger.info("Handling chained shadow report", report_id=self.id, submission_id=self.submission.id)
            existing_shadow_dependency = self.shadow_origin_dependency
            shadowed_report = existing_shadow_dependency.report
            existing_shadow_dependency.is_active = False

        shadow_dependency = ReportShadowDependency(
            report_id=shadowed_report.id,
            report=shadowed_report,
            shadow_report_id=new.id,
            shadow_report=new,
            is_active=True,
        )
        shadowed_report.shadow_dependencies.append(shadow_dependency)
        new.shadow_origin_dependency = shadow_dependency
        logger.info(
            "Shadow report created (shadow)",
            submission_id=new.submission.id,
            report_id=new.id,
            shadowed_report_id=shadowed_report.id,
            shadowed_submission_id=shadowed_report.submission.id,
        )
        logger.info(
            "Shadow report created (shadowed)",
            submission_id=shadowed_report.submission.id,
            report_id=shadowed_report.id,
            shadow_report_id=new.id,
            shadow_submission_id=new.submission.id,
        )
        self._create_slack_alert_for_shadow_with_dependencies(restart_shadow, revert_shadow)
        return new

    def copy(self, old_to_new: dict | None = None, external_id: str | None = None) -> ReportV2:
        old_to_new_ids = old_to_new if old_to_new is not None else {}
        new = ReportV2()
        new.id = uuid4()  # type: ignore
        new.is_copy = True
        new.name = self.name
        new.owner_id = self.owner_id
        new.organization_id = self.organization_id
        new.org_group = self.org_group
        old_to_new_ids[self.id] = new.id
        db.session.add(new)
        if self.submission:
            external_ids = [external_id] if external_id else []
            new.submission = self.submission.copy(old_to_new_ids, client_submission_ids=external_ids)
        else:
            new.submission = None  # type: ignore
        new.is_deleted = self.is_deleted
        new.deletion_reason = self.deletion_reason
        new.is_archived = self.is_archived
        for permission in self.report_permissions:
            if permission.grantee_user_id == new.owner_id:
                continue
            new.report_permissions.append(permission.copy())
        # The assignment below has to be executed after copying permissions to avoid duplicated entries for permission
        # as asking for self.report_permissions triggers the listener listen_for_org_permission_level_change. When
        # organization_permission_level is set, the listener will add entries, otherwise no, hence no duplicates.
        new.organization_permission_level = self.organization_permission_level
        for subscription in self.subscriptions:
            new.subscriptions.append(subscription.copy())
        new.additional_data = self.additional_data_for_copy
        new.email_body = self.email_body
        new.email_subject = self.email_subject
        new.email_message_id = self.email_message_id
        new.email_references = self.email_references
        new.correspondence = self.correspondence
        new.routing_tags = self.routing_tags
        new.tier = self.tier

        self.copy_execution_events(old_to_new_ids)

        if self.report_bundle is None:
            self.report_bundle = ReportBundle()

        new.report_bundle = self.report_bundle

        for link in self.links:
            new.links.append(ReportLink(report_2=link.report_2))
            link.report_2.links.append(ReportLink(report_2=new))

        new.links.append(ReportLink(report_2=self))
        self.links.append(ReportLink(report_2=new))

        return new

    def create_renewal_shell_report(self) -> ReportV2:
        old_to_new_ids = {}
        new = ReportV2()
        new.id = uuid4()
        new.name = self.name
        new.owner_id = self.owner_id
        new.organization_id = self.organization_id
        old_to_new_ids[self.id] = new.id
        new.org_group = self.org_group
        db.session.add(new)
        new.submission = self.submission.copy_for_renewal_shell(old_to_new_ids)
        new.submission.report = new

        for permission in self.report_permissions:
            if permission.grantee_user_id == new.owner_id:
                continue
            new.report_permissions.append(permission.copy())
        # The assignment below has to be executed after copying permissions to avoid duplicated entries for permission
        # as asking for self.report_permissions triggers the listener listen_for_org_permission_level_change. When
        # organization_permission_level is set, the listener will add entries, otherwise no, hence no duplicates.
        new.organization_permission_level = self.organization_permission_level

        renewal_relation = SubmissionRelation(
            from_submission_id=new.submission.id,
            to_submission_id=self.submission.id,
            type=SubmissionRelationType.RENEWAL,
            reason="Created automatically for renewal shell",
            confidence=1.0,
            is_active=True,
            source=SubmissionRelationSource.PRE_RENEWAL.value,
        )
        db.session.add(renewal_relation)

        return new

    def copy_execution_events(self, old_to_new_ids: dict) -> None:
        execution_events = db.session.query(ExecutionEvent).filter_by(report_id=self.id).all()
        for execution_event in execution_events:
            new_execution_event = execution_event.copy(old_to_new_ids)
            db.session.add(new_execution_event)

    def get_url(self) -> str:
        return generate_report_url(self.id)

    @property
    def is_backfill(self) -> bool:
        if not self.additional_data:
            return False
        return self.additional_data.get("is_backfill", False)

    @property
    def last_assigned_at(self):
        return self.support_user_reports[0].created_at if self.support_user_reports else None

    @property
    def url(self) -> str | None:
        if self.submission.is_verification_required and not self.submission.is_verified:
            return None
        return self.get_url()

    @property
    def businesses(self) -> list[SubmissionBusiness]:
        return self.submission.businesses

    @property
    def business_ids(self) -> list[UUID | str | None]:
        return [business.business_id for business in self.businesses]

    @property
    def is_frozen(self) -> bool:
        return self.submission.is_frozen

    @property
    def additional_data_for_copy(self) -> dict | None:
        if self.additional_data:
            return {
                k: v
                for k, v in self.additional_data.items()
                if k not in [ZENDESK_TICKET_ID_KEY, FULL_PDS_KEY, CACHED_PDS_KEY]
            }
        return None

    @property
    def zendesk_ticket_id(self) -> str | None:
        if self.additional_data and ZENDESK_TICKET_ID_KEY in self.additional_data:
            return self.additional_data[ZENDESK_TICKET_ID_KEY]
        return None

    @zendesk_ticket_id.setter
    def zendesk_ticket_id(self, value: str | None) -> None:
        if value is None:
            if self.additional_data:
                self.additional_data.pop(ZENDESK_TICKET_ID_KEY, None)
        else:
            if not self.additional_data:
                self.additional_data = {}
            self.additional_data[ZENDESK_TICKET_ID_KEY] = value
        flag_modified(self, "additional_data")
        return None

    @property
    def lr_manually_processing_enabled(self) -> bool:
        return self.owner and self.owner.applicable_settings and self.owner.applicable_settings.loss_runs_manual

    @property
    def full_pds(self) -> bool:
        pds_keys = [FULL_PDS_KEY, CACHED_PDS_KEY]
        return self.additional_data is not None and any(pds_key in self.additional_data for pds_key in pds_keys)

    @full_pds.setter
    def full_pds(self, value: bool) -> None:
        if value:
            if not self.additional_data:
                self.additional_data = {}
            self.additional_data[FULL_PDS_KEY] = "true"
        else:
            if self.additional_data:
                self.additional_data.pop(FULL_PDS_KEY, None)
        flag_modified(self, "additional_data")
        return None

    @property
    def going_back(self) -> bool:
        """
        Property indicating if the submission has been restarted by adding a new file during PDS and we need to go back.
        """
        return self.additional_data is not None and GOING_BACK_KEY in self.additional_data

    @going_back.setter
    def going_back(self, value: bool) -> None:
        if value:
            if not self.additional_data:
                self.additional_data = {}
            self.additional_data[GOING_BACK_KEY] = "true"
        else:
            if self.additional_data:
                self.additional_data.pop(GOING_BACK_KEY, None)
        flag_modified(self, "additional_data")
        return None

    @property
    def cached_pds(self) -> bool:
        return self.additional_data is not None and CACHED_PDS_KEY in self.additional_data

    @cached_pds.setter
    def cached_pds(self, value: bool) -> None:
        if value:
            if not self.additional_data:
                self.additional_data = {}
            self.additional_data[CACHED_PDS_KEY] = "true"
        else:
            if self.additional_data:
                self.additional_data.pop(CACHED_PDS_KEY, None)
        flag_modified(self, "additional_data")
        return None

    @property
    def current_user_permission_type(self) -> str | None:
        if current_user.is_internal_machine_user:
            return None
        if current_user.is_admin:
            return PermissionType.ADMIN.name
        if hasattr(self, "_current_user_permission_type"):
            # Do not overwrite NONE permissions
            if self._current_user_permission_type and current_user.is_read_only_account:
                return PermissionType.VIEWER.name
            return self._current_user_permission_type
        permission = current_user.get_permission_for_report(self.id)
        self._current_user_permission_type = PermissionType(permission).name if permission else None
        return self._current_user_permission_type

    @current_user_permission_type.setter
    def current_user_permission_type(self, value: str | None) -> None:
        self._current_user_permission_type = value
        return None

    @property
    def is_active_shadow(self) -> bool:
        return self.shadow_type == ReportShadowType.IS_ACTIVE_SHADOW

    @property
    def has_active_shadow(self) -> bool:
        return self.shadow_type == ReportShadowType.HAS_ACTIVE_SHADOW

    @property
    def is_part_of_active_shadow(self) -> bool:
        return self.has_active_shadow or self.is_active_shadow

    @property
    def is_document_ingestion(self) -> bool:
        return self.organization_id == ExistingOrganizations.MarkelDemo.value

    @property
    def email_body_text(self) -> str:
        if not self.email_body:
            return ""
        try:
            soup = BeautifulSoup(self.email_body, "html.parser")
            return soup.get_text().strip() if soup.find() else self.email_body.strip()
        except Exception:
            return self.email_body.strip()

    def _create_slack_alert_for_shadow_with_dependencies(self, restart_shadow: bool, revert_shadow: bool):
        try:
            parent_not_processed_correctly = (
                self.processing_depends_on_report
                and self.submission.processing_state == SubmissionProcessingState.COMPLETED
                and self.submission.is_verified is False
            )
            if not parent_not_processed_correctly:
                return
            logger.info(
                "Probably parent report was not loaded correctly",
                submission_id=self.submission.id,
                report_id=self.id,
                restart_shadow=restart_shadow,
                revert_shadow=revert_shadow,
            )
            additional_file_added = not (restart_shadow or revert_shadow)
            if additional_file_added:
                added_files = [
                    f for f in self.submission.files if f.is_required_shadow_processing and f.origin == Origin.COPILOT
                ]
                if not added_files:
                    logger.info(
                        "No additional files for reloaded child report",
                        submission_id=self.submission.id,
                        files=self.submission.files,
                    )
                    return
                action = f"Additional file(s) added ({', '.join([f.name for f in added_files])})"
            else:
                action = "Processing restarted" if restart_shadow else "Processing reverted to previous state"

            additional_info = (
                f"\n\nParent report: https://copilot.kalepa.com/report/{self.processing_depends_on_report.report_id}"
            )
            company = Organization.get_company_for_id(self.organization_id).value
            message = (
                f"<!here> WARNING - probably parent report wasn't loaded correctly: {action} for report"
                f" https://copilot.kalepa.com/report/{self.id} during verification -"
                f" {self.name} ({company}).{additional_info}"
            )

            import flask

            from copilot.constants import SLA_ALERTS_SLACK_CHANNEL

            flask.current_app.slack_client.send_slack_message(
                SLA_ALERTS_SLACK_CHANNEL,
                message,
            )
        except Exception:
            logger.exception(
                "Failed to send Slack message for shadow report with dependencies", submission_id=self.submission.id
            )

    @property
    def sql_escaped_email_subject(self) -> str | None:
        return escape_sql_like_pattern(self.email_subject)

    @property
    def sql_escaped_report_name(self) -> str | None:
        return escape_sql_like_pattern(self.name)


@dataclass
class ExternalCustomStatusPart:
    name: str
    value: str


@dataclass
class ExternalCustomStatus:
    custom_status: list[ExternalCustomStatusPart] = field(default_factory=list)

    @property
    def key_value_pairs(self) -> dict:
        return {part.name: part.value for part in self.custom_status}


@dataclass
class ExternalReportsResponse:
    name: str | None = None
    id: UUID | None = None


@dataclass
class ReportsEnvelope:
    reports: list[ReportV2]
    page: int | None = None
    total_pages: int | None = None
    total_reports: int | None = None
    has_next: bool | None = None
    total_with_the_account_id: int | None = None


@dataclass
class PermissionsEnvelope:
    permissions: list[ReportPermission]


@dataclass
class SubmissionHistoryElement:
    report_id: UUID
    report_name: str
    effective_date: datetime | None = None


@dataclass
class LossSummary:
    year: str
    count: int
    sum_of_total_net_incurred: float


# noinspection PyUnusedLocal
@event.listens_for(ReportV2, "after_insert")
def listen_for_report_created(mapper, connection, report) -> None:  # type: ignore
    log = logger.bind(report_id=report.id)
    log.info("Report created, triggering listener")
    if not report.report_permissions:
        add_after_commit_event(AfterCommitEventType.REPORTS_CREATED, (report.id,))


@event.listens_for(ReportV2, "before_update")
@event.listens_for(ReportV2, "after_insert")
def listen_for_org_permission_level_change(mapper, connection, target) -> None:  # type: ignore
    permission_level_history = db.inspect(target).attrs["organization_permission_level"].history
    if permission_level_history.has_changes():
        old_value = permission_level_history.deleted[0] if permission_level_history.deleted else None
        new_value = permission_level_history.added[0]

        if new_value is None and old_value is None:
            return

        # Doing complicated logic here breaks other parts of endpoints processing because it messes with
        # SqlAlchemy transaction. So, let's put all required data into current thread (processing current request)
        # local data and process below after transaction has been committed.
        current_thread_id = threading.get_ident()
        logger.info(
            f"[Thread({current_thread_id!s})] Adding Submission({target.id!s}) because its org permission level"
            " changed."
        )
        submission_id = str(target.submission.id)
        add_after_commit_event(AfterCommitEventType.SUBMISSIONS_ORG_PERMISSION_LEVEL_CHANGED, (submission_id,))

    return None


class ReportAlert(BaseModel):
    __tablename__ = "report_alerts"

    report_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, index=True)
    alert_type = Column(Enum(AlertType), nullable=False)
    alert_count = Column(Integer, default=0, nullable=True)
    report = relationship("ReportV2", lazy="select")


class ReportTriage(BaseModel):
    __tablename__ = "report_triage"

    report_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, index=True)
    result = Column(Enum(ReportTriageResult), nullable=False)
    email_sent = Column(Boolean, nullable=False, default=False)
    report = relationship("ReportV2", lazy="select")
    explanations = Column(ARRAY(String), nullable=True)


class SubmissionUser(BaseModel):
    __tablename__ = "submissions_users"

    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    user = relationship("User", lazy="joined")
    source = Column(Enum(SubmissionUserSource), nullable=True)

    def copy(self):
        new = SubmissionUser()
        new.user_id = self.user_id
        new.submission_id = self.submission_id
        new.source = self.source
        return new


class SubmissionClientId(BaseModel):
    __tablename__ = "submissions_client_ids"

    client_submission_id = Column(String, nullable=False, index=True)
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    submission = relationship("Submission", uselist=False, back_populates="client_submission_ids", lazy="select")
    source = Column(Enum(SubmissionClientIdSource), nullable=True)


Index(
    "submissions_client_ids_client_submission_id_submission_id_idx",
    SubmissionClientId.client_submission_id,
    SubmissionClientId.submission_id,
    unique=True,
)


# noinspection PyUnusedLocal
@event.listens_for(SubmissionClientId, "after_insert")
def listen_for_submission_client_id_created(mapper, connection, target) -> None:  # type: ignore
    add_after_commit_event(AfterCommitEventType.CLIENT_SUBMISSION_ID_ADDED, (target.submission_id,))


@event.listens_for(SubmissionClientId, "after_delete")
def listen_for_submission_client_id_removed(mapper, connection, target) -> None:  # type: ignore
    add_after_commit_event(AfterCommitEventType.CLIENT_SUBMISSION_ID_REMOVED, (target.submission_id,))


class ReportBundle(BaseModel):
    __tablename__ = "report_bundle"

    reports = relationship("ReportV2", back_populates="report_bundle", lazy="joined")


class ReportLink(db.Model):
    __tablename__ = "report_link"

    report_1_id = Column(
        UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, primary_key=True
    )
    report_2_id = Column(
        UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, primary_key=True
    )

    report_1 = relationship("ReportV2", foreign_keys=report_1_id)
    report_2 = relationship("ReportV2", foreign_keys=report_2_id)


class ReportProcessingDependency(BaseModel):
    __tablename__ = "report_processing_dependency"

    report_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, index=True)
    dependent_report_id = Column(
        UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, index=True
    )
    dependency_type = Column(Enum(ReportDependencyType), nullable=False)
    is_invalidated = Column(Boolean, nullable=True, default=False)

    report = relationship("ReportV2", foreign_keys=report_id)
    dependent_report = relationship("ReportV2", foreign_keys=dependent_report_id)

    @property
    def is_same_org(self) -> bool:
        return self.dependency_type == ReportDependencyType.SAME_ORG


class ReportShadowDependency(BaseModel):
    __tablename__ = "report_shadow_dependency"

    report_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, index=True)
    shadow_report_id = Column(
        UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, index=True
    )
    is_active = Column(Boolean, nullable=False, default=True)

    report = relationship("ReportV2", foreign_keys=report_id)
    shadow_report = relationship("ReportV2", foreign_keys=shadow_report_id)


class SubmissionCoverage(BaseModel):
    __tablename__ = "submission_coverages"

    coverage_id = Column(UUID(as_uuid=True), ForeignKey("coverages.id", ondelete="CASCADE"), nullable=False, index=True)
    estimated_premium = Column(Float)
    is_quoted = Column(Boolean, default=False, index=True)
    quoted_premium = Column(Float)
    rating_premium = Column(Float, nullable=True)
    bound_premium = Column(Float, nullable=True)
    total_premium = Column(Float, nullable=True)
    total_premium_or_bound_premium = Column(Float, Computed("COALESCE(total_premium, bound_premium)"), nullable=True)
    limit = Column(Float, nullable=True)
    attachment_point = Column(Float, nullable=True)
    self_insurance_retention = Column(Float, nullable=True)
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    coverage = relationship("Coverage", uselist=False, lazy="joined")
    coverage_type = Column(Enum(CoverageType, native_enum=False), nullable=True)
    source = Column(Enum(SubmissionCoverageSource), nullable=True)
    source_details = Column(String, nullable=True)
    period = Column(String, nullable=True)
    other_terms = Column(String, nullable=True)
    additional_data = Column(JSONB, nullable=False, server_default="{}")
    stage = Column(Enum(SubmissionStage), nullable=True)

    __table_args__ = (
        Index(
            "uq_submission_coverages_coverage_id_type_submission_id",
            "submission_id",
            "coverage_id",
            "coverage_type",
            unique=True,
            postgresql_where="coverage_type IS NOT NULL",
        ),
        Index(
            "uq_submission_coverages_coverage_id_type_submission_id_nulls",
            "submission_id",
            "coverage_id",
            unique=True,
            postgresql_where="coverage_type IS NULL",
        ),
    )

    def copy(self) -> SubmissionCoverage:
        return SubmissionCoverage(
            coverage_id=self.coverage_id,
            coverage_type=self.coverage_type,
            is_quoted=self.is_quoted,
            quoted_premium=self.quoted_premium,
            rating_premium=self.rating_premium,
            bound_premium=self.bound_premium,
            total_premium=self.total_premium,
            limit=self.limit,
            attachment_point=self.attachment_point,
            estimated_premium=self.estimated_premium,
            self_insurance_retention=self.self_insurance_retention,
            source=SubmissionCoverageSource.COPY,
            source_details=f"Copied from submission coverage {self.id!s}",
            period=self.period,
            other_terms=self.other_terms,
            additional_data=self.additional_data,
            stage=self.stage,
        )


@event.listens_for(ReportV2, "after_update")
def listen_for_report_data_update(mapper, connection, target: ReportV2) -> None:  # type: ignore
    report_level_recs_variables: list[str] = ["org_group"]
    for variable_name in report_level_recs_variables:
        variable_data = db.inspect(target).attrs[variable_name].history
        if variable_data.has_changes():
            key = f"{AfterCommitEventType.RECOMMENDATIONS_ML_VARIABLE_CHANGED}-{target.submission.id}"
            add_after_commit_event(
                AfterCommitEventType.RECOMMENDATIONS_ML_VARIABLE_CHANGED, (target.submission.id,), key=key
            )
            break


@event.listens_for(SubmissionCoverage, "after_update")
def listen_for_coverage_data_update(mapper, connection, target: SubmissionCoverage) -> None:  # type: ignore
    coverage_level_recs_variables: list[str] = [
        "attachment_point",
        "limit",
        "self_insurance_retention",
        "quoted_premium",
        "bound_premium",
        "total_premium",
        "coverage_type",
    ]
    for variable_name in coverage_level_recs_variables:
        variable_data = db.inspect(target).attrs[variable_name].history
        if variable_data.has_changes():
            key = f"{AfterCommitEventType.RECOMMENDATIONS_ML_VARIABLE_CHANGED}-{target.submission_id}"
            add_after_commit_event(
                AfterCommitEventType.RECOMMENDATIONS_ML_VARIABLE_CHANGED, (target.submission_id,), key=key
            )
            break


class NAICSCode(db.Model):  # type: ignore
    __tablename__ = "naics_code"

    id = Column(Integer, primary_key=True)
    title = Column(String, nullable=True)
    description = Column(String, nullable=True)


class SubmissionPriority(BaseModel):
    __tablename__ = "submission_priority"

    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    escalated_at = Column(DateTime(), nullable=True)
    removed_from_auto_assign = Column(Boolean, nullable=False, default=False)
    overwritten_priority = Column(Integer, nullable=True)


class StuckDetails(BaseModel):
    __tablename__ = "stuck_details"

    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    processing_state = Column(Enum(SubmissionProcessingState), nullable=True)
    stuck_at = Column(DateTime(timezone=True), nullable=False)
    unstuck_at = Column(DateTime(timezone=True), nullable=True)
    stuck_by = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)

    stuck_reason_buckets = Column(ARRAY(String))
    stuck_reason_explanation = Column(String)
    was_correct_to_stuck = Column(Boolean)


class Submission(BaseModel):
    __tablename__ = "submissions"

    name = Column(String, nullable=False, index=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    account_name = Column(String, nullable=True)
    stage = Column(Enum(SubmissionStage), nullable=True, default=SubmissionStage.ON_MY_PLATE, index=True)
    stage_details = Column(JSONB())  # TODO: Define stage_details model separately.
    proposed_effective_date = Column(DateTime(), index=True)
    policy_expiration_date = Column(DateTime(), index=True, nullable=True)
    due_date = Column(DateTime())
    received_date = Column(DateTime())
    policies = relationship("Policy", uselist=True, cascade="all, delete-orphan", backref="submission")
    read_by_users = relationship(
        "ReadSubmission",
        back_populates="submission",
        foreign_keys="ReadSubmission.submission_id",
        cascade="all, delete-orphan",
    )
    coverages = relationship(
        "SubmissionCoverage",
        uselist=True,
        cascade="all, delete-orphan",
        backref="submission",
        order_by="asc(SubmissionCoverage.coverage_id)",
    )
    deductibles = relationship(
        "SubmissionDeductible",
        uselist=True,
        cascade="all, delete-orphan",
        backref="submission",
        order_by="asc(SubmissionDeductible.coverage_id)",
    )
    businesses = relationship(
        "SubmissionBusiness",
        uselist=True,
        cascade="all, delete-orphan",
        order_by="asc(SubmissionBusiness.serial_id)",
        back_populates="submission",
    )
    underlying_policies = relationship(
        "UnderlyingPolicy",
        uselist=True,
        cascade="all, delete-orphan",
        backref="submission",
    )
    recommendation_result = relationship(
        "SubmissionRecommendationResult",
        uselist=False,
        backref="submission",
        foreign_keys="SubmissionRecommendationResult.submission_id",
        cascade="all, delete-orphan",
    )
    report_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id"), index=True, nullable=False)
    report = relationship("ReportV2", back_populates="submission")

    @property
    def number_of_businesses(self) -> int:
        return len(self.businesses)

    @property
    def formatted_coverages(self) -> list[str]:
        formatted_coverages = []
        for sub_coverage in self.coverages:
            coverage = sub_coverage.coverage
            coverage_types = ""
            formatted_name = coverage.display_name
            if coverage.coverage_types and len(coverage.coverage_types) > 0:
                coverage_types = coverage.coverage_types[0].capitalize()
            if coverage.name == "liability":
                if CoverageType.PRIMARY in coverage.coverage_types:
                    coverage_types = "Primary/General"
                else:
                    coverage_types = "Excess/Umbrella"

            if coverage_types:
                formatted_name += f" ({coverage_types})"

            formatted_coverages.append(formatted_name)
        return formatted_coverages

    @property
    def report_is_deleted(self) -> bool | None:
        return self.report.is_deleted if self.report else None

    @property
    def reports(self) -> list[ReportV2]:
        # This is for schema dumping as API change will be the second step of this bigger refactor
        return [self.report] if self.report else []

    files = relationship("File", uselist=True, cascade="all, delete-orphan", backref="submission")
    user = relationship("User", uselist=False)
    is_deleted = Column(Boolean, default=False, nullable=True)
    description_of_operations = Column(String, nullable=True)
    generated_description_of_operations = Column(String, nullable=True)
    email_description = Column(String, nullable=True)
    email_project_description = Column(String, nullable=True)
    is_renewal = Column(Boolean, default=False, nullable=True, index=True)
    renewal_creation_date = Column(DateTime, nullable=True, index=True)
    recommendation_v2_action = Column(Enum(RecommendationActionEnum), nullable=True, default=None, index=True)
    recommendation_v2_priority = Column(DECIMAL, nullable=True, index=True)
    recommendation_v2_is_refer = Column(Boolean, nullable=True, index=True)
    recommendation_v2_score = Column(Integer, nullable=True, index=False)
    declined_user_id = Column(Integer, nullable=True, index=False)
    declined_date = Column(DateTime(), index=True)
    reason_for_declining = Column(String, nullable=True, index=True)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("submissions.id"), nullable=True, index=True)
    clearing_issues = relationship(
        "SubmissionClearingIssue", uselist=True, cascade="all, delete-orphan", backref="submission"
    )
    lob_id = Column(UUID(as_uuid=True), ForeignKey("lob.id"), nullable=True, index=True)
    lob = relationship("Lob", uselist=False)

    broker_id = Column(UUID(as_uuid=True), ForeignKey("brokerage_employees.id"), nullable=True, index=True)
    broker = relationship("BrokerageEmployee", uselist=False, foreign_keys=[broker_id])
    brokerage_id = Column(UUID(as_uuid=True), ForeignKey("brokerages_v2.id"), nullable=True, index=True)
    brokerage = relationship("Brokerage", uselist=False)
    brokerage_contact_id = Column(UUID(as_uuid=True), ForeignKey("brokerage_employees.id"), nullable=True, index=True)
    brokerage_contact = relationship("BrokerageEmployee", uselist=False, foreign_keys=[brokerage_contact_id])
    mode = Column(Enum(SubmissionMode), default=SubmissionMode.STANDARD)
    coverage_type = Column(String, nullable=True)
    primary_naics_code = Column(String, nullable=True)
    iso_gl_code = Column(String, nullable=True)
    sic_code = Column(String, nullable=True)
    icc_code = Column(String, nullable=True)
    is_naics_verified = Column(Boolean, nullable=True, default=False)
    is_verified = Column(Boolean, nullable=False, default=False)
    is_verification_required = Column(Boolean, nullable=False, default=False)
    is_auto_verified = Column(Boolean, nullable=True, default=False)
    _verified_at = Column("verified_at", DateTime(), nullable=True)
    _auto_verified_at = Column("auto_verified_at", DateTime(), nullable=True)
    is_manual_verified = Column(Boolean, nullable=True, default=False)
    _manual_verified_at = Column("manual_verified_at", DateTime(), nullable=True)
    is_metrics_set_manually = Column(Boolean, nullable=True, default=False)
    client_submission_ids = relationship(
        "SubmissionClientId", uselist=True, cascade="all, delete-orphan", back_populates="submission"
    )
    bookmarks = relationship("SubmissionBookmark", uselist=True, cascade="all, delete-orphan", backref="submission")
    assigned_underwriters = relationship(
        "SubmissionUser", uselist=True, cascade="all, delete-orphan", backref="submission", lazy="joined"
    )
    clearing_assignee_id = Column(Integer, nullable=True, index=True)

    verification_checks = relationship(
        "VerificationCheckResult", uselist=True, cascade="all, delete-orphan", backref="submission", lazy="select"
    )
    _processing_state = Column("processing_state", Enum(SubmissionProcessingState), nullable=True)
    businesses_resolving_state = Column(Enum(BusinessesResolvingState), nullable=True)
    origin = Column(Enum(Origin), nullable=True)
    lost_reasons = Column(ARRAY(String))
    decline_email_sent = Column(Boolean, default=False, nullable=True)
    decline_email_recipient_address = Column(String, nullable=True)
    send_decline_email = Column(Boolean, default=False, nullable=True)
    send_decline_email_error = Column(Enum(SendNotificationError), nullable=True)
    decline_email_tracking_id = Column(UUID(as_uuid=True), nullable=True)
    decline_email_delivered = Column(Boolean, nullable=True)
    active_email_template_id = Column(String, nullable=True)
    decline_email_cc = Column(ARRAY(String), nullable=True)
    decline_custom_template = Column(String, nullable=True)
    decline_custom_subject = Column(String, nullable=True)
    # Array of strings in the format "name:base64 encoded content"
    decline_attachments = Column(ARRAY(String), nullable=True)
    account_id = Column(String, nullable=True)
    notes = Column(String)
    is_auto_processed = Column(Boolean, nullable=True, default=False)
    is_shadow_processed = Column(Boolean, nullable=True, default=False)
    is_processing = Column(Boolean, nullable=True, default=False)
    is_waiting_for_auto_verify = Column(Boolean, nullable=True)
    light_cleared = Column(Boolean, nullable=True, default=False)
    target_premium = Column(Float, nullable=True, index=True)
    expired_premium = Column(Float, nullable=True, index=True)
    sales = Column(Float, nullable=True, index=True)
    _stuck_reason = Column("stuck_reason", String, nullable=True)
    _is_stuck_engineering = Column("is_stuck_engineering", Boolean, nullable=True, default=False)
    pds_special_note = Column(String, nullable=True)
    is_for_audit = Column(Boolean, nullable=True, default=False)
    audited_at = Column(DateTime(), nullable=True)
    audited_by = Column(Integer, nullable=True)
    is_verified_shell = Column(Boolean, nullable=False, default=False)
    priority = relationship("SubmissionPriority", uselist=False, cascade="all, delete-orphan")
    submission_notes = relationship("SubmissionNote", uselist=True, cascade="all, delete-orphan")
    manual_naics_assignment_required = Column(Boolean, nullable=True, default=False, index=True)
    missing_data_status = Column(Enum(MissingDataStatus), nullable=True)
    missing_documents = Column(ARRAY(String), nullable=True)
    sent_rule_email = Column(String, nullable=True)
    stuck_details = relationship("StuckDetails", uselist=True, cascade="all, delete-orphan")
    synced_at = Column(DateTime(), nullable=True)
    quoted_date = Column(DateTime(), nullable=True)
    bound_date = Column(DateTime(), nullable=True)
    frozen_as_of = Column(DateTime(), nullable=True)
    is_escalated = Column(Boolean, nullable=True)
    contractor_submission_type = Column(Enum(ContractorSubmissionType), nullable=True)
    project_insurance_type = Column(Enum(ProjectInsuranceType), nullable=True)
    incumbent_carrier = Column(JSONB(none_as_null=True), nullable=True)
    fni_state = Column(String, nullable=True)
    workers_comp_experience = relationship(
        "WorkersCompExperience",
        # We want to get all workers comp experiences related to the submission with the additional rule that for
        # the ones with source API, we want only the ones that are from the past 365 days from the effective date
        # of the submission
        primaryjoin="""and_(WorkersCompExperience.submission_id == Submission.id, or_(
            WorkersCompExperience.source != 'API',
            WorkersCompExperience.rating_effective_date > func.coalesce(Submission.proposed_effective_date,
            Submission.created_at)
                - text("INTERVAL '365 days'"),
        ))""",
        uselist=True,
        cascade="all, delete-orphan",
    )
    workers_comp_rating_info = relationship("WorkersCompStateRatingInfo", uselist=True, cascade="all, delete-orphan")
    is_enhanced_shell = Column(Boolean, nullable=True, default=False)
    fni_fein = Column(String, nullable=True)
    adjusted_tiv = Column(Float, nullable=True)
    client_stage = relationship("ClientSubmissionStageConfig", uselist=False)
    client_stage_id = Column(Integer, ForeignKey("client_submission_stage_config.id"), nullable=True, index=True)
    primary_state = Column(String, nullable=True)
    client_stage_comment = Column(String, nullable=True)
    tiv = Column(Float, nullable=True)
    client_clearing_status = Column(String, nullable=True)
    clearing_status = Column(Enum(ClearingStatus), nullable=False, default=ClearingStatus.CLEARED)
    clearing_sub_statuses = relationship("SubmissionClearingSubStatus", uselist=True, cascade="all, delete-orphan")
    cleared_date = Column(DateTime(), nullable=True)
    brokerage_office = Column(String, nullable=True)
    sub_producer_name = Column(String, nullable=True)
    sub_producer_email = Column(String, nullable=True)
    prevent_clearing_updates = Column(Boolean, nullable=True)
    client_recommendations_score = Column(String, nullable=True)
    is_pre_renewal = Column(Boolean, nullable=True)
    is_renewal_shell = Column(Boolean, nullable=True)
    facts_config = Column(JSONB(none_as_null=True), nullable=True)
    policy_status = Column(String, nullable=True)
    lookalike_bind_rate = Column(Float, nullable=True)
    is_stub = Column(Boolean, nullable=False, default=False)

    assigned_broker = relationship(
        "SubmissionBrokerageEmployee",
        primaryjoin=(
            "and_(Submission.id==SubmissionBrokerageEmployee.submission_id, "
            f"SubmissionBrokerageEmployee.role=='{BrokerageEmployeeRoles.AGENT.value}')"
        ),
        uselist=False,
        cascade="all, delete-orphan",
        overlaps="assigned_brokerage_contact, submissions",
    )
    assigned_brokerage_contact = relationship(
        "SubmissionBrokerageEmployee",
        primaryjoin=(
            "and_(Submission.id==SubmissionBrokerageEmployee.submission_id, "
            f"SubmissionBrokerageEmployee.role=='{BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT.value}')"
        ),
        uselist=False,
        cascade="all, delete-orphan",
        overlaps="assigned_broker, submissions",
    )
    assigned_brokerage = relationship("SubmissionBrokerage", uselist=False, cascade="all, delete-orphan")
    identifiers = relationship("SubmissionIdentifier", uselist=True, cascade="all, delete-orphan", backref="submission")
    submission_processing_list = relationship(
        "SubmissionProcessing", uselist=True, cascade="all, delete-orphan", backref="submission"
    )
    field_sources = relationship(
        "SubmissionFieldSource", uselist=True, cascade="all, delete-orphan", backref="submission"
    )

    premises = relationship("SubmissionPremises", uselist=True, cascade="all, delete-orphan", backref="submission")

    _grouped_first_party_fields: FirstPartyFields | None = None

    _use_internal_account_id = False
    _is_ally_auto = None
    _is_paragon_wc = None
    _is_paragon_psp = None
    _is_paragon_trident_public_risk = None
    _is_paragon_es = None
    _shared_with_user_groups = None
    _is_k2_aegis = None
    _is_k2_vikco = None

    def copy_data_for_files_without_entities(
        self,
        target_submission: Submission,
        old_to_new_ids: dict,
        log_error_on_not_copied_files: bool = True,
        exclude_data_populated_during_completion: bool = False,
        is_cross_org: bool = True,
    ) -> None:
        if target_submission.user.applicable_settings and target_submission.user.applicable_settings.loss_runs_enabled:
            self.copy_loss_policies(old_to_new_ids=old_to_new_ids)
            self.copy_losses(
                old_to_new_ids=old_to_new_ids,
                log_error_on_not_copied_files=log_error_on_not_copied_files,
                is_cross_org=is_cross_org,
            )
        self.copy_workers_comp_experience(
            old_to_new_ids=old_to_new_ids,
            log_error_on_not_copied_files=log_error_on_not_copied_files,
            is_cross_org=is_cross_org,
        )
        self.copy_workers_comp_state_rating_info(
            old_to_new_ids=old_to_new_ids,
            log_error_on_not_copied_files=log_error_on_not_copied_files,
            is_cross_org=is_cross_org,
        )
        self.copy_ifta_data(
            old_to_new_ids=old_to_new_ids,
            log_error_on_not_copied_files=log_error_on_not_copied_files,
            is_cross_org=is_cross_org,
        )
        if not exclude_data_populated_during_completion:
            self.copy_shareholders(
                old_to_new_ids=old_to_new_ids,
                log_error_on_not_copied_files=log_error_on_not_copied_files,
                is_cross_org=is_cross_org,
            )

    def copy_processing_data_to(self, new: Submission, old_to_new_ids: dict, is_cross_org: bool = True) -> None:
        if not new.proposed_effective_date:
            new.proposed_effective_date = self.proposed_effective_date
        if not new.policy_expiration_date:
            new.policy_expiration_date = self.policy_expiration_date
        if not new.due_date:
            new.due_date = self.due_date
        if not new.received_date:
            new.received_date = self.received_date
        if not new.lob_id:
            new.lob_id = self.lob_id

        if self.owner_id == new.owner_id:
            if not new.brokerage_id:
                new.set_brokerage_id(self.brokerage_id, BrokerageEmployeeSource.COPY)

            if new.brokerage_id:
                if not new.broker_id and self.broker_id and self.broker:
                    new.set_brokerage_id(self.broker.brokerage_id, BrokerageEmployeeSource.COPY)
                    new.set_broker_id(self.broker_id, BrokerageEmployeeSource.COPY)

                if not new.brokerage_contact_id and self.brokerage_contact_id and self.brokerage_contact:
                    if not new.broker_id:
                        new.set_brokerage_id(self.brokerage_contact.brokerage_id, BrokerageEmployeeSource.COPY)
                        new.set_brokerage_contact_id(self.brokerage_contact_id, BrokerageEmployeeSource.COPY)
                    elif new.brokerage_id == self.brokerage_contact.brokerage_id:
                        self.set_brokerage_contact_id(self.brokerage_contact_id)

            new.notes = self.notes
            new.account_id = self.account_id
            for bookmark in self.bookmarks:
                bookmark_copy = bookmark.copy(old_to_new_ids)
                new.bookmarks.append(bookmark_copy)

            new_read_users = {x.user_id for x in new.read_by_users}
            for read_sub in self.read_by_users:
                if read_sub.user_id in new_read_users:
                    continue

                read_sub_copy = read_sub.copy(old_to_new_ids)
                new.read_by_users.append(read_sub_copy)

        if new.origin != Origin.API:
            new.name = self.name

        if not new.is_naics_verified and (
            self.primary_naics_code != "NAICS_230000" or new.owner_id == NATIONWIDE_DEFAULT_OWNER_ID
        ):
            from copilot.logic.taxonomy import (
                synchronize_field_source_between_submissions,  # Imported here to avoid circular import
            )

            synchronize_field_source_between_submissions(
                from_submission=self,
                to_submission=new,
                field_name="primary_naics_code",
            )
            new.primary_naics_code = self.primary_naics_code
            new.is_naics_verified = self.is_naics_verified

        new.businesses = []
        new_submission_business_by_old: dict = {}
        self.copy_businesses(new, old_to_new_ids, new_submission_business_by_old)
        self.copy_files(new, old_to_new_ids, full_copy=False, emit_events=True)
        self.copy_data_for_files_without_entities(
            target_submission=new, old_to_new_ids=old_to_new_ids, is_cross_org=is_cross_org
        )
        self.copy_submission_relations(self.id, new.id, old_to_new_ids)
        if not is_cross_org:
            self.copy_premises_data(old_to_new_ids)
        self.update_email_files_processing_state(new)
        if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
            new_submission_business_id_by_old = {key: sb.id for key, sb in new_submission_business_by_old.items()}
            copy_grouped_first_party_fields_and_save_to_s3(
                self.id, new.id, new_submission_business_id_by_old, old_to_new_ids
            )
        new.description_of_operations = self.description_of_operations
        new.mode = self.mode
        new.generated_description_of_operations = self.generated_description_of_operations
        new.email_project_description = self.email_project_description
        new.email_description = self.email_description
        new.target_premium = self.target_premium
        new.expired_premium = self.expired_premium
        new.sales = self.sales
        new.stuck_reason = self.stuck_reason
        new.pds_special_note = self.pds_special_note
        new.report_id = old_to_new_ids.get(self.report_id, new.report_id)
        new.synced_at = self.synced_at
        new.quoted_date = self.quoted_date
        new.bound_date = self.bound_date
        new.cleared_date = self.cleared_date
        new.contractor_submission_type = self.contractor_submission_type
        new.project_insurance_type = self.project_insurance_type
        new.incumbent_carrier = self.incumbent_carrier
        new.fni_state = self.fni_state
        new.fni_fein = self.fni_fein
        new.adjusted_tiv = self.adjusted_tiv
        new.primary_state = self.primary_state
        new.tiv = self.tiv
        new.prevent_clearing_updates = self.prevent_clearing_updates
        new.facts_config = self.facts_config

    def copy(
        self,
        old_to_new_ids: dict,
        for_shadow_report: bool = False,
        restart_shadow: bool = False,
        client_submission_ids: list[str] = None,
    ) -> Submission:
        new = Submission()
        new.id = uuid4()
        old_to_new_ids[self.id] = new.id
        new.name = self.name
        new.user = self.user
        new.owner_id = self.owner_id
        new.account_name = self.account_name
        new.stage = SubmissionStage.ON_MY_PLATE
        new.proposed_effective_date = self.proposed_effective_date
        new.policy_expiration_date = self.policy_expiration_date
        new.due_date = self.due_date
        new.received_date = self.received_date or self.created_at
        new.primary_naics_code = self.primary_naics_code
        new.iso_gl_code = self.iso_gl_code
        new.sic_code = self.sic_code
        new.icc_code = self.icc_code
        new.light_cleared = self.light_cleared
        for clearing_sub_status in self.clearing_sub_statuses:
            new.clearing_sub_statuses.append(clearing_sub_status.copy())
        new.report_id = old_to_new_ids.get(self.report_id)
        new.prevent_clearing_updates = self.prevent_clearing_updates
        db.session.add(new)
        self.copy_premises_data(old_to_new_ids)
        for submission_user in self.assigned_underwriters:
            new.assigned_underwriters.append(submission_user.copy())
        for coverage in self.coverages:
            new.coverages.append(coverage.copy())
        for underlying_policy in self.underlying_policies:
            new.underlying_policies.append(underlying_policy.copy())
        for deductible in self.deductibles:
            new.deductibles.append(deductible.copy())
        for sci in self.clearing_issues:
            if not sci.is_light:
                new.clearing_issues.append(sci.copy())
        if for_shadow_report:
            new.client_recommendations_score = self.client_recommendations_score
            self.copy_files(new, old_to_new_ids, full_copy=True, only_parent_files=restart_shadow, for_shadow=True)
            self.copy_submission_level_extracted_data(old_to_new_ids)
        else:
            new.client_clearing_status = self.client_clearing_status
            new.clearing_status = self.clearing_status
            new_submission_business_by_old: dict = {}
            self.copy_businesses(new, old_to_new_ids, new_submission_business_by_old)
            self.copy_files(new, old_to_new_ids, full_copy=True)
            self.copy_data_for_files_without_entities(target_submission=new, old_to_new_ids=old_to_new_ids)

            if os.environ.get("INTERACT_WITH_EXTERNAL_RESOURCES", True) is True:
                new_submission_business_id_by_old = {key: sb.id for key, sb in new_submission_business_by_old.items()}
                copy_grouped_first_party_fields_and_save_to_s3(
                    self.id, new.id, new_submission_business_id_by_old, old_to_new_ids
                )

        self.copy_submission_relations(self.id, new.id, old_to_new_ids)
        new.is_deleted = self.is_deleted
        new.description_of_operations = self.description_of_operations
        new.is_renewal = self.is_renewal
        new.renewal_creation_date = self.renewal_creation_date
        new.declined_user_id = self.declined_user_id
        new.clearing_assignee_id = self.clearing_assignee_id
        new.declined_date = self.declined_date
        new.reason_for_declining = self.reason_for_declining
        new.parent_id = self.parent_id
        new.lob = self.lob
        new.set_brokerage(self.brokerage, BrokerageEmployeeSource.COPY)
        new.set_broker(self.broker, BrokerageEmployeeSource.COPY)
        new.set_brokerage_contact(self.brokerage_contact, BrokerageEmployeeSource.COPY)
        new.mode = self.mode
        new.coverage_type = self.coverage_type
        new.processing_state = self.processing_state
        new.businesses_resolving_state = self.businesses_resolving_state
        new.origin = self.origin
        new.is_naics_verified = self.is_naics_verified
        new.is_verification_required = self.is_verification_required
        new.is_verified_shell = self.is_verified_shell
        new.lost_reasons = self.lost_reasons
        new.decline_email_sent = self.decline_email_sent
        new.decline_email_recipient_address = self.decline_email_recipient_address
        new.send_decline_email = self.send_decline_email
        new.active_email_template_id = self.active_email_template_id
        new.notes = self.notes
        new.recommendation_v2_action = self.recommendation_v2_action
        new.recommendation_v2_priority = self.recommendation_v2_priority
        new.recommendation_v2_is_refer = self.recommendation_v2_is_refer
        new.recommendation_v2_score = self.recommendation_v2_score
        new.is_auto_processed = False
        new.is_processing = False
        new.account_id = self.account_id
        new.generated_description_of_operations = self.generated_description_of_operations
        new.email_project_description = self.email_project_description
        new.email_description = self.email_description
        new.target_premium = self.target_premium
        new.expired_premium = self.expired_premium
        new.sales = self.sales
        new.stuck_reason = self.stuck_reason
        new.pds_special_note = self.pds_special_note
        new.sent_rule_email = self.sent_rule_email
        new.synced_at = self.synced_at
        new.quoted_date = self.quoted_date
        new.bound_date = self.bound_date
        new.cleared_date = self.cleared_date
        new.frozen_as_of = self.frozen_as_of
        new.contractor_submission_type = self.contractor_submission_type
        new.project_insurance_type = self.project_insurance_type
        new.incumbent_carrier = self.incumbent_carrier
        new.fni_state = self.fni_state
        new.is_enhanced_shell = self.is_enhanced_shell
        new.fni_fein = self.fni_fein
        new.adjusted_tiv = self.adjusted_tiv
        new.primary_state = self.primary_state
        new.tiv = self.tiv
        new.brokerage_office = self.brokerage_office
        new.sub_producer_name = self.sub_producer_name
        new.sub_producer_email = self.sub_producer_email
        new.facts_config = self.facts_config
        new.policy_status = self.policy_status
        new.lookalike_bind_rate = self.lookalike_bind_rate
        new.is_stub = self.is_stub or False
        new.field_sources = []

        for bookmark in self.bookmarks:
            bookmark_copy = bookmark.copy(old_to_new_ids)
            new.bookmarks.append(bookmark_copy)

        for note in self.submission_notes:
            note_copy = note.copy(old_to_new_ids)
            new.submission_notes.append(note_copy)

        for read_sub in self.read_by_users:
            read_sub_copy = read_sub.copy(old_to_new_ids)
            new.read_by_users.append(read_sub_copy)

        if client_submission_ids:
            new.submission.client_submission_ids = [
                SubmissionClientId(client_submission_id=csi) for csi in client_submission_ids
            ]

        return new

    def copy_for_renewal_shell(self, old_to_new_ids: dict) -> Submission:
        new = Submission()
        new.id = uuid4()
        old_to_new_ids[self.id] = new.id
        new.name = self.name
        new.user = self.user
        new.owner_id = self.owner_id
        new.account_name = self.account_name
        new.stage = SubmissionStage.ON_MY_PLATE
        new.proposed_effective_date = (
            self.proposed_effective_date.replace(year=self.proposed_effective_date.year + 1)
            if self.proposed_effective_date
            else None
        )
        new.received_date = datetime.today()
        new.primary_naics_code = self.primary_naics_code
        new.field_sources = [field_source.copy(old_to_new_ids) for field_source in self.field_sources]
        new.iso_gl_code = self.iso_gl_code
        new.sic_code = self.sic_code
        new.icc_code = self.icc_code
        new.report_id = old_to_new_ids.get(self.report_id)
        db.session.add(new)
        for submission_user in self.assigned_underwriters:
            new.assigned_underwriters.append(submission_user.copy())
        for coverage in self.coverages:
            new.coverages.append(coverage.copy())
        for underlying_policy in self.underlying_policies:
            new.underlying_policies.append(underlying_policy.copy())
        for deductible in self.deductibles:
            new.deductibles.append(deductible.copy())

        self.copy_businesses(new, old_to_new_ids, new_submission_business_by_old={})

        new.description_of_operations = self.description_of_operations
        new.is_renewal = self.is_renewal
        new.renewal_creation_date = datetime.today()
        new.lob = self.lob
        new.set_brokerage(self.brokerage, BrokerageEmployeeSource.COPY)
        new.set_broker(self.broker, BrokerageEmployeeSource.COPY)
        new.set_brokerage_contact(self.brokerage_contact, BrokerageEmployeeSource.COPY)
        new.mode = self.mode
        new.coverage_type = self.coverage_type
        new.origin = self.origin
        new.is_naics_verified = self.is_naics_verified
        new.is_verification_required = self.is_verification_required
        new.is_verified_shell = True
        new.is_renewal_shell = True
        new.is_renewal = True
        new.account_id = self.account_id
        new.generated_description_of_operations = self.generated_description_of_operations
        new.contractor_submission_type = self.contractor_submission_type
        new.project_insurance_type = self.project_insurance_type
        new.incumbent_carrier = self.incumbent_carrier
        new.fni_state = self.fni_state
        new.fni_fein = self.fni_fein
        new.primary_state = self.primary_state
        new.brokerage_office = self.brokerage_office
        new.sub_producer_name = self.sub_producer_name
        new.sub_producer_email = self.sub_producer_email
        return new

    @staticmethod
    def _copy_file(
        file,
        old_to_new_ids: dict,
        full_copy: bool,
        submission_s3_client,
        for_shadow: bool = False,
        running_in_main_thread: bool = True,
        should_copy_processed_data: bool = True,
    ):
        logger.info("Copying file to new submission", file_id=file.id, full_copy=full_copy, for_shadow=for_shadow)
        new_file = file.copy(
            old_to_new_ids,
            submission_s3_client,
            should_copy_processed_data=should_copy_processed_data,
            for_shadow=for_shadow,
            running_on_main_thread=running_in_main_thread,
        )
        return new_file

    @staticmethod
    def _update_file(existing_file: File, file: File, new: Submission, old_to_new_ids: dict):
        logger.info(
            "Updating file for the same checksum",
            file_id=file.id,
            existing_file_id=existing_file.id,
            new_submission_id=new.id,
        )
        existing_file.file_type = file.file_type
        existing_file.processing_state = file.processing_state
        existing_file.classification = file.classification
        existing_file.sensible_status = file.sensible_status
        existing_file.comment = file.comment
        existing_file.user_file_type = file.user_file_type
        existing_file.additional_info = file.additional_info
        existing_file.initial_classification = file.initial_classification
        existing_file.initial_classification_confidence = file.initial_classification_confidence
        existing_file.issues = file.issues
        existing_file.metrics = [m.copy(old_to_new_ids) for m in file.metrics]
        existing_file.internal_notes = [f"File state copied from file {file.id}."] + (
            existing_file.internal_notes or []
        )
        file.copy_processed_data(
            new=existing_file,
            old_to_new_ids=old_to_new_ids,
            submission_s3_client=flask.current_app.submission_s3_client,
        )
        existing_file.custom_file_type_id = file.custom_file_type_id
        existing_file.custom_classification = file.custom_classification
        existing_file.replaced_by_file_ids = file.generate_replaced_by_file_ids_copy(
            old_to_new_ids, str(existing_file.submission_id)
        )

    def copy_premises_data(self, old_to_new_ids: dict) -> None:
        premises: list[SubmissionPremises] = SubmissionPremises.query.filter(
            SubmissionPremises.submission_id == self.id
        ).all()
        for premise in premises:
            new_premise = premise.copy(old_to_new_ids)
            if new_premise:
                db.session.add(new_premise)

    # noinspection PyUnusedLocal
    def copy_submission_relations(
        self,
        old_submission_id: UUID,
        new_submission_id: UUID,
        old_to_new_ids: dict,
    ) -> None:
        # imported here to avoid circular import
        from copilot.models.submission_relations import SubmissionRelation

        exists_copy_query = (
            db.session.query(SubmissionRelation)
            .filter(
                SubmissionRelation.from_submission_id == old_submission_id,
                SubmissionRelation.to_submission_id == new_submission_id,
                SubmissionRelation.type == SubmissionRelationType.COPY,
            )
            .exists()
        )
        exists_copy = db.session.query(exists_copy_query).scalar()

        if not exists_copy:
            copy_relation = SubmissionRelation(
                from_submission_id=old_submission_id,
                to_submission_id=new_submission_id,
                type=SubmissionRelationType.COPY,
                reason=f"Created by duplicating Submission[id={old_submission_id}]",
                confidence=1.0,
            )
            db.session.add(copy_relation)
        return None

    def copy_files(
        self,
        new: Submission,
        old_to_new_ids: dict,
        full_copy: bool,
        only_parent_files: bool = False,
        for_shadow: bool = False,
        emit_events: bool = False,
        should_copy_processed_data: bool = True,
    ) -> None:
        # import here to avoid circular import
        from flask import request

        from copilot.kalepa_domain_events.kalepa_events_handler import (
            KalepaEventsHandler,
        )

        is_copy_by_sync = (
            "/submissions/sync/run" in request.path or os.environ.get("ADHOC_TASK_NAME") == "submission_sync_matching"
        )

        # Mapping to be used for updating file.user_id when copying
        old_to_new_ids[self.owner_id] = new.owner_id
        existing_files = {f.checksum: f for f in new.files if f.file_type != FileType.EMAIL}
        same_org = self.owner_id == new.owner_id
        processed_files = {}

        html_email_file_name = "email_body_html.html"
        if not full_copy:
            parent_email: File | None = next(
                (f for f in self.files if f.classification == ClassificationDocumentType.EMAIL), None
            )
            new_email: File | None = next(
                (f for f in new.files if f.classification == ClassificationDocumentType.EMAIL), None
            )
            if parent_email and new_email:
                old_to_new_ids[parent_email.id] = new_email.id
                compare_extracted_data_from_email(parent_email, new_email)
                new_email.processed_file.entity_mapped_data = parent_email.processed_file.entity_mapped_data
                new_email.processed_file.onboarded_data = parent_email.processed_file.onboarded_data
                new_email.processed_file.business_resolution_data = parent_email.processed_file.business_resolution_data
                new_email.processing_state = FileProcessingState.COMPLETED
            elif parent_email and same_org:
                logger.info(
                    "Copying parent email file to new submission (same org)",
                    parent_submission_id=self.id,
                    new_submission_id=new.id,
                )
                new_file = self._copy_file(
                    parent_email,
                    old_to_new_ids,
                    full_copy,
                    flask.current_app.submission_s3_client,
                    for_shadow,
                    should_copy_processed_data,
                )
                new.files.append(new_file)
            else:
                logger.error(
                    "Parent or dependent report does not have email file",
                    parent_submission_id=self.id,
                    new_submission_id=new.id,
                )

        def is_same_file(f: File) -> bool:
            return any(f.checksum == checksum and f.checksum is not None for checksum in existing_files.keys())

        def is_replacing_file(f: File) -> bool:
            return any(
                pf.processing_state == FileProcessingState.REPLACED
                and pf.replaced_by_file_ids
                and f.id in pf.replaced_by_file_ids
                for pf in processed_files.values()
            )

        def parent_file_applicable_to_process(f: File) -> bool:
            parent_raw_email_ids = (
                [f.id for f in self.files if f.file_type == FileType.RAW_EMAIL and f.parent_file_id is None]
                if self.origin == Origin.API
                else []
            )
            return (
                (
                    (
                        f.file_type != FileType.EMAIL
                        and (f.file_type != FileType.HTML_DOCUMENT or f.name != html_email_file_name)
                    )
                    or full_copy
                )
                and (f.parent_file_id is None or f.parent_file_id in parent_raw_email_ids)
                and (same_org or f.is_internal or is_same_file(f))
                and (f.id not in parent_raw_email_ids or (same_org and f.checksum in existing_files))
                and (
                    not for_shadow
                    or (f.user_shadow_state not in [UserShadowFileState.DELETED, UserShadowFileState.REPLACED])
                )
            ) or (f.parent_file_id in old_to_new_ids and not only_parent_files)

        # At each iteration we copy/update file at single level of hierarchy.
        # This way we ensure that the parent file(s) are processed before the child files.
        file_batches = []
        while True:
            files_to_process = [
                f
                for f in self.files
                if f.id not in processed_files and (parent_file_applicable_to_process(f) or is_replacing_file(f))
            ]
            if not files_to_process:
                break
            files_for_update = []
            files_for_copy = []
            for file in files_to_process:
                processed_files[file.id] = file
                if file.checksum in existing_files:
                    old_to_new_ids[file.id] = existing_files[file.checksum].id
                    files_for_update.append(file)
                else:
                    old_to_new_ids[file.id] = uuid4()
                    files_for_copy.append(file)
            file_batches.append((files_for_update, files_for_copy))

        for files_for_update, files_for_copy in file_batches:
            for file in files_for_update:
                existing_file = existing_files[file.checksum]
                self._update_file(existing_file, file, new, old_to_new_ids)

            if len(files_for_copy) < COPY_FILES_IN_MULTITHREADING_COUNT_THRESHOLD or is_copy_by_sync:
                for file in files_for_copy:
                    new_file = self._copy_file(
                        file,
                        old_to_new_ids,
                        full_copy,
                        flask.current_app.submission_s3_client,
                        for_shadow,
                        should_copy_processed_data=should_copy_processed_data,
                    )
                    new.files.append(new_file)
                    if emit_events:
                        KalepaEventsHandler.send_submission_file_added_event(new, new_file, start_file_processing=False)
            else:

                def copy_file_in_app_context(ctx, *args, **kwargs):
                    ctx.push()
                    return self._copy_file(*args, **kwargs)

                with concurrent.futures.ThreadPoolExecutor(max_workers=COPY_FILES_MULTITHREADING_WORKERS) as executor:
                    future_to_file = {}
                    for file in files_for_copy:
                        hydrated_file = (
                            db.session.query(File)
                            .options(joinedload(File.processed_file), joinedload(File.metrics))
                            .filter(File.id == file.id)
                            .first()
                        )
                        future_to_file[
                            executor.submit(
                                copy_file_in_app_context,
                                flask.current_app.app_context(),
                                hydrated_file,
                                old_to_new_ids,
                                full_copy,
                                flask.current_app.submission_s3_client,
                                for_shadow,
                                running_in_main_thread="IS_TEST_ENV" in os.environ,
                                should_copy_processed_data=should_copy_processed_data,
                            )
                        ] = hydrated_file

                new_files = []
                for future in concurrent.futures.as_completed(future_to_file):
                    try:
                        new_file = future.result()
                        new_files.append(new_file)
                    except Exception as exc:
                        file = future_to_file[future]
                        logger.error("File copying generated an exception", exc_info=exc, file_id=file.id)

                if emit_events:
                    for nf in new_files:
                        KalepaEventsHandler.send_submission_file_added_event(new, nf, start_file_processing=False)
                new.files.extend(new_files)

    def update_email_files_processing_state(self, new: Submission) -> None:
        email_files = [
            f
            for f in new.files
            if f.file_type == FileType.EMAIL
            and f.processing_state in FileProcessingState.data_extraction_complete_states()
        ]
        for email_file in email_files:
            email_file.processing_state = FileProcessingState.COMPLETED

    def copy_businesses(self, new: Submission, old_to_new_ids: dict, new_submission_business_by_old: dict) -> None:
        for submission_business in self.businesses:
            submission_business_copy = submission_business.copy()
            submission_business_copy.id = uuid4()
            old_to_new_ids[submission_business.id] = submission_business_copy.id
            submission_business_copy.submission_id = new.id
            new_submission_business_by_old[submission_business.id] = submission_business_copy
            db.session.add(submission_business_copy)
            new.businesses.append(submission_business_copy)
        return None

    # returns a list of LossPolicy, not importing due to circular import
    def copy_loss_policies(self, old_to_new_ids: dict) -> None:
        # imported here to avoid circular import
        from copilot.models.policy import LossPolicy

        loss_policies = db.session.query(LossPolicy).filter_by(submission_id=self.id).all()
        for loss_policy in loss_policies:
            new_loss_policy = loss_policy.copy(old_to_new_ids)
            db.session.add(new_loss_policy)
        return None

    # returns a list of Loss, not importing due to circular import
    def copy_losses(
        self, old_to_new_ids: dict, log_error_on_not_copied_files: bool = True, is_cross_org: bool = True
    ) -> None:
        # imported here to avoid circular import
        from copilot.models import Loss

        losses = db.session.query(Loss).filter_by(submission_id=self.id).all()
        filtered_losses = filter_out_data_from_not_copied_files(
            items=losses,
            old_to_new_ids=old_to_new_ids,
            log_error=log_error_on_not_copied_files,
            is_cross_org=is_cross_org,
        )
        for loss in filtered_losses:
            new_loss = loss.copy(old_to_new_ids)
            db.session.add(new_loss)
        return None

    def copy_workers_comp_experience(
        self, old_to_new_ids: dict, log_error_on_not_copied_files: bool = True, is_cross_org: bool = True
    ) -> None:
        from copilot.models.workers_comp_experience import WorkersCompExperience

        workers_comp_experiences = db.session.query(WorkersCompExperience).filter_by(submission_id=self.id).all()
        filtered_workers_comp_experiences = filter_out_data_from_not_copied_files(
            items=workers_comp_experiences,
            old_to_new_ids=old_to_new_ids,
            log_error=log_error_on_not_copied_files,
            is_cross_org=is_cross_org,
        )
        for wce in filtered_workers_comp_experiences:
            new_wce = wce.copy(old_to_new_ids)
            db.session.add(new_wce)
        return None

    def copy_workers_comp_state_rating_info(
        self, old_to_new_ids: dict, log_error_on_not_copied_files: bool = True, is_cross_org: bool = True
    ) -> None:
        from copilot.models.workers_comp_experience import WorkersCompStateRatingInfo

        workers_comp_state_rating_info = (
            db.session.query(WorkersCompStateRatingInfo).filter_by(submission_id=self.id).all()
        )
        filtered_workers_comp_state_rating_info = filter_out_data_from_not_copied_files(
            items=workers_comp_state_rating_info,
            old_to_new_ids=old_to_new_ids,
            log_error=log_error_on_not_copied_files,
            is_cross_org=is_cross_org,
        )
        for wcsri in filtered_workers_comp_state_rating_info:
            new_wcsri = wcsri.copy(old_to_new_ids)
            db.session.add(new_wcsri)
        return None

    def has_client_submission_id(self, client_submission_id: str | None) -> bool:
        if not client_submission_id:
            return False
        if self.organization_id == ExistingOrganizations.Paragon.value:
            return any([client_submission_id in cs.client_submission_id for cs in self.client_submission_ids])
        return client_submission_id in [cs.client_submission_id for cs in self.client_submission_ids]

    def copy_ifta_data(
        self, old_to_new_ids: dict, log_error_on_not_copied_files: bool = True, is_cross_org: bool = True
    ) -> None:
        from copilot.models.ifta import IFTAData

        ifta_data = db.session.query(IFTAData).filter_by(submission_id=self.id).all()
        filtered_ifta_data = filter_out_data_from_not_copied_files(
            items=ifta_data,
            old_to_new_ids=old_to_new_ids,
            log_error=log_error_on_not_copied_files,
            is_cross_org=is_cross_org,
        )
        for data in filtered_ifta_data:
            new_data = data.copy(old_to_new_ids)
            db.session.add(new_data)
        return None

    def copy_submission_level_extracted_data(self, old_to_new_ids: dict) -> None:
        from copilot.models.submission_level_extracted_data import (
            SubmissionLevelExtractedData,
        )

        submission_level_extracted_data = (
            db.session.query(SubmissionLevelExtractedData)
            .filter_by(submission_id=self.id, source_details=SourceDetails.API)
            .all()
        )
        for data in submission_level_extracted_data:
            new_data = data.copy(old_to_new_ids)
            db.session.add(new_data)
        return None

    def copy_shareholders(
        self, old_to_new_ids: dict, log_error_on_not_copied_files: bool = True, is_cross_org: bool = True
    ) -> None:
        from copilot.models.shareholders import Shareholder

        shareholder_data = db.session.query(Shareholder).filter_by(submission_id=self.id).all()
        filtered_shareholder_data = filter_out_data_from_not_copied_files(
            items=shareholder_data,
            old_to_new_ids=old_to_new_ids,
            log_error=log_error_on_not_copied_files,
            is_cross_org=is_cross_org,
        )
        for data in filtered_shareholder_data:
            new_data = data.copy(old_to_new_ids)
            db.session.add(new_data)
        return None

    @property
    def cleared_at(self) -> datetime | None:
        if self.cleared_date is not None:
            return self.cleared_date

        from copilot.models.submission_audit import SubmissionAudit

        # TODO(ENG-29062): Remove this query once the cleared date is backfilled
        audit_entry: SubmissionAudit | None = (
            db.session.query(SubmissionAudit)
            .filter(
                SubmissionAudit.submission_id == self.id,
                SubmissionAudit.data_before["clearing_status"].astext.in_(ClearingStatus.pending_clearing_statuses()),
                SubmissionAudit.data_after["clearing_status"].astext.in_(ClearingStatus.resolved_statuses()),
            )
            .order_by(SubmissionAudit.occurred_at.desc())
            .first()
        )
        if audit_entry:
            return audit_entry.occurred_at

        # If there's no audit entry, take the date from the first client submission ID
        client_ids: list[SubmissionClientId] = self.client_submission_ids or []
        client_ids = sorted(client_ids, key=lambda x: x.created_at)

        if client_ids:
            return client_ids[0].created_at

        return None

    @hybrid_property
    def processing_state(self) -> SubmissionProcessingState:
        return self._processing_state

    @processing_state.setter
    def processing_state(self, value: SubmissionProcessingState) -> None:
        self._processing_state = value
        if not self.is_auto_processed:
            return

        from copilot.logic.dao.submission_dao import SubmissionDAO

        SubmissionDAO.create_or_update_submission_processing(self, value)

    @hybrid_property
    def verified_at(self) -> datetime | None:
        return self._verified_at

    @verified_at.setter
    def verified_at(self, value: datetime | None) -> None:
        self._verified_at = value
        active_processing = self.active_submission_processing
        if active_processing:
            active_processing.verified_at = value
            if value is not None:
                active_processing.is_active = False
                initial_verification_processing = (
                    db.session.query(SubmissionProcessing.id)
                    .filter(
                        SubmissionProcessing.report_id == active_processing.report_id,
                        SubmissionProcessing.is_initial_verification.is_(True),
                    )
                    .one_or_none()
                )
                if not initial_verification_processing:
                    active_processing.is_initial_verification = True
            elif active_processing.is_initial_verification:
                active_processing.is_initial_verification = False
        else:
            logger.warning("No active processing found for submission during verification", submission_id=self.id)

    @hybrid_property
    def auto_verified_at(self) -> datetime | None:
        return self._auto_verified_at

    @auto_verified_at.setter
    def auto_verified_at(self, value: datetime | None) -> None:
        self._auto_verified_at = value
        active_processing = self.active_submission_processing
        if active_processing:
            active_processing.auto_verified_at = value
        else:
            logger.warning("No active processing found for submission during auto verification", submission_id=self.id)

    @hybrid_property
    def manual_verified_at(self) -> datetime | None:
        return self._manual_verified_at

    @manual_verified_at.setter
    def manual_verified_at(self, value: datetime | None) -> None:
        self._manual_verified_at = value
        active_processing = self.active_submission_processing
        if active_processing:
            active_processing.manual_verified_at = value
        else:
            logger.warning(
                "No active processing found for submission during manual verification", submission_id=self.id
            )

    @hybrid_property
    def stuck_reason(self) -> str | None:
        return self._stuck_reason

    @stuck_reason.setter
    def stuck_reason(self, value: str | None) -> None:
        self._stuck_reason = value
        active_processing = self.active_submission_processing
        if active_processing:
            active_processing.stuck_reason = value
        else:
            logger.warning(
                "No active processing found for submission during setting stuck reason", submission_id=self.id
            )

    @hybrid_property
    def is_stuck_engineering(self) -> bool:
        return self._is_stuck_engineering

    @is_stuck_engineering.setter
    def is_stuck_engineering(self, value: bool) -> None:
        self._is_stuck_engineering = value
        active_processing = self.active_submission_processing
        if active_processing:
            active_processing.is_stuck_engineering = value
        else:
            logger.warning(
                "No active processing found for submission during setting stuck engineering", submission_id=self.id
            )

    @staticmethod
    def _is_shell_from_sync(origin: Origin, created_at: datetime) -> bool:
        return (
            origin == Origin.SYNC
            and created_at.hour == 0
            and created_at.minute == 0
            and created_at.second == 0
            and created_at.microsecond == 0
        )

    @property
    def is_processing_completed(self) -> bool:
        return self.processing_state == SubmissionProcessingState.COMPLETED or (
            self.processing_state
            in [SubmissionProcessingState.LIGHT_CLEARING, SubmissionProcessingState.NEEDS_CLEARING]
            and self.active_submission_processing
            and self.active_submission_processing.is_dependency_processing
        )

    @property
    def quote_number(self) -> str | None:
        for identifier in self.identifiers or []:
            if identifier.identifier_type == AdditionalIdentifierType.QUOTE_NUMBER:
                return identifier.identifier
        return None

    @property
    def policy_number(self) -> str | None:
        for identifier in self.identifiers or []:
            if identifier.identifier_type == AdditionalIdentifierType.POLICY_NUMBER:
                return identifier.identifier
        return None

    @property
    def is_shell_from_sync(self) -> bool:
        return self._is_shell_from_sync(self.origin, self.created_at)

    @property
    def brokerage_name(self) -> str | None:
        return self.brokerage.name if self.brokerage else None

    @property
    def use_internal_account_id(self) -> bool:
        return self._use_internal_account_id

    @staticmethod
    def _is_processing_enabled(is_auto_processed: bool, has_report_processing_dependency: bool) -> bool:
        return is_auto_processed or has_report_processing_dependency

    @property
    def is_processing_enabled(self) -> bool:
        return self._is_processing_enabled(self.is_auto_processed, self.report.processing_depends_on_report is not None)

    @property
    def decline_email_attachment_requests(self):
        from copilot.schemas.report import AttachmentRequest

        res = []

        for a in self.decline_attachments or []:
            [name, content] = a.split(":", 1)
            res.append(AttachmentRequest(name=name, content=content))

        return res

    @use_internal_account_id.setter
    def use_internal_account_id(self, value) -> None:
        self._use_internal_account_id = value

    @property
    def broker_name(self) -> str | None:
        return f"{self.broker.name}".lower().strip() if self.broker else None

    @property
    def brokerage_contact_or_broker_name(self) -> str | None:
        return f"{self.brokerage_contact.name}".lower().strip() if self.brokerage_contact else self.broker_name

    @property
    def broker_email(self) -> str | None:
        return f"{self.broker.email}".lower().strip() if (self.broker and self.broker.email is not None) else None

    @property
    def brokerage_contact_or_broker_email(self) -> str | None:
        return f"{self.brokerage_contact.email}".lower().strip() if self.brokerage_contact else self.broker_email

    @property
    def brokerage_contact_email(self) -> str | None:
        return f"{self.brokerage_contact.email}".lower().strip() if self.brokerage_contact else None

    @property
    def grouped_first_party_fields(self) -> FirstPartyFields | None:
        return self._grouped_first_party_fields

    @grouped_first_party_fields.setter
    def grouped_first_party_fields(self, grouped_first_party_fields: FirstPartyFields) -> None:
        # noinspection PyAttributeOutsideInit
        self._grouped_first_party_fields = grouped_first_party_fields

    @property
    def full_clearing_issues(self) -> list[SubmissionClearingIssue]:
        return [ci for ci in self.clearing_issues if not ci.is_light]

    @property
    def history(self) -> list[SubmissionHistoryElement] | None:
        try:
            return self.recurrent_history()
        except RecursionError:
            return None

    @property
    def has_unresolved_clearing_issues(self) -> bool:
        return any(ci for ci in self.clearing_issues if not ci.is_resolved)

    @property
    def has_unresolved_light_clearing_issues(self) -> bool:
        return any(ci for ci in self.clearing_issues if not ci.is_resolved and ci.is_light)

    @property
    def was_fully_cleared(self) -> bool:
        if not self.full_clearing_issues:
            return False
        return self.full_clearing_issues[0].is_resolved

    def recurrent_history(self, submission_history: list | None = None) -> list[SubmissionHistoryElement]:
        if submission_history is None:
            submission_history = []
        if self.parent_id:
            report = db.session.query(ReportV2).join(Submission).filter(Submission.id == self.parent_id).first()
            if report.id in [h.report_id for h in submission_history]:
                error = RecursionError(
                    f"Recursion Cycle Detected. Report ID = {self.parent_id} appeared twice as parent"
                )
                logger.error("Recursion Cycle Detected", report_id=self.parent_id, error=error)
                raise error
            submission = Submission.query.get(self.parent_id)
            submission_history.append(
                SubmissionHistoryElement(
                    report_id=report.id, report_name=report.name, effective_date=submission.proposed_effective_date
                )
            )
            return submission.recurrent_history(submission_history=submission_history)
        return submission_history

    @hybrid_property
    def is_bind_likely(self) -> bool | None:
        if self.stage == SubmissionStage.QUOTED_BOUND:
            return True
        if self.stage == SubmissionStage.EXPIRED:
            return False

        if self.recommendation_result:
            if self.recommendation_result.score_ml is None:
                return None
            if self.stage in SubmissionStage.recommendable_stages():
                return self.recommendation_result.score >= COMBINED_SCORE_BIND_LIKELIHOOD_THRESHOLD
            if self.stage == SubmissionStage.QUOTED:
                return self.recommendation_result.score_ml >= SCORE_ML_BIND_LIKELIHOOD_THRESHOLD

        return None

    @is_bind_likely.expression
    def is_bind_likely(cls):
        return case(
            [
                (cls.stage == SubmissionStage.QUOTED_BOUND, literal(True)),
                (cls.stage == SubmissionStage.EXPIRED, literal(False)),
                (
                    and_(
                        cls.stage.in_(list(SubmissionStage.recommendable_stages())),
                        SubmissionRecommendationResult.score.isnot(None),
                        SubmissionRecommendationResult.score >= COMBINED_SCORE_BIND_LIKELIHOOD_THRESHOLD,
                    ),
                    literal(True),
                ),
                (
                    and_(
                        cls.stage == SubmissionStage.QUOTED,
                        SubmissionRecommendationResult.score_ml.isnot(None),
                        SubmissionRecommendationResult.score_ml >= SCORE_ML_BIND_LIKELIHOOD_THRESHOLD,
                    ),
                    literal(True),
                ),
            ],
            else_=literal(False),
        )

    @property
    def is_frozen(self) -> bool:
        return SubmissionStage.is_frozen(self.stage)

    @property
    def can_be_updated(self) -> bool:
        if not self.is_frozen:
            return True
        try:
            nw_api_and_bound = (
                Organization.is_nationwide_for_id(self.organization_id)
                and self.origin == Origin.API
                and self.stage in {SubmissionStage.QUOTED_BOUND, SubmissionStage.QUOTED_LOST}
            )
            return nw_api_and_bound
        except:
            # We don't always have current_user available, for example when processing on the background thread
            return False

    @property
    def is_in_terminal_stage(self) -> bool:
        return SubmissionStage.is_terminal(self.stage)

    @property
    def expected_premium(self) -> float:
        if self.target_premium:
            return self.target_premium
        if self.expired_premium:
            return self.expired_premium
        total_premium = 0.0
        for coverage in self.coverages:
            total_premium += coverage.estimated_premium or 0.0
        if total_premium:
            return total_premium
        owner_organization = self.user.organization
        if owner_organization.is_nationwide:
            return 10000
        if average_premium := _get_average_premium(self.owner_id):
            return average_premium
        return 0.0

    @property
    def report_ids(self) -> list[UUID]:
        return [self.report_id]

    @property
    def line_of_business(self) -> str:
        return self.lob.display_name

    def has_unconfirmed_businesses(self) -> bool:
        return any([b for b in self.businesses if b.business_id is None])

    @property
    def is_shareable(self) -> bool:
        if self.is_stub:
            return False
        if not self.is_verification_required:
            return True
        return self.is_verified

    @property
    def first_client_submission_id(self) -> str | None:
        if self.client_submission_ids:
            return self.client_submission_ids[0].client_submission_id
        return None

    @property
    def first_named_insured(self) -> list[SubmissionBusiness]:
        return [b for b in self.businesses if b.is_fni]

    @property
    def general_contractors(self) -> list[SubmissionBusiness]:
        return [b for b in self.businesses if b.entity_role == SubmissionBusinessEntityRole.GENERAL_CONTRACTOR]

    @property
    def projects(self) -> list[SubmissionBusiness]:
        return [b for b in self.businesses if b.entity_role == SubmissionBusinessEntityRole.PROJECT]

    @property
    def has_gc_or_project(self) -> bool:
        return any(b.is_project or b.is_gc for b in self.businesses)

    @property
    def is_submission_processed(self) -> bool:
        # pds went live on 2023-05-09. After that date we can easily distinguish PDS submissions by using the
        # flag is_auto_processed. Before that, this flag was used on submissions that were not created by pds

        if (
            self.created_at.replace(tzinfo=None) > datetime(2023, 5, 9).replace(tzinfo=None)
            and self.is_auto_processed is True
            and self.processing_state != SubmissionProcessingState.COMPLETED
        ):
            return False
        return True

    @property
    def is_being_processed(self) -> bool:
        if (
            self.is_auto_processed
            and self.processing_state is not None
            and self.processing_state not in SubmissionProcessingState.final_states()
        ):
            return True
        return False

    def get_url(self) -> str | None:
        if self.report:
            return self.report.get_url()
        return None

    @property
    def organization_id(self) -> int | None:
        if self.report:
            return self.report.organization_id
        return None

    @property
    def effective_priority(self) -> int:
        if self.priority and self.priority.overwritten_priority:
            return self.priority.overwritten_priority
        return self.default_priority

    @property
    def default_priority(self) -> int:
        if self.organization_id is None:
            return LOWEST_PRIORITY
        if self.report.is_rush:
            return RUSH_PRIORITY
        # If this logic changes, remember to update the logic for DB query in support_users.get_submission_queue_query
        tier = self.report.tier if self.report.tier else DEFAULT_TIER_FOR_PRIORITY
        priority_config = Organization.get_priority(self.organization_id)
        priority = (
            priority_config.older_subs_priority
            if priority_config.older_subs_priority
            else priority_config.default_priority
        )
        return priority + tier

    @property
    def is_transportation_submission(self) -> bool:
        if self.primary_naics_code and (
            TRANSPORTATION_NAICS_PREFIX in self.primary_naics_code
            or WAREHOUSING_NAICS_PREFIX in self.primary_naics_code
        ):
            return True
        return False

    @property
    def is_construction_submission(self) -> bool:
        if self.primary_naics_code and CONSTRUCTION_NAICS_PREFIX in self.primary_naics_code:
            return True
        return False

    @property
    def is_boss(self) -> bool:
        owner_id_to_use = flask.current_app.report_owners_cache.get_owner_id_for_organization_id(
            ExistingOrganizations.Nationwide.value
        )
        if owner_id_to_use:
            return self.owner_id == owner_id_to_use and self.origin == Origin.API
        return Organization.is_nationwide_for_id(self.organization_id) and self.origin == Origin.API

    @property
    def has_been_synced(self) -> bool:
        from copilot.logic.dao.submission_client_id_dao import SubmissionClientIdDAO
        from copilot.logic.dao.submission_sync_dao import SubmissionSyncRequestDAO

        submission_client_ids: list[SubmissionClientId] = SubmissionClientIdDAO.get_submission_client_ids_by_submission(
            self.id,  # type: ignore
            self.organization_id,  # type: ignore
        )
        ids: list[str] = [i.client_submission_id for i in submission_client_ids]

        sync_requests = SubmissionSyncRequestDAO.get_sync_requests(ids, self.organization_id)  # type: ignore
        return len(sync_requests) > 0

    @property
    def has_unique_submission_client_id(self) -> bool:
        from copilot.logic.dao.submission_client_id_dao import SubmissionClientIdDAO

        if self.client_submission_ids:
            ids = [i.client_submission_id for i in self.client_submission_ids]
            return SubmissionClientIdDAO.has_unique_submission_client_id(ids, self.id, self.organization_id)

        return False

    @property
    def organization_group(self) -> str | OrganizationGroups | None:
        if self.report.org_group:
            return self.report.org_group
        if self.is_ally_auto:
            return OrganizationGroups.PARAGON_ALLY_AUTO.value
        if self.is_paragon_wc:
            return OrganizationGroups.PARAGON_WC.value
        if self.is_paragon_psp:
            return OrganizationGroups.PARAGON_PSP_E3.value
        if Organization.is_paragon_for_id(self.organization_id):
            return OrganizationGroups.PARAGON_XS.value

        if (
            OrganizationGroups.is_organization_group_based(self.organization_id)
            and self.report.correspondence
            and self.report.correspondence.email_account
            and (org_group := OrganizationGroups.get_from_forward_account(self.report.correspondence.email_account))
        ):
            return org_group.value

        return None

    @property
    def is_brokerage_set_by_machine_user(self) -> bool | None:
        order_case = case(
            [(SubmissionBrokerage.updated_at is not None, SubmissionBrokerage.updated_at)],
            else_=SubmissionBrokerage.created_at,
        )

        brokerage_log = (
            db.session.query(SubmissionBrokerage)
            .filter(SubmissionBrokerage.submission_id == self.id)
            .order_by(desc(order_case))
            .first()
        )

        if brokerage_log:
            return brokerage_log.source != BrokerageEmployeeSource.MANUAL

        return None

    @property
    def is_broker_agent_set_by_machine_user(self) -> bool | None:
        order_case = case(
            [(SubmissionBrokerageEmployee.updated_at is not None, SubmissionBrokerageEmployee.updated_at)],
            else_=SubmissionBrokerageEmployee.created_at,
        )

        broker_agent_log = (
            db.session.query(SubmissionBrokerageEmployee)
            .filter(SubmissionBrokerageEmployee.submission_id == self.id)
            .filter(SubmissionBrokerageEmployee.role == BrokerageEmployeeRoles.AGENT)
            .order_by(desc(order_case))
            .first()
        )

        if broker_agent_log:
            return broker_agent_log.source != BrokerageEmployeeSource.MANUAL

    @property
    def is_broker_correspondence_contact_set_by_machine_user(self) -> bool | None:
        order_case = case(
            [(SubmissionBrokerageEmployee.updated_at is not None, SubmissionBrokerageEmployee.updated_at)],
            else_=SubmissionBrokerageEmployee.created_at,
        )

        broker_agent_log = (
            db.session.query(SubmissionBrokerageEmployee)
            .filter(SubmissionBrokerageEmployee.submission_id == self.id)
            .filter(SubmissionBrokerageEmployee.role == BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT)
            .order_by(desc(order_case))
            .first()
        )

        if broker_agent_log:
            return broker_agent_log.source != BrokerageEmployeeSource.MANUAL

        return None

    @property
    def is_ally_auto(self) -> bool:
        if self._is_ally_auto is None:
            if Organization.is_paragon_for_id(self.organization_id):
                self._is_ally_auto = (
                    self.report.correspondence and self.report.correspondence.email_account == ALLY_AUTO_EMAIL
                )
            else:
                self._is_ally_auto = False

        return self._is_ally_auto

    @property
    def is_paragon_es(self) -> bool:
        # This is for Paragon E&S forward, which is not yet used
        if self._is_paragon_es is None:
            if Organization.is_paragon_for_id(self.organization_id):
                self._is_paragon_es = (
                    self.report.correspondence and self.report.correspondence.email_account == PARAGON_ES_INBOX
                )
            else:
                self._is_paragon_es = False

        return self._is_paragon_es

    @property
    def is_paragon_wc(self) -> bool:
        if self._is_paragon_wc is None:
            if Organization.is_paragon_for_id(self.organization_id):
                self._is_paragon_wc = (self.report.org_group == OrganizationGroups.PARAGON_WC.value) or (
                    self.report.correspondence and self.report.correspondence.email_account == PARAGON_WC_EMAIL
                )
            else:
                self._is_paragon_wc = False

        return self._is_paragon_wc

    @property
    def is_paragon_psp(self) -> bool:
        if self._is_paragon_psp is None:
            if Organization.is_paragon_for_id(self.organization_id):
                self._is_paragon_psp = (self.report.org_group == OrganizationGroups.PARAGON_PSP_E3.value) or (
                    self.report.correspondence and self.report.correspondence.email_account == PARAGON_PSP_EMAIL
                )
            else:
                self._is_paragon_psp = False

        return self._is_paragon_psp

    @property
    def is_paragon_trident_public_risk(self) -> bool:
        if self._is_paragon_trident_public_risk is None:
            if Organization.is_paragon_for_id(self.organization_id):
                self._is_paragon_trident_public_risk = self.report.org_group == OrganizationGroups.PARAGON_TRIDENT.value
            else:
                self._is_paragon_trident_public_risk = False

        return self._is_paragon_trident_public_risk

    @property
    def is_k2_aegis(self) -> bool:
        if self._is_k2_aegis is None:
            if Organization.is_k2_for_id(self.organization_id):
                self._is_k2_aegis = (
                    self.report.correspondence and self.report.correspondence.email_account == K2_AEGIS_EMAIL
                )
            else:
                self._is_k2_aegis = False

        return self._is_k2_aegis

    @property
    def is_k2_vikco(self) -> bool:
        if self._is_k2_vikco is None:
            if Organization.is_k2_for_id(self.organization_id):
                self._is_k2_vikco = (
                    self.report.correspondence and self.report.correspondence.email_account == K2_VIKCO_EMAIL
                )
            else:
                self._is_k2_vikco = False

        return self._is_k2_vikco

    @property
    def two_digit_naics_code(self) -> str | None:
        if not self.primary_naics_code:
            return None
        # currently we have constraint in place making priary_naics_code be in format NAICS_XXXXXX
        else:
            return self.primary_naics_code[6:8]

    @property
    def all_entities_resolved(self) -> bool:
        processed_files = (
            db.session.query(ProcessedFile)
            .options(load_only(ProcessedFile.business_resolution_data))
            .join(File, File.id == ProcessedFile.file_id)
            .filter(File.submission_id == self.id)
            .filter(
                File.processing_state.notin_(
                    {FileProcessingState.IGNORED, FileProcessingState.REPLACED, FileProcessingState.PROCESSING_FAILED}
                )
            )
            .all()
        )
        return all(file.all_entities_resolved for file in processed_files)

    @property
    def all_files_classified(self) -> bool:
        return all(file.processing_state != FileProcessingState.NOT_CLASSIFIED for file in self.files)

    @property
    def all_files_ready(self) -> bool:
        is_document_ingestion = any(f.is_document_ingestion for f in self.files)
        not_ready_states = FileProcessingState.not_ready()
        if is_document_ingestion:
            not_ready_states.add(FileProcessingState.WAITING_FOR_ENTITY_MAPPING)
        return all(
            file.processing_state not in not_ready_states
            for file in self.files
            if file.classification not in ClassificationDocumentType.loss_run_classifications()
        )

    @property
    def all_files_ready_document_ingestion(self) -> bool:
        return all(
            file.processing_state
            in {
                FileProcessingState.WAITING_FOR_COMPLETION,
                FileProcessingState.WAITING_FOR_DATA_ONBOARDING,
                FileProcessingState.NOT_APPLICABLE_FOR_PROCESSING,
            }
            for file in self.files
        )

    @property
    def is_ready_for_completing_processing_dependency(self) -> bool:
        # There have been 8 failed emails in the last 2 months, but need to account for the case when
        # the email has failed and hence we have finished processing
        completed_consolidation_process = (
            db.session.query(SubmissionConsolidationProcess)
            .filter(
                SubmissionConsolidationProcess.submission_id == self.id,
                SubmissionConsolidationProcess.status == SubmissionConsolidationStatus.COMPLETED,
            )
            .first()
        )
        return completed_consolidation_process is not None or all(
            f.processing_state == FileProcessingState.PROCESSING_FAILED
            for f in self.files
            if f.file_type == FileType.EMAIL
        )

    @property
    def has_acord(self) -> bool:
        return any(file.file_type == FileType.ACORD_FORM for file in self.files)

    @property
    def has_bor(self) -> bool:
        return any(file.file_type == FileType.BROKER_OF_RECORD_LETTER for file in self.files)

    @property
    def has_loss_run_or_summary(self) -> bool:
        return any(file.file_type in {FileType.LOSS_RUN, FileType.LOSS_RUN_SUMMARY} for file in self.files)

    @property
    def has_supplemental_form(self) -> bool:
        return any(file.file_type == FileType.SUPPLEMENTAL_FORM for file in self.files)

    @property
    def has_sov(self) -> bool:
        return any(file.file_type == FileType.SOV for file in self.files)

    @property
    def any_file_requires_shadow_processing(self) -> bool:
        return any(f.is_required_shadow_processing for f in self.files)

    @property
    def is_required_shadow_processing(self) -> bool:
        return self.is_submission_processed and self.all_files_classified and self.any_file_requires_shadow_processing

    @property
    def shared_with_user_groups(self) -> set[str]:
        if self._shared_with_user_groups is not None:
            return self._shared_with_user_groups

        report: ReportV2 = self.report
        permissions: list[ReportPermission] = report.report_permissions
        groups = []

        for permission in permissions:
            if permission.grantee_group:
                groups.append(permission.grantee_group.name)

        groups_set = set(groups)
        self._shared_with_user_groups = groups_set
        return self._shared_with_user_groups

    def is_file_for_enhanced_shell_processing(self, file: File) -> bool:
        if self.is_paragon_wc:
            return file.classification in (ClassificationDocumentType.ACORD_130, ClassificationDocumentType.ACORD_125)
        if self.organization_group == OrganizationGroups.PARAGON_XS.value:
            return file.classification == ClassificationDocumentType.ACORD_125
        if Organization.is_secura_for_id(self.organization_id):
            return file.classification in (
                ClassificationDocumentType.ACORD_125,
                ClassificationDocumentType.ACORD_130,
                ClassificationDocumentType.ACORD_160,
            )
        return False

    def files_for_enhanced_shell_processing(self) -> list[File]:
        return [f for f in self.files if self.is_file_for_enhanced_shell_processing(f)]

    def files_for_di_consolidation(self) -> list[File]:
        return [
            f
            for f in self.files
            if f.classification in ClassificationDocumentType.di_data_consolidation_classifications()
        ]

    def is_enhanced_shell_processing_finished(self) -> bool:
        return all(
            f.processing_state
            in (
                FileProcessingState.final_states_without_processed_data()
                | FileProcessingState.already_consolidated_states()
            )
            for f in self.files_for_enhanced_shell_processing()
        )

    def always_processed_files(self) -> list[File]:
        return [f for f in self.files if f.file_type in FileType.always_processed_types()]

    @property
    def name_without_client_id(self) -> str:
        name = self.name
        if " : " not in self.name:
            return self.name
        for client_id in self.client_submission_ids:
            name = name.replace(f" : {client_id.client_submission_id}", "")
        return name

    @property
    def broker_group(self) -> str | None:
        # get the broker group of the organization
        assigned_user_ids = [su.user_id for su in self.assigned_underwriters]
        user_id = assigned_user_ids[0] if len(assigned_user_ids) == 1 else None

        groups: list[BrokerGroup] = (
            db.session.query(BrokerGroup)
            .join(BrokerGroup.broker_group_mappings)
            .filter(BrokerGroup.organization_id == self.organization_id)
            .filter(BrokerGroupMapping.broker_id == self.broker_id)
            .filter(
                or_(BrokerGroupMapping.user_id == user_id, BrokerGroupMapping.organization_id == self.organization_id)
            )
            .all()
        )
        grp_id_to_name = {group.id: group.name for group in groups}
        org_name = None
        user_name = None
        for mapping in flatten([group.broker_group_mappings for group in groups]):
            if mapping.organization_id == self.organization_id:
                org_name = grp_id_to_name[mapping.broker_group_id]
            if mapping.user_id == user_id:
                user_name = grp_id_to_name[mapping.broker_group_id]

        return user_name or org_name or NO_BROKER_GROUP

    @property
    def active_submission_processing(self) -> SubmissionProcessing:
        return next(iter([sp for sp in (self.submission_processing_list or []) if sp.is_active]), None)

    def entity_mapped_files(self) -> list[File]:
        entity_mapped_files = (
            db.session.query(File)
            .join(ProcessedFile, File.id == ProcessedFile.file_id)
            .options(load_only(File.id))
            .filter(File.submission_id == self.id)
            .filter(ProcessedFile.entity_mapped_data != JSON.NULL)
            .filter(ProcessedFile.entity_mapped_data.isnot(None))
            .filter(
                or_(
                    File.initial_processing_state.is_(None),
                    File.initial_processing_state != FileProcessingState.CACHE_PROCESSED,
                )
            )
            .all()
        )
        return entity_mapped_files

    def files_with_entities(self) -> list[File]:
        files_with_entities = (
            db.session.query(File)
            .join(ProcessedFile, File.id == ProcessedFile.file_id)
            .options(load_only(File.id, File.file_type))
            .filter(File.submission_id == self.id)
            .filter(ProcessedFile.business_resolution_data.isnot(None))
            .all()
        )
        return files_with_entities

    def _set_brokerage_employee_id(
        self, brokerage_employee_id: UUID | None, role: BrokerageEmployeeRoles, source: BrokerageEmployeeSource | None
    ):
        if not brokerage_employee_id:
            SubmissionBrokerageEmployee.query.filter_by(submission_id=self.id, role=role).delete()
        elif existing_employee := SubmissionBrokerageEmployee.query.filter_by(submission_id=self.id, role=role).first():
            existing_employee.brokerage_employee_id = brokerage_employee_id
            existing_employee.source = source
        else:
            employee = SubmissionBrokerageEmployee(
                submission_id=self.id, brokerage_employee_id=brokerage_employee_id, role=role, source=source
            )
            db.session.add(employee)

    def set_broker_id(self, broker_id: UUID | None, source: BrokerageEmployeeSource | None = None):
        self.broker_id = broker_id
        self._set_brokerage_employee_id(broker_id, BrokerageEmployeeRoles.AGENT, source)

    def set_broker(self, broker: BrokerageEmployee | None, source: BrokerageEmployeeSource | None = None):
        broker_id = broker.id if broker else None
        self.set_broker_id(broker_id, source)
        self.broker = broker

    def set_brokerage_contact_id(
        self, brokerage_contact_id: UUID | None, source: BrokerageEmployeeSource | None = None
    ):
        self.brokerage_contact_id = brokerage_contact_id
        self._set_brokerage_employee_id(brokerage_contact_id, BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT, source)

    def set_brokerage_contact(
        self, brokerage_contact: BrokerageEmployee | None, source: BrokerageEmployeeSource | None = None
    ):
        brokerage_contact_id = brokerage_contact.id if brokerage_contact else None
        self.set_brokerage_contact_id(brokerage_contact_id, source)
        self.brokerage_contact = brokerage_contact

    def set_brokerage_id(self, brokerage_id: UUID | None, source: BrokerageEmployeeSource | None = None):
        if self.brokerage_id == brokerage_id:
            return

        self.brokerage_id = brokerage_id
        self.broker_id = None
        self.brokerage_contact_id = None
        self.set_broker_id(None)
        self.set_brokerage_contact_id(None)

        if not brokerage_id:
            SubmissionBrokerage.query.filter_by(submission_id=self.id).delete()
        elif existing := SubmissionBrokerage.query.filter_by(submission_id=self.id).first():
            existing.brokerage_id = brokerage_id
            existing.source = source
        else:
            sb = SubmissionBrokerage(submission_id=self.id, brokerage_id=brokerage_id, source=source)
            db.session.add(sb)

    def set_brokerage(self, brokerage: Brokerage | None, source: BrokerageEmployeeSource | None = None):
        brokerage_id = brokerage.id if brokerage else None
        self.set_brokerage_id(brokerage_id, source)
        self.brokerage = brokerage

    def get_submission_brokerage(self) -> SubmissionBrokerage:
        return SubmissionBrokerage.query.filter_by(submission_id=self.id).first()

    def get_submission_broker(self) -> SubmissionBrokerageEmployee:
        return SubmissionBrokerageEmployee.query.filter_by(
            submission_id=self.id, role=BrokerageEmployeeRoles.AGENT
        ).first()

    def get_submission_broker_contact(self) -> SubmissionBrokerageEmployee:
        return SubmissionBrokerageEmployee.query.filter_by(
            submission_id=self.id, role=BrokerageEmployeeRoles.CORRESPONDENCE_CONTACT
        ).first()

    @property
    def is_split(self) -> bool:
        has_report_bundle = self.report.report_bundle_id is not None
        supported_split_suffixes = {
            "liability-primary",
            "liability-excess",
        }
        return has_report_bundle or any(suffix in self.name.lower() for suffix in supported_split_suffixes)

    @property
    def agent_code(self) -> str | None:
        code_from_broker = self.broker.get_agent_code(self.organization_id) if self.broker else None

        if code_from_broker:
            return code_from_broker

        return self.brokerage.get_agent_code(self.organization_id) if self.brokerage else None

    @property
    def agency_code(self) -> str | None:
        code_from_broker = self.broker.get_agency_code(self.organization_id) if self.broker else None

        if code_from_broker:
            return code_from_broker

        return self.brokerage.get_agency_code(self.organization_id) if self.brokerage else None

    def create_consolidation_process(self) -> SubmissionConsolidationProcess:
        files_waiting_for_consolidation = [
            f.checksum
            for f in self.files
            if f.processing_state == FileProcessingState.WAITING_FOR_DATA_CONSOLIDATION and f.checksum
        ]

        files_already_consolidated = [
            f.checksum
            for f in self.files
            if f.classification in ClassificationDocumentType.data_consolidation_classifications()
            and f.processing_state in FileProcessingState.already_consolidated_states()
            and f.checksum
        ]
        consolidated_files_hash = hash(
            (tuple(sorted(files_waiting_for_consolidation)), tuple(sorted(files_already_consolidated)))
        )

        return SubmissionConsolidationProcess(
            id=uuid4(),
            submission_id=self.id,
            consolidated_files_hash=consolidated_files_hash,
            status=SubmissionConsolidationStatus.PENDING,
        )


Index("submissions_account_id_idx", Submission.account_id, postgresql_where=Submission.account_id.isnot(None))


# noinspection PyUnusedLocal
@event.listens_for(Submission, "before_update")
@event.listens_for(Submission, "before_insert")
def listen_for_policy_expiration_date_change(mapper, connection, target):
    expiration_date_history = db.inspect(target).attrs["policy_expiration_date"].history
    if expiration_date_history.has_changes() and target.policy_expiration_date:
        add_after_commit_event(
            AfterCommitEventType.SUBMISSIONS_EXPIRATION_DATE_CHANGED, (str(target.id), target.owner_id)
        )


@event.listens_for(Submission, "before_update")
@event.listens_for(Submission, "before_insert")
def listen_for_stuck_engineering_change(mapper, connection, target) -> None:
    import flask

    from copilot.constants import STUCK_FOR_ENGINEERING_SLACK_CHANNEL_ID

    try:
        stuck_eng_history = db.inspect(target).attrs["_is_stuck_engineering"].history
        if stuck_eng_history.has_changes() and flask.current_app.is_prod:
            is_stuck_eng = stuck_eng_history.added[0] if stuck_eng_history.added else None
            if is_stuck_eng:
                flask.current_app.slack_client.send_slack_message(
                    STUCK_FOR_ENGINEERING_SLACK_CHANNEL_ID,
                    (
                        "<!here> Submission"
                        f" {target.name} (https://copilot.kalepa.com/report/{target.report_id!s}) is stuck"
                        f" for engineers to review. Stuck reason: {target.stuck_reason}"
                    ),
                )
    except Exception:
        logger.exception("Failed to send stuck for engineering Slack message", submission_id=target.id)


@event.listens_for(Submission, "before_update")
@event.listens_for(Submission, "before_insert")
def listen_for_stuck_reason_change(mapper, connection, target) -> None:
    stuck_reason_history = db.inspect(target).attrs["_stuck_reason"].history
    if stuck_reason_history.has_changes():
        old_reason = stuck_reason_history.deleted[0] if stuck_reason_history.deleted else None
        new_reason = stuck_reason_history.added[0] if stuck_reason_history.added else None
        if old_reason is None and new_reason is None:
            return
        is_escalated = None
        if old_reason and not new_reason:
            is_escalated = False

        add_after_commit_event(
            AfterCommitEventType.SUBMISSIONS_STUCK_REASON_CHANGED,
            (str(target.id), is_escalated, new_reason, target.processing_state),
        )


@event.listens_for(Submission, "before_update")
@event.listens_for(Submission, "before_insert")
def listen_for_client_stage_id_change(mapper, connection, target) -> None:
    client_stage_id_history = db.inspect(target).attrs["client_stage_id"].history
    if client_stage_id_history.has_changes():
        old_stage = client_stage_id_history.deleted[0] if client_stage_id_history.deleted else None
        new_stage = client_stage_id_history.added[0] if client_stage_id_history.added else None
        if new_stage is None or old_stage == new_stage:
            return

        add_after_commit_event(
            AfterCommitEventType.CLIENT_SUBMISSION_STAGE_CHANGED,
            (str(target.id), new_stage),
        )


@event.listens_for(ReportV2, "after_insert")
def track_report_creation(_mapper, _connection, report: ReportV2) -> None:
    from copilot.clients.amplitude import AmplitudeClient
    from copilot.models import User

    if report.owner_id:
        if not report.owner:
            user = User.query.get(report.owner_id)
        else:
            user = report.owner
        AmplitudeClient.track_report_created(user, report)
    else:
        AmplitudeClient.track_report_created(current_user, report)


@event.listens_for(Submission, "before_update")
def listen_for_effective_date_change(mapper, connection, target: Submission) -> None:  # type: ignore
    effective_date = db.inspect(target).attrs["proposed_effective_date"].history
    if effective_date.has_changes():
        add_after_commit_event(AfterCommitEventType.SUBMISSION_EFFECTIVE_DATE_CHANGED, (target.report_id,))


@event.listens_for(Submission, "before_update")
def listen_for_verification_change(mapper, connection, target: Submission) -> None:  # type: ignore
    is_verified_history = db.inspect(target).attrs["is_verified"].history
    is_verified_shell_history = db.inspect(target).attrs["is_verified_shell"].history

    is_verified = is_verified_history.added[0] if is_verified_history.added else None
    is_verified_shell = is_verified_shell_history.added[0] if is_verified_shell_history.added else None
    if (
        is_verified_history.has_changes()
        or is_verified_shell_history.has_changes()
        and (is_verified or is_verified_shell)
    ):
        # only fire if there were changes made to is_verified or is_verified_shell and at least
        # one of them was set to true
        add_after_commit_event(AfterCommitEventType.SUBMISSION_VERIFIED, (target.id,))


@event.listens_for(Submission, "before_update")
def listen_for_submission_stage_and_not_client_stage_change(mapper, connection, target: Submission) -> None:
    # Heuristic to determine if the organization has a client stage enabled
    # All subs should have a client stage filled once created
    if not target.client_stage_id:
        return

    stage_history = db.inspect(target).attrs["stage"].history
    client_stage_history = db.inspect(target).attrs["client_stage_id"].history

    if stage_history.has_changes() and not client_stage_history.has_changes():
        add_after_commit_event(AfterCommitEventType.SUBMISSION_STAGE_CHANGED_WITHOUT_CLIENT_STAGE_CHANGE, (target.id,))


@event.listens_for(ReportV2, "before_update")
def listen_for_org_group_change(mapper, connection, target: ReportV2) -> None:  # type: ignore
    org_group = db.inspect(target).attrs["org_group"].history
    if org_group.has_changes():
        data = (
            str(target.submission.id),
            SubmissionActionType.ORG_GROUP_CHANGED,
            None,
            target.id,
            SubmissionParentType.REPORT,
        )
        add_after_commit_event(AfterCommitEventType.ORG_GROUP_CHANGED, data)


@event.listens_for(Submission, "after_update")
def listen_for_submission_ml_variable_change(mapper, connection, target: Submission) -> None:  # type: ignore
    submission_level_ml_variables: list[str] = [
        "proposed_effective_date",
        "fni_state",
        "sales",
        "broker_id",
        "brokerage_id",
        "is_renewal",
        "primary_naics_code",
        "report_id",
        "sic_code",
        "iso_gl_code",
    ]
    for variable_name in submission_level_ml_variables:
        variable_data = db.inspect(target).attrs[variable_name].history
        if variable_data.has_changes():
            key = f"{AfterCommitEventType.RECOMMENDATIONS_ML_VARIABLE_CHANGED}-{target.id}"
            add_after_commit_event(AfterCommitEventType.RECOMMENDATIONS_ML_VARIABLE_CHANGED, (target.id,), key=key)
            break


@event.listens_for(Submission, "after_update")
def listen_for_clearing_status_changed(mapper, connection, target: Submission) -> None:  # type: ignore
    variable_data = db.inspect(target).attrs["clearing_status"].history
    if variable_data.has_changes():
        add_after_commit_event(AfterCommitEventType.CLEARING_STATUS_UPDATED, (target.id,))


# noinspection PyUnusedLocal
@event.listens_for(Submission, "before_update")  # type: ignore
def listen_for_stage_change(mapper, connection, target: Submission) -> None:  # type: ignore
    stage_history = db.inspect(target).attrs["stage"].history
    old_stage = stage_history.deleted[0] if stage_history.deleted else None
    new_stage = stage_history.added[0] if stage_history.added else None
    if stage_history.has_changes():
        # Doing complicated logic here breaks other parts of endpoints processing because it messes with
        # SqlAlchemy transaction. So, let's put all required data into current thread (processing current request)
        # local data and process below after transaction has been committed.
        current_thread_id = threading.get_ident()
        logger.info(f"[Thread({current_thread_id!s})] Adding Submission({target.id!s}) because it changed state.")
        add_after_commit_event(
            AfterCommitEventType.SUBMISSIONS_STATE_CHANGE,
            (
                target.id,
                old_stage,
                new_stage,
                target.lost_reasons,
                target.reason_for_declining,
                target.organization_id,
                target.stage_details,
            ),
        )
    return None


@event.listens_for(Submission, "before_update")
def listen_for_taxonomy_change(mapper, connection, target: Submission) -> None:  # type: ignore
    primary_naics_code_history = db.inspect(target).attrs["primary_naics_code"].history
    is_naics_verified_history = db.inspect(target).attrs["is_naics_verified"].history
    if primary_naics_code_history.has_changes() or is_naics_verified_history.has_changes():
        add_after_commit_event(AfterCommitEventType.SUBMISSION_PRIMARY_NAICS_CODE_CHANGED, (target.id,))

    iso_gl_code_history = db.inspect(target).attrs["iso_gl_code"].history
    sic_code_history = db.inspect(target).attrs["sic_code"].history

    taxonomy_changes = []
    if primary_naics_code_history.has_changes() and target.primary_naics_code:
        taxonomy_changes.append((TaxonomyChangeType.NAICS_CHANGED, target.primary_naics_code))
    if iso_gl_code_history.has_changes() and target.iso_gl_code:
        taxonomy_changes.append((TaxonomyChangeType.ISO_GL_CHANGED, target.iso_gl_code))
    if sic_code_history.has_changes() and target.sic_code:
        taxonomy_changes.append((TaxonomyChangeType.SIC_CHANGED, target.sic_code))

    if taxonomy_changes:
        add_after_commit_event(AfterCommitEventType.SUBMISSION_TAXONOMY_CHANGED, (target.id, taxonomy_changes))


class SubmissionDeductible(BaseModel):
    __tablename__ = "submission_deductibles"

    coverage_id = Column(UUID(as_uuid=True), ForeignKey("coverages.id", ondelete="CASCADE"), nullable=False, index=True)
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )

    policy_limit = Column(Float)
    policy_level = Column(Float)
    policy_level_type = Column(Enum(PolicyLevelDeductibleType))
    minimum = Column(Float)
    comment = Column(String)

    coverage = relationship("Coverage", uselist=False, lazy="joined")
    coverage_type = Column(Enum(CoverageType, native_enum=False), nullable=True)

    __table_args__ = (
        Index(
            "uq_submission_deductibles_coverage_id_type_submission_id",
            "submission_id",
            "coverage_id",
            "coverage_type",
            unique=True,
            postgresql_where="coverage_type IS NOT NULL",
        ),
        Index(
            "uq_submission_deductibles_coverage_id_type_submission_id_nulls",
            "submission_id",
            "coverage_id",
            unique=True,
            postgresql_where="coverage_type IS NULL",
        ),
    )

    def copy(self):
        return SubmissionDeductible(
            coverage_id=self.coverage_id,
            policy_limit=self.policy_limit,
            policy_level=self.policy_level,
            policy_level_type=self.policy_level_type,
            minimum=self.minimum,
            comment=self.comment,
            coverage_type=self.coverage_type,
        )


class Coverage(BaseModel):
    class ExistingNames(StrEnum):
        """
        Enum which holds all currently existing Coverages by theirs names in the table.
        DB First approach is used here
        """

        BoilerMachinery = "boilerMachinery"
        BusinessAuto = "businessAuto"
        BusinessOwners = "businessOwners"
        CommercialInlandMarine = "commercialInlandMarine"
        Crime = "crime"
        CyberPrivacy = "cyberPrivacy"
        EmploymentPracticesLiability = "employmentPracticesLiability"
        FiduciaryLiability = "fiduciaryLiability"
        FiduciaryLiabilityTemp = "fiduciaryLiabilityTemp"
        ForeignLiability = "foreignLiability"
        GarageDealers = "garageDealers"
        Liability = "liability"
        LiquorLiability = "liquorLiability"
        ManagementLiability = "managementLiability"
        Other = "other"
        Package = "package"
        PolicyFee = "policyFee"
        Property = "property"
        Terrorism = "terrorism"
        Truckers = "truckers"
        WorkersComp = "workersComp"
        professionalLiability = "professionalLiability"
        CannabisLiability = "cannabisLiability"
        sitePollution = "sitePollution"
        contractorsPollution = "contractorsPollution"
        PolicyPremium = "policyPremium"
        events = "events"
        productLiability = "productLiability"
        equipmentBreakdown = "equipmentBreakdown"

    __tablename__ = "coverages"

    name = Column(String, unique=True, nullable=False)
    display_name = Column(String, unique=True, nullable=False)
    is_disabled = Column(Boolean, default=False, nullable=False)
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=True, index=True)
    coverage_types = Column(ARRAY(Enum(CoverageType, native_enum=False)), nullable=False, default=[])
    logical_group = Column(Enum(CoverageGroup, native_enum=False, validate_strings=False), nullable=True)
    """
    _groups is currently not being utilized, but left in code in anticipation of future usage.
    The intent of this is to allow groups of permitted coverages within a single submission.
    """
    _groups = Column("groups", MutableDict.as_mutable(JSONB))

    @property
    def groups(self):
        if self._groups:
            return self._groups

        if len(self.coverage_types) == 0:
            return {"None": "1"}
        else:
            return {ct: "1" for ct in self.coverage_types}

    @groups.setter
    def groups(self, value):
        if not self._is_valid_group_setup(value):
            raise Conflict("Invalid group setup")

        self._groups = value

    def _is_valid_group_setup(self, groups: dict):
        if len(self.coverage_types) == 0 and len(groups) == 1 and "NONE" in groups:
            return True

        return len(self.coverage_types) == len(groups) and all([ct in groups for ct in self.coverage_types])

    def can_create_submission_coverage_with(self, coverage_type: CoverageType | None) -> bool:
        if not self.coverage_types:
            return not coverage_type

        return coverage_type is not None and {coverage_type}.issubset(set(self.coverage_types))


class SubmissionBusiness(BaseModel):
    __tablename__ = "submission_businesses"

    # User input fields
    requested_name = Column(String)
    requested_legal_name = Column(String)
    requested_address = Column(String)
    requested_phone_number = Column(String)
    requested_industries = Column(ARRAY(String))
    entity_role = Column(Enum(SubmissionBusinessEntityRole), nullable=True)
    named_insured = Column(Enum(SubmissionBusinessEntityNamedInsured), nullable=True)
    project_insurance_type = Column(Enum(ProjectInsuranceType), nullable=True)
    description_of_operations = Column(String)
    additional_info = Column(JSONB, nullable=True)

    # We need this field for search, but there is no need to store this
    requested_identifiers = None

    # ERS fields
    business_id = Column(UUID(as_uuid=True), index=True)
    is_user_confirmed = Column(Boolean)

    aliases = Column(ARRAY(String))
    selected_alias = Column(String)

    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    submission = relationship("Submission", uselist=False, lazy="select", back_populates="businesses")

    serial_id_seq = Sequence("serial_id_seq", metadata=meta)
    serial_id = Column(Integer, serial_id_seq, nullable=False, server_default=serial_id_seq.next_value())

    # Snapshot fields
    ers_snapshot_id = Column(UUID(as_uuid=True), index=True)
    hide_property_facts = Column(Boolean, default=False)

    hide = Column(Boolean, default=False)

    file_id_sources = Column(ARRAY(UUID(as_uuid=True)), nullable=True)

    _explode_address_range = False
    _force_automatch = False
    _expand_address_range = False

    def copy(self) -> SubmissionBusiness:
        new = SubmissionBusiness(
            requested_name=self.requested_name,
            requested_address=self.requested_address,
            requested_industries=self.requested_industries,
            requested_legal_name=self.requested_legal_name,
            requested_phone_number=self.requested_phone_number,
            entity_role=self.entity_role,
            named_insured=self.named_insured,
            project_insurance_type=self.project_insurance_type,
            description_of_operations=self.description_of_operations,
            business_id=self.business_id,
            is_user_confirmed=self.is_user_confirmed,
            aliases=self.aliases,
            selected_alias=self.selected_alias,
            ers_snapshot_id=self.ers_snapshot_id,
            hide_property_facts=self.hide_property_facts,
            additional_info=self.additional_info,
            file_id_sources=self.file_id_sources,
            hide=self.hide,
        )
        return new

    @property
    def entity_data(self) -> Entity | None:
        if hasattr(self, "_ers_v3_data"):
            return self._ers_v3_data
        return None

    @entity_data.setter
    def entity_data(self, entity_data: Entity) -> None:
        # noinspection PyAttributeOutsideInit
        self._ers_v3_data = entity_data

    @property
    def expand_address_range(self) -> bool | None:
        return self._expand_address_range

    @expand_address_range.setter
    def expand_address_range(self, expand_address_range: Boolean) -> None:
        # noinspection PyAttributeOutsideInit
        self._expand_address_range = expand_address_range  # type: ignore

    @property
    def explode_address_range(self) -> bool | None:
        return self._explode_address_range

    @explode_address_range.setter
    def explode_address_range(self, explode_address_range: Boolean):
        # noinspection PyAttributeOutsideInit
        self._explode_address_range = explode_address_range  # type: ignore

    @property
    def force_automatch(self) -> bool | None:
        return self._force_automatch

    @force_automatch.setter
    def force_automatch(self, force_automatch: bool) -> None:
        # noinspection PyAttributeOutsideInit
        self._force_automatch = force_automatch

    @property
    def resolution_results(self) -> list[ResolutionResult] | None:
        # todo initialize in constructor
        if hasattr(self, "_resolution_results"):
            return self._resolution_results
        return None

    @property
    def is_fni(self) -> bool:
        return self.named_insured == SubmissionBusinessEntityNamedInsured.FIRST_NAMED_INSURED

    @property
    def is_oni(self) -> bool:
        return self.named_insured == SubmissionBusinessEntityNamedInsured.OTHER_NAMED_INSURED

    @property
    def is_gc(self) -> bool:
        return self.entity_role == SubmissionBusinessEntityRole.GENERAL_CONTRACTOR

    @property
    def is_project(self) -> bool:
        return self.entity_role == SubmissionBusinessEntityRole.PROJECT

    @resolution_results.setter
    def resolution_results(self, resolution_results: list[ResolutionResult]) -> None:
        # noinspection PyAttributeOutsideInit
        self._resolution_results = resolution_results

    @property
    def resolution_confidence(self) -> str:
        return self._resolution_confidence

    @resolution_confidence.setter
    def resolution_confidence(self, resolution_confidence: str) -> None:
        # noinspection PyAttributeOutsideInit
        self._resolution_confidence = resolution_confidence

    @property
    def resolved_name(self) -> str | None:
        if self.entity_data and self.entity_data.names:
            return next((n.value for n in self.entity_data.names if n.value != REAL_ESTATE_NAME), None)
        return None

    @property
    def premises(self) -> Premises | None:
        if self.entity_data and self.entity_data.premises:
            premises = self.entity_data.premises
            return next(
                (p.premises for p in premises if p.type == EntityPremisesType.PHYSICAL_ADDRESS.value and p.premises),
                premises[0].premises if premises[0].premises else None,
            )
        return None

    @property
    def resolved_address(self) -> str | None:
        return self.premises.address_line_1 if self.premises else None

    def get_name_for_report(self, use_resolved_attributes: bool = False, use_address: bool = False) -> str | None:
        req_name = self.requested_name if self.requested_name else None
        req_address = self.requested_address.split(",")[0] if self.requested_address else None
        result = None

        if use_resolved_attributes:
            req_name = req_name or self.resolved_name
            req_address = req_address or self.resolved_address

        if use_address:
            result = req_address

        result = req_name if req_name else result
        return result

    def __eq__(self, other) -> bool:
        return self.business_id == other.business_id

    def __hash__(self) -> int:
        return hash(self.id)


@event.listens_for(SubmissionBusiness, "after_update")
def listen_for_business_ml_variable_change(mapper, connection, target: SubmissionBusiness) -> None:  # type: ignore
    business_level_ml_variables: list[str] = [
        "project_insurance_type",
    ]
    for variable_name in business_level_ml_variables:
        variable_data = db.inspect(target).attrs[variable_name].history
        if variable_data.has_changes():
            key = f"{AfterCommitEventType.RECOMMENDATIONS_ML_VARIABLE_CHANGED}-{target.submission_id}"
            add_after_commit_event(
                AfterCommitEventType.RECOMMENDATIONS_ML_VARIABLE_CHANGED, (target.submission_id,), key=key
            )
            break


class SummaryPreference(BaseModel):
    display_name = Column(String, nullable=False, index=True)
    group_display_name = Column(String, nullable=False)
    is_default = Column(Boolean, nullable=False, index=True)
    icon_name = Column(String, nullable=True)
    metric_type = Column(String, nullable=True)


# TODO: Rename to SubmissionSummaryPreference
class ReportSummaryPreference(BaseModel):
    report_v2_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id"), nullable=True, index=True)
    summary_preference_id = Column(UUID(as_uuid=True), ForeignKey("summary_preference.id"), nullable=False, index=True)
    summary_preference = relationship("SummaryPreference", uselist=False, lazy="joined")


class Subscription(BaseModel):
    report_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    is_muted = Column(Boolean, default=False)
    user = relationship("User", uselist=False, lazy="joined")  # type: ignore

    def copy(self):
        return Subscription(user_id=self.user_id, is_muted=self.is_muted)


class SubmissionClearingIssue(BaseModel):
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    # Report_id of the report that potentially conflicts with self.submission_id
    suspected_report_id = Column(
        UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="CASCADE"), nullable=False, index=True
    )
    is_resolved = Column(Boolean, default=False)
    is_light = Column(Boolean, default=False, nullable=False)
    reason = Column(String)

    suspected_report = relationship("ReportV2")
    clearing_sub_statuses = relationship(
        "SubmissionClearingSubStatus", cascade="all, delete-orphan", uselist=True, backref="SubmissionClearingIssue"
    )

    def copy(self):
        return SubmissionClearingIssue(
            submission_id=self.submission_id,
            suspected_report_id=self.suspected_report_id,
            is_resolved=self.is_resolved,
            is_light=self.is_light,
            reason=self.reason,
        )

    @property
    def suspected_report_name(self) -> str:
        return self.suspected_report.name

    @property
    def report_broker_id(self) -> str | None:
        if self.suspected_report.submission:
            return self.suspected_report.submission.broker_id
        return None

    @property
    def report_broker_name(self) -> str | None:
        if self.suspected_report.submission:
            return self.suspected_report.submission.broker_name
        return None

    @property
    def report_brokerage_name(self) -> str | None:
        if self.suspected_report.submission:
            return self.suspected_report.submission.brokerage_name
        return None

    @property
    def report_client_stage_name(self) -> str | None:
        if self.suspected_report.submission:
            return (
                self.suspected_report.submission.client_stage.client_stage
                if self.suspected_report.submission.client_stage
                else None
            )
        return None

    @property
    def report_created_date(self) -> datetime:
        return self.suspected_report.created_at

    @property
    def report_effective_date(self) -> datetime | None:
        if self.suspected_report.submission:
            return self.suspected_report.submission.proposed_effective_date
        return None

    @property
    def report_stage(self) -> SubmissionStage | None:
        if self.suspected_report.submission:
            return self.suspected_report.submission.stage
        return None

    @property
    def report_is_auto_processed(self) -> bool | None:
        if self.suspected_report.submission:
            return self.suspected_report.submission.is_auto_processed
        return None

    @property
    def report_coverages(self) -> list[SubmissionCoverage] | None:
        if self.suspected_report.submission:
            return self.suspected_report.submission.coverages
        return None

    @property
    def report_origin(self) -> Origin | None:
        if self.suspected_report.submission:
            return self.suspected_report.submission.origin
        return None

    @property
    def report_has_email(self) -> bool:
        if self.suspected_report.submission:
            return bool(self.suspected_report.email_body)
        return False

    @property
    def report_is_deleted(self) -> bool:
        return self.suspected_report.is_deleted


class SubmissionBookmark(BaseModel):
    __tablename__ = "submission_bookmarks"
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    __table_args__ = (
        UniqueConstraint("submission_id", "user_id", name="uq_submission_bookmarks_user_id_submission_id"),
    )

    def copy(self, old_to_new_ids: dict) -> SubmissionBookmark:
        new = SubmissionBookmark()
        new.id = uuid4()  # type: ignore
        old_to_new_ids[self.id] = new.id
        new.submission_id = old_to_new_ids.get(self.submission_id, self.submission_id)
        new.user_id = self.user_id
        return new


class SubmissionNote(BaseModel):
    __tablename__ = "submission_note"

    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    author_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True, index=True)
    author = relationship("User", foreign_keys=[author_id])  # type: ignore
    last_edit_by_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True, index=True)
    last_editor = relationship("User", foreign_keys=[last_edit_by_id])  # type: ignore
    text = Column(String, nullable=False)
    referred_to_user_ids = Column(ARRAY(Integer), nullable=True)
    referrals_closed_to_user_ids = Column(ARRAY(Integer), nullable=True)
    is_generated_note = Column(Boolean, default=False, nullable=False)
    is_editable = Column(Boolean, nullable=True)
    rule_id = Column(UUID(as_uuid=True), nullable=True)
    hashed_text = Column(String, nullable=True)
    canonical_id = Column(UUID(as_uuid=True), nullable=True)
    # note or message
    is_note = Column(Boolean, nullable=True)
    html_content = Column(String, nullable=True)

    @property
    def author_name(self) -> str | None:
        if not self.author:
            return None

        return self.author.name

    @property
    def last_editor_name(self) -> str | None:
        if not self.last_editor:
            return None

        return self.last_editor.name

    @property
    def text_content(self) -> str:
        try:
            text_dict = json.loads(self.text)
        except:
            logger.warning(
                "Failed to parse submission note text as JSON", submission_id=self.submission_id, exc_info=True
            )
            return self.text

        return extract_text_from_note(text_dict)

    def copy(self, old_to_new_ids: dict) -> SubmissionNote:
        new = SubmissionNote()
        new.id = uuid4()  # type: ignore
        old_to_new_ids[self.id] = new.id
        new.submission_id = old_to_new_ids.get(self.submission_id, self.submission_id)

        new.author_id = self.author_id
        new.last_edit_by_id = self.last_edit_by_id
        new.created_at = self.created_at
        new.updated_at = self.updated_at
        new.text = self.text
        new.referred_to_user_ids = self.referred_to_user_ids
        new.referrals_closed_to_user_ids = self.referrals_closed_to_user_ids
        new.rule_id = self.rule_id
        new.hashed_text = self.hashed_text
        new.is_generated_note = self.is_generated_note
        new.is_editable = self.is_editable
        new.canonical_id = self.canonical_id
        new.is_note = self.is_note
        new.html_content = self.html_content

        return new


@dataclass
class RecommendationSubmissionNoteRequest:
    submission_id: uuid.UUID
    text: str
    rule_id: uuid.UUID
    is_editable: bool = False
    is_delete_request: bool = False


class SubmissionRecommendationResult(db.Model):
    __tablename__ = "submission_recommendation_results"
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, primary_key=True
    )
    action = Column(Enum(RecommendationActionEnum), nullable=True, index=True)
    priority = Column(DECIMAL, nullable=True, index=True)
    is_refer = Column(Boolean, nullable=True)
    score = Column(Integer, nullable=True)
    score_ml = Column(Float, nullable=True)
    pm_rules_modifier = Column(Float, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())


class SubmissionIdentifier(BaseModel):
    __tablename__ = "submission_identifiers"
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    identifier = Column(String, nullable=False)
    identifier_type = Column(String, nullable=False)
    __table_args__ = (
        UniqueConstraint(
            "submission_id", "identifier_type", name="uq_submission_identifiers_submission_id_identifier_type"
        ),
    )

    def copy(self, old_to_new_ids: dict) -> SubmissionIdentifier:
        new = SubmissionIdentifier()
        new.id = uuid4()  # type: ignore
        old_to_new_ids[self.id] = new.id
        new.submission_id = old_to_new_ids.get(self.submission_id, self.submission_id)
        new.identifier = self.identifier
        new.identifier_type = self.identifier_type
        return new


class SubmissionFieldSource(BaseModel):
    __tablename__ = "submission_field_source"
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    field_name = Column(String, nullable=False)
    source = Column(String, nullable=False)

    def copy(self, old_to_new_ids: dict) -> SubmissionFieldSource:
        new = SubmissionFieldSource()
        new.id = uuid4()  # type: ignore
        old_to_new_ids[self.id] = new.id
        new.submission_id = old_to_new_ids.get(self.submission_id, self.submission_id)
        new.field_name = self.field_name
        new.source = self.source
        return new


@event.listens_for(SubmissionIdentifier, "after_insert")
def listen_for_submission_identifier_created(mapper, connection, target) -> None:  # type: ignore
    add_after_commit_event(AfterCommitEventType.SUBMISSION_IDENTIFIER_ADDED, (target.submission_id,))


@event.listens_for(SubmissionIdentifier, "after_delete")
def listen_for_submission_identifier_removed(mapper, connection, target) -> None:  # type: ignore
    add_after_commit_event(
        AfterCommitEventType.SUBMISSION_IDENTIFIER_REMOVED, (target.submission_id, target.identifier_type)
    )


@event.listens_for(Submission, "after_update")
def liste_for_submission_prevent_clearing_updates(mapper, connection, target) -> None:
    prevent_clearing_change_history = db.inspect(target).attrs["prevent_clearing_updates"].history
    if prevent_clearing_change_history.has_changes():
        add_after_commit_event(AfterCommitEventType.SUBMISSION_PREVENT_CLEARING_UPDATES_CHANGED, (target.id,))


@dataclass
class SaveSubmissionNoteOptions:
    note_html: str
    users_to_notify: list[int]
    users_to_refer: list[int] | None = None


@dataclass
class AdditionalIdentifier:
    type: AdditionalIdentifierType | None
    identifier: str


@dataclass
class CreateReportRequest:
    original_report_id: UUID | None = None
    target_stage: SubmissionStage | None = None
    user_email: str | None = None
    external_id: str | None = None
    is_renewal: bool | None = None
    proposed_effective_date: datetime | None = None
    policy_expiration_date: datetime | None = None
    coverage_type: SubmissionCoverageType | None = None
    broker: str | None = None
    broker_email: str | None = None
    brokerage: str | None = None
    brokerage_office: str | None = None
    correspondence_contact_name: str | None = None
    correspondence_contact_email: str | None = None
    sub_producer_name: str | None = None
    sub_producer_email: str | None = None
    submission_status: str | None = None
    s3_key: str | None = None
    s3_bucket: str | None = None
    businesses: list[dict] = field(default_factory=list)
    submission_link: str | None = None
    name: str | None = None
    status: ReportStatus = ReportStatus.CLEARED
    additional_data: dict | None = None
    pds: bool = False
    origin: Origin | None = None
    email_body: str | None = None
    email_subject: str | None = None
    email_message_id: str | None = None
    email_references: str | None = None
    correspondence_id: str | None = None
    client_clearing_status: str | None = None
    additional_identifiers: list[AdditionalIdentifier] = field(default_factory=list)
    is_pre_renewal: bool = False
    is_stub: bool = False
    org_group: str | None = None

    @property
    def quote_number(self) -> str | None:
        for identifier in self.additional_identifiers or []:
            if identifier.type == AdditionalIdentifierType.QUOTE_NUMBER:
                return identifier.identifier
        return None


@dataclass
class SubmissionUserRequest:
    submission_id: UUID
    user_id: int
    should_share: bool | None = None
    ignore_frozen: bool = False


@dataclass
class ReportsQueueEnvelope:
    reports: list[ReportV2]
    page: int | None = None
    total_pages: int | None = None
    total_reports: int | None = None
    has_next: bool | None = None


class ReadSubmission(db.Model):
    __tablename__ = "read_submission"

    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, primary_key=True
    )
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, primary_key=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    submission = relationship("Submission", foreign_keys=submission_id)
    user = relationship("User", foreign_keys=user_id)

    def copy(self, old_to_new_ids: dict) -> ReadSubmission:
        new = ReadSubmission()
        new.id = uuid4()  # type: ignore
        new.submission_id = old_to_new_ids.get(self.submission_id, self.submission_id)
        new.user_id = self.user_id
        return new


class UserSubmissionNotification(db.Model):
    __tablename__ = "user_submission_notification"
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, primary_key=True)
    user = relationship("User", uselist=False)

    submission_id = Column(
        UUID(as_uuid=True),
        ForeignKey("submissions.id", ondelete="CASCADE"),
        nullable=False,
        primary_key=True,
    )
    submission = relationship("Submission", uselist=False)

    seen_emails = Column(Integer, default=0, nullable=False)
    seen_notes = Column(Integer, default=0, nullable=False)


class ClearingSubStatus(StrEnum):
    CONFLICT = "Conflict"
    MISSING_DETAILS = "Missing Details"


class SubmissionClearingSubStatus(BaseModel):
    __tablename__ = "submission_clearing_sub_status"

    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    status = Column(Enum(ClearingSubStatus), nullable=False)
    sub_status = Column(String, nullable=True)
    submission_clearing_issue_id = Column(
        UUID(as_uuid=True), ForeignKey("submission_clearing_issue.id", ondelete="CASCADE"), nullable=True
    )

    submission_clearing_issue = relationship("SubmissionClearingIssue")

    def copy(self) -> SubmissionClearingSubStatus:
        new = SubmissionClearingSubStatus(
            status=self.status,
            sub_status=self.sub_status,
            submission_clearing_issue_id=self.submission_clearing_issue_id,
        )

        return new


class ClientSubmissionStageConfig(db.Model):
    __tablename__ = "client_submission_stage_config"

    id = Column(Integer, primary_key=True)
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=False)
    client_stage = Column(String, nullable=False)
    tags = Column(ARRAY(String), nullable=False)
    tag_labels = Column(ARRAY(String), nullable=False)
    copilot_stage = Column(Enum(SubmissionStage), nullable=False)
    with_comment = Column(Boolean, nullable=True)
    default = Column(Boolean, nullable=True)
    not_selectable = Column(Boolean, nullable=True)

    @property
    def status_array(self) -> list[str]:
        result = [self.client_stage]
        result.extend(self.tags or [])
        return result

    @property
    def status_dict(self) -> dict:
        if self.organization_id not in CUSTOM_STATUS_FIELDS_PER_CLIENT:
            return {}

        config = CUSTOM_STATUS_FIELDS_PER_CLIENT[self.organization_id]
        # we need to make sure we have enough elements for zipping
        status_array = self.status_array + len(config.status_fields) * [None]
        return {key: val for key, val in zip(config.status_fields, status_array)}


@dataclass
class CreateOrReplaceNoteRequest:
    notes: str
    smart_matching: bool = False
