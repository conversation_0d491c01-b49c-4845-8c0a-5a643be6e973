from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import J<PERSON><PERSON><PERSON>, UUID
from static_common.enums.async_ops import AsyncOperation, AsyncOperationStatus

from copilot.models import BaseModel  # type: ignore


class AsyncOperationDB(BaseModel):
    """
    Represents details related to single asynchronous operation within Kalepa domain.
    """

    __tablename__ = "async_operations"

    organization_id = Column(Integer, ForeignKey("organization.id", ondelete="SET NULL"), nullable=True, index=True)
    report_id = Column(UUID(as_uuid=True), ForeignKey("reports_v2.id", ondelete="SET NULL"), nullable=True, index=True)
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="SET NULL"), nullable=True, index=True
    )

    status = Column(Enum(AsyncOperationStatus, native_enum=False), nullable=False)
    operation = Column(Enum(AsyncOperation, native_enum=False), nullable=False, index=True)

    error_details = Column(JSONB, nullable=True)
    additional_data = Column(JSONB, nullable=True)

    # This can be any additional identifier like client ID, quote number, request id etc. depending on the operation
    # and client needs.
    logical_identifier = Column(String, nullable=True, index=True)

    attempts = Column(Integer, nullable=False)
    executing_user_email = Column(String, nullable=False)
