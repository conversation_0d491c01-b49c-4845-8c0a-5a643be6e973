from __future__ import annotations

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>loat, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.dialects.postgresql import ARRAY, UUID
from sqlalchemy.orm import relationship
from static_common.enums.classification import (
    ClassifierOutputType,
    ClassifierUnits,
    ExtractionType,
    FilterRuleType,
)
from static_common.enums.task_model_execution_type import TaskModelExecutionType
from static_common.enums.task_model_processing_type import TaskModelProcessingType
from static_common.enums.task_model_type import TaskModelType

from copilot.models._private import BaseModel, db
from copilot.models.tasks import TaskModel


class CustomizableClassifierV2(BaseModel):
    __tablename__ = "customizable_classifiers_v2"

    name = Column(String, nullable=False)
    fact_subtype_id = Column(String, nullable=True)
    extracted_value_name = Column(String, nullable=True)
    output_type = Column(Enum(ClassifierOutputType), nullable=True)
    output_unit = Column(Enum(ClassifierUnits), nullable=True)
    tags = Column(ARRAY(String), nullable=False, server_default="{}")
    input_types = Column(ARRAY(String), nullable=False)
    organization_ids = Column(ARRAY(Integer), nullable=False, server_default="{}")
    run_in_pds = Column(Boolean, nullable=False, default=False)
    is_internal = Column(Boolean, nullable=False, default=False)

    versions = relationship(
        "ClassifierVersion", uselist=True, cascade="all, delete-orphan", back_populates="classifier", lazy="joined"
    )

    active_version = relationship(
        "ClassifierVersion",
        uselist=False,
        primaryjoin=(
            "and_(CustomizableClassifierV2.id == ClassifierVersion.classifier_id, "
            "ClassifierVersion.is_active.is_(True))"
        ),
        lazy="joined",
        viewonly=True,
    )

    filter_rules = relationship(
        "FilterRule",
        uselist=True,
        cascade="all, delete-orphan",
        backref="classifier",
        lazy="joined",
    )

    __table_args__ = (
        db.UniqueConstraint(
            "fact_subtype_id", "extracted_value_name", "output_unit", "output_type", name="uq_classifier_v2_identifier"
        ),
        db.CheckConstraint("(fact_subtype_id IS NULL) != (extracted_value_name IS NULL)", name="ck_classifier_v2_xor"),
    )

    @property
    def extracted_value(self) -> str:
        return self.fact_subtype_id or self.extracted_value_name


class ClassifierVersion(BaseModel):
    __tablename__ = "classifier_versions"

    name = Column(String, nullable=False)
    comment = Column(String, nullable=True)
    classifier_description = Column(String, nullable=False)
    is_active = Column(Boolean, nullable=False, default=False)
    classifier_id = Column(
        UUID(as_uuid=True), ForeignKey("customizable_classifiers_v2.id", ondelete="CASCADE"), nullable=False, index=True
    )

    classifier = relationship("CustomizableClassifierV2", back_populates="versions", viewonly=True)
    configs = relationship(
        "ClassifierConfig",
        secondary="classifier_to_config_version",
        primaryjoin="ClassifierVersion.id == foreign(ClassifierToConfigVersion.classifier_version_id)",
        secondaryjoin="foreign(ClassifierToConfigVersion.classifier_config_id) == ClassifierConfig.id",
        back_populates="classifier_versions",
        lazy="joined",
    )
    classifier_to_config_versions = relationship(
        "ClassifierToConfigVersion", back_populates="classifier_version", cascade="all, delete-orphan"
    )

    classifier_task_definitions = relationship(
        "ClassifierVersionTaskDefinition",
        primaryjoin="ClassifierVersion.id == foreign(ClassifierVersionTaskDefinition.classifier_version_id)",
        backref="classifier_version",
        lazy="joined",
    )

    __table_args__ = (
        db.Index(
            "ix_unique_active_version_per_classifier",
            "classifier_id",
            unique=True,
            postgresql_where=db.text("is_active IS TRUE"),
        ),
    )


class ClassifierVersionTaskDefinition(BaseModel):
    __tablename__ = "classifier_version_task_definition"

    classifier_version_id = Column(
        UUID(as_uuid=True), ForeignKey("classifier_versions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    classifier_config_id = Column(
        UUID(as_uuid=True), ForeignKey("classifier_config.id", ondelete="CASCADE"), nullable=False, index=True
    )
    task_definition_id = Column(
        UUID(as_uuid=True), ForeignKey("task_definition.id", ondelete="CASCADE"), nullable=False, index=True
    )


class ClassifierConfig(BaseModel):
    __tablename__ = "classifier_config"

    input_types = Column(ARRAY(String), nullable=False, server_default="{}")
    task_dataset_id = Column(
        UUID(as_uuid=True), ForeignKey("task_dataset.id", ondelete="SET NULL"), nullable=True, index=True
    )

    classifier_versions = relationship(
        "ClassifierVersion",
        secondary="classifier_to_config_version",
        primaryjoin="foreign(ClassifierToConfigVersion.classifier_config_id) == ClassifierConfig.id",
        secondaryjoin=(
            "and_(foreign(ClassifierToConfigVersion.classifier_version_id) == ClassifierVersion.id,"
            " ClassifierToConfigVersion.classifier_config_version_id != None)"
        ),
        back_populates="configs",
        viewonly=True,
    )
    versions = relationship("ClassifierConfigVersion", back_populates="config", cascade="all, delete-orphan")
    classifier_to_config_versions = relationship(
        "ClassifierToConfigVersion", back_populates="classifier_config", cascade="all, delete-orphan"
    )

    @property
    def active_versions(self) -> list[ClassifierConfigVersion]:
        versions = []
        for mapping in self.classifier_to_config_versions:
            if mapping.classifier_version.is_active and mapping.classifier_config_version:
                versions.append(mapping.classifier_config_version)

        return versions


class ClassifierConfigVersion(BaseModel):
    __tablename__ = "classifier_config_versions"

    classifier_config_id = Column(
        UUID(as_uuid=True), ForeignKey("classifier_config.id", ondelete="CASCADE"), nullable=False, index=True
    )
    input_processing_type = Column(String, nullable=False)
    extraction_type = Column(Enum(ExtractionType), nullable=False)
    is_autogenerated = Column(Boolean, nullable=False, default=False)
    task_model_id = Column(
        UUID(as_uuid=True), ForeignKey("task_model.id", ondelete="CASCADE"), nullable=True, index=True
    )

    config = relationship("ClassifierConfig", back_populates="versions")
    classifier_to_config_versions = relationship(
        "ClassifierToConfigVersion", back_populates="classifier_config_version", cascade="all, delete-orphan"
    )

    __mapper_args__ = {"polymorphic_identity": "base", "polymorphic_on": extraction_type}

    def extend_task_model_config(self, config) -> None:
        return config

    def get_task_model(self) -> TaskModel:
        has_llm_model = self.extraction_type != ExtractionType.PHRASES
        extraction_type_to_task_model_type = {
            ExtractionType.PHRASES: TaskModelType.HEURISTIC,
            ExtractionType.PHRASES_WITH_LLM: TaskModelType.COMBINED,
            ExtractionType.LLM: TaskModelType.LLM_PROMPT,
        }

        handler_class = (
            "PhraseCustomizableClassifier"
            if self.extraction_type == ExtractionType.PHRASES
            else "CustomizableClassifier"
        )

        config = {
            "handler_class": handler_class,
            "extraction_type": self.extraction_type.value,
            "input_processing_type": self.input_processing_type,
            "size": "xl",
        }
        self.extend_task_model_config(config)
        return TaskModel(
            name=f"CC_{self.input_processing_type}_{self.extraction_type}_{self.id}",
            type=extraction_type_to_task_model_type[self.extraction_type],
            execution_config=config,
            execution_type=TaskModelExecutionType.LAMBDA,
            processing_type=TaskModelProcessingType.CLASSIFICATION,
            use_task_output_processor=True,
            llm_model=self.llm_model if has_llm_model else None,
        )


class ClassifierPhrase(BaseModel):
    __tablename__ = "classifier_phrases"

    classifier_config_version_id = Column(
        UUID(as_uuid=True), ForeignKey("classifier_config_versions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    phrase = Column(String, nullable=False)
    weight = Column(Float, nullable=False, server_default="1.0")
    excludes = Column(ARRAY(String), nullable=False, server_default="{}")

    __table_args__ = (db.CheckConstraint("weight >= 0 AND weight <= 1", name="ck_weight_range"),)

    def get_config_for_task_model(self) -> dict:
        return {"phrase": self.phrase, "weight": self.weight, "excludes": self.excludes}


class PhrasesWithLLMConfigVersion(ClassifierConfigVersion):
    __tablename__ = "phrases_with_llm_config_versions"

    id = Column(UUID(as_uuid=True), ForeignKey("classifier_config_versions.id", ondelete="CASCADE"), primary_key=True)
    llm_model = Column(String, nullable=False)
    prompt = Column(String, nullable=False)

    phrases = relationship(
        "ClassifierPhrase",
        primaryjoin="ClassifierPhrase.classifier_config_version_id == PhrasesWithLLMConfigVersion.id",
        foreign_keys=[ClassifierPhrase.classifier_config_version_id],
        backref="phrases_with_llm_config_version",
        lazy="joined",
    )

    __mapper_args__ = {
        "polymorphic_identity": ExtractionType.PHRASES_WITH_LLM,
    }

    def extend_task_model_config(self, config: dict) -> None:
        config.update(
            {
                "prompt": self.prompt,
                "phrases": [phrase.get_config_for_task_model() for phrase in self.phrases],
            }
        )


class PhrasesConfigVersion(ClassifierConfigVersion):
    __tablename__ = "phrases_config_versions"

    id = Column(UUID(as_uuid=True), ForeignKey("classifier_config_versions.id", ondelete="CASCADE"), primary_key=True)

    phrases = relationship(
        "ClassifierPhrase",
        primaryjoin="ClassifierPhrase.classifier_config_version_id == PhrasesConfigVersion.id",
        foreign_keys=[ClassifierPhrase.classifier_config_version_id],
        backref="phrases_config_version",
        lazy="joined",
    )

    __mapper_args__ = {
        "polymorphic_identity": ExtractionType.PHRASES,
    }

    def extend_task_model_config(self, config: dict) -> None:
        config.update({"phrases": [phrase.get_config_for_task_model() for phrase in self.phrases]})


class LLMConfigVersion(ClassifierConfigVersion):
    __tablename__ = "llm_config_versions"

    id = Column(UUID(as_uuid=True), ForeignKey("classifier_config_versions.id", ondelete="CASCADE"), primary_key=True)
    llm_model = Column(String, nullable=False)
    prompt = Column(String, nullable=False)

    __mapper_args__ = {
        "polymorphic_identity": ExtractionType.LLM,
    }

    def extend_task_model_config(self, config: dict) -> None:
        config.update({"prompt": self.prompt})


class ClassifierToConfigVersion(BaseModel):
    __tablename__ = "classifier_to_config_version"

    classifier_version_id = Column(
        UUID(as_uuid=True), ForeignKey("classifier_versions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    classifier_config_id = Column(
        UUID(as_uuid=True), ForeignKey("classifier_config.id", ondelete="CASCADE"), nullable=False, index=True
    )
    classifier_config_version_id = Column(
        UUID(as_uuid=True), ForeignKey("classifier_config_versions.id", ondelete="CASCADE"), nullable=True, index=True
    )

    classifier_version = relationship("ClassifierVersion", back_populates="classifier_to_config_versions")
    classifier_config = relationship("ClassifierConfig", back_populates="classifier_to_config_versions")
    classifier_config_version = relationship("ClassifierConfigVersion", back_populates="classifier_to_config_versions")

    __table_args__ = (
        db.UniqueConstraint(
            "classifier_version_id",
            "classifier_config_id",
            "classifier_config_version_id",
            name="uq_classifier_to_config_version",
        ),
    )


class FilterRule(BaseModel):
    __tablename__ = "filter_rules"

    classifier_id = Column(
        UUID(as_uuid=True), ForeignKey("customizable_classifiers_v2.id", ondelete="CASCADE"), nullable=False, index=True
    )
    filter_type = Column(Enum(FilterRuleType), nullable=False)
    negated = Column(Boolean, nullable=False)
    values = Column(ARRAY(String), nullable=False)


class ClassifierVersionTaskDataset(BaseModel):
    __tablename__ = "classifier_version_task_dataset"

    classifier_version_id = Column(
        UUID(as_uuid=True), ForeignKey("classifier_versions.id", ondelete="CASCADE"), nullable=False, index=True
    )
    task_dataset_id = Column(
        UUID(as_uuid=True), ForeignKey("task_dataset.id", ondelete="CASCADE"), nullable=False, index=True
    )

    classifier_version = relationship("ClassifierVersion", backref="classifier_version_task_datasets")
    task_dataset = relationship("TaskDataset", backref="classifier_version_task_datasets")

    __table_args__ = (
        db.UniqueConstraint(
            "classifier_version_id",
            "task_dataset_id",
            name="uq_classifier_version_task_dataset",
        ),
    )
