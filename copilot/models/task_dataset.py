from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON>umn,
    Enum,
    Float,
    Foreign<PERSON>ey,
    Index,
    Integer,
    String,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import JSONB, UUID
from sqlalchemy.orm import relationship

from copilot.models._private import BaseModel
from copilot.models.types import PredictionResult, TaskDatasetExecutionStatus


class TaskDataset(BaseModel):
    __tablename__ = "task_dataset"

    description = Column(String, nullable=False)
    organization_id = Column(Integer, ForeignKey("organization.id"), nullable=True)
    processing_type = Column(String, nullable=False)
    output_type = Column(String, nullable=False)
    dataset_inputs_id = Column(UUID(as_uuid=True), nullable=False)


class TaskDatasetInput(BaseModel):
    __tablename__ = "task_dataset_input"

    dataset_inputs_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    submission_id = Column(
        UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="SET NULL"), nullable=True, index=True
    )
    file_id = Column(UUID(as_uuid=True), ForeignKey("files.id", ondelete="SET NULL"), nullable=True, index=True)
    business_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    context = Column(JSONB, nullable=True)
    input = Column(JSONB, nullable=True)

    __table_args__ = (Index("ix_task_dataset_input_dataset_inputs_id", "dataset_inputs_id"),)


class TaskDatasetGroundTruth(BaseModel):
    __tablename__ = "task_dataset_ground_truth"

    task_dataset_id = Column(UUID(as_uuid=True), ForeignKey("task_dataset.id", ondelete="CASCADE"), nullable=False)
    task_dataset_input_id = Column(
        UUID(as_uuid=True), ForeignKey("task_dataset_input.id", ondelete="CASCADE"), nullable=False
    )
    value = Column(JSONB, nullable=True)
    has_value = Column(Boolean, nullable=False, default=False)

    __table_args__ = (
        UniqueConstraint("task_dataset_id", "task_dataset_input_id", name="uq_dataset_input_ground_truth"),
        Index("ix_task_dataset_ground_truth_dataset_id", "task_dataset_id"),
        Index("ix_task_dataset_ground_truth_input_id", "task_dataset_input_id"),
    )


class TaskDatasetModelOutcome(BaseModel):
    __tablename__ = "task_dataset_model_outcome"

    task_dataset_input_id = Column(
        UUID(as_uuid=True), ForeignKey("task_dataset_input.id", ondelete="CASCADE"), nullable=False, index=True
    )
    task_model_id = Column(
        UUID(as_uuid=True), ForeignKey("task_model.id", ondelete="CASCADE"), nullable=True, index=True
    )
    validation_task_model_id = Column(
        UUID(as_uuid=True), ForeignKey("task_model.id", ondelete="CASCADE"), nullable=True, index=True
    )
    task_definition_id = Column(
        UUID(as_uuid=True), ForeignKey("task_definition.id", ondelete="CASCADE"), nullable=True, index=True
    )
    output = Column(JSONB, nullable=True)
    is_valid_output = Column(Boolean, nullable=False, default=False)
    prediction_result = Column(Enum(PredictionResult), nullable=True)
    score = Column(Float, nullable=True)
    input_tokens = Column(Integer, nullable=True)
    output_tokens = Column(Integer, nullable=True)
    validation_input_tokens = Column(Integer, nullable=True)
    validation_output_tokens = Column(Integer, nullable=True)
    processing_time = Column(Integer, nullable=True)  # in seconds

    # Relationships
    task_model = relationship("TaskModel", uselist=False, foreign_keys=[task_model_id])
    validation_task_model = relationship("TaskModel", uselist=False, foreign_keys=[validation_task_model_id])
    task_definition = relationship("TaskDefinition", uselist=False)
    task_dataset_input = relationship("TaskDatasetInput", foreign_keys=[task_dataset_input_id])

    __table_args__ = (
        UniqueConstraint(
            "task_dataset_input_id",
            "task_model_id",
            "validation_task_model_id",
            "task_definition_id",
            name="uq_dataset_input_model_definition",
        ),
    )


class TaskDatasetExecution(BaseModel):
    __tablename__ = "task_dataset_execution"

    task_dataset_id = Column(
        UUID(as_uuid=True), ForeignKey("task_dataset.id", ondelete="CASCADE"), nullable=False, index=True
    )
    task_definition_id = Column(
        UUID(as_uuid=True), ForeignKey("task_definition.id", ondelete="CASCADE"), nullable=False, index=True
    )
    execution_arn = Column(String, nullable=False)
    status = Column(Enum(TaskDatasetExecutionStatus), nullable=False)
    task_dataset = relationship("TaskDataset", foreign_keys=[task_dataset_id])
