from dataclasses import replace
from typing import TYPE_CHECKING
from uuid import UUID, uuid4
import base64
import datetime
import json
import pickle

from common.clients.arch_api_client import ArchApi<PERSON>lient
from common.clients.smtp import SMTPNotificationsClient
from common.utils.logging import log_function_inputs
from email_validator import EmailNotValidError, validate_email
from infrastructure_common.logging import get_logger
from jinja2 import TemplateSyntaxError
from retrying import retry
from static_common.enums.emails import EmailType
from static_common.enums.organization import EMAIL_ACCOUNT_PER_ORG, PDS_INBOXES_PER_ORG
from static_common.models.sendgrid import SendgridEventType
from werkzeug.datastructures import FileStorage
import jinja2

from copilot.clients.feature_flags import FeatureFlagsClient, FeatureType
from copilot.constants import ALLY_AUTO_INBOX, PARAGON_PSP_EMAIL, PARAGON_WC_INBOX
from copilot.exceptions import NoPDSMailBox
from copilot.kalepa_domain_events.kalepa_events_handler import <PERSON><PERSON>pa<PERSON><PERSON>sHandler
from copilot.models import <PERSON><PERSON><PERSON><PERSON><PERSON>loy<PERSON>, NAICSCode, ReportV2, Submission, User, db
from copilot.models.email_template import EmailTemplate, ScheduledEmail
from copilot.models.emails import (
    Email,
    EmailStatus,
    ReportEmailCorrespondence,
    SendgridEmailData,
)
from copilot.models.errors.emails import MultipleEmailOrganizationsError
from copilot.models.organization import Organization
from copilot.models.sent_email import SentEmail
from copilot.models.types import EmailTemplateType
from copilot.models.verification_check import VerificationCheckResult
from copilot.notifications.email_data import EmailAddress, EmailAttachment, EmailData
from copilot.notifications.errors import (
    MissingRecipientEmailError,
    SendNotificationError,
)
from copilot.notifications.sendgrid_notification_client import (
    SendgridNotificationsClient,
    SendgridTemplateIds,
)
from copilot.schemas.report import AttachmentRequest
from copilot.utils import normalize_user_name, normalize_user_name_casing

if TYPE_CHECKING:
    from sendgrid.helpers.mail.mail import Mail

logger = get_logger()


ONE_SECOND_IN_MS = 1000

PDS_INBOXES_PER_ENV = dict(stage="<EMAIL>", dev="<EMAIL>", test="<EMAIL>")
NATIONWIDE_SENDER_EMAIL = dict(
    _default="<EMAIL>",
    prod="<EMAIL>",
)


def new_flow_disabled():
    return not FeatureFlagsClient.is_feature_enabled(FeatureType.ENABLE_NEW_EMAIL_FLOW)


def get_dynamic_template_data(
    submission: Submission,
    user: User,
    broker: BrokerageEmployee | None,
    broker_contact: BrokerageEmployee | None,
    extra_dynamic_data: dict | None = None,
    email_data: EmailData | None = None,
    custom_subject: str | None = None,
):
    assigned_underwriters = [x.user for x in submission.assigned_underwriters]
    user_name = normalize_user_name(user.name) or ""

    submission_name = submission.name if submission.name else ""
    for client_submission_id in [x.client_submission_id for x in submission.client_submission_ids]:
        potential_submission_name_suffix = " : " + client_submission_id
        if potential_submission_name_suffix in submission.name:
            submission_name = submission_name.split(potential_submission_name_suffix)[0].strip()

    if email_data and submission.name and submission_name != submission.name and submission.name in email_data.subject:
        email_data.subject = email_data.subject.replace(submission.name, submission_name)

    primary_naics = ""
    if submission.primary_naics_code:
        numeric_code = int(submission.primary_naics_code.replace("NAICS_", ""))
        code_detail = NAICSCode.query.get(numeric_code)
        if code_detail and code_detail.title:
            primary_naics = f"{numeric_code} {code_detail.title}"
        else:
            primary_naics = str(numeric_code)

    # use broker contact name if the broker does not have an email. This is because in this case we end up
    # sending an email to the broker contact with broker's name
    broker_name = normalize_user_name_casing(broker_contact.name) if broker_contact and broker_contact.name else ""
    if broker and broker.email:
        broker_name = normalize_user_name_casing(broker.name) if broker and broker.name else ""

    dynamic_data = {
        "submission_name": submission_name if submission_name else (email_data.subject if email_data else ""),
        "user_email": user.email,
        "broker_email": email_data and email_data.to_emails[0],
        "user_name": user_name,
        "broker_name": broker_name,
        "broker_first_name": broker_name.split(" ")[0],
        "subject": custom_subject or (email_data and email_data.subject),
        "rule_name": submission.sent_rule_email,
        "primary_naics": primary_naics,
        "uw_name": normalize_user_name(assigned_underwriters[0].name) if assigned_underwriters else "",
        "uw_email": assigned_underwriters[0].email if assigned_underwriters else "",
        "client_id": ", ".join([x.client_submission_id for x in submission.client_submission_ids]),
        "submission_number": ", ".join([x.client_submission_id for x in submission.client_submission_ids]),
    }
    dynamic_data.update(extra_dynamic_data or {})

    if email_data:
        email_data.dynamic_template_data = dynamic_data

    return dynamic_data


class NotificationHandlerV2:
    KALEPA_ADMIN_EMAIL = "<EMAIL>"
    KALEPA_ADMIN_NAME = "Kalepa Copilot Admin"

    def __init__(
        self,
        env: str,
        sendgrid_notifications_client: SendgridNotificationsClient,
        nationwide_smtp_notifications_client: SMTPNotificationsClient | None,
        arch_api_client: ArchApiClient | None,
    ):
        self._env = env
        self._allow_notifications = env == "prod" or FeatureFlagsClient.is_feature_enabled(
            FeatureType.ALLOW_NON_PROD_EMAILS
        )
        self._sendgrid_notification_client = sendgrid_notifications_client
        self._nationwide_smtp_notifications_client = nationwide_smtp_notifications_client
        self._arch_api_client = arch_api_client

    def queue_and_store_email(self, email_data: EmailData, email: Email, submission: Submission):
        if self._env != "prod":
            email_data.to_emails = [e for e in email_data.to_emails if self._is_email_safe_to_send(e)]
            email_data.cc_emails = [e for e in email_data.cc_emails if self._is_email_safe_to_send(e.email)]
            email_data.bcc_emails = [e for e in email_data.bcc_emails if self._is_email_safe_to_send(e.email)]

        if not self._allow_notifications:
            self.log.info("Notifiactions are disabled", email_data=email_data, submission_id=submission.id)
            return
        else:
            mail = email_data.to_sendgrid_mail()
            sendgrid_email_data = SendgridEmailData(email_id=email.id, data=pickle.dumps(mail))
            email.sendgrid_data = sendgrid_email_data

            db.session.add(email)
            db.session.add(sendgrid_email_data)
            db.session.commit()

            KalepaEventsHandler.send_submission_email_requested_event(
                submission.id, submission.report_id, submission.organization_id, email_data.email_id
            )

    def send_request_demo_email(self, mail_params: dict) -> None:
        email_data = EmailData(
            from_email=self.KALEPA_ADMIN_EMAIL,
            to_emails=["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            dynamic_template_data=mail_params,
        )
        self._sendgrid_notification_client.send_email(email_data, SendgridTemplateIds.REQUEST_DEMO)

    def send_submission_business_changed(self, mail_params: dict, report: ReportV2, submission: Submission) -> None:
        to_emails = set()
        user: User = submission.user
        if True and user.email:  # TODO(Bartek) remove after test round on prod
            to_emails.add("<EMAIL>")
            if user.email.endswith("kalepa.com") or user.email.endswith("kalepa.co"):
                to_emails.add(user.email)
        if not user.email:
            logger.error("User has no email address but have a submission", user=user.id, submission=submission.id)

        dynamic_template_data = {
            "user_name": user.name or user.email or "",
            "submission_name": report.name or str(submission.id),
            "requested_name": mail_params.get("requested_name") or report.name or "(no name provided)",
            "requested_address": mail_params.get("requested_address") or "(no address provided)",
            "submission_id": str(submission.id),
            "old_names": mail_params.get("old_names", ""),
            "old_addresses": mail_params.get("old_addresses", ""),
            "new_names": mail_params.get("new_names", ""),
            "new_addresses": mail_params.get("new_addresses", ""),
            "report_url": report.get_url(),
        }
        email_data = EmailData(
            from_email=self.KALEPA_ADMIN_EMAIL, to_emails=list(to_emails), dynamic_template_data=dynamic_template_data
        )
        self._sendgrid_notification_client.send_email(email_data, SendgridTemplateIds.SUBMISSION_BUSINESS_CHANGED)

    def send_missing_underwriter_notification(
        self, report_identifier: str, organization_id: int, user_email: str
    ) -> None:
        logger.info(
            "Sending notification about missing underwriter for report",
            underwriter=user_email,
            report=report_identifier,
        )
        subject = f"Missing underwriter: {user_email}."
        content = (
            "There was an API call to set underwriter who doesn't exist or "
            "who doesn't have specified name (never logged in).\r\n"
            f"Report id: {report_identifier}\r\n"
            f"Underwriter email: {user_email}"
        )
        to_emails = ["<EMAIL>"]
        if Organization.is_nationwide_for_id(organization_id):
            to_emails.append("<EMAIL>")

        email_data = EmailData(
            from_email=self.KALEPA_ADMIN_EMAIL, to_emails=to_emails, subject=subject, plain_text_content=content
        )
        self._sendgrid_notification_client.send_email(email_data)

    def send_submission_verified_with_failed_checks(
        self, submission: Submission, check_results: list[VerificationCheckResult]
    ) -> None:
        report = submission.reports[0]
        logger.info("Sending notification about failed_checks for report", report_id=report.id, report_name=report.name)
        subject = f"Submission verified with failed checks - {report.id}"

        content = f"REPORT: {report.name}\r\nOWNER: {report.owner.email}\r\n"
        content += f"https://copilot.kalepa.com/report/{report.id}/\r\n\r\n"

        for check_result in check_results:
            content += (
                f"{check_result.name}:"
                f" {check_result.status.value} "
                f"{f'({check_result.error_message})' if check_result.error_message else ''}\r\n"
            )
        to_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
        ]

        email_data = EmailData(
            from_email=self.KALEPA_ADMIN_EMAIL, to_emails=to_emails, subject=subject, plain_text_content=content
        )
        self._sendgrid_notification_client.send_email(email_data)

    @retry(stop_max_attempt_number=3, wait_fixed=ONE_SECOND_IN_MS)
    def send_terms_and_conditions(self, user: User, file: FileStorage, file_name: str) -> None:
        user_name = normalize_user_name(user.name or "")
        user_email = user.email

        dynamic_data = {
            "user_email": user.email,
            "user_name": user_name,
            "subject": "Terms and Conditions",
        }

        attachment = EmailAttachment(
            content=base64.b64encode(file.read()).decode(), file_type="application/pdf", file_name=file_name
        )

        email_data = EmailData(
            from_email=self.KALEPA_ADMIN_EMAIL,
            from_name=self.KALEPA_ADMIN_NAME,
            to_emails=[user_email],
            subject="Terms and Conditions",
            dynamic_template_data=dynamic_data,
            attachments=[attachment],
        )

        self._sendgrid_notification_client.send_email(email_data, SendgridTemplateIds.TERMS_AND_CONDITIONS)

        internal_email_data = replace(
            email_data,
            to_emails=["<EMAIL>", "<EMAIL>"],
            subject="Terms and Conditions (Internal)",
        )
        self._sendgrid_notification_client.send_email(
            internal_email_data, SendgridTemplateIds.TERMS_AND_CONDITIONS_INTERNAL
        )

    def send_submission_declined_notification(
        self,
        user: User,
        submission: Submission,
        preview_only: bool = False,
        cc_emails: list[str] | None = None,
        attachments: list[AttachmentRequest] | None = None,
    ) -> SentEmail:
        fallback_subject = str(submission.name) + " application has been declined"
        correspondence = submission.reports[0].correspondence
        template_id = correspondence.active_email_template_id if correspondence else None
        if not template_id:
            template_id = SendgridTemplateIds.DEFAULT_SUBMISSION_DECLINED.value

        return self.send_submission_notification(
            user=user,
            submission=submission,
            fallback_subject=fallback_subject,
            preview_only=preview_only,
            cc_emails=cc_emails,
            decline_email=True,
            attachments=submission.decline_email_attachment_requests or attachments,
            template_id=template_id,
            custom_template=submission.decline_custom_template,
            custom_subject=submission.decline_custom_subject,
            email_type=EmailType.DECLINE,
        )

    @log_function_inputs
    def send_submission_notification(
        self,
        user: User,
        submission: Submission,
        template_id: str | None = None,
        fallback_subject: str | None = None,
        preview_only: bool = False,
        decline_email: bool = False,
        cc_emails: list[str] | None = None,
        custom_template: str | None = None,
        custom_subject: str | None = None,
        attachments: list[AttachmentRequest] | None = None,
        recipient_emails: list[str] | None = None,
        add_user_to_cc: bool = True,
        extra_dynamic_data: dict | None = None,
        email_type: EmailType | None = EmailType.OTHER,
        override_values: dict | None = None,
    ) -> SentEmail:
        recipient_emails = recipient_emails or []
        attachments = attachments or []
        extra_dynamic_data = extra_dynamic_data or {}

        self.log = logger.bind(
            submission_id=submission.id,
            user_id=user.id,
            template_id=template_id,
            fallback_subject=fallback_subject,
            preview_only=preview_only,
            decline_email=decline_email,
            cc_emails=cc_emails,
            custom_template=custom_template,
            custom_subject=custom_subject,
            recipient_emails=recipient_emails,
        )

        broker = submission.broker
        correspondence_contact = submission.brokerage_contact

        report = submission.reports[0]
        has_broker_email = broker and broker.email
        has_recipient_address = report.correspondence and report.correspondence.recipient_address
        assigned_underwriters: list[User] = [x.user for x in submission.assigned_underwriters]
        assigned_underwriters = sorted(assigned_underwriters, key=lambda x: x.name or x.email)
        template: EmailTemplate = (
            EmailTemplate.query.filter(EmailTemplate.id == template_id).first() if template_id else None
        )
        has_template_email = template and (
            template.to_address or template.type == EmailTemplateType.RECOMMENDATIONS and len(assigned_underwriters) > 0
        )

        if not correspondence_contact and not has_broker_email and not has_recipient_address and not has_template_email:
            self.log.info("No broker email found")
            return SentEmail(error=SendNotificationError.BROKER_EMAIL_NOT_SET)

        try:
            preview_email_data = self.create_preview_email_data(
                submission,
                user,
                recipient_emails,
                cc_emails,
                template,
                broker,
                correspondence_contact,
                add_user_to_cc,
                extra_dynamic_data,
                fallback_subject,
                custom_template,
                custom_subject,
                attachments,
                override_values,
            )

            result = SentEmail(
                tracking_id=preview_email_data.tracking_id,
                email_from=preview_email_data.from_email,
                emails_to=[*preview_email_data.to_emails, *preview_email_data.cc_email_addresses],
                email_reply_to=(
                    preview_email_data.reply_to_emails[0].email if preview_email_data.reply_to_emails else None
                ),
                subject=preview_email_data.subject,
                dynamic_template_data=json.dumps(preview_email_data.dynamic_template_data),
            )

            if preview_only:
                return result
            else:
                is_recommendations_email = template and template.type == EmailTemplateType.RECOMMENDATIONS
                if is_recommendations_email and len(assigned_underwriters) == 0:
                    return self._schedule_email(template, submission, user)
                return self.queue_email(
                    preview_email_data, result, email_type, submission, user, is_recommendations_email
                )

        except EmailNotValidError as e:
            self.log.warning("Invalid email address provided for notification")
            return SentEmail(error=SendNotificationError.INVALID_EMAIL_ADDRESS, error_message=str(e))
        except MissingRecipientEmailError as e:
            self.log.warning("No recipient emails provided for notification")
            return SentEmail(error=SendNotificationError.INVALID_EMAIL_ADDRESS, error_message=str(e))
        except TemplateSyntaxError as e:
            self.log.warning("Invalid template syntax", error=e)
            return SentEmail(error=SendNotificationError.TEMPLATE_SYNTAX_ERROR, error_message=str(e))
        except NoPDSMailBox as e:
            raise e
        except Exception:
            self.log.exception("Failed to send email")
            return SentEmail(error=SendNotificationError.OTHER_ERROR)
        finally:
            self.log = logger

    def queue_email(
        self,
        email_data: EmailData,
        sent_email: SentEmail,
        email_type: EmailType,
        submission: Submission,
        user: User,
        is_recommendations_email: bool = False,
    ) -> SentEmail:
        try:
            email = Email(
                message_id=None,
                email_subject=email_data.subject,
                email_from=email_data.from_email,
                email_body=email_data.html_content,
                email_to=email_data.to_emails[0],
                email_cc=email_data.cc_email_addresses,
                email_attachments_count=len(email_data.attachments),
                attachments=[{"name": x.file_name} for x in email_data.attachments],
                email_tracking_id=email_data.tracking_id,
                type=email_type,
                email_sent_at=datetime.datetime.now(),
                email_reply_to=email_data.reply_to_emails[0].email if email_data.reply_to_emails else None,
                in_reply_to=email_data.message_id,
                email_references=email_data.references,
                email_account=email_data.internal_bcc_email,
                was_sent=False,
            )

            for email_to in email_data.to_emails:
                if not self._is_email_safe_to_send(email_to):
                    self.log.warning(
                        "Emails cannot be sent to external users from a non-production environment", email_to=email_to
                    )
                    sent_email.error = SendNotificationError.OTHER_ERROR
                    return sent_email

            if email_data.internal_bcc_email:
                email_data.bcc_emails.append(EmailAddress(email_data.internal_bcc_email, email_data.internal_bcc_email))
            elif not email_data.internal_bcc_email and not Organization.is_arch_or_arch_test_for_id(
                submission.organization_id
            ):
                self.log.warning(
                    "There is no pds mailbox for organization",
                    organization_name=submission.user.organization.name,
                )
                raise NoPDSMailBox(str(submission.user.organization.name))

            email_data.correspondence_id = self.get_or_create_correspondence_id(submission)
            email_data.email_id = uuid4()

            email.id = email_data.email_id
            email.correspondence_id = email_data.correspondence_id
            email.email_body = email_data.html_content
            if Organization.is_arch_or_arch_test_for_id(submission.organization_id):
                self._handle_arch_email(email, email_data, user)
            if self._is_nationwide(submission.organization_id):
                self._handle_nationwide_email(
                    user=user,
                    email=email,
                    email_data=email_data,
                    overwrite_sender=False,
                    is_recommendations_email=is_recommendations_email,
                )

            self.log.info("Sending email through queue", account=email.email_account, email_data=email_data)
            KalepaEventsHandler.send_submission_email_requested_event(
                submission.id, submission.report_id, submission.organization_id, email_data.email_id
            )

            sendgrid_email_data = SendgridEmailData(email_id=email.id, data=pickle.dumps(email_data.to_sendgrid_mail()))
            email.sendgrid_data = sendgrid_email_data

            db.session.add(email)
            db.session.add(sendgrid_email_data)
            db.session.commit()
            sent_email.email = email
        except NoPDSMailBox as e:
            raise e
        except Exception:
            self.log.info("Email not sent", email_data=email_data)
            self.log.exception("Caught SG exception. Look at info messages for more.")
            sent_email.error = SendNotificationError.OTHER_ERROR
        return sent_email

    def create_preview_email_data(
        self,
        submission: Submission,
        user: User | None,
        recipient_emails: list[str],
        cc_emails: list[str] | None,
        template: EmailTemplate | None,
        broker: BrokerageEmployee | None,
        correspondence_contact: BrokerageEmployee | None,
        add_user_to_cc: bool,
        extra_dynamic_data: dict | None,
        fallback_subject: str | None,
        custom_template: str | None,
        custom_subject: str | None,
        attachments: list[AttachmentRequest],
        override_values: dict | None = None,
    ) -> EmailData:
        override_values = override_values or {}

        email_data = EmailData(tracking_id=uuid4())

        override_reply_to = override_values.get("reply_to")
        override_display_name = override_values.get("display_name")

        if override_reply_to:
            email_data.reply_to_emails = [EmailAddress(override_reply_to, override_display_name)]
        else:
            email_data.reply_to_emails = [EmailAddress(user.email, user.name)]

        if subject := next((report.email_subject for report in submission.reports if report.email_subject), None):
            email_data.subject = "Re: " + subject
        else:
            email_data.subject = fallback_subject or f"New message regarding {submission.name}"

        email_data.references = next(
            (report.email_references for report in submission.reports if report.email_references), None
        )
        email_data.message_id = next(
            (report.email_message_id for report in submission.reports if report.email_message_id), None
        )
        if template:
            self._update_from_template_data(email_data, template, submission)

        self.add_recipient_data(
            email_data,
            recipient_emails,
            cc_emails,
            submission,
            user,
            broker,
            correspondence_contact,
            template,
            add_user_to_cc,
        )

        for email in email_data.to_emails:
            validate_email(email)
        for email in email_data.reply_to_emails:
            validate_email(email.email)

        self.add_sender_data(email_data, template, submission, user, override_display_name)
        self._add_dynamic_template_data(
            custom_subject, email_data, submission, user, broker, correspondence_contact, extra_dynamic_data
        )
        self.render_template(email_data, custom_template, custom_subject, template)

        email_data.internal_bcc_email = PDS_INBOXES_PER_ORG.get(submission.user.organization_id)
        if non_prod_email := PDS_INBOXES_PER_ENV.get(self._env):
            email_data.internal_bcc_email = non_prod_email
        if submission.user.organization.name.startswith("Kalepa"):
            email_data.internal_bcc_email = "<EMAIL>"

        for attachment in attachments:
            email_data.attachments.append(EmailAttachment(attachment.content, attachment.name))

        if Organization.is_arch_or_arch_test_for_id(submission.organization_id):
            self._handle_arch_preview(email_data, user)
        elif Organization.is_paragon_for_id(submission.organization_id):
            self._handle_paragon_preview(email_data, submission, user, add_user_to_cc)
        elif self._is_nationwide(submission.organization_id):
            self._handle_nationwide_email(
                user=user,
                email_data=email_data,
                overwrite_sender=True,
                is_recommendations_email=template and template.type == EmailTemplateType.RECOMMENDATIONS,
            )

        return email_data

    def get_or_create_correspondence_id(self, submission: Submission) -> UUID:
        if not (correspondence_id := submission.report.correspondence_id):
            correspondence = ReportEmailCorrespondence(
                email_account=EMAIL_ACCOUNT_PER_ORG.get(submission.user.organization_id)
            )
            db.session.add(correspondence)
            db.session.flush()
            correspondence_id = correspondence.id
            submission.report.correspondence_id = correspondence_id
            db.session.add(submission.report)
            db.session.commit()

        return correspondence_id

    def render_template(
        self, email_data: EmailData, custom_template: str | None, custom_subject: str | None, template: EmailTemplate
    ):
        custom_template = custom_template or template.html_content
        custom_subject = custom_subject or (template.subject if template else None)

        template = jinja2.Environment(variable_start_string="{{", variable_end_string="}}").from_string(custom_template)
        email_data.html_content = template.render(**email_data.dynamic_template_data)

        if custom_subject:
            template = jinja2.Environment(variable_start_string="{{", variable_end_string="}}").from_string(
                custom_subject
            )
            email_data.subject = template.render(**email_data.dynamic_template_data)

    def add_sender_data(
        self,
        email_data: EmailData,
        template: EmailTemplate | None,
        submission: Submission,
        user: User | None,
        override_display_name: str | None,
    ):
        assigned_underwriters = [x.user for x in submission.assigned_underwriters]

        recommendations_template_display_name = "Copilot"
        display_name_suffix = " via Copilot"
        if self._is_nationwide_with_smtp(submission.organization_id):
            display_name_suffix = ""
            recommendations_template_display_name = "Nationwide"

        if override_display_name:
            display_name = override_display_name
        elif template and template.type == EmailTemplateType.RECOMMENDATIONS:
            if template.reply_to:
                display_name = recommendations_template_display_name
            else:
                display_name = normalize_user_name(assigned_underwriters[0].name) + display_name_suffix
        elif user and user.name:
            display_name = normalize_user_name(user.name) + display_name_suffix
        else:
            display_name = "User" + display_name_suffix

        email_data.from_email = self.KALEPA_ADMIN_EMAIL
        email_data.from_name = display_name

    def add_recipient_data(
        self,
        email_data: EmailData,
        recipient_emails: list[str],
        cc_emails: list[str] | None,
        submission: Submission,
        user: User,
        broker: BrokerageEmployee | None,
        correspondence_contact: BrokerageEmployee | None,
        template: EmailTemplate | None,
        add_user_to_cc: bool,
    ):
        if not template or not template.to_address:
            if recipient_emails:
                email_data.to_emails.extend(recipient_emails)
            elif submission.report.correspondence and submission.report.correspondence.recipient_address:
                email_data.to_emails.append(submission.report.correspondence.recipient_address)
            elif correspondence_contact and correspondence_contact.email:
                email_data.to_emails.append(correspondence_contact.email)
                if broker and broker.email and broker.email != correspondence_contact.email:
                    # we only want to add broker if they are explicitly requested or no cc emails were
                    # requested, empty list of cc_emails means user doesn't want any ccs, including the broker
                    if cc_emails is None or broker.email in cc_emails:
                        email_data.cc_emails.append(EmailAddress(broker.email, broker.name))
            elif broker and broker.email:
                email_data.to_emails.append(broker.email)
            elif not email_data.to_emails:
                raise MissingRecipientEmailError()

        if template and template.type == EmailTemplateType.RECOMMENDATIONS:
            add_user_to_cc = False

        if add_user_to_cc and user.email not in (cc_emails or []) and user.email not in recipient_emails:
            email_data.cc_emails.append(EmailAddress(user.email, user.name))

        if cc_emails:
            safe_emails = [
                email for email in cc_emails if self._is_email_safe_to_send(email) and email not in recipient_emails
            ]
            for cc_email in safe_emails:
                try:
                    validate_email(cc_email)
                    email_data.cc_emails.append(EmailAddress(cc_email))
                except EmailNotValidError:
                    self.log.warning(
                        "Invalid email address provided for decline notification cc", invalid_address=cc_email
                    )

    def _handle_arch_email(self, email: Email, email_data: EmailData, user: User):
        # in case of Arch API we don't have an option of adding a bcc, so we add the internal inbox to cc
        # in case of arch test the inbox is not present so we simply set user email as email account
        if email_data.internal_bcc_email:
            email_data.cc_emails.append(EmailAddress(email_data.internal_bcc_email, "Kalepa Copilot"))
        else:
            email.email_account = user.email

        email.email_cc = email_data.cc_email_addresses

    def _handle_nationwide_email(
        self,
        *,
        user: User,
        email: Email | None = None,
        email_data: EmailData | None = None,
        overwrite_sender: bool = False,
        is_recommendations_email: bool = False,
    ):
        if not email_data and not email:
            logger.error(
                "Nationwide email data and email are both None, how could we proceed? "
                "(Show must go on and so will we anyway, but this is a bug)"
            )
            return

        if FeatureFlagsClient.is_feature_enabled(FeatureType.AUTO_CC_NW_MOREINFO_EMAIL):
            if email_data:
                email_data.cc_emails.append(EmailAddress("<EMAIL>", "Nationwide Moreinfo"))

            if email:
                email.email_cc = (email.email_cc or []) + ["<EMAIL>"]
        else:
            logger.info("Not appending Nationwide Moreinfo to email CCs as feature flag is not enabled")

        if overwrite_sender and self._should_override_sender(user, is_recommendations_email):
            logger.info(
                "Nationwide SMTP enabled, overwriting data",
                new_from_email=user.email,
                email_id=email.id if email else None,
                email_data_set=email_data is not None,
            )
            sender_email = NATIONWIDE_SENDER_EMAIL.get(self._env, NATIONWIDE_SENDER_EMAIL["_default"])
            if email_data:
                email_data.from_email = sender_email
            if email:
                email.email_account = sender_email
                email.email_from = sender_email

    def _should_override_sender(self, user: User, is_recommendations_email: bool = False) -> bool:
        return self._is_nationwide_with_smtp(user.organization_id) or (
            is_recommendations_email
            and user.is_internal_machine_user
            and FeatureFlagsClient.is_feature_enabled(FeatureType.USE_SMTP_FOR_EMAILS)
        )

    def _handle_arch_preview(self, email_data: EmailData, user: User):
        user_email_address = EmailAddress(user.email, user.name)
        if user_email_address not in email_data.cc_emails:
            email_data.cc_emails.append(user_email_address)

        email_data.from_email = user.email
        email_data.from_name = user.name

    def _handle_paragon_preview(self, email_data: EmailData, submission: Submission, user: User, add_user_to_cc: bool):
        user_email_address = EmailAddress(user.email, user.name)
        if user_email_address not in email_data.cc_emails and add_user_to_cc:
            email_data.cc_emails.append(user_email_address)

        if email_data.internal_bcc_email:
            if submission.is_ally_auto:
                email_data.internal_bcc_email = ALLY_AUTO_INBOX
            elif submission.is_paragon_wc:
                email_data.internal_bcc_email = PARAGON_WC_INBOX
            elif submission.is_paragon_psp:
                email_data.internal_bcc_email = PARAGON_PSP_EMAIL

        email_data.reply_to_emails.append(EmailAddress(email_data.internal_bcc_email, "Kalepa Copilot"))

    def _update_from_template_data(self, email_data: EmailData, template: EmailTemplate, submission: Submission):
        assigned_underwriters = [x.user for x in submission.assigned_underwriters]

        if template.to_address:
            email_data.to_emails = [template.to_address]

        if template.cc_addresses:
            email_data.cc_emails = [EmailAddress(email) for email in template.cc_addresses]

        if template.reply_to:
            email_data.reply_to_emails = [EmailAddress(template.reply_to)]
        if template.type == EmailTemplateType.RECOMMENDATIONS:
            if not template.reply_to:
                email_data.reply_to_emails = [EmailAddress(assigned_underwriters[0].email)]

            for au in assigned_underwriters:
                email_data.cc_emails.append(EmailAddress(au.email))

    @staticmethod
    def _add_dynamic_template_data(
        custom_subject: str | None,
        email_data: EmailData,
        submission: Submission,
        user: User,
        broker: BrokerageEmployee | None,
        broker_contact: BrokerageEmployee | None,
        extra_dynamic_data: dict | None,
    ):
        return get_dynamic_template_data(
            submission, user, broker, broker_contact, extra_dynamic_data, email_data, custom_subject
        )

    def _schedule_email(self, template: EmailTemplate, submission: Submission, user: User) -> SentEmail:
        if (
            "{{ uw_name }}" in (template.html_content or "")
            or "{{ uw_email }}" in (template.html_content or "")
            or not template.reply_to
            or not template.cc_addresses
        ):
            db.session.add(
                ScheduledEmail(
                    submission_id=submission.id, rule_name=template.name, template_id=template.id, user_id=user.id
                )
            )
            db.session.commit()

            self.log.info("Scheduling recommendations email for later. No underwriters assigned yet.")

            return SentEmail(scheduled=True)

    def send_queued_email(self, email: Email) -> SentEmail | None:
        log = logger.bind(email_id=email.id)
        org_ids = {r.organization_id for r in email.correspondence.reports}

        if len(org_ids) > 1:
            raise MultipleEmailOrganizationsError(email.id, org_ids)

        org_id = next(iter(org_ids), None)

        result = SentEmail.from_email(email)

        if not self._can_send_email():
            log.warning("Sending email is disabled on non-prod env")
            return result

        # If Arch: Send email via Arch API
        if org_id and Organization.is_arch_or_arch_test_for_id(org_id):
            if not self._arch_api_client:
                log.error("Arch API client is not defined")
                result.error = SendNotificationError.OTHER_ERROR
                return result
            try:
                was_sent = self._arch_api_client.send_email(email.to_email_data())
                if not was_sent:
                    result.error = SendNotificationError.OTHER_ERROR
                else:
                    email.email_delivered = True
            except Exception:
                log.exception("Failed to send Arch email")
                result.error = SendNotificationError.OTHER_ERROR

            return result

        # Try to load SendGrid email data — it's used for both SendGrid and SMTP emails
        try:
            sendgrid_mail: "Mail" = pickle.loads(email.sendgrid_data.data)
        except Exception:
            log.exception("Failed to extract SendGrid email data")
            result.error = SendNotificationError.OTHER_ERROR
            return result

        log = log.bind(sendgrid_mail=sendgrid_mail, email=email)

        # If Nationwide (and FF enabled): Send email via SMTP
        if self._is_nationwide_with_smtp(org_id) and (
            sendgrid_mail.from_email.email
            == NATIONWIDE_SENDER_EMAIL.get(self._env, NATIONWIDE_SENDER_EMAIL["_default"])
        ):
            all_target_emails = []
            for p in sendgrid_mail.personalizations:
                all_target_emails.extend([to.get("email") for to in p.tos])
                all_target_emails.extend([cc.get("email") for cc in p.ccs])
                all_target_emails.extend([bcc.get("email") for bcc in p.bccs])

            log.info("Sending email via SMTP", email_id=email.id, all_target_emails=all_target_emails)
            if not self._nationwide_smtp_notifications_client:
                log.error("Nationwide SMTP client is missing, falling back to SendGrid")
                return self._send_via_sendgrid(result, sendgrid_mail, email)

            if sendgrid_mail.dynamic_template_data or sendgrid_mail.template_id:
                log.error("Email templates are not supported when using SMTP, falling back to SendGrid")
                return self._send_via_sendgrid(result, sendgrid_mail, email)

            log.info("SMTP pre-checks complete, triggering notifications client", email_id=email.id)
            try:
                self._nationwide_smtp_notifications_client.send(mail=sendgrid_mail, force_send=True)
            except:
                log.exception("Failed to send queued SMTP email, falling back to SendGrid")
            else:
                # Save email status to DB — if no exception was raised, the email was sent successfully
                new_status = EmailStatus(
                    email_id=email.id,
                    status=SendgridEventType.DELIVERED,
                    status_details="Email was sent via SMTP and delivered to all recipients",
                    update_timestamp=datetime.datetime.now(),
                )
                db.session.add(new_status)
                db.session.commit()

                result.email_delivered = True
                return result

        # Otherwise: Send email via Sendgrid
        log.info("Sending email via SendGrid", email_id=email.id)
        return self._send_via_sendgrid(result, sendgrid_mail, email)

    def _send_via_sendgrid(self, result: SentEmail, sendgrid_mail: "Mail", email: Email) -> SentEmail:
        try:
            self._sendgrid_notification_client.send(sendgrid_mail, force_send=True)
        except Exception:
            logger.exception("Failed to send queued sendgrid email", email_id=email.id)
            result.error = SendNotificationError.OTHER_ERROR

        return result

    def _is_nationwide(self, org_id: int | None) -> bool:
        return org_id and Organization.is_nationwide_for_id(org_id)

    def _is_nationwide_with_smtp(self, org_id: int | None) -> bool:
        return self._is_nationwide(org_id) and FeatureFlagsClient.is_feature_enabled(FeatureType.USE_SMTP_FOR_EMAILS)

    def _can_send_email(self):
        return self._env == "prod" or FeatureFlagsClient.is_feature_enabled(FeatureType.ALLOW_NON_PROD_EMAILS)

    @staticmethod
    def _generate_and_add_tracking_id(email_data: EmailData) -> UUID:
        email_data.tracking_id = uuid4()
        return email_data.tracking_id

    def _is_email_safe_to_send(self, email: str) -> bool:
        return "@kalepa.co" in email or self._env in ["prod", "test"]
